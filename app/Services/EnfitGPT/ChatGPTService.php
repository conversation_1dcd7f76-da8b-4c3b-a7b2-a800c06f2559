<?php

namespace App\Services\EnfitGPT;

use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ChatGPTService
{
    protected string $apiKey;
    protected string $apiUrl;
    protected string $model;
    protected float $temperature;
    protected int $maxTokens;

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key');
        $this->apiUrl = config('services.openai.api_url', 'https://api.openai.com/v1');
        $this->model = config('services.openai.model', 'gpt-3.5-turbo');
        $this->temperature = config('services.openai.temperature', 0.7);
        $this->maxTokens = config('services.openai.max_tokens', 1000);
    }

    /**
     * Send a chat completion request to ChatGPT
     *
     * @param string $message The user message
     * @param array $options Optional parameters
     * @return array
     * @throws Exception
     */
    public function chat(string $message, array $options = []): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/chat/completions', [
                'model' => $options['model'] ?? $this->model,
                'messages' => [
                    ['role' => 'user', 'content' => $message]
                ],
                'temperature' => $options['temperature'] ?? $this->temperature,
                'max_tokens' => $options['max_tokens'] ?? $this->maxTokens,
                'n' => $options['n'] ?? 1,
                'stream' => $options['stream'] ?? false,
            ]);

            return $this->handleResponse($response);
        } catch (Exception $e) {
            Log::error('ChatGPT API Error: ' . $e->getMessage());
            throw new Exception('Failed to communicate with ChatGPT: ' . $e->getMessage());
        }
    }

    /**
     * Send a chat completion request with conversation history
     *
     * @param array $messages Array of messages with role and content
     * @param array $options Optional parameters
     * @return array
     * @throws Exception
     */
    public function chatWithHistory(array $messages, array $options = []): array
    {
        try {
            // Validate messages format
            foreach ($messages as $message) {
                if (!isset($message['role']) || !isset($message['content'])) {
                    throw new Exception('Each message must have a role and content');
                }
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/chat/completions', [
                'model' => $options['model'] ?? $this->model,
                'messages' => $messages,
                'temperature' => $options['temperature'] ?? $this->temperature,
                'max_tokens' => $options['max_tokens'] ?? $this->maxTokens,
                'n' => $options['n'] ?? 1,
                'stream' => $options['stream'] ?? false,
            ]);

            return $this->handleResponse($response);
        } catch (Exception $e) {
            Log::error('ChatGPT API Error: ' . $e->getMessage());
            throw new Exception('Failed to communicate with ChatGPT: ' . $e->getMessage());
        }
    }

    /**
     * Send a completion request (for older models like text-davinci-003)
     *
     * @param string $prompt The prompt text
     * @param array $options Optional parameters
     * @return array
     * @throws Exception
     */
    public function completion(string $prompt, array $options = []): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/completions', [
                'model' => $options['model'] ?? 'text-davinci-003',
                'prompt' => $prompt,
                'temperature' => $options['temperature'] ?? $this->temperature,
                'max_tokens' => $options['max_tokens'] ?? $this->maxTokens,
                'n' => $options['n'] ?? 1,
                'stream' => $options['stream'] ?? false,
            ]);

            return $this->handleResponse($response);
        } catch (Exception $e) {
            Log::error('ChatGPT API Error: ' . $e->getMessage());
            throw new Exception('Failed to communicate with ChatGPT: ' . $e->getMessage());
        }
    }

    /**
     * Get available models from OpenAI
     *
     * @return array
     * @throws Exception
     */
    public function listModels(): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/models');

            return $this->handleResponse($response);
        } catch (Exception $e) {
            Log::error('ChatGPT API Error: ' . $e->getMessage());
            throw new Exception('Failed to retrieve models: ' . $e->getMessage());
        }
    }

    /**
     * Handle API response
     *
     * @param Response $response
     * @return array
     * @throws Exception
     */
    protected function handleResponse(Response $response): array
    {
        if ($response->successful()) {
            return $response->json();
        }

        $error = $response->json();
        $errorMessage = $error['error']['message'] ?? 'Unknown error occurred';

        Log::error('ChatGPT API Error Response', [
            'status' => $response->status(),
            'error' => $error
        ]);

        throw new Exception('ChatGPT API Error: ' . $errorMessage);
    }

    /**
     * Extract the message content from the response
     *
     * @param array $response
     * @return string|null
     */
    public function extractMessage(array $response): ?string
    {
        if (isset($response['choices'][0]['message']['content'])) {
            return $response['choices'][0]['message']['content'];
        }

        if (isset($response['choices'][0]['text'])) {
            return $response['choices'][0]['text'];
        }

        return null;
    }

    /**
     * Set a custom model for the next request
     *
     * @param string $model
     * @return self
     */
    public function setModel(string $model): self
    {
        $this->model = $model;
        return $this;
    }

    /**
     * Set custom temperature for the next request
     *
     * @param float $temperature
     * @return self
     */
    public function setTemperature(float $temperature): self
    {
        $this->temperature = $temperature;
        return $this;
    }

    /**
     * Set custom max tokens for the next request
     *
     * @param int $maxTokens
     * @return self
     */
    public function setMaxTokens(int $maxTokens): self
    {
        $this->maxTokens = $maxTokens;
        return $this;
    }
}