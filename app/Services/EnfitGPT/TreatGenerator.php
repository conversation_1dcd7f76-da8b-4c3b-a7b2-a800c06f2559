<?php

namespace App\Services\EnfitGPT;

use App\Model\User;
use Illuminate\Support\Facades\Cache;

class TreatGenerator
{
    const MAIN_PROMPT = "Energetisch.fit is a digital platform that combines various alternative healing modalities, such as numerology, biorhythm analysis, and frequency therapy, to address energetic imbalances in individuals. While the platform offers a range of tools for energetic analysis and therapy. The platform's claims are based on alternative healing practices. Energetisch.fit stands out as a holistic platform that combines modern technology with alternative healing practices. Its integration of various therapeutic modalities and educational resources makes it a valuable tool for both professionals and individuals seeking to explore energetic healing. \n\nYou are enfitGPT,\n\nyou are givin advices to people to improve their health quality. So make suggestion to the user, you need to consider the users birth date, name, and birth place.\n\nGive advices on the topics, and you will see the holistic value of the users on the given topic. Create an article based on those informations. Give more specific and personal advices, deeply analysis user's connection with the analysis. Make sure you give advices on how to improve the users health quality, don't return prices, but use them to evaluate the data.";

    const CHAT_PROMPT = "You are enfitGPT. You have already provided the user with a detailed treatment plan. Now, your role is to answer the user's questions about the plan you generated. Be helpful and refer back to the context of the treatment plan. The original language of the plan was %s. Please respond in the same language.\n\nHere is the treatment plan you generated for the user:\n\n---\n%s\n---\n\nIMPORTANT: Please format your entire response in simple HTML. Use <h4> for main titles, <h5> for days of the week or subtitles, <strong> for labels like 'Morning:', and <ul> with <li> for lists of activities. Do not use markdown (like ### or **). Ensure the output is clean HTML content ready to be embedded in a <div>.";

    public function execute($userId)
    {
        $cartData = getCartData();
        $userInfo = User::find($userId);

        $prompt = self::MAIN_PROMPT . "\n\nUser Information:\n" .
            "Name: " . $userInfo->first_name . " " . $userInfo->last_name . "\n" .
            "Email: " . $userInfo->email . "\n" .
            "Telephone: " . $userInfo->telephone . "\n" .
            "Address: " . $userInfo->address . ", " . $userInfo->post_code . " " . $userInfo->village . "\n" .
            "Birth Date: " . $userInfo->gebdatum . "\n" .
            "Birth Place: " . $userInfo->gebort . "\n" .
            "Gender: " . ($userInfo->gender == 1 ? 'Male' : 'Female') . "\n" .
            "Account Created: " . $userInfo->created_at . "\n" .
            "Last Updated: " . $userInfo->updated_at . "\n" .
            "Login Count: " . $userInfo->login_count . "\n" .
            "Timezone: " . $userInfo->timezone_id . "\n\n" .
            "Current Date: ". date('Y-m-d') . "\n\n" .
            "Cart Data:\n";

        $locale = app()->getLocale() ?? 'en'; // Default to English if locale is not set

        $itemNumber = 1;
        foreach ($cartData as $item) {
            $prompt .= $itemNumber . ". Cart Analysis Name: " . $item->options->analysisName . "\n" .
                $itemNumber . ". Cart Analysis Price: " . $item->options->price . "\n" .
                $itemNumber . ". Cart Analysis Duration: " . $item->options->minute . " minutes\n" .
                $itemNumber . ". Cart Product Type: " . $item->options->type . "\n" .
                $itemNumber . ". Cart Analysis User Value: " . $item->options->calculation . "\n" .
                $itemNumber . ". Cart Quantity: " . $item->qty . "\n";
            if (!empty($item->options->color)) {
                $prompt .= $itemNumber . ". Color: " . $item->options->color . "\n";
            }
            $prompt .= "\n"; // Add a newline for readability between items
            $itemNumber++;
        }

        $prompt .= "Generate a detailed treatment plan based on the user's information and cart data. Give output in html code format, give only html code to embed it to my website, don't give whole page just the article. The language should be " . $locale . ".\n";
        $chatGPTService = new ChatGPTService();

        try {
            $response = $chatGPTService->chat($prompt, [
                'model' => 'gpt-4o-mini',
                'temperature' => 0.7,
                'max_tokens' => 2000,
            ]);

            $data = $chatGPTService->extractMessage($response) ?? 'No response from EnfitGPT.';
            $data =  str_replace(['```html', '```'], '', $data);

            // Cache the treatment plan and locale for 1 day
            Cache::put('treatment_plan_' . $userId, $data, 1440);
            Cache::put('treatment_locale_' . $userId, $locale, 1440);

            return $data;
        } catch (\Exception $e) {
            return 'Error generating treatment plan: ' . $e->getMessage();
        }
    }

    public function getCachedTreatmentPlan($userId)
    {
        return Cache::get('treatment_plan_' . $userId);
    }

    public function chat(array $history, string $newMessage, $userId)
    {
        $treatmentPlan = $this->getCachedTreatmentPlan($userId);
        $locale = Cache::get('treatment_locale_' . $userId, 'en');

        if (!$treatmentPlan) {
            return 'No treatment plan found. Please generate a plan first.';
        }

        $systemPrompt = sprintf(self::CHAT_PROMPT, $locale, strip_tags($treatmentPlan));

        $messages = [['role' => 'system', 'content' => $systemPrompt]];

        foreach ($history as $turn) {
            $messages[] = $turn;
        }

        $messages[] = ['role' => 'user', 'content' => $newMessage];

        $chatGPTService = new ChatGPTService();

        try {
            $response = $chatGPTService->chatWithHistory($messages, [
                'model' => 'gpt-4o-mini',
                'temperature' => 0.5,
                'max_tokens' => 1000,
            ]);

            return $chatGPTService->extractMessage($response) ?? 'I am unable to respond at the moment.';
        } catch (\Exception $e) {
            return 'Error processing your message: ' . $e->getMessage();
        }
    }
}