<?php

namespace App\Services\Dashboard;

use Gloudemans\Shoppingcart\Facades\Cart;

class CartManager
{
    const CART_INSTANCE_NAME = 'wishlist';

    public function addToCart($id, $name, $options = [], $qty = 1, $price = 1, $weight = 1)
    {
        $cartData = ['id' => $id, 'name' => $name, 'qty' => $qty, 'price' => $price, 'weight' => $weight, 'options' => $options];

        Cart::instance(self::CART_INSTANCE_NAME)->add($cartData);
    }
    public function getCartItems()
    {
        return Cart::instance(self::CART_INSTANCE_NAME)->content();
    }

    public function getCartItemIds()
    {
        $cartItems = [];
        foreach (Cart::instance(self::CART_INSTANCE_NAME)->content() as $item) {
            $cartItems[] = $item->options['analysisID'];
        }

        return $cartItems;
    }

    /**
     * Update cart item with new duration
     *
     * @param mixed $item
     * @param string $duration
     */
    public function updateCartItemDuration($item, $duration, $price)
    {
        $options = $item->options->toArray();
        $options['price'] = $price;
        Cart::update($item->rowId, ['options' => $options]);
    }

    public function getTotalTime()
    {
        $totalSeconds = collect($this->getCartItems())->sum(function ($item) {
            return $item->options['price'] ?? 0;
        });

        return $this->formatTime($totalSeconds);
    }

    private function formatTime(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
    }
}
