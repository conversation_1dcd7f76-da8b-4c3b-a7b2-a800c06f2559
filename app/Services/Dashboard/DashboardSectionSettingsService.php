<?php

namespace App\Services\Dashboard;

use App\Model\Dashboards\BaseDashboardModal;
use App\Model\Dashboards\ModuleHeadDashboard;
use App\Model\Dashboards\V2Dashboard;
use App\Model\User;
use App\Statics\Calculation;
use App\Traits\PoolAnalysesGeneralFunctions;

class DashboardSectionSettingsService
{
    use PoolAnalysesGeneralFunctions;

    public function getModelInstance(string $model): BaseDashboardModal
    {
        switch ($model) {
            case 'ModuleHeadDashboard':
                return new ModuleHeadDashboard();
            default:
                return new V2Dashboard();
        }
    }

    public function processDashboardDiagramSection(string $modelClass,int $id, array $filters){

        $dashboardSectionSetting = $this->getModelInstance($modelClass)
                ->select('id', 'diagram_type', 'pool_id', 'sorted_diagram','is_override_sorting')
                ->where('id', $id)
                ->first();
        $isOverrideSorting = $dashboardSectionSetting->is_override_sorting ?? false;
        $userId = getUserDetails()->id;
        $user = User::with('userfilter:user_id,filter_type')->find($userId, [
                'id',
                'language_id',
                'first_name',
                'last_name',
                'gebort',
                'gebdatum',
                'datumcore',
                'calculation_with',
                'thema_speichern'
            ]);
        $sorting =  $user->userfilter ? $user->userfilter->filter_type : 3;
        $langBaseAnalyses = $this->poolIdToUserLngBaseAnalysesGet($dashboardSectionSetting->pool_id);
        #need all time de formated analyses name
        $deAnalysesForCalculation = $dashboardSectionSetting->sorted_diagram ? $dashboardSectionSetting->sorted_diagram : $langBaseAnalyses->pluck(app()->getLocale() == 'de' ? 'name' : 'de_name', 'id')->toArray() ?? [];
        #According to the user's language
        $targetedLangbaseAnalysesName = [
            'targetedAnalysesName' => $langBaseAnalyses->pluck('name','id')->toArray() ?? [],
            'description' => $langBaseAnalyses->pluck('description','id')->toArray() ?? [],
            'desc_img'=> $langBaseAnalyses->pluck('desc_image','id')->toArray() ?? [],
            'body_desc' => $langBaseAnalyses->pluck('body_desc','id')->toArray() ?? [],
            'mental_desc' => $langBaseAnalyses->pluck('mental_desc','id')->toArray() ?? []   
        ];
        #bussness logic
        if($filters['longDay']){
            $longDayAnalyses = [];
            $start_date = $user->datumcore;
            for ($i = 0; $i < $filters['longDay']; $i++) {
                $curdate = date('Y-m-d', strtotime($start_date . '+' . $i . ' days'));
                Calculation::setDatumcore($curdate);
                $longDayAnalyses[] = $this->executeAnalysisCalculation($dashboardSectionSetting->pool_id);
            }
            $analysisCalculationResponse = $this->analysesColorCountAlgorithm($longDayAnalyses);
        }else{
            $analysisCalculationResponse = $this->executeAnalysisCalculation($dashboardSectionSetting->pool_id);
        }
        $sortResult = $this->processAndSortLangBaseData($targetedLangbaseAnalysesName, $deAnalysesForCalculation, $analysisCalculationResponse);
        if (!$isOverrideSorting) {
            $sortResult = $this->filterAndSortArray($sortResult, $sorting);
        }
        // Format the sorted result as expected for final response
        return $this->diagramTypeBaseFormat($sortResult, $dashboardSectionSetting->diagram_type, $filters['longDay']);
    }

    private function diagramTypeBaseFormat($result, $diagramType,$filter=null)
    {
        switch ($diagramType) {
            case 'progress':
                return $this->progressFormat($result, $diagramType, $filter);
            default:
                $formatted = $this->formatResultAsExpected($result, $filter);

                return [
                    'success' => true,
                    'filter' => $filter ? true : false,
                    'labels' => $formatted['labels'],
                    'results' => !$filter ? $formatted['values'] : [$formatted['red'], $formatted['green']],
                    'analysis_ids' => $formatted['analysis_ids'],
                    'full_results' => $result,
                    'diagram_type' => $diagramType,
                ];
        }
    }

    private function progressFormat($result, $diagramType,$filter)
    {
        return [
            'success' => true,
            'filter'=> $filter ? true : false,
            'results' => $result,
            'diagram_type' => $diagramType,
        ];
    }

    private function formatResultAsExpected(array $result, $filter): array
    {
        if (!$filter) {
            return [
                'labels' => array_map(fn ($item) => $item['name'] . ': ' . $item['val'], $result),
                'values' => array_column($result, 'val'),
                'analysis_ids' => array_column($result, 'analysis_id'),
            ];
        }

        return [
            'labels' => array_column($result, 'name'),
            'red' => array_column($result, 'RED'),
            'green' => array_column($result, 'GREEN'),
            'analysis_ids' => array_column($result, 'analysis_id'),
        ];
    }

    private function filterAndSortArray(array $array, $sortType): array
    {
        usort($array, function($a, $b) use ($sortType) {
            switch ($sortType) {
                case 1:
                    return strcmp($a['name'], $b['name']); // A-Z
                case 2:
                    return strcmp($b['name'], $a['name']); // Z-A
                case 3:
                    return $a['val'] - $b['val']; // 1-100
                case 4:
                    return $b['val'] - $a['val']; // 100-1
                default:
                    return [];
            }
        });
        return $array;
    }
}