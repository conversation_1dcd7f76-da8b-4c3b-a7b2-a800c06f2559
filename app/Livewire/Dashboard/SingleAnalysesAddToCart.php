<?php

namespace App\Livewire\Dashboard;

use App\Services\Dashboard\CartManager;
use App\Services\Dashboard\ColorAnalysesAddToCartService;
use App\Services\Users\UserService;
use App\Traits\LivewireGeneralFunctions;
use Livewire\Component;

// You need to import the CartModal class if you are dispatching an event to it.
// Assuming the namespace is App\Http\Livewire\Dashboard\CartModal


class SingleAnalysesAddToCart extends Component
{
    use LivewireGeneralFunctions;

    public $poolId;

    public $analysesId = null;

    public $cartData = [];

    public $isAddedToCart = false;

    private $cartManager;

    private $colorAnalysesAddToCartService;

    /**
     * Define dynamic event listeners.
     * This method is evaluated on every request after the component's properties
     * have been initialized, making it ideal for creating event names
     * that depend on component data.
     */
    protected function getListeners()
    {
        return [
            "singleCartItemAdded.{$this->analysesId}" => 'addSimpleTocart',
            "cartItemRemoved.{$this->analysesId}" => 'removeSimpleFromCart',
        ];
    }

    public function __construct()
    {
        $this->colorAnalysesAddToCartService = new ColorAnalysesAddToCartService();
        $this->cartManager = new CartManager();
    }

    public function mount($poolId, $analysesId)
    {
        $this->poolId = $poolId;
        $this->analysesId = $analysesId;
        $this->getAddedCartItem();
    }

    public function addToCart()
    {
        if ($this->isAddedToCart) {
            return;
        }

        if (!UserService::checkUserAccess()) {
            return $this->showToastr(
                'warning',
                trans('action.warning'),
                trans('action.payment_due_message', ['siteName' => env('APP_NAME')])
            );
        }
        // Use the component's property $this->analysesId instead of a method parameter.
        $response = $this->colorAnalysesAddToCartService->addToCart($this->poolId, $this->analysesId);
        $this->showToastrMessage($response);
        $this->dispatch('refreshAllCartInstances');
    }

    private function showToastrMessage($response)
    {
        $this->showToastr(
            $response ? 'success' : 'error',
            $response ? 'Success' : 'Error',
            $response ?  trans('action.cart_save') : trans('action.cart_not_save')
        );
        $this->showCart();
        $this->dispatch('cartUpdated')->to(CartModal::class);
    }

    public function getAddedCartItem()
    {
        $this->cartData = $this->cartManager->getCartItemIds();
        $this->isAddedToCart = in_array($this->analysesId, $this->cartData);
    }

    public function addSimpleTocart()
    {
        $this->isAddedToCart = true;
    }

    public function removeSimpleFromCart()
    {
        $this->isAddedToCart = false;
    }

    #[On('refreshAllCartInstances')]
    public function refreshCartState()
    {
        $this->getAddedCartItem();
    }

    public function render()
    {
        return view('livewire.dashboard.single-analyses-add-to-cart');
    }
}