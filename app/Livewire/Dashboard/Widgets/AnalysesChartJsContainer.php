<?php

namespace App\Livewire\Dashboard\Widgets;

use Livewire\Component;

class AnalysesChartJsContainer extends Component
{
    public $content;
    public $results;
    public $poolId;

    public function mount($content, $results, $poolId = null)
    {
        $this->content = $content;
        $this->results = $results;
        $this->poolId = $poolId;
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.analyses-chart-js-container');
    }
}
