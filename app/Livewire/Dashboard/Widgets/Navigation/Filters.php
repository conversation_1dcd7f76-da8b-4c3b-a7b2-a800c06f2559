<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use App\Traits\LivewireGeneralFunctions;
use Livewire\Component;
use Illuminate\Support\Facades\DB;

class Filters extends Component
{
    use LivewireGeneralFunctions;

    public $lta_days = [];
    public $selectedLongDay = null;
    public $poolId;
    public $widget;

    public function mount($poolId = null, $widget = null)
    {
        $this->poolId = $poolId;
        $this->widget = $widget;
        $this->selectedLongDay = request()->query('longDay') ?? null;
        $this->lta_days = DB::table('lta_days')->orderBy('days', 'ASC')->get(['id','days']);
    }

    public function updatedSelectedLongDay($value)
    {
        // Dispatch event to notify other components about day selection
        $this->dispatch('daySelected', $value);
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.filters');
    }
}
