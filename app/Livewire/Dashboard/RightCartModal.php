<?php

namespace App\Livewire\Dashboard;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Services\Dashboard\CartManager;
use App\Traits\CartTimeParserTrait;
use Livewire\Attributes\On;

class RightCartModal extends Component
{
    use CartTimeParserTrait;

    public $cartData = [];
    private $cartManager;

    public $useroption;
    public $user_data;
    public $auth;
    public $userCanTreat = true;

    public $maxHour = 0;
    public $maxMinute = 10;
    public $maxSecond = 0;
    public $isFirstRenderRightCart = true;

    public function __construct()
    {
        $this->cartManager = new CartManager();
        $this->auth = Auth::user();
        $this->useroption = $this->auth->useroption;
        $this->user_data = $this->auth;
        $this->userCanTreat = session()->get('total_order'. Auth::id()) || ($this->user_data->user_type == 0);
    }

    public function mount()
    {
        $this->refreshCartData();
        $this->isFirstRenderRightCart = true;
        $this->loadTimeFromSession();
        $this->validateTimeValues();
    }

    public function refreshCartData()
    {
        $cartItems = $this->cartManager->getCartItems();

        $this->cartData = collect($cartItems)->map(function ($item) {
            return [
                'rowId' => $item->rowId,
                'id' => $item->id,
                'name' => $item->name,
                'qty' => $item->qty,
                'price' => $item->price,
                'weight' => $item->weight,
                'options' => $item->options,
            ];
        })->toArray();
        
        $this->validateTimeValues();
    }

    /**
     * Calculate total time from cart items
     * @return string formatted as HH:MM:SS
     */
    public function getTotalTimeProperty()
    {
        return $this->cartManager->getTotalTime();
    }

    /**
     * Apply maximum time to all cart items
     */
    #[On('applyMaxTimeRightCart')]
    public function applyMaxTime()
    {
        $itemCount = count($this->cartData);
        
        if ($itemCount === 0) {
            return;
        }
        
        $maxAllowedHour = max(1, $itemCount);
        
        if ($this->maxHour >= $maxAllowedHour) {
            $this->maxHour = $maxAllowedHour;
            $this->maxMinute = 0;
            $this->maxSecond = 0;
        }
        
        $totalDurationSeconds = ($this->maxHour * 3600) + ($this->maxMinute * 60) + $this->maxSecond;
        $perItemDurationSeconds = intval(round($totalDurationSeconds / $itemCount));
        $perItemPrice = $perItemDurationSeconds;
        $formattedDuration = $this->formatDuration($perItemDurationSeconds);

        $cartItems = $this->cartManager->getCartItems();
        
        foreach ($cartItems as $item) {
            $this->cartManager->updateCartItemDuration($item, $formattedDuration, $perItemPrice);
        }
        
        $this->dispatch('cartUpdated');
    }
    
    /**
     * Format duration for display
     */
    private function formatDuration(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
        } else {
            return sprintf('%02d:%02d', $minutes, $remainingSeconds);
        }
    }

    /**
     * Handle external updates to refresh cart data (like from JS)
     */
    #[On('cartUpdated')]
    public function onCartUpdated()
    {
        $this->refreshCartData();
    }

    #[On('refreshComponent')]
    public function refreshComponent()
    {
        $this->refreshCartData();
        $this->updateTimeFromTotalTime();
        $this->saveTimeToSession();
        $this->render();
    }

    #[On('cartCleared')]
    public function clearCart()
    {
        $analyseIds = collect($this->cartData)->pluck('options.analysisID')->toArray();

        foreach ($analyseIds as $analyseId) {
            $this->dispatch("cartItemRemoved.{$analyseId}")->to(SingleAnalysesAddToCart::class);
        }

        $this->cartData = [];

        $this->resetTimeToDefault();
    }

    #[On('toggleRightCartModal')]
    public function toggleModal()
    {
        $this->isFirstRenderRightCart = false;
    }

    #[On('updateMaxHourRight')]
    public function updateMaxHour($value)
    {
        $maxAllowedHour = max(1, count($this->cartData));
        $this->maxHour = max(0, min($maxAllowedHour, intval($value)));
        $this->saveTimeToSession();
    }

    #[On('updateMaxMinuteRight')]
    public function updateMaxMinute($value)
    {
        $maxAllowedHour = max(1, count($this->cartData));
        
        if ($this->maxHour >= $maxAllowedHour) {
            $this->maxMinute = 0;
            $this->saveTimeToSession();
            return;
        }
        
        $minMinute = $this->maxHour > 0 ? 0 : 1;
        $this->maxMinute = max($minMinute, min(59, intval($value)));
        $this->saveTimeToSession();
    }

    #[On('updateMaxSecondRight')]
    public function updateMaxSecond($value)
    {
        $maxAllowedHour = max(1, count($this->cartData));
        
        if ($this->maxHour >= $maxAllowedHour) {
            $this->maxSecond = 0;
            $this->saveTimeToSession();
            return;
        }
        
        $this->maxSecond = max(0, min(59, intval($value)));
        $this->saveTimeToSession();
    }

    /**
     * Load time values from session
     */
    private function loadTimeFromSession()
    {
        $this->maxHour = session('cart_max_hour', 0);
        $this->maxMinute = session('cart_max_minute', 10);
        $this->maxSecond = session('cart_max_second', 0);
    }

    /**
     * Save time values to session
     */
    private function saveTimeToSession()
    {
        session(['cart_max_hour' => $this->maxHour]);
        session(['cart_max_minute' => $this->maxMinute]);
        session(['cart_max_second' => $this->maxSecond]);
    }
    
    public function render()
    {
        return view('livewire.dashboard.right-cart-modal');
    }
}
