<?php

namespace App\Livewire\Dashboard\Chart;

use Livewire\Component;

class Polar<PERSON>rea<PERSON>hart extends Component
{
    public $chartId;
    public $results;
    public $labels;
    public $filter;
    public $diagramType;
    public $fullResults;
    public $poolId;
    public $content;
    public $allowInfo = true;

    public function mount($content, $results, $labels, $filter, $diagramType, $fullResults, $poolId)
    {
        $this->content = $content;
        $this->chartId = $content->id;
        $this->results = $results;
        $this->labels = $labels;
        $this->filter = $filter;
        $this->diagramType = $diagramType;
        $this->fullResults = $fullResults;
        $this->poolId = $poolId;
    }
    
    public function render()
    {
        return view('livewire.dashboard.chart.polar-area-chart');
    }
}
