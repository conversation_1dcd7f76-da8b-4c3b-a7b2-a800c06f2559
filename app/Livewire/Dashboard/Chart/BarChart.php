<?php

namespace App\Livewire\Dashboard\Chart;

use Livewire\Component;

class <PERSON><PERSON><PERSON> extends Component
{
    public $content;
    public $results;
    public $labels;
    public $filter;
    public $fullResults;
    public $poolId;
    public $allowInfo;

    public function mount($content, $results, $labels, $filter, $fullResults, $poolId, $allowInfo = true)
    {
        $this->content = $content;
        $this->results = $results;
        $this->labels = $labels;
        $this->filter = $filter;
        $this->fullResults = $fullResults;
        $this->poolId = $poolId;
        $this->allowInfo = $allowInfo;
    }

    public function render()
    {
        return view('livewire.dashboard.chart.bar-chart');
    }
} 