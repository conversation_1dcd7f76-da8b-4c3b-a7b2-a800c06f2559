<?php

use App\Enums\RequestAttributes;
use App\Model\Analyse;
use App\Model\BodyImage;
use App\Model\EnAnalyses;
use App\Model\GlobalSetting;
use App\Model\Group;
use App\Model\MentalImage;
use App\Model\Menu;
use App\Model\MenuSetting;
use App\Model\Product;
use App\Model\ProductSetting;
use App\Model\Submenu;
use App\Model\User;
use App\Model\UserSubMenu;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

function checkIfDashbordDesignerEnabled()
    {
        if(!request()->route()?->getPrefix() == '/dashboard') {
            return false;
        }

        if (!checkIfUserAllowedToAccessDashboardDesigner()) {
            return false;
        }

        $forbiddenNames = [
            'dashboard.focusView',
            'dashboard.designer',
            'dashboard.designer.edit',
        ];

        $routeName = request()->route()?->getName();
        if (in_array($routeName, $forbiddenNames)) {
            return false;
        }

        return true;
    }

    function checkIfUserAllowedToAccessDashboardDesigner()
    {
        $allowedUserIds = [31, 1288];
        if (auth()->check() && in_array(auth()->id(), $allowedUserIds)) {
            return true;
        }

        return false;
    }

    function checkIfDashboardDesignerExist()
    {
        //get url path of current page
        $path = '/'.request()->path();
        //get the path remove the host
        $model =  \App\Model\Dashboards\Dashboard::where('url', $path)->whereNot('active', 0)->firstOr(function () {
            return false;
        });

        if(!$model || $model->isActive() === false) {
            return false;
        }

        request()->attributes->set(RequestAttributes::DASHBOARD_URL->value, url($path));

        return $model->dashboard_data;
    }

    function setServerLocal($locale)
    {
        /*
         * This function is disabled
         *
        //check server local_id
        $langId = env('SERVER_LANG_ID', null);
        $fullLocale = $locale . '_' . $langId;

        //check if the language folder exists
        if($langId && \Illuminate\Support\Facades\File::exists(resource_path() . '/lang/' . $fullLocale)) {
            $locale = $fullLocale;
        }
        */

        app()->setLocale($locale);
    }

    function userFilterid($userid)
    {
        $userdetails = User::with(["userfilter", "useroption"])->find($userid);
        return $userdetails;
    }

    function productColors($proid)
    {
        return ProductSetting::with("product")->where('product_id', $proid)->first();
    }
    function ownProductColors($proid)
    {
        // $colors = ProductSetting::with("product")->where('product_id', $proid)->firstOrfail();
        $colors = MenuSetting::where('menu_id', $proid)->first();
        return $colors;
    }


    function productByFirstTabid($proid)
    {
        $productTab = Submenu::where('id', $proid)->firstOrfail();
        // $productTab = Submenu::with("product")->where('id', $proid)->firstOrfail();
        return $productTab;
    }

    function analysesByid($poolid, $anaid)
    {
        if (app()->getLocale() == 'de')  $analyses = Analyse::whereHas('pools', function ($query) use ($anaid) {
                                                        $query->where('analyse_id', '=', $anaid);
                                                    })->where('id', $anaid)->first();
        else if (app()->getLocale() == 'en')  $analyses = EnAnalyses::whereHas('pools', function ($query) use ($anaid) {
                                                        $query->where('analyse_id', '=', $anaid);
                                                    })->where('id', $anaid)->first();
        // $analyses = Analyse::whereHas('pools', function ($query) use ($anaid) {
        //     $query->where('analyse_id', '=', $anaid);
        // })->where('id', $anaid)->first();

        return $analyses;
    }


    function poolsId($subid)
    {
        $name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'menu_name' : Lang::locale() . '_menu_name as menu_name';
        $db_pool_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'pool_name' : Lang::locale() . '_pool_name as pool_name';
        $submenuPools = Submenu::select('id', 'product_id', $name)->with(["pools:pools.id,". $db_pool_name])->find($subid);
        return $submenuPools;
    }

    function poolsIds($subid)
    {
        return DB::table('pool_submenus')->where('submenu_id',$subid)->groupBy('pool_id')->pluck('pool_id')->toArray();
        // $submenuPools = Submenu::select('id')->with(["pools:pools.id"])->find($subid);
        // if($submenuPools->pools > 0)
        //     foreach($submenuPools->pools as $pools)
        //         $poolids[] = $pools->id;
        // return $poolids;
    }

    function getPoolIdsBySubID($subid){
        // dd(DB::table('pool_submenus')->where('submenu_id',$subid)->pluck('id'));
        return DB::table('pool_submenus')->where('submenu_id',$subid)->pluck('pool_id')->toArray();
    }

    function poolsIdsOwn($subid)
    {
        $name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'menu_name' : Lang::locale() . '_menu_name as menu_name';
        $db_pool_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'pool_name' : Lang::locale() . '_pool_name as pool_name';
        $submenuPools = Submenu::select('id','product_id', $name)->with(["pools:pools.id," . $db_pool_name])->whereIn('id', $subid)->get();
        $poolids = array();

        if(count($submenuPools) > 0){
            foreach($submenuPools as $sub){
                if($sub->pools > 0){
                    foreach($sub->pools as $pools)
                        $poolids[] = $pools->id;
                }
            }
        }

        return $poolids;
    }


    function checkAllProduct($proid)
    {
        return Product::with("submenu")->where('id', $proid)->firstOrfail();
    }

    function biorythVisibleDetails()
    {
        return cache()->remember("global_settings", now()->addMinutes(60), function () {
            return GlobalSetting::where('gs_type', 'other')->first();
        });
    }
    function biorythVisibleDetailsByName($name)
    {
        return GlobalSetting::where('gs_type', $name)->firstOrfail();
    }


    function randomValue($userid)
    {
        $ranValue = DB::table("save_calculations")->select("save_calculations.ran_value", "analyse_save_calculations.analyse_id")
                    ->join("analyse_save_calculations", "analyse_save_calculations.save_calculation_id", "save_calculations.id")
                    ->where("save_calculations.user_id", $userid)->get();
        $ran_values = [];

        if(!empty($ranValue)){
            for($i=0;$i<count($ranValue);$i++){
                $ran_values[$ranValue[$i]->analyse_id] = $ranValue[$i]->ran_value;
            }
        }

        return $ran_values;
    }


    function savedCauses($subid, $anaid, $userid)
    {
        $date = date("Y-m-d");
        $table_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'causes' : Lang::locale() . '_causes';
        $todayCause = DB::table("record_causes")
                     ->join("analyse_record_causes", "analyse_record_causes.record_cause_id", "=", "record_causes.id")
                     ->join("submenu_record_causes", "submenu_record_causes.record_cause_id", "=", "record_causes.id")
                     ->join($table_name, $table_name.".id", "=", "record_causes.causes_id")
                     ->select("record_causes.*","analyse_record_causes.analyse_id", $table_name.".*")
                     ->where("record_causes.user_id", $userid)
                     ->where("record_causes.date", $date)
                     ->where("submenu_record_causes.submenu_id", $subid)
                     ->whereIn("analyse_record_causes.analyse_id", $anaid)->get();

        $cauids = array(); $midids = array(); $tipids = array(); $all_causes = array();

        if(count($todayCause) > 0)
        {
            foreach($todayCause as $cause)
            {
                if($cause->type == "Causes") $cauids[$cause->analyse_id] = $cause;
                if($cause->type == "Medium") $midids[$cause->analyse_id] = $cause;
                if($cause->type == "Tipp") $tipids[$cause->analyse_id] = $cause;
            }
        }

        $all_causes = array();
        $all_causes['causes'] = $cauids;
        $all_causes['medium'] = $midids;
        $all_causes['tipp'] = $tipids;

        return $all_causes;
    }
    function savedCausesRemote($subid, $anaids, $userid, $date)
    {
        $cauids = $midids = $tipids = array();
        $table_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'causes' : Lang::locale() . '_causes';
        $todayCauses = DB::table("record_causes")
            ->join("analyse_record_causes", "analyse_record_causes.record_cause_id", "record_causes.id")
            ->join("submenu_record_causes", "submenu_record_causes.record_cause_id", "record_causes.id")
            ->join($table_name, $table_name.".id", "record_causes.causes_id")
            ->select("record_causes.*","analyse_record_causes.analyse_id", $table_name.".*")
            ->where("record_causes.date", $date)
            ->where("record_causes.user_id", $userid)
            ->where("submenu_record_causes.submenu_id", $subid)
            ->whereIn("analyse_record_causes.analyse_id", $anaids)
            ->get();

            foreach ($todayCauses as $cause) {
                switch ($cause->type) {
                    case 'Causes':
                        $cauids[$cause->analyse_id] = $cause;
                        break;
                    case 'Medium':
                        $midids[$cause->analyse_id] = $cause;
                        break;
                    case 'Tipp':
                        $tipids[$cause->analyse_id] = $cause;
                        break;
                }
            }
        return [
            'causes' => $cauids,
            'medium' => $midids,
            'tipp'  => $tipids
        ];

    }


    function randomCauses($subid)
    {
        $table_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'causes' : Lang::locale() . '_causes';
        $randomCau = DB::table("group_causes")
                    ->join($table_name, $table_name.".group_id", "=", "group_causes.group_id")
                    ->select("group_causes.*", $table_name.".*")
                    ->where("group_causes.submenu_id", $subid)->get();

        $randomMid = DB::table("group_mediums")
                    ->join($table_name, $table_name.".group_id", "=", "group_mediums.group_id")
                    ->select("group_mediums.*", $table_name.".*")
                    ->where("group_mediums.submenu_id", $subid)->get();

        $randomTip = DB::table("group_tips")
                    ->join($table_name, $table_name.".group_id", "=", "group_tips.group_id")
                    ->select("group_tips.*", $table_name.".*")
                    ->where("group_tips.submenu_id", $subid)->get();

        $all_causes = array();
        $all_causes['causes'] = $randomCau;
        $all_causes['medium'] = $randomMid;
        $all_causes['tipp'] = $randomTip;

        return $all_causes;

    }

    function _randomCauses($subid){
        $all_causes = array();
        $table_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'causes' : Lang::locale() . '_causes';
        $all_causes['causes'] = DB::table("group_causes")
                    ->join($table_name, $table_name.".group_id", "=", "group_causes.group_id")
                    ->select("group_causes.*", $table_name.".*")
                    ->where("group_causes.submenu_id", $subid)->count();

        $all_causes['medium'] = DB::table("group_mediums")
                    ->join($table_name, $table_name.".group_id", "=", "group_mediums.group_id")
                    ->select("group_mediums.*", $table_name.".*")
                    ->where("group_mediums.submenu_id", $subid)->count();

        $all_causes['tipp'] = DB::table("group_tips")
                    ->join($table_name, $table_name.".group_id", "=", "group_tips.group_id")
                    ->select("group_tips.*", $table_name.".*")
                    ->where("group_tips.submenu_id", $subid)->count();
        return $all_causes;
    }


    function randomCauseOwn($subid)
    {
        $table_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'causes' : Lang::locale() . '_causes';
        $randomCau = DB::table("group_causes")
        ->join($table_name, $table_name . ".group_id", "=", "group_causes.group_id")
        ->select("group_causes.*", $table_name . ".*")
            ->where("group_causes.submenu_id", $subid)->get();

        $randomMid = DB::table("group_mediums")
        ->join($table_name, $table_name . ".group_id", "=", "group_mediums.group_id")
        ->select("group_mediums.*", $table_name . ".*")
            ->where("group_mediums.submenu_id", $subid)->get();

        $randomTip = DB::table("group_tips")
        ->join($table_name, $table_name . ".group_id", "=", "group_tips.group_id")
        ->select("group_tips.*", $table_name . ".*")
            ->where("group_tips.submenu_id", $subid)->get();

        $all_causes = array();
        $all_causes['causes'] = $randomCau;
        $all_causes['medium'] = $randomMid;
        $all_causes['tipp'] = $randomTip;

        return $all_causes;

    }

    function getCauses($groupid)
    {
        $causes = Group::with("causes")->where('id', $groupid)->firstOrfail();
        return $causes;
    }

    function priceValue($proid = null, $subid = null, $anaid = null)
    {
        $anaPrice = DB::table('analyses')->where('ana_min_price','!=','')->where('ana_max_price','!=','')->select('ana_min_price', 'ana_max_price')->find($anaid);
        if ($anaPrice)  return array('min_price' => $anaPrice->ana_min_price, 'max_price' => $anaPrice->ana_max_price);

        $subPrice = DB::table('submenus')->where('sub_min','!=','')->where('sub_max','!=','')->select('sub_min', 'sub_max')->find($subid);
        if ($subPrice) return array('min_price' => $subPrice->sub_min, 'max_price' => $subPrice->sub_max);

        $productPrice = DB::table('product_settings')->where('min_price','!=','')->where('max_price','!=','')->select('min_price', 'max_price')->where('product_id', $proid)->first();
        if ($productPrice) return array('min_price' => $productPrice->min_price, 'max_price' => $productPrice->max_price);

        $globalPrice = DB::table('global_settings')->select('gs_min_price', 'gs_max_price')->where('gs_type', "other")->first();
        return array('min_price' => $globalPrice->gs_min_price, 'max_price' => $globalPrice->gs_max_price);
    }


    function getSubmenusByProductId($proid)
    {
        $submenus = Product::with("submenu")->find($proid);
        return $submenus;
    }
    function getOwnSubmenusByProductId($proid)
    {
        $submenus = Product::with("submenu")->find($proid);
        return $submenus;
    }


    function getUserSubmenusByProductId($proid)
    {
        $submenus = Menu::with("usersubmenus")->find($proid);
        return $submenus;
    }

    function getOwnSubmenusByMenuid($ownid)
    {
        $submenus = Menu::with('usersubmenus')->find($ownid);
        return $submenus;
    }

    function getProductBySubmenu($subid)
    {
        $all_pro = array();
        Submenu::with(['product','product_setting'])->whereIn("id", $subid)->get(['id','product_id'])->map(function($sub) use(&$all_pro){
            return $all_pro[$sub->id] = $sub->product;
        });

        // if(count($product) > 0)
        // {
        //     foreach($product as $pro)
        //         $all_pro[$pro->id] = $pro->product;
        // }

        return $all_pro;
    }

    function getUserOwnMenuBySubId($subid)
    {
        $all_pro = array();
        UserSubMenu::with(['menu','menu_setting'])->whereIn("id", $subid)->get(['id','menu_id'])->map(function($sub) use(&$all_pro){
            return $all_pro[$sub->id] = $sub;
        });

        // if(count($menu) > 0)
        // {
        //     foreach($menu as $pro)
        //         $all_pro[$pro->id] = $pro->menu;
        // }

        return $all_pro;
    }

    function getMenuNameBySubid($id)
    {
        $menu = Menu::find($id);
        return $menu->name;
    }

    function getBodyImage($anaid)
    {
        $bodyimages = [];
        $bodyImg = BodyImage::whereIn('analyse_id', $anaid)->get();

        if(!empty($bodyImg))
            foreach($bodyImg as $key => $val)
                $bodyimages[$val->image]  = $val->analyse_id;

        return $bodyimages;
    }

    function getMentalImage($anaid)
    {
        $mentalimages = [];
        $mentalImg = MentalImage::whereIn('analyse_id', $anaid)->get();

        if(!empty($mentalImg))
            foreach($mentalImg as $key => $val)
                $mentalimages[$val->image]  = $val->analyse_id;

        return $mentalimages;
    }


    function getAllCause($causes_ids)
    {
        $all_cause = DB::table("causes")->whereIn("id", $causes_ids)->get();
        $causes = array();

        if(count($all_cause) > 0)
        {
            foreach($all_cause as $cau)
                $causes[$cau->id] = $cau;
        }

        return $causes;
    }

    function getProducts()
    {
        $proids = Product::pluck('id')->toArray();
        return $proids;
    }
    #search data on a array using id
    function searchIdInArray($id,$type, $array)
    {
        foreach ($array as $key => $val) {
            $checkType = (array_key_exists('Type', $val)) ? 2 : 1;
            if ($val['id'] == $id && $type == $checkType) {
                return $array[$key];
            }
        }
    }

/*
 * Get all product from session
 * status 1 system
 * status 2 own
 * status 3 system combo
 * status 4 own combo
 */
    function getMenus($menus_list){
        $menu_list = array();
        $menus = $menus_list;
        $id_array = array();
        $_sys = (isset(request()->proid) && !isset(request()->comid) )? request()->proid : false;
        $_own = (isset(request()->ownid) && !isset(request()->comid) )? request()->ownid : false;
        if(!empty($menus)){
            foreach ($menus as $key => $menu) {
                if($menu['menus'] != null && $menu['status'] == 4){
                    if($menu['menus'] != null){
                        $search = array();
                        foreach ($menu['menus'] as $key => $com) {
                            $getResult = searchIdInArray($com->menu_id, $com->type, $menus);
                            if($getResult == null) continue;
                            $array = array(
                                'id' => $getResult['id'],
                                'product_name' => $getResult['product_name'],
                                'menus' => null,
                                'submenus' => $getResult['submenus'],
                                'sp' => array_key_exists('sp', $getResult) ? $getResult['sp'] : null,
                                'package_name' => array_key_exists('package_name', $getResult) ? $getResult['package_name'] : null,
                                'type' => array_key_exists('Type', $getResult) ? 2 : 1
                            );
                            array_push($search, $array);
                            $array = array();
                        }
                        $data = array(
                            'id' => $menu['id'],
                            'product_name' => $menu['product_name'],
                            'menus' =>  $search,
                            'submenus' => null,
                            'sp' => array_key_exists('sp', $menu) ? $menu['sp'] : null,
                            'package_name' => array_key_exists('package_name', $menu) ? $menu['package_name'] : null,
                            'type' => 4
                        );
                        $search = array();
                        array_push($menu_list, $data);
                        $data = array();
                    }
                    else continue;
                }elseif ($menu['menus'] != null && $menu['status'] == 3) {
                    $data = array(
                        'id' => $menu['id'],
                        'product_name' => $menu['product_name'],
                        'menus' =>  $menu['menus'],
                        'submenus' => null,
                        'sp' => array_key_exists('sp', $menu) ? $menu['sp'] : null,
                        'package_name' => array_key_exists('package_name', $menu) ? $menu['package_name'] : null,
                        'type' => 3
                    );
                    array_push($menu_list, $data);

                } else{
                    $data = array(
                        'id' => $menu['id'],
                        'product_name' => $menu['product_name'],
                        'menus' => null,
                        'submenus' => $menu['submenus'],
                        'sp' => array_key_exists('sp', $menu) ? $menu['sp'] : null,
                        'package_name' => array_key_exists('package_name', $menu) ? $menu['package_name'] : null,
                        'type' => array_key_exists('Type', $menu) ? 2 : 1,
                        'menu_active' => (array_key_exists('Type', $menu)) ? (($_own && $_own == $menu['id']) ? true:false) : (($_sys && $_sys == $menu['id']) ? true:false)
                    );
                    array_push($menu_list, $data);
                    $data = array();
                }

            }
        }
        $ids = array_unique($id_array);
        foreach ($ids as $key => $id) {
            foreach ($menu_list as $key => $menu) {
                if ($menu['id'] == $id && 1 == $menu['type']) {
                    unset($menu_list[$key]);
                }
            }
        }
        return $menu_list;
    }
/*
    check array have the id data available or not
*/
    function searchDataArray($id, $type, $array)
    {
        foreach ($array as $key => $val) {
            if ($val['id'] == $id && $type == $val['status']) {
                return $array[$key];
            }
        }
    }

