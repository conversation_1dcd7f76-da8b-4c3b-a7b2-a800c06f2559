<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\ActionType;
use App\Enums\CausesType;
use App\Enums\UserRoll;
use App\Http\Controllers\Controller;
use App\Model\AllSingleAction;
use App\Model\Analyse;
use App\Model\Dashboards\ModuleHeadDashboard;
use App\Model\DashboardWidgetSetting;
use App\Model\DcIconCategory;
use App\Model\EinflueImage;
use App\Model\EnEinflueImage;
use App\Model\FrabklangWatchSubmenu;
use App\Model\GlobalSetting;
use App\Model\imageEin;
use App\Model\Pdf;
use App\Model\Product;
use App\Model\Submenu;
use App\Model\User;
use App\Model\UserSubMenu;
use App\Services\Users\UserService;
use App\Traits\PoolAnalysesGeneralFunctions;
use Carbon\Carbon;
use Gloudemans\Shoppingcart\Facades\Cart;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Yajra\Datatables\Datatables;
use App\Enums\AnalysisColorEnum;

class DashboardController extends Controller
{
    use PoolAnalysesGeneralFunctions;

    public function index()
    {
        $data['all_menus'] = __getMenus();
        $data['isAppPurposeServer'] = Cache::rememberForever('dashboard_widget_setting_exists', function () {
            return DashboardWidgetSetting::first();
        }) ? true : false || AllSingleAction::getFlag(ActionType::NEW_DASHBOARD_PRODUCT_VIEW);

        $data['isActiveNewDashboardProductView'] = AllSingleAction::getFlag(ActionType::NEW_DASHBOARD_PRODUCT_VIEW);

        $data['isNewDashboardV2Active'] = AllSingleAction::getFlag(ActionType::NEW_DASHBOARD_V2_ACTIVE);

        return view('Frontend.dashboard.View.dashboard', compact('data'));
    }

    public function moduleDashboard($moduleId)
    {
        ModuleHeadDashboard::setModuleId($moduleId);
        return view('Frontend.dashboard.View.module-head-dashboard-container')->render();
    }

    public function getDashboardContent()
    {
        $admin = User::with(['userfilter', 'useroption'])->find(getID());
        $loginUser = ($admin->id == getUserId()) ? $admin : User::with(['userfilter', 'useroption'])->find(getUserId() ?? Auth::id());

        $widgetInfo = Cache::get('dashboard_widget_setting_exists');

        if (checkDashAccess(getAuthId()) && !$widgetInfo) {
            $analyses = $this->productAnalyses(41, 20, $loginUser);
            return view('Frontend.dashboard.View.chakra_dashboard_view', ['analyses' => $analyses['analyses'], 'anaids' => $analyses['anaids'], 'bodyimages' => $analyses['bodyimages'], 'mentalimages' => $analyses['mentalimages']])->render();
        }

        $filterId = $loginUser->userfilter;
        $userOption = $loginUser->useroption;
        $all_menus = __getMenus();

        $data['randomAnalysis'] = $userOption->ran_ana;
        $data['allMenus'] = $all_menus;

        if (env('APP_NAME') == 'Reset4me' || $this->_frabkCheck($loginUser, $data['allMenus'])) {
            $farbklangProducts = $this->farbklangdashboard($all_menus);
            if ($farbklangProducts['status'] == true) {
                return view('Frontend.dashboard.View.Content.frabklang', ['products' => $farbklangProducts['collection'], 'farbklang' => true])->render();
            }
        }

        $product = $this->getDashVisiableProduct($admin, $all_menus);

        if ($admin->user_type == UserRoll::Therapist) {
            if ($userOption->dashboard_option == 33) $proAnalyses = $this->averageDashboardView($product, $loginUser, $filterId, $submenu_check = array());
            else $proAnalyses = $this->DashboardView($product, $loginUser, $filterId);

            if ($userOption->dashboard_option == 11)
                return view('Frontend.dashboard.View.Content.progress', ['data' => $data, 'product' => $proAnalyses['product'], 'submenu' => $proAnalyses['submenu']])->render();
            else if ($userOption->dashboard_option == 33)
                return view('Frontend.dashboard.View.Content.average', ['data' => $data, 'product' => $proAnalyses['product'], 'submenu' => $proAnalyses['submenu'], 'filterid' => $filterId->filter_type])->render();
            else
                return view('Frontend.dashboard.View.Content.default', ['data' => $data, 'product' => $proAnalyses['product'], 'submenu' => $proAnalyses['submenu']])->render();
        } elseif ($admin->user_type == UserRoll::Admin || $admin->user_type == UserRoll::Demo) {

            if ($userOption->dashboard_option == 33) $proAnalyses = $this->averageDashboardsView($all_menus, $loginUser, $filterId);
            else $proAnalyses = $this->adminDashboardsView($all_menus, $loginUser, $filterId);

            if ($userOption->dashboard_option == 11)
                return view('Frontend.dashboard.View.Content.admin-progress', ['data' => $data, 'product' => $proAnalyses['product']])->render();
            else if ($userOption->dashboard_option == 33)
                return view('Frontend.dashboard.View.Content.admin-average', ['data' => $data, 'products' => $proAnalyses['product']])->render();
            else
                return view('Frontend.dashboard.View.Content.admin-default', ['data' => $data, 'product' => $proAnalyses['product']])->render();
        } else {#for sub users
            if ($userOption->dashboard_option == 33) {
                $proAnalyses = $this->averageDashboardsView($all_menus, $loginUser, $filterId);
            } else {
                $proAnalyses = $this->DashboardsView($all_menus, $loginUser, $filterId);
            }
            // if(request()->test) dd($product,$proAnalyses);
            if ($userOption->dashboard_option == 11)
                return view('Frontend.dashboard.View.Content.subuser-progress', ['data' => $data, 'product' => $proAnalyses['product']])->render();
            else if ($userOption->dashboard_option == 33)
                return view('Frontend.dashboard.View.Content.subuser-average', ['data' => $data, 'products' => $proAnalyses['product']])->render();
            else
                return view('Frontend.dashboard.View.Content.subuser-default', ['data' => $data, 'product' => $proAnalyses['product']])->render();
        }
    }

    private function getDashVisiableProduct($admin, $all_menus = [])
    {
        if (is_object($all_menus)) {
            $check = $all_menus->whereIn('id', [41, 42, 43, 44, 45, 46, 47, 48, 69, 120]);
            if ($check && count($check)) return $check->first()->toArray();
            else return $all_menus->where('submenus', '!=', null)->first()->toArray();
        }
        return null;
    }

    private function _frabkCheck($admin, $all_menus = [])
    {
        if (empty($all_menus)) {
            return false;
        }

        return collect($all_menus)
            ->where('status', 1)
            ->contains('package_name', 'Frabklang');
    }

    public function farbklangDashboard($all_menus)
    {
        $farbklangStatus = true;
        $track = 0;
        if (!empty($all_menus)) {
            foreach ($all_menus as $menus) {
                if ($menus['package_name'] == "Frabklang") {
                    $track++;
                    if ($track > 3) break;
                    if ($menus['id'] == 163 || $menus['id'] == 171) continue;
                    $frabklangProducts[] = $menus ?? null;
                } else {
                    $farbklangStatus = false;
                }
            }
        }

        if (isset($frabklangProducts) && $farbklangStatus) {
            foreach ($frabklangProducts as $product) {
                if ($product['submenus'] != null) {
                    $product['submenus']->sortBy('menu_name');
                    foreach ($product['submenus'] as $submenu) {
                        $color = $this->farbklangSubmenuAnalyses($submenu->product_id, $submenu->id);

                        $submenu->frabklangwatch = FrabklangWatchSubmenu::where('submenu_id', $submenu->id)->where('user_id', getUserId())->whereDate('updated_at', Carbon::today()->format('Y-m-d'))->exists() ?? false;

                        if (!empty($color['red_analyses'])) {
                            $submenu->direction_status = "#E84E1B";
                        } else if (!empty($color['orange_analyses'])) {
                            $submenu->direction_status = "#F8B133";
                        } else {
                            $submenu->direction_status = "#2FAB66";
                        }
                    }
                }
            }
        }
        return ['collection' => $frabklangProducts, 'status' => $farbklangStatus];
    }

    public function product(Request $request, $proid, $subid)
    {
        // $start = microtime(true);
        $user_id = session()->get('id');
        $filterId = userFilterid($user_id);
        $userDetails = $filterId;
        $data['user'] = $filterId;
        $menus = __getMenus();
        $analyses_paginate = [];
        $bodyimages = [];
        $mentalimages = [];
        $data['redcount'] = 0;
        $data['orangecount'] = 0;
        $data['greencount'] = 0;

        $client_display_size = session()->get('client_display_size');
        if($client_display_size && $client_display_size['screenWidth'] > 2000){
           $per_page = 16;
        }else{
            $per_page = 12;
        }

        $product_name = $menus->where('id', $proid)->first()->product_name;
        $submenu_name = $menus->where('id', $proid)->first()->submenus->where('id', $subid)->first()->menu_name;


        $data['proName'] = $product_name;
        $data['subName'] = $submenu_name;
        $data['proid'] = $proid;
        $data['subid'] = $subid;
        $data['filterid'] = $filterId->userfilter->filter_type;

        $data['lastpage'] = 1;
        $thema_speichern = str_replace(' ', '', $userDetails->thema_speichern ?? '');
        $calculation_with = ($userDetails->calculation_with) ?? '';
        $datumcore = $userDetails->datumcore ?? '';
        $locale = app()->getLocale() ?? config('app.locale');
        $dynamic_cache_key = 'analysis_' . $user_id . '_' . $proid . '_' . $subid . '_' . $calculation_with . '_' . $datumcore . '_' . $thema_speichern;

        if (Cache::has('get_ran_status' . $user_id)) {
            Cache::pull('get_ran_status' . $user_id);
            Cache::forget($dynamic_cache_key);
        }

        if (Cache::has($dynamic_cache_key)) $analyses = Cache::get($dynamic_cache_key);
        else {
            $analyses = $this->productAnalyses($proid, $subid, $userDetails);
            Cache::forever($dynamic_cache_key, $analyses);
        }
        $data['redcount'] = $analyses['red_count'];
        $data['orangecount'] = $analyses['orange_count'];
        $data['greencount'] = $analyses['green_count'];

        $sorted_analyses = $analyses['analyses'];
        if (!empty($sorted_analyses)) {
            $random_values = randomValue($userDetails->id);
            if (!empty($random_values)) {
                foreach ($random_values as $key => $val) {
                    if (array_key_exists($key, $sorted_analyses)) {
                        $curAnaVal = $sorted_analyses[$key]['ma'];
                        if ($curAnaVal != $val) {
                            if ($val >= 0 && $val <= 10) $color = "#E84E1B";
                            else if ($val >= 11 && $val <= 69) $color = "#F8B133";
                            else if ($val >= 70 && $val <= 100) $color = "#2FAB66";
                            $sorted_analyses[$key]['anaVal'] = $val;
                            $sorted_analyses[$key]['anaColor'] = $color;
                            $sorted_analyses[$key]['ranValStatus'] = 1;
                        }
                    }
                }
            }
            if ($filterId->userfilter->filter_type == 1 && $subid != 20) $sorted_analyses = $this->array_sort($sorted_analyses, "anaName", $order = 'SORT_ASC');
            elseif ($filterId->userfilter->filter_type == 2 && $subid != 20) $sorted_analyses = $this->array_sort($sorted_analyses, "anaName", $order = 'SORT_DESC');
            elseif ($filterId->userfilter->filter_type == 3 && $subid != 20) sort($sorted_analyses);
            elseif ($filterId->userfilter->filter_type == 4 && $subid != 20) rsort($sorted_analyses);

            $analyses_paginate = $this->paginate($sorted_analyses,$per_page);
            $bodyimages = $analyses['bodyimages'];
            $mentalimages = $analyses['mentalimages'];
        }


        if ($request->ajax()) {
            if ($userDetails->useroption->dashboard_option == 11) {
                return [
                    'posts' => view('Frontend.dashboard.progressbar-views', ['data' => $data, 'analyses' => $analyses_paginate, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages])->render()
                ];
            } else {
                return [
                    'posts' => view('Frontend.dashboard.product-view', ['data' => $data, 'analyses' => $analyses_paginate, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages])->render()
                ];
            }
        }
        // dd(microtime(true) - $start);
        if (($userDetails->useroption->dashboard_option == 11 and $subid == 20) or $subid == 20)
            return view('Frontend.dashboard.chakra_view', ['data' => $data, 'analyses' => $analyses_paginate, 'anaids' => $analyses['anaids'], 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages]);
        elseif ($userDetails->useroption->dashboard_option == 11)
            return view('Frontend.dashboard.progressbar_view', ['data' => $data, 'analyses' => $analyses_paginate, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages]);
        else
            return view('Frontend.dashboard.product', ['data' => $data, 'analyses' => $analyses_paginate, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages]);
    }


    public function products($proid)
    {
        $loginUser = User::find(getUserId());
        $admin = (getUserId() == getID()) ? $loginUser : User::find(getID());
        $filterId = $loginUser->userfilter()->first();
        $userOption = $loginUser->useroption()->first();
        $data['userDetails'] = $loginUser;
        $data['randomAnalysis'] = $userOption->ran_ana;


        $all_menus = __getMenus();
        $data['all_menus'] = $all_menus->toArray();
        $farbklang = $this->_frabkCheck($loginUser, $data['all_menus']);

        if ($admin->user_type == '0') {

            // $product = getSubmenusByProductId($proid);
            // $product_Details = array(
            //     'id' => $product->id,
            //     'product_name' => $product->product_name,
            //     'submenus' => count($product->submenu) > 0 ? $product->submenu : null,
            //     'sp' => $product->sp_status
            // );
            $product = $all_menus->where('status', 1)->where('id', $proid)->first();
            if ($product) {
                $submenu = new Collection();
                foreach ($product->submenus as $key => $_submenu) {
                    $submenu->push((object)[
                        'id' => $_submenu->id,
                        'name' => $_submenu->menu_name,
                        'product_id' => $_submenu->product_id,
                        'type' => $_submenu->type,
                        'from' => $_submenu->from
                    ]);
                }
            }

            $product_Details = array(
                'id' => $product->id,
                'product_name' => $product->product_name,
                'submenus' => $submenu,
                'sp' => $product->sp
            );

            $submenu_check = array();

            // foreach ($all_menus['submenu'] as $key => $submID) {
            //     if ($submID['product_id'] == $proid) $submenu_check[] = $submID['submenu_id'];
            // }

            if ($userOption->dashboard_option == 33) $products = $this->averageDashboardView($product_Details, $loginUser, $filterId, $submenu_check);
            else $products = $this->DashboardView($product_Details, $loginUser, $filterId);

            if (getID() != getAuthid()) {
                if ($userOption->dashboard_option == 11)
                    return view('Frontend.dashboard.products_progressbar', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
                else if ($userOption->dashboard_option == 33) {
                    $products = $this->averageDashboardView($product_Details, $loginUser, $filterId, $submenu_check);
                    return view('Frontend.dashboard.average_dashboardview', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
                } else
                    return view('Frontend.dashboard.products', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
            }

            // if(!empty($products['submenu'])){
            //     foreach ($products['submenu'] as $key => $sub) {
            //         foreach ($all_menus['submenu'] as $key => $value) {
            //             if ($value['submenu_id'] == $sub['submenuId']) {
            //                 $submenu[$key] = $sub;
            //             }
            //         }
            //     }
            // }

            if ($userOption->dashboard_option == 11)
                return view('Frontend.dashboard.products_progressbar', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
            else if ($userOption->dashboard_option == 33) {
                $products = $this->averageDashboardView($product_Details, $loginUser, $filterId, $submenu_check);
                return view('Frontend.dashboard.average_dashboardview', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
            } else
                return view('Frontend.dashboard.products', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
        }

        if (is_object($all_menus) && $admin->user_type != 0) {
            $proid = $all_menus->where('status', 1)->where('id', $proid)->first();
        }

        $products = $this->DashboardView($proid, $loginUser, $filterId);

        if ($userOption->dashboard_option == 11)
            return view('Frontend.dashboard.products_progressbar', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
        else if ($userOption->dashboard_option == 33) {
            $products = $this->averageDashboardView($proid, $loginUser, $filterId, $submenu_check = array());
            return view('Frontend.dashboard.average_dashboardview', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
        } else if($userOption->dashboard_option == 44 && $dashboardData = checkIfDashboardDesignerExist()) {
            return view('Frontend.dashboard.dashboard_designer_view', ['dashboardData' => $dashboardData]);
        }

        return view('Frontend.dashboard.products', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $filterId->filter_type, 'farbklang' => $farbklang]);
    }


    public function ownProduct(Request $request, $ownid, $ownsubid)
    {
        $userDetails = userFilterid(getUserId());
        $product = null;

        $product = __getMenus()->where('status', 2)->where('id', $ownid)->first();
        $data['proName'] = $product->product_name;

        if(!$product->submenus) {

            if($request->ajax()) {
                return response()->json(['error' => 'Product not found'], 404);
            }
            return redirect()->route('dashboard')->with('error', 'Product not found');
        }

        $submenus = $product->submenus->toArray();
        $data['subName'] = $product->submenus->where('id', $ownsubid)->first()->name ?? $product->submenus->where('id', $ownsubid)->first()->submenu_name;


        $data['user'] = $userDetails;
        $data['proid'] = $ownid;
        $data['subid'] = $ownsubid;

        $data['filterid'] = $userDetails->userfilter->filter_type;

        $analyses = $this->userOwnProduct($ownid, $ownsubid, $userDetails, $product, $submenus);
        // $analyses = Cache::remember('user_own_analyses_for_oid'.$ownid.'_osid_'.$ownsubid.'_user_'.$userDetails->id, 100, function () use($ownid, $ownsubid, $userDetails,$product,$submenus){
        //     return $this->userOwnProduct($ownid, $ownsubid, $userDetails,$product,$submenus);
        // });
        $analyses_paginate = $this->paginate($analyses['analyses']);
        $bodyimages = $analyses['bodyimages'];
        $mentalimages = $analyses['mentalimages'];
        $data['redcount'] = $analyses['red_count'];
        $data['orangecount'] = $analyses['orange_count'];
        $data['greencount'] = $analyses['green_count'];

        if ($request->ajax()) {

            if ($userDetails->useroption->dashboard_option == 11) {
                return [
                    'posts' => view('Frontend.dashboard.ownproduct_progressbar_views', ['data' => $data, 'analyses' => $analyses_paginate, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages])->render()
                ];
            } else {
                return [
                    'posts' => view('Frontend.dashboard.ownproduct-view', ['data' => $data, 'analyses' => $analyses_paginate, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages])->render()
                ];
            }
        }

        if ($userDetails->useroption->dashboard_option == 11)
            return view('Frontend.dashboard.ownproduct_progressbar', ['data' => $data, 'analyses' => $analyses_paginate, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages]);
        else
            return view('Frontend.dashboard.own_product', ['data' => $data, 'analyses' => $analyses_paginate, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages]);
    }


    public function ownProducts($ownid)
    {
        $loginUser = userFilterid(getUserId());//User::find(getUserId());
        #$admin              = (getUserId() != getAuthID())?User::find(getAuthId()) : $loginUser;
        #$filterId           = $loginUser->userfilter()->first();
        $userOption = $loginUser->useroption;
        $data['userDetails'] = $loginUser;
        $data['randomAnalysis'] = $userOption->ran_ana;

        $menus = __getMenus();
        $product = $menus->where('status', 2)->where('id', $ownid)->first()->toArray();
        $farbklang = $this->_frabkCheck($loginUser, $product);

        if (checkTherapist(getAuthID()) == '0') {
            if ($userOption->dashboard_option == 33) {
                $products = $this->userAverageOwnProducts($ownid, $loginUser, $product);
            }else {
                $products = $this->userOwnProducts($ownid, $loginUser);
            }

            if ($userOption->dashboard_option == 11)
                return view('Frontend.dashboard.ownproducts_progressbar', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $loginUser->userfilter->filter_type, 'farbklang' => $farbklang]);
            else if ($userOption->dashboard_option == 33)
                return view('Frontend.dashboard.average_owndashboardview', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $loginUser->userfilter->filter_type, 'farbklang' => $farbklang]);
            else
                return view('Frontend.dashboard.own_products', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $loginUser->userfilter->filter_type, 'farbklang' => $farbklang]);
        }
        if ($userOption->dashboard_option == 33) {
            $products = $this->userAverageOwnProducts($ownid, $loginUser);
        }
        else {
            $products = $this->userOwnProducts($ownid, $loginUser);
        }

        if ($userOption->dashboard_option == 11){
            return view('Frontend.dashboard.ownproducts_progressbar', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $loginUser->userfilter->filter_type, 'farbklang' => $farbklang]);
        }
        else if ($userOption->dashboard_option == 33){
            return view('Frontend.dashboard.average_owndashboardview', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $loginUser->userfilter->filter_type, 'farbklang' => $farbklang]);
        }
        else{
            return view('Frontend.dashboard.own_products', ['data' => $data, 'product' => $products['product'], 'submenu' => $products['submenu'], 'filterid' => $loginUser->userfilter->filter_type, 'farbklang' => $farbklang]);
        }
    }


    public function focusView($proid, $subid)
    {
        $imgids = imageEin::where('submenu_id', $subid)->pluck('einflue_img_id')->toArray();

        if (!empty($imgids)) {
            if (app()->getLocale() == 'en') $images = EnEinflueImage::whereIn('id', $imgids)->get();
            elseif (app()->getLocale() != '' && app()->getLocale() != 'de') $images = DB::table(app()->getLocale() . '_einflue_images')->whereIn('id', $imgids)->get();
            else $images = EinflueImage::whereIn('id', $imgids)->get();
            $images = $images->map(function ($img) {
                $img->thumbnil = (file_exists($img->image) ? asset($img->image) : 'storage/' . str_replace("einflueimage/", "einflueimage/uploads_thumbnail/", $img->image));
                $img->image = (file_exists($img->image) ? asset($img->image) : 'storage/' . $img->image);
                return $img;
            });
        }

        $subids[] = $subid;
        $user = User::with('useroption:user_id,dc_direction_view')->find(getAuthID());

        $menus = __getMenus();
        $productDetail = $menus->where('status', 1)->where('id', $proid)->first();
        $product = $productDetail->product_name;
        $db_pool_name = (app()->getLocale() == '' || app()->getLocale() == 'de') ? 'pool_name' : app()->getLocale() . '_pool_name';
        $db_group_name = (app()->getLocale() == '' || app()->getLocale() == 'de') ? 'group_name' : app()->getLocale() . '_group_name as group_name';
        $submenu = $productDetail->submenus->where('id', $subid)->load(['dcicon_category', 'group_einflus:id,' . $db_group_name, 'group_focus:id,' . $db_group_name, 'pools:pools.id,' . $db_pool_name . ' as group_name'])->first();


        $dcicon_default_category = DcIconCategory::where('default', 1)->get();
        if ($submenu->dcicon_category) {
            $dcicon_category = $submenu->dcicon_category;
            $dcicon_category = $dcicon_category->merge($dcicon_default_category)->sortBy('serial_no')->map(function ($icon) {
                return collect([
                    'default_image' => $icon->default_image,
                    'name' => (app()->getLocale() == '' || app()->getLocale() == 'de') ? $icon->name : $icon->{app()->getLocale() . '_name'},
                    'id' => $icon->id
                ]);
            });
        } else {
            $dcicon_category = $dcicon_default_category;
        }

        $groups = new Collection();
        $groups = $groups->merge($submenu->group_einflus)->merge($submenu->group_focus)->merge($submenu->pools)->map(function ($group) {
            $group->group_name = Str::ucfirst($group->group_name);
            $group->status = ($group->getTable() == 'pools') ? false : true;
            return $group;
        })->sortBy('group_name');

        $subStatus = true;
        if ($user->boss_id != 0) {
            (User::find($user->boss_id)->user_type == 2) ? $subStatus = false : $subStatus = true;
        }
        $data['user'] = $user;

        return view('Frontend.dashboard.focus_view', compact('data', 'product', 'images', 'groups', 'submenu', 'subStatus', 'dcicon_category'));
    }

    /**
     * return package view with treatment packages
     *
     * @return $user->packages
     */
    public function packageView()
    {
        return view('Frontend.dashboard.package_list');
    }

    public function getPackageViewData(Request $request)
    {
        // dd($request->order[0]['column'],$request->all());
        if ($request->ajax()) {
            $cartpackagelist = DB::table('cart_package_users')
                ->select('cart_packages.*')
                ->join('cart_packages', 'cart_package_users.cart_package_id', 'cart_packages.id');

            if ($request->user != null) {
                $cartpackagelist->where('cart_package_users.user_id', $request->user);
            } else {
                $cartpackagelist->whereIn('cart_package_users.user_id', User::where('boss_id', getAuthId())->orWhere('id', getAuthId())->pluck('id')->toArray());
            }
            if ($request->order[0]['column'] == 1) $cartpackagelist->orderBy('cart_packages.package_name', $request->order[0]['dir']);
            else if ($request->order[0]['column'] == 2) $cartpackagelist->orderBy(DB::raw("DATE_FORMAT(cart_packages.created_at, '%Y-%m-%d')"), $request->order[0]['dir']);
            else $cartpackagelist->orderBy(DB::raw("DATE_FORMAT(cart_packages.created_at, '%Y-%m-%d')"), 'desc');

            return Datatables::of($cartpackagelist)->addIndexColumn()->addColumn('action', function ($row) {
                $btns = "";
                if (Gate::allows('checkAccess', 'hasMenus')) $btns .= '<a class="text-success" style="max-width: fit-content;margin-bottom: 2px;" href="javascript:void(0)" onclick="addCartToPackage(' . $row->id . ')"><i class="ion ion-md-cart mr-1 f-16"></i>&nbsp;<span class="hide-table-btn-text">' . trans("action.add_to_cart") . '</span></a>';
                $btns .= '<p class="hover-primary cursor-pointer" style="max-width: fit-content;margin-bottom: 2px;" data-toggle="modal" id="pkg' . $row->id . '" data-content="' . $row->note . '" data-url="' . route('Sajax.addNotePKG') . '" data-target="#note-modal" onclick="noteModalView(' . $row->id . ')"><i class="fas fa-pen"></i>&nbsp;<span class="hide-table-btn-text">' . trans("action.note_cron_view") . '</span></p>' . '<p class="hover-primary text-primary cursor-pointer" style="max-width: fit-content;margin-bottom: 2px;" data-toggle="modal" data-target="#view-modal" id="_content' . $row->id . '"  data-url="' . route('Sajax.get-package-content') . '" onclick="viewPackageContent(' . $row->id . ')"><i class="fas fa-eye"></i>&nbsp;<span class="hide-table-btn-text">' . trans("action.view") . '</span></p>' . '<p class="hover-danger text-danger cursor-pointer" style="max-width: fit-content;margin-bottom: 2px;" onclick="deletePackage([' . $row->id . '])"><i class="fas fa-trash-alt"></i>&nbsp;<span class="hide-table-btn-text">' . trans("action.delete") . '</span></p>';
                return $btns;
            })->addColumn('id', function ($row) {
                return $row->id;
            })->addColumn('created_at', function ($row) {
                $date = date_change_with_time($row->created_at);
                return $date;
            })->addColumn('unique_id', function ($row) {
                // $unique_id = '123456789';
                // return $unique_id;

                $unique_id = $row->unique_id;
                return $unique_id === null ? '' : $unique_id;

            })->addColumn('package_name', function ($row) {
                return (in_array($row->package_name, ['auto_save', 'automatic Save']) ? trans('action.auto_save') : $row->package_name);
            })->rawColumns(['action', 'created_at'])->make(true);
        }
    }


    public function productAnalyses($proid = null, $subid = null, $filterId = null, $target_pool_ids = [])
    {
        $userid = session()->get('id');

        if ($filterId == null) {
            $filterId = userFilterid($userid); // Assuming your userFilterid function exists
        }

        $user = $filterId; // Assuming $filterId returns the user object or similar
        $sortBy = ""; // Initialize sortBy

        $productColors = null;
        if ($proid) {
            $productColors = DB::table('product_settings')->where('product_id', $proid)->first();
        }


        $biorythDetail = biorythVisibleDetails(); // Assuming your biorythVisibleDetails function exists
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        // Ensure $target_pool_ids is an array (even if a single ID is passed)
        $pool_ids_to_query = is_array($target_pool_ids) ? $target_pool_ids : [$target_pool_ids];

        // Fallback to $subid if $target_pool_ids is empty
        if (empty(array_filter($pool_ids_to_query))) {
            if ($subid) { // Only use subid if it's provided
                $pool_ids_to_query = getPoolIdsBySubID($subid) ?? []; // Assuming getPoolIdsBySubID exists
            } else {
                $pool_ids_to_query = []; // Ensure it's an empty array if no IDs are found
            }
        }

        // Early return if no pool IDs are available to query
        if (empty(array_filter($pool_ids_to_query))) {
            return [
                'analyses' => "No Analyses Available: No valid Pool IDs provided or found.",
                'bodyimages' => [],
                'mentalimages' => [],
                'red_count' => 0,
                'orange_count' => 0,
                'green_count' => 0,
            ];
        }

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        // Determine sort order
        if ($filterId->userfilter->filter_type == 1) {
            $sortBy = "asc";
        } elseif ($filterId->userfilter->filter_type == 2 || ($subid && $subid == 20)) { // $subid check added
            $sortBy = "desc";
        }

        // Default sortBy if not set by filter_type (e.g., for filter_type 3 or 4 where value sort happens later)
        if (empty($sortBy)) {
            $sortBy = "asc"; // Default to ascending order by name if no specific sort order is set yet
        }

        $anaids = array();
        $redCount = 0;
        $orangeCount = 0;
        $greenCount = 0;
        $allAnalyses = array();
        $body_images = array();
        $mental_images = array();

        $anaQuery = Analyse::whereHas('pools', function ($query) use ($pool_ids_to_query) {
            $query->whereIn('pool_id', $pool_ids_to_query);
        })->when(!empty($sortBy), function ($query) use ($sortBy) {
            return $query->orderBy('name', $sortBy); // Sort by name from database
        });

        // Join for language-specific analysis names (as in your original code)
        if (app()->getLocale() != '' && app()->getLocale() != 'de') {
            $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')
                ->join('analyses', 'analyses.id', '=', app()->getLocale() . '_analyses.id');
        }
        $analyses = $anaQuery->get();

        if (count($analyses) > 0) {
            $all_analysis_id = $analyses->pluck('id')->toArray();

            // Body and mental images (assuming getBodyImage and getMentalImage functions exist)
            $body_images = getBodyImage($all_analysis_id);
            $mental_images = getMentalImage($all_analysis_id);

            // Causes: Works if $subid is provided
            $todayCause = [];
            $randomCauses = ['causes' => collect(), 'medium' => collect(), 'tipp' => collect()]; // Default empty collections
            if ($subid) {
                $todayCause = savedCauses($subid, $all_analysis_id, $user->id); // Your savedCauses function
                $randomCauses = randomCauses($subid); // Your randomCauses function
            }

            // Random anaval query (assuming randomValue function exists)
            $random_values = randomValue($user->id);

            foreach ($analyses as $print) {
                $anaid = $print->id;
                // Analysis name based on locale (as in your original code, with null coalescing for safety)
                $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : ($print->de_name ?? $print->name);

                $bioryth = $print->bioryth; // Each analysis has its own biorhythm cycle

                $bodydesc = $print->body_desc;
                $mentaldesc = $print->mental_desc;
                $desc = $print->description;

                $anaids[] = $anaid;
                $ranValStatus = 0;
                if ($bioryth == 0 || $bioryth < 0) { // Default if biorhythm is zero or negative
                    $bioryth = 28;
                }

                // Core Calculations (your calculationPS, calculationPK, calculation, calculationoneday functions)
                $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                $beerg = (isset($user->userOption->ran_ana) && $user->userOption->ran_ana == 1) ? rand(0, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergod = calculationoneday($strM, $strP, $user, $bioryth);

                $ma = round($beerg, 0);
                $beergK = round($beergK, 0);
                $beergS = round($beergS, 0);
                $beergod = round($beergod, 0);

                if (!empty($random_values) && isset($user->useroption->pattern_switch) && $user->useroption->pattern_switch == 1) {
                    if (array_key_exists($print->id, $random_values)) {
                        $ma = $random_values[$print->id];
                        $ranValStatus = 1;
                    }
                }

                // Male/Heart Colors
                $heart = ($beergS >= 50) ? "color:red" : "color:gray";
                $male = ($beergK <= 30) ? "color:red" : "color:gray";

                // Analysis Value Color
                $class_skill = ""; // Default color or status
                if (isset($productColors)) { // Only use product colors if $productColors is defined
                    if ($ma >= $productColors->red_min && $ma <= $productColors->red_max) {
                        $class_skill = "#E84E1B";
                        $redCount++;
                    } elseif ($ma >= $productColors->orange_min && $ma <= $productColors->orange_max) {
                        $class_skill = "#F8B133";
                        $orangeCount++;
                    } elseif ($ma >= $productColors->green_min && $ma <= $productColors->green_max) {
                        $class_skill = "#2FAB66";
                        $greenCount++;
                    } elseif (isset($productColors->custom_min) && $ma >= $productColors->custom_min && isset($productColors->custom_max) && $ma <= $productColors->custom_max) {
                        $class_skill = $productColors->custom_color;
                    }
                } else if($setting = global_settings(getAuthId())) {
                    if (intval($ma) >= $setting->gs_red_min && intval($ma) <= $setting->gs_red_max) {
                        $class_skill = "#E84E1B";
                    }
                    elseif (intval($ma) >= $setting->gs_orange_min && intval($ma) <= $setting->gs_orange_max) {
                        $class_skill = "#F8B133";
                    }
                    elseif (intval($ma) >= $setting->gs_green_min && intval($ma) <= $setting->gs_green_max) {
                        $class_skill = "#2FAB66";
                    }
                } else {
                    // Fallback color logic if $productColors is not set (e.g., $proid was null)
                    if ($ma <= 33) { $class_skill = "#E84E1B"; $redCount++; }
                    elseif ($ma <= 66) { $class_skill = "#F8B133"; $orangeCount++; }
                    else { $class_skill = "#2FAB66"; $greenCount++; }
                }

                // Cause/Medium/Tipp: Works if $subid is provided
                $causeGroupid = 0;
                if ($subid && !empty($todayCause['causes']) && $todayCause['causes'] instanceof \Illuminate\Support\Collection && $todayCause['causes']->count() > 0) {
                    if (isset($todayCause['causes'][$print->id])) { // Check if key exists
                        $causeGroupid = $todayCause['causes'][$print->id];
                    } elseif ($randomCauses['causes']->count() > 0) {
                        $causeGroupid = $randomCauses['causes']->random(); // Use Laravel collection's random method
                    }
                } elseif ($subid && isset($randomCauses['causes']) && $randomCauses['causes'] instanceof \Illuminate\Support\Collection && $randomCauses['causes']->count() > 0) {
                    $causeGroupid = $randomCauses['causes']->random();
                }

                $mediumGroupid = 0;
                if ($subid && !empty($todayCause['medium']) && $todayCause['medium'] instanceof \Illuminate\Support\Collection && $todayCause['medium']->count() > 0) {
                    if (isset($todayCause['medium'][$print->id])) {
                        $mediumGroupid = $todayCause['medium'][$print->id];
                    } elseif ($randomCauses['medium']->count() > 0) {
                        $mediumGroupid = $randomCauses['medium']->random();
                    }
                } elseif ($subid && isset($randomCauses['medium']) && $randomCauses['medium'] instanceof \Illuminate\Support\Collection && $randomCauses['medium']->count() > 0) {
                    $mediumGroupid = $randomCauses['medium']->random();
                }

                $tippGroupid = 0;
                if ($subid && !empty($todayCause['tipp']) && $todayCause['tipp'] instanceof \Illuminate\Support\Collection && $todayCause['tipp']->count() > 0) {
                    if (isset($todayCause['tipp'][$print->id])) {
                        $tippGroupid = $todayCause['tipp'][$print->id];
                    } elseif ($randomCauses['tipp']->count() > 0) {
                        $tippGroupid = $randomCauses['tipp']->random();
                    }
                } elseif ($subid && isset($randomCauses['tipp']) && $randomCauses['tipp'] instanceof \Illuminate\Support\Collection && $randomCauses['tipp']->count() > 0) {
                    $tippGroupid = $randomCauses['tipp']->random();
                }

                // Pricing: Works if $proid and $subid are provided
                $randPrice = 0; // Default price
                $priceValue = priceValue($proid, $subid, $anaid);

                if ($priceValue && isset($priceValue['min_price']) && isset($priceValue['max_price'])) {
                    if (is_numeric($priceValue['min_price']) && is_numeric($priceValue['max_price']) && $priceValue['min_price'] <= $priceValue['max_price']) {
                        $randPrice = rand((int)$priceValue['min_price'], (int)$priceValue['max_price']);
                    } else {
                        $randPrice = (int)($priceValue['min_price'] ?? 0); // Default to min_price or 0 if invalid range
                    }
                }

                $allAnalyses[$anaid] = array(
                    "anaVal" => $ma,
                    "anaid" => $anaid,
                    "anaName" => $print->name, // Store the name in the base language
                    "url_name" => $print->url_name,
                    "url_link" => $print->url_link,
                    "desc" => $desc,
                    "desc_img" => $print->desc_image,
                    "bodyDesc" => $bodydesc,
                    "mentalDesc" => $mentaldesc,
                    "bioyrth" => $bioryth, // The biorhythm value used
                    "anaColor" => $class_skill,
                    "maleVal" => $beergK,
                    "heartVal" => $beergS,
                    "maleColor" => $male,
                    "heartColor" => $heart,
                    "beergod" => $beergod,
                    "causes" => $causeGroupid,
                    "medium" => $mediumGroupid,
                    "tipp" => $tippGroupid,
                    // Attempt to get a pool_id; assumes 'pools' relation is loaded or accessible
                    "poolid" => $print->pools->isNotEmpty() ? $print->pools->first()->id : 0,
                    "randPrice" => $randPrice,
                    "ranValStatus" => $ranValStatus,
                );
            }
        } else {
            $allAnalyses = "No Analyses Available"; // If $analyses is empty or pool IDs were empty initially
        }

        // Sorting: Works if $subid is provided and conditions are met
        // Note: sort() and rsort() on an associative array re-indexes it numerically and sorts by comparing inner arrays.
        // This effectively sorts by 'anaVal' if it's the first or dominant differing element.
        if ($subid && $filterId->userfilter->filter_type == 3 && $subid != 20) {
            if (is_array($allAnalyses) && !empty($allAnalyses)) {
                usort($allAnalyses, function($a, $b) { return $a['anaVal'] <=> $b['anaVal']; }); // Explicit sort by anaVal
            }
        }
        if ($subid && $filterId->userfilter->filter_type == 4 && $subid != 20) {
            if (is_array($allAnalyses) && !empty($allAnalyses)) {
                usort($allAnalyses, function($a, $b) { return $b['anaVal'] <=> $a['anaVal']; }); // Explicit sort by anaVal desc
            }
        }
        // If $allAnalyses was "No Analyses Available", convert it back to an empty array for consistency if needed by frontend.
        if (!is_array($allAnalyses)) {
            $allAnalyses = [];
        }


        $data['analyses'] = array_values($allAnalyses); // Ensure it's a numerically indexed array if sorted by usort or if it was associative
        $data['bodyimages'] = $body_images;
        $data['mentalimages'] = $mental_images;
        $data['red_count'] = $redCount;
        $data['orange_count'] = $orangeCount;
        $data['green_count'] = $greenCount;

        if ($subid && $subid == 20) { // $subid check added
            $data['anaids'] = $anaids;
        }

        return $data;
    }


    public function DashboardView($product, $user, $filter)
    {
        $data = array();
        $sortBy = "";
        if (is_object($product)) {
            $proid = $product->id;
            $submenus = $product->submenus;
            $product = $product->toArray();
        } else {
            if (empty($product) || $product == null) return $data;

            if (!empty($product['submenus']) == 0) return $data;
            $proid = $product['id'];

            #submenu details by product id
            $submenus = (!empty($product['submenus'])) ? $product['submenus'] : getSubmenusByProductId($proid);
        }
        #products color
        $productColors = productColors($proid);

        #bioryth details
        $biorythDetail = biorythVisibleDetails();

        if (is_object($submenus) && count($submenus) < 1) return $data;

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        if ($filter->filter_type == 1) $sortBy = "asc";
        if ($filter->filter_type == 2) $sortBy = "desc";

        #Red, Orange, Green color count
        $totalRed = 0;
        $totalOrange = 0;
        $totalGreen = 0;
        $totalBlue = 0;

        #random analyses values fetch
        $ranValues = randomValue($user->id);
        $anaCount = array();
        if (is_array($submenus) || is_countable($submenus)) {
            if (count($submenus) > 0) {

                // #all submenus
                // foreach ($submenus as $key => $sm)
                //     $submenu_id[] = $sm->id;

                #all submenu pools
                // $submenuPools = Submenu::with("pools")->whereIn('id',$submenu_id)->get();

                foreach ($submenus as $key => $submenu) {
                    if ($submenu->type == 12) continue;

                    // $pools  = (!empty($submenuPools->pools[$key])? $submenuPools->pools[$key]: poolsIds($submenu->id));
                    $poolid = poolsIds($submenu->id) ?? [];

                    $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

                    $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
                        $query->whereIn('pool_id', $poolid);
                    })->when(!empty($sortBy), function ($query) use ($sortBy) {
                        return $query->orderBy('name', $sortBy);
                    });
                    if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
                    $analyses = $anaQuery->get();

                    $anaRed = 0;
                    $anaOrange = 0;
                    $anaGreen = 0;
                    $anaBlue = 0;

                    if (count($analyses) > 0 && !empty($poolid)) {
                        foreach ($analyses as $keyana => $print) {
                            // $anaid  = $print->id;
                            $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                            $bioryth = $print->bioryth;

                            if ($print->bioryth == 0) $bioryth = 28;
                            elseif ($print->bioryth < 0) $bioryth = 28;
                            else  $bioryth = $print->bioryth;

                            #Analysis/Male/Heart/Oneday Calculation
                            $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergod = calculationoneday($strM, $strP, $user, $bioryth);

                            $ma = round($beerg, 0);
                            $beergK = round($beergK, 0);
                            $beergS = round($beergS, 0);
                            $beergod = round($beergod, 00);

                            if (!empty($ranValues) && $user->useroption->pattern_switch == 1) {
                                if (array_key_exists($print->id, $ranValues)) {
                                    $ma = $ranValues[$print->id];
                                }
                            }

                            #Analysis Value Color
                            if (isset($productColors) && $ma >= $productColors->red_min && $ma <= $productColors->red_max) {
                                $anaRed++;
                                $totalRed++;
                            } elseif (isset($productColors) && $ma >= $productColors->orange_min && $ma <= $productColors->orange_max) {
                                $anaOrange++;
                                $totalOrange++;
                            } elseif (isset($productColors) && $ma >= $productColors->green_min && $ma <= $productColors->green_max) {
                                $anaGreen++;
                                $totalGreen++;
                            } elseif (isset($productColors) && $ma >= $productColors->custom_min && $ma <= $productColors->custom_max) {
                                $anaBlue++;
                                $totalBlue++;
                            }

                        }
                    }

                    $anaCount[] = array(

                        'submenuId' => $submenu->id,
                        'submenuName' => $submenu->menu_name ?? $submenu->name,
                        'anaRed' => $anaRed,
                        'anaOrange' => $anaOrange,
                        'anaGreen' => $anaGreen,
                        'anaBlue' => $anaBlue
                    );
                }
            }
        }
        $anaTotalCount = array(
            'proid' => $proid,
            'proName' => $product['product_name'],
            'anaRed' => $totalRed,
            'anaOrange' => $totalOrange,
            'anaGreen' => $totalGreen,
            'anaBlue' => $totalBlue
        );

        $data['product'] = $anaTotalCount;
        $data['submenu'] = $anaCount;

        return $data;
    }


    public function averageDashboardView($product, $user, $filter, $submenu_check = null)
    {
        $data = array();
        $sortBy = "";
        if (empty($product) || $product == null) return $data;

        if ($product['submenus'] == null) return $data;
        $proid = $product['id'];
        #submenu details by product id
        $submenus = (!empty($product['submenus'])) ? $product['submenus'] : getSubmenusByProductId($proid);
        if ($user->user_type == 0 && count($submenu_check) > 0) {
            if (app()->getLocale() != '' && app()->getLocale() != 'de') {
                $submenu_column_name = app()->getLocale() . "_menu_name";
                $submenus = Submenu::with("pools")->select('id', 'product_id', 'sub_min', 'sub_max', 'show_icon', 'show_button', 'icon', 'type', 'slide_speed', 'direction_status', $submenu_column_name . " as menu_name")->whereIn('id', $submenu_check)->get();
            } else $submenus = Submenu::with("pools")->whereIn('id', $submenu_check)->get();
        }

        #products color
        $productColors = productColors($proid);

        #bioryth details
        $biorythDetail = biorythVisibleDetails();

        if (is_object($submenus) && count($submenus) < 1) return $data;

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        if ($filter->filter_type == 1) $sortBy = "asc";
        if ($filter->filter_type == 2) $sortBy = "desc";

        #Red, Orange, Green color count
        $totalAna = 0;
        $totalSum = 0;
        $subAna = 0;
        $subSum = 0;
        $total = 0;

        #random analyses values fetch
        $ranValues = randomValue($user->id);

        if (count($submenus) > 0) {
            #all submenus
            foreach ($submenus as $key => $sm)
                $submenu_id[] = $sm->id;
            #all submenu pools
            // $submenuPools = Submenu::with("pools")->whereIn('id',$submenu_id)->get();

            foreach ($submenus as $key => $submenu) {
                if ($submenu->type == 12) continue;

                // $pools  = (!empty($submenuPools[$key])? $submenuPools[$key]: poolsIds($submenu->id));
                $poolid = poolsIds($submenu->id) ?? [];
                // $subid = $submenu->id;

                $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

                $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
                    $query->whereIn('pool_id', $poolid);
                })->when($sortBy, function ($query, $sortBy) {
                    return $query->orderBy('name', $sortBy);
                });
                if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
                $analyses = $anaQuery->get();


                $subSum = 0;
                $subAna = 0;

                if (count($analyses) > 0 && !empty($poolid)) {
                    foreach ($analyses as $keyana => $print) {
                        // $anaid  = $print->id;
                        $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                        $bioryth = $print->bioryth;

                        if ($print->bioryth == 0) $bioryth = 28;
                        elseif ($print->bioryth < 0) $bioryth = 28;
                        else  $bioryth = $print->bioryth;

                        #Analysis/Male/Heart/Oneday Calculation
                        $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                        $ma = round($beerg, 0);
                        $beergK = round($beergK, 0);
                        $beergS = round($beergS, 0);
                        $beergod = round($beergod, 00);

                        if (!empty($ranValues) && $user->useroption->pattern_switch == 1) {
                            if (array_key_exists($print->id, $ranValues)) {
                                $ma = $ranValues[$print->id];
                            }
                        }

                        $subSum = $subSum + $ma;
                        $subAna++;
                    }

                    $total1 = (100 / ($subAna * 100)) * $subSum;
                    $subAvg = round($total1);

                    $totalSum = $totalSum + $subAvg;
                    $totalAna = $totalAna + 1;

                    #Analysis Value Color
                    if (isset($productColors) && $subAvg >= $productColors->red_min && $subAvg <= $productColors->red_max) {
                        $subColor = "#E84E1B";
                    } elseif (isset($productColors) && $subAvg >= $productColors->orange_min && $subAvg <= $productColors->orange_max) {
                        $subColor = "#F8B133";
                    } elseif (isset($productColors) && $subAvg >= $productColors->green_min && $subAvg <= $productColors->green_max) {
                        $subColor = "#2FAB66";
                    }
                }

                $anaCount[] = array(

                    'submenuId' => $submenu->id,
                    'submenuName' => $submenu->menu_name,
                    'subAvg' => $subAvg,
                    'subcolor' => $subColor,
                );
            }
        }

        if ($totalAna != 0)
            $total = (100 / ($totalAna * 100)) * $totalSum;
        $totalAvg = round($total);

        #Analysis Value Color
        if (isset($productColors) && $totalAvg >= $productColors->red_min && $totalAvg <= $productColors->red_max) {
            $Color = "#E84E1B";
        } elseif (isset($productColors) && $totalAvg >= $productColors->orange_min && $totalAvg <= $productColors->orange_max) {
            $Color = "#F8B133";
        } elseif (isset($productColors) && $totalAvg >= $productColors->green_min && $totalAvg <= $productColors->green_max) {
            $Color = "#2FAB66";
        }

        $anaTotalCount = array(
            'proid' => $proid,
            'proName' => $product['product_name'],
            'totalAvg' => $totalAvg,
            'color' => $Color,
        );

        $data['product'] = $anaTotalCount;
        $data['submenu'] = $anaCount;

        return $data;
    }


    public function averageDashboardsView($products, $user, $filter)
    {
        $data = array();
        $sortBy = "";
        $anaTotalCount = array();

        if (empty($products) || $products == null) return $data;
        $cnt = 0;

        foreach ($products as $product) {
            if ($product['sp'] == 1 || empty($product['submenus'])) continue;

            $proid = $product['id'] ?? $product['product_id'];
            #submenu details by product id
            $submenus = (!empty($product['submenus'])) ? $product['submenus'] : getSubmenusByProductId($proid);

            #products color
            $productColors = productColors($proid);

            #bioryth details
            $biorythDetail = biorythVisibleDetails();

            // if (is_object($submenus) && count($submenus) < 1) continue;

            $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

            if ($filter->filter_type == 1) $sortBy = "asc";
            if ($filter->filter_type == 2) $sortBy = "desc";


            #random analyses values fetch
            $ranValues = randomValue($user->id);
            // $submenu_id = array();

            if (count($submenus) > 0) {
                $anaCount = array();

                #total average
                $totalAna = 0;
                $totalSum = 0;
                $total = 0;
                $totalAvg = 0;

                foreach ($submenus as $key => $submenu) {

                    if ($submenu->type == 12) continue;

                    // $pools  = (!empty($submenuPools[$key])? $submenuPools[$key]: poolsIds($submenu->id));
                    $poolid = poolsIds($submenu->id ?? $submenu['submenu_id']) ?? [];

                    $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

                    $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
                        $query->whereIn('pool_id', $poolid);
                    })->when($sortBy, function ($query, $sortBy) {
                        return $query->orderBy('name', $sortBy);
                    });
                    if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
                    $analyses = $anaQuery->get();


                    $subSum = 0;
                    $subAna = 0;
                    $totalSub = 0;
                    $subAvg = 0;

                    if (count($analyses) > 0 && !empty($poolid)) {
                        foreach ($analyses as $keyana => $print) {
                            $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                            $bioryth = $print->bioryth;

                            if ($print->bioryth == 0) $bioryth = 28;
                            elseif ($print->bioryth < 0) $bioryth = 28;
                            else  $bioryth = $print->bioryth;

                            #Analysis/Male/Heart/Oneday Calculation
                            $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                            $ma = round($beerg, 0);
                            $beergK = round($beergK, 0);
                            $beergS = round($beergS, 0);
                            $beergod = round($beergod, 00);

                            if (!empty($ranValues) && $user->useroption->pattern_switch == 1) {
                                if (array_key_exists($print->id, $ranValues)) {
                                    $ma = $ranValues[$print->id];
                                }
                            }

                            $subSum = $subSum + $ma;
                            $subAna++;
                        }

                        $totalSub = (100 / ($subAna * 100)) * $subSum;
                        $subAvg = round($totalSub);

                        $totalSum = $totalSum + $subAvg;
                        $totalAna = $totalAna + 1;

                        #Analysis Value Color
                        if (isset($productColors) && $subAvg >= $productColors->red_min && $subAvg <= $productColors->red_max) {
                            $subColor = "#E84E1B";
                        } elseif (isset($productColors) && $subAvg >= $productColors->orange_min && $subAvg <= $productColors->orange_max) {
                            $subColor = "#F8B133";
                        } elseif (isset($productColors) && $subAvg >= $productColors->green_min && $subAvg <= $productColors->green_max) {
                            $subColor = "#2FAB66";
                        }
                    }

                    $anaCount[] = array(

                        'submenuId' => $submenu->id ?? $submenu['submenu_id'],
                        'submenuName' => $submenu->menu_name ?? $submenu['submenu_name'],
                        'subAvg' => $subAvg,
                        'subcolor' => $subColor,
                    );
                }
            }

            $total = (100 / ($totalAna * 100)) * $totalSum;
            $totalAvg = round($total);

            #Analysis Value Color
            if (isset($productColors) && $totalAvg >= $productColors->red_min && $totalAvg <= $productColors->red_max) {
                $Color = "#E84E1B";
            } elseif (isset($productColors) && $totalAvg >= $productColors->orange_min && $totalAvg <= $productColors->orange_max) {
                $Color = "#F8B133";
            } elseif (isset($productColors) && $totalAvg >= $productColors->green_min && $totalAvg <= $productColors->green_max) {
                $Color = "#2FAB66";
            }

            $anaTotalCount[] = array(
                'proid' => $proid,
                'proName' => $product['product_name'],
                'totalAvg' => $totalAvg,
                'color' => $Color,
                'submenus' => $anaCount,
            );

            $cnt++;
            if ($cnt == 3) break;
        }

        $data['product'] = $anaTotalCount;

        return $data;
    }


    public function DashboardsView($products, $user)
    {

        if (!empty($products)) {
            $count = 1;

            #random analyses values fetch
            $ranValues = randomValue($user->id);
            #biroyth details
            $biorythDetail = GlobalSetting::where('gs_type', 'other')->first(['gs_bioryth_p', 'gs_bioryth_m']);

            foreach ($products as $pro) {

                if ($pro['sp'] == 1) continue;
                // $submenu = Submenu::where('product_id', $pro['product_id'])->get()->toArray();

                if (empty($pro) && !is_array($pro['submenus']) && count($pro['submenus']) == 0) continue;

                if ($count > 3) break;

                $proid = $pro->id;

                $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;
                $sortBy = "";
                if ($user->userfilter->filter_type == 1) $sortBy = "asc";
                if ($user->userfilter->filter_type == 2) $sortBy = "desc";

                $totalRed = 0;
                $totalOrange = 0;
                $totalGreen = 0;
                $totalBlue = 0;
                $anaCount = array();

                if (!empty($pro['submenus'])) {
                    foreach ($pro['submenus'] as $key => $submenu) {
                        $sub_id = $submenu->id;
                        if ($proid == 85 || $submenu->type == 12) continue;

                        $poolid = poolsIds($sub_id) ?? [];

                        $productColors = productColors($proid) ?? [];
                        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

                        $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
                            $query->whereIn('pool_id', $poolid);
                        });
                        if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.id', app()->getLocale() . '_analyses.name', app()->getLocale() . '_analyses.bioryth', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
                        else $anaQuery->select('id', 'name', 'bioryth');
                        if (!empty($sortBy)) $anaQuery->orderBy('name', $sortBy);
                        $analyses = $anaQuery->get();

                        $anaRed = 0;
                        $anaOrange = 0;
                        $anaGreen = 0;
                        $anaBlue = 0;

                        if (count($analyses) > 0 && !empty($poolid)) {
                            foreach ($analyses as $print) {
                                $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                                $bioryth = $print->bioryth;

                                if ($print->bioryth == 0) $bioryth = 28;
                                elseif ($print->bioryth < 0) $bioryth = 28;
                                else  $bioryth = $print->bioryth;

                                #Analysis/Male/Heart/Oneday Calculation
                                $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                                $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                                $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                                $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                                $ma = round($beerg, 0);
                                $beergK = round($beergK, 0);
                                $beergS = round($beergS, 0);
                                $beergod = round($beergod, 00);

                                if (!empty($ranValues) && $user->useroption->pattern_switch == 1) {
                                    if (array_key_exists($print->id, $ranValues)) {
                                        $ma = $ranValues[$print->id];
                                    }
                                }

                                #Analysis Value Color
                                if (isset($productColors) && $ma >= $productColors->red_min && $ma <= $productColors->red_max) {
                                    $anaRed++;
                                    $totalRed++;
                                } elseif (isset($productColors) && $ma >= $productColors->orange_min && $ma <= $productColors->orange_max) {
                                    $anaOrange++;
                                    $totalOrange++;
                                } elseif (isset($productColors) && $ma >= $productColors->green_min && $ma <= $productColors->green_max) {
                                    $anaGreen++;
                                    $totalGreen++;
                                } elseif (isset($productColors) && $ma >= $productColors->custom_min && $ma <= $productColors->custom_max) {
                                    $anaBlue++;
                                    $totalBlue++;
                                }

                            }
                        }

                        $anaCount[] = array(
                            'submenuId' => $sub_id,
                            'submenuName' => $submenu['menu_name'],
                            'anaRed' => $anaRed,
                            'anaOrange' => $anaOrange,
                            'anaGreen' => $anaGreen,
                            'anaBlue' => $anaBlue
                        );
                    }

                    $anaTotalCount[] = array(
                        'proid' => $proid,
                        'proName' => $pro['product_name'],
                        'anaRed' => $totalRed,
                        'anaOrange' => $totalOrange,
                        'anaGreen' => $totalGreen,
                        'anaBlue' => $totalBlue,
                        'submenus' => $anaCount
                    );
                    $count++;
                }
            }
            $data['product'] = $anaTotalCount;
        }
        return $data;
    }

    public function adminDashboardsView($products, $user, $filter)
    {
        if (!empty($products)) {
            $count = 1;
            $anaTotalCount = [];
            #user random analyses values
            $ranValues = randomValue($user->id);
            #bioryth details
            $biorythDetail = biorythVisibleDetails();
            $sortBy = "";
            foreach ($products as $pro) {
                if (count($anaTotalCount) > 2 || empty($pro) || $pro['sp'] == 1 || in_array($pro->id, [33, 98, 99, 58, 73]) || $pro['submenus'] == null) continue;

                $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;
                if ($filter->filter_type == 1) $sortBy = "asc";
                if ($filter->filter_type == 2) $sortBy = "desc";

                $totalRed = 0;
                $totalOrange = 0;
                $totalGreen = 0;
                $totalBlue = 0;
                $anaCount = array();
                $productColors = productColors($pro['id']);

                if (isset($productColors) && $productColors->red_min == null && $productColors->red_max == null) {
                    if ($productColors->orange_min == null && $productColors->orange_max == null) {
                        if ($productColors->green_min == null && $productColors->green_max == null) {
                            $productColors = GlobalSetting::find(1);
                            $colorStatus = true;
                        }
                    }
                }

                foreach ($pro['submenus'] as $key => $submenu) {
                    $sub_id = $submenu->id;
                    $poolid = poolsIds($sub_id) ?? [];
                    $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

                    $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
                        $query->whereIn('pool_id', $poolid);
                    })->when($sortBy, function ($query, $sortBy) {
                        return $query->orderBy('name', $sortBy);
                    });
                    if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
                    $analyses = $anaQuery->get();

                    $anaRed = 0;
                    $anaOrange = 0;
                    $anaGreen = 0;
                    $anaBlue = 0;

                    if (count($analyses) > 0 && !empty($poolid)) {
                        foreach ($analyses as $print) {
                            $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                            $bioryth = $print->bioryth;

                            if ($print->bioryth == 0) $bioryth = 28;
                            elseif ($print->bioryth < 0) $bioryth = 28;
                            else  $bioryth = $print->bioryth;

                            #Analysis/Male/Heart/Oneday Calculation
                            $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergod = calculationoneday($strM, $strP, $user, $bioryth);

                            $ma = round($beerg, 0);
                            $beergK = round($beergK, 0);
                            $beergS = round($beergS, 0);
                            $beergod = round($beergod, 00);

                            if (!empty($ranValues) && $user->useroption->pattern_switch == 1) {
                                if (array_key_exists($print->id, $ranValues)) {
                                    $ma = $ranValues[$print->id];
                                }
                            }

                            #Analysis Value Color
                            if ($colorStatus == true) {
                                if (isset($productColors) && $ma >= $productColors->gs_red_min && $ma <= $productColors->gs_red_max) {
                                    $anaRed++;
                                    $totalRed++;
                                } elseif (isset($productColors) && $ma >= $productColors->gs_orange_min && $ma <= $productColors->gs_orange_max) {
                                    $anaOrange++;
                                    $totalOrange++;
                                } elseif (isset($productColors) && $ma >= $productColors->gs_green_min && $ma <= $productColors->gs_green_max) {
                                    $anaGreen++;
                                    $totalGreen++;
                                }
                            } else {
                                if (isset($productColors) && $ma >= $productColors->red_min && $ma <= $productColors->red_max) {
                                    $anaRed++;
                                    $totalRed++;
                                } elseif (isset($productColors) && $ma >= $productColors->orange_min && $ma <= $productColors->orange_max) {
                                    $anaOrange++;
                                    $totalOrange++;
                                } elseif (isset($productColors) && $ma >= $productColors->green_min && $ma <= $productColors->green_max) {
                                    $anaGreen++;
                                    $totalGreen++;
                                }
                            }
                        }
                    }

                    $anaCount[] = array(
                        'submenuId' => $sub_id,
                        'submenuName' => $submenu->menu_name,
                        'anaRed' => $anaRed,
                        'anaOrange' => $anaOrange,
                        'anaGreen' => $anaGreen,
                        'anaBlue' => $anaBlue
                    );
                }

                $anaTotalCount[] = array(
                    'proid' => $pro['id'],
                    'proName' => $pro['product_name'],
                    'anaRed' => $totalRed,
                    'anaOrange' => $totalOrange,
                    'anaGreen' => $totalGreen,
                    'anaBlue' => $totalBlue,
                    'submenus' => $anaCount
                );
                $count++;
            }

            $data['product'] = $anaTotalCount;
        }

        return $data;
    }

    public function userOwnProduct($ownid, $ownsubid, $user, $product = [], $submenus = [])
    {
        if (empty($product) || empty($submenus)) return;
        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        // if ($user->userfilter->filter_type == 1) $sortBy = "asc";
        // if ($user->userfilter->filter_type == 2) $sortBy = "desc";

        $ranValues = randomValue($user->id);
        $redCount = 0;
        $orangeCount = 0;
        $greenCount = 0;

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        $submenu = $submenus[array_search($ownsubid, array_column($submenus, 'id'))];

        $analyses = array();
        $ownPrice = null;
        $globalValues = GlobalSetting::find(1);

        if ($submenu['status'] == 1) {
            $subDetails = UserSubMenu::with('linkSubMenus')->where('id', $ownsubid)->first();
            $normalSubMenus = array();
            $ownSubMenus = array();
            $ownSubMenus[] = $ownsubid;
            if ($subDetails->linkSubMenus && $subDetails->linkSubMenus->count()) {
                foreach ($subDetails->linkSubMenus as $linksub) {
                    if ($linksub->type == 1) $normalSubMenus[] = $linksub->link_submenu_id;
                    else $ownSubMenus[] = $linksub->link_submenu_id;
                }
            }

            if (!empty($normalSubMenus)) {
                $productColor = getProductSettingBySubmenu($normalSubMenus);
                $randomCauses = randomCauses($normalSubMenus);
            }
            if (!empty($ownSubMenus)) {
                $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
            }

            if (!empty($normalSubMenus)) {

                $pools = poolsIdsOwn($normalSubMenus);
                $poolid = $pools;

                $analyses[] = DB::table('analyse_pools')
                    ->join('pool_submenus', 'pool_submenus.pool_id', '=', 'analyse_pools.pool_id')
                    ->join('analyses', 'analyses.id', '=', 'analyse_pools.analyse_id')
                    ->whereIn('pool_submenus.submenu_id', $normalSubMenus)
                    ->whereIn('analyse_pools.pool_id', $poolid)->get();
            }

            if (!empty($ownSubMenus)) {
                $analyses[] = DB::table('analyse_user_submenus')
                    ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                    ->whereIn('analyse_user_submenus.user_submenu_id', $ownSubMenus)->get();
            }

        } else {
            $analyses[] = DB::table('analyse_user_submenus')
                ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                ->where('analyse_user_submenus.user_submenu_id', $ownsubid)->get();

            $ownSubMenus[] = $ownsubid;
            $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
        }

        if (!empty($analyses) > 0) {
            foreach ($analyses as $analyse) {
                $all_analysis_id = array();

                foreach ($analyse as $analysis)
                    $all_analysis_id[] = $analysis->id;

                #body and mental images
                $body_images = getBodyImage($all_analysis_id);
                $mental_images = getMentalImage($all_analysis_id);

                foreach ($analyse as $print) {
                    $class_skill = null;
                    $anaid = $print->id;
                    $strP = $print->name;
                    $bioryth = $print->bioryth;
                    $bodydesc = $print->body_desc;
                    $mentaldesc = $print->mental_desc;
                    // $anaids[]   = $anaid;
                    $ranValStatus = 0;
                    $causeGroupid = null;
                    $mediumGroupid = null;
                    $tippGroupid = null;

                    if ($print->bioryth == 0) $bioryth = 28;
                    elseif ($print->bioryth < 0) $bioryth = 28;
                    else  $bioryth = $print->bioryth;

                    #Analysis/Male/Heart/Oneday Calculation
                    $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergod = calculationoneday($strM, $strP, $user, $bioryth);

                    $ma = round($beerg, 0);
                    $beergK = round($beergK, 0);
                    $beergS = round($beergS, 0);
                    $beergod = round($beergod, 00);

                    if (!empty($ranValues)) {
                        if (array_key_exists($print->id, $ranValues)) {
                            $ma = $ranValues[$print->id];
                            $ranValStatus = 1;
                        }
                    }

                    #Male/Heart Color
                    if ($beergS >= 50) $heart = "color:red";
                    else if ($beergS <= 49) $heart = "color:gray";
                    if ($beergK <= 30) $male = "color:red";
                    else if ($beergK >= 31) $male = "color:gray";

                    if (isset($subDetails) && $subDetails->status == 1) {
                        if (isset($print->user_submenu_id) && $productColor[$print->user_submenu_id])
                            $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                        else if (isset($print->submenu_id) && $productColor[$print->submenu_id]) {
                            $colorValue = $productColor[$print->submenu_id] ?? null;

                            if (count($randomCauses['causes']) > 0) {
                                $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                            }

                            if (count($randomCauses['medium']) > 0) {
                                $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                            }

                            if (count($randomCauses['tipp']) > 0) {
                                $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                            }
                        }
                    } else {
                        $colorValue = ((array)$ownColor)[$print->user_submenu_id] ?? null;
                        $ownPrice = ((array)$ownColor)[$print->user_submenu_id] ?? null;
                    }

                    #Analysis Value Color
                    if (isset($colorValue) && $colorValue->green_min == null && $colorValue->green_max == null) {
                        $colorValue = $globalValues;
                        if (isset($colorValue) && $ma >= $colorValue->gs_red_min && $ma <= $colorValue->gs_red_max) {
                            $class_skill = "#E84E1B";
                            $redCount++;
                        } elseif (isset($colorValue) && $ma >= $colorValue->gs_orange_min && $ma <= $colorValue->gs_orange_max) {
                            $class_skill = "#F8B133";
                            $orangeCount++;
                        } elseif (isset($colorValue) && $ma >= $colorValue->gs_green_min && $ma <= $colorValue->gs_green_max) {
                            $class_skill = "#2FAB66";
                            $greenCount++;
                        }
                    } else {
                        if (isset($colorValue) && $ma >= $colorValue->red_min && $ma <= $colorValue->red_max) {
                            $class_skill = "#E84E1B";
                            $redCount++;
                        } elseif (isset($colorValue) && $ma >= $colorValue->orange_min && $ma <= $colorValue->orange_max) {
                            $class_skill = "#F8B133";
                            $orangeCount++;
                        } elseif (isset($colorValue) && $ma >= $colorValue->green_min && $ma <= $colorValue->green_max) {
                            $class_skill = "#2FAB66";
                            $greenCount++;
                        }
                    }

                    if(!$class_skill) {
                        if(!$ownColor->id) $ownColor = reset($ownColor);
                        if(isset($ownColor) && $ma >= $ownColor->red_min && $ma <= $ownColor->red_max) {
                            $class_skill = "#E84E1B";
                            $redCount++;
                        } elseif(isset($ownColor) && $ma >= $ownColor->orange_min && $ma <= $ownColor->orange_max) {
                            $class_skill = "#F8B133";
                            $orangeCount++;
                        } elseif(isset($ownColor) && $ma >= $ownColor->green_min && $ma <= $ownColor->green_max) {
                            $class_skill = "#2FAB66";
                            $greenCount++;
                        }
                    }

                    #Get Price for cart
                    if ($ownPrice != null && ($print->ana_min_price == '' && $print->ana_max_price == '')) {
                        if ($ownPrice->gs_min_price != null && $ownPrice->gs_max_price != null)
                            $randPrice = rand($ownPrice->gs_min_price, $ownPrice->gs_max_price);
                        else
                            $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);

                    } elseif ($print->ana_min_price != '' && $print->ana_max_price != '') {
                        $randPrice = rand($print->ana_min_price, $print->ana_max_price);
                    } else {
                        #Random Price For Cart
                        $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);
                    }

                    #All Analysis
                    $allAnalyses[] = array(
                        "anaVal" => $ma,
                        "anaid" => $anaid,
                        "anaName" => $strP,
                        "url_name" => $print->url_name,
                        "url_link" => $print->url_link,
                        "desc" => $print->description,
                        "desc_img" => $print->desc_image,
                        "bodyDesc" => $bodydesc,
                        "mentalDesc" => $mentaldesc,
                        "bioyrth" => $print->bioryth,
                        "anaColor" => $class_skill ?? "",
                        "maleVal" => $beergK,
                        "heartVal" => $beergS,
                        "maleColor" => $male,
                        "heartColor" => $heart,
                        "beergod" => $beergod,
                        "causes" => ($causeGroupid == null) ? "" : $causeGroupid,
                        "medium" => ($mediumGroupid == null) ? "" : $mediumGroupid,
                        "tipp" => ($tippGroupid == null) ? "" : $tippGroupid,
                        "randPrice" => $randPrice,
                        "ranValStatus" => $ranValStatus
                    );

                }
            }
        } else {
            $analyses = "No Analyses Available";
        }

        if ($user->userfilter->filter_type == 1) if (!empty($allAnalyses)) $allAnalyses = $this->array_sort($allAnalyses, "anaName", $order = 'SORT_ASC');
        if ($user->userfilter->filter_type == 2) if (!empty($allAnalyses)) $allAnalyses = $this->array_sort($allAnalyses, "anaName", $order = 'SORT_DESC');
        if ($user->userfilter->filter_type == 3) if (!empty($allAnalyses)) sort($allAnalyses);
        if ($user->userfilter->filter_type == 4) if (!empty($allAnalyses)) rsort($allAnalyses);

        $data['analyses'] = $allAnalyses;
        $data['bodyimages'] = $body_images;
        $data['mentalimages'] = $mental_images;
        $data['red_count'] = $redCount;
        $data['orange_count'] = $orangeCount;
        $data['green_count'] = $greenCount;

        return $data;
    }


    public function userOwnProducts(int $ownid, $user)
    {
        #functions
        $i = 0;
        $product = null;

        $menus = __getMenus();
        $product = $menus->where('status', 2)->where('id', $ownid)->first()->toArray();

        #user random values
        $ranValue = randomValue($user->id);
        $biorythDetail = biorythVisibleDetails();

        #user info
        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        $totalRed = 0;
        $totalOrange = 0;
        $totalGreen = 0;
        $totalBlue = 0;
        if ($product['submenus'] && count($product['submenus']) > 0) {
            if (is_array($product['submenus'])) $product['submenus'] = collect($product['submenus']);

            foreach ($product['submenus']->sortBy('sortingNo') as $key => $submenu) {

                $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);
                $subid = $submenu['id'];

                $analyses = array();

                if ($submenu['status'] == 1) {
                    $subDetails = UserSubMenu::with('linkSubMenus')->where('id', $subid)->first();
                    $normalSubMenus = array();
                    $ownSubMenus = array();
                    $ownSubMenus[] = $submenu['id'];

                    foreach ($subDetails->linkSubMenus as $linksub) {
                        if ($linksub->type == 1) $normalSubMenus[] = $linksub->link_submenu_id;
                        else $ownSubMenus[] = $linksub->link_submenu_id;
                    }

                    if (!empty($normalSubMenus)) {
                        $productColor = getProductSettingBySubmenu($normalSubMenus);
                    }
                    if (!empty($ownSubMenus)) {
                        $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
                    }

                    if (!empty($normalSubMenus)) {
                        $pools = poolsIdsOwn($normalSubMenus);
                        $poolid = $pools;

                        $analyses[] = DB::table('analyse_pools')
                            ->join('pool_submenus', 'pool_submenus.pool_id', '=', 'analyse_pools.pool_id')
                            ->join('analyses', 'analyses.id', '=', 'analyse_pools.analyse_id')
                            ->whereIn('pool_submenus.submenu_id', $normalSubMenus)
                            ->whereIn('analyse_pools.pool_id', $poolid)->get();
                    }

                    if (!empty($ownSubMenus)) {
                        $analyses[] = DB::table('analyse_user_submenus')
                            ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                            ->whereIn('analyse_user_submenus.user_submenu_id', $ownSubMenus)->get();
                    }

                } else {
                    $analyses[] = DB::table('analyse_user_submenus')
                        ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                        ->where('analyse_user_submenus.user_submenu_id', $subid)->get();

                    $ownSubMenus[] = $subid;
                    $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
                }

                //$analyses = UserSubmenu::with('analyses')->find($subid);
                $anaRed = 0;
                $anaOrange = 0;
                $anaGreen = 0;
                $anaBlue = 0;

                if (!empty($analyses)) {
                    foreach ($analyses as $analyse) {
                        foreach ($analyse as $print) {
                            $strP = $print->name;
                            $bioryth = $print->bioryth;


                            if ($print->bioryth == 0) $bioryth = 28;
                            elseif ($print->bioryth < 0) $bioryth = 28;
                            else  $bioryth = $print->bioryth;

                            #Analysis/Male/Heart/Oneday Calculation
                            $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                            $ma = round($beerg, 0);
                            $beergK = round($beergK, 0);
                            $beergS = round($beergS, 0);
                            $beergod = round($beergod, 00);

                            if (!empty($ranValue)) {

                                if (array_key_exists($print->id, $ranValue)) {
                                    $ma = $ranValue[$print->id];
                                }
                            }

                            if (isset($subDetails->status) && $subDetails->status == 1) {
                                if (isset($print->user_submenu_id) && $ownColor[$print->user_submenu_id])
                                    $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                                else if (isset($print->submenu_id) && $productColor[$print->submenu_id])
                                    $colorValue = $productColor[$print->submenu_id] ?? null;
                            } else {
                                $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                            }

                            #Analysis Value Color
                            if ($colorValue !=null && $colorValue->green_min == null && $colorValue->green_max == null) {
                                $colorValue = GlobalSetting::find(1);
                                if (isset($colorValue) && $ma >= $colorValue->gs_red_min && $ma <= $colorValue->gs_red_max) {
                                    $anaRed++;
                                    $totalRed++;
                                } elseif (isset($colorValue) && $ma >= $colorValue->gs_orange_min && $ma <= $colorValue->gs_orange_max) {
                                    $anaOrange++;
                                    $totalOrange++;
                                } elseif (isset($colorValue) && $ma >= $colorValue->gs_green_min && $ma <= $colorValue->gs_green_max) {
                                    $anaGreen++;
                                    $totalGreen++;
                                }
                            } else {
                                $colorValue = reset($ownColor);
                                if (isset($colorValue) && $ma >= $colorValue->red_min && $ma <= $colorValue->red_max) {
                                    $anaRed++;
                                    $totalRed++;
                                } elseif (isset($colorValue) && $ma >= $colorValue->orange_min && $ma <= $colorValue->orange_max) {
                                    $anaOrange++;
                                    $totalOrange++;
                                } elseif (isset($colorValue) && $ma >= $colorValue->green_min && $ma <= $colorValue->green_max) {
                                    $anaGreen++;
                                    $totalGreen++;
                                }
                            }
                        }
                    }

                }

                $anaCount[] = array(
                    'submenuId' => $submenu['id'],
                    'submenuName' => $submenu['name'] ?? $submenu['submenu_name'],
                    'anaRed' => $anaRed,
                    'anaOrange' => $anaOrange,
                    'anaGreen' => $anaGreen,
                    'anaBlue' => $anaBlue
                );
            }
        }

        $anaTotalCount = array(
            'proid' => $ownid,
            'proName' => $product['product_name'],
            'anaRed' => $totalRed,
            'anaOrange' => $totalOrange,
            'anaGreen' => $totalGreen,
            'anaBlue' => $totalBlue
        );

        $data['product'] = $anaTotalCount;
        $data['submenu'] = $anaCount;

        return $data;
    }

    public function userAverageOwnProducts($ownid, $user, $product = [])
    {
        #functions
        if (!empty($product)) {
            $userSubmenu = collect();
            if (!empty($product['submenus'])) {
                foreach ($product['submenus'] as $key => $submenu) {
                    $userSubmenu->push((object)[
                        'id' => $submenu['id'],
                        'menu_id' => $submenu['product_id'],
                        'name' => $submenu['submenu_name'],
                        'sortingNo' => $submenu['sortingNo']
                    ]);
                }
            }
            $submenus = collect();
            $submenus->id = $product['product_id'];
            $submenus->name = $product['product_name'];
            $submenus->usersubmenus = $userSubmenu;

        } else $submenus = getOwnSubmenusByMenuid($ownid);

        #user random values
        $ranValue = randomValue($user->id);
        $biorythDetail = biorythVisibleDetails();

        #user info
        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        $totalSub = 0;
        $totalSum = 0;
        $subAna = 0;
        $subSum = 0;
        $totalAvg = 0;

        if (count($submenus->usersubmenus) > 0) {
            foreach ($submenus->usersubmenus as $key => $sub) {
                if (is_array($sub)) $submenu_list[$sub['sortingNo']] = $sub;
                else $submenu_list[$sub->sortingNo] = $sub;
            }
            ksort($submenu_list);
            if (count($submenu_list) == 1) $submenu_list = $submenus->usersubmenus;

            foreach ($submenu_list as $key => $submenu) {

                $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);
                $subid = $submenu->id;

                $subDetails = UserSubMenu::with('linkSubMenus')->where('id', $subid)->first();
                $analyses = array();

                if ($subDetails->status == 1) {
                    $normalSubMenus = array();
                    $ownSubMenus = array();
                    $ownSubMenus[] = $subDetails->id;

                    foreach ($subDetails->linkSubMenus as $linksub) {
                        if ($linksub->type == 1) $normalSubMenus[] = $linksub->link_submenu_id;
                        else $ownSubMenus[] = $linksub->link_submenu_id;
                    }

                    if (!empty($normalSubMenus)) {
                        $productColor = getProductSettingBySubmenu($normalSubMenus);
                        $randomCauses = randomCauses($normalSubMenus);
                    }
                    if (!empty($ownSubMenus)) {
                        $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
                    }

                    if (!empty($normalSubMenus)) {
                        $pools = poolsIdsOwn($normalSubMenus);
                        $poolid = $pools;

                        $analyses[] = DB::table('analyse_pools')
                            ->join('pool_submenus', 'pool_submenus.pool_id', '=', 'analyse_pools.pool_id')
                            ->join('analyses', 'analyses.id', '=', 'analyse_pools.analyse_id')
                            ->whereIn('pool_submenus.submenu_id', $normalSubMenus)
                            ->whereIn('analyse_pools.pool_id', $poolid)->get();
                    }

                    if (!empty($ownSubMenus)) {
                        $analyses[] = DB::table('analyse_user_submenus')
                            ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                            ->whereIn('analyse_user_submenus.user_submenu_id', $ownSubMenus)->get();
                    }

                } else {
                    $analyses[] = DB::table('analyse_user_submenus')
                        ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                        ->where('analyse_user_submenus.user_submenu_id', $subid)->get();

                    $ownSubMenus[] = $subid;
                    $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
                }

                $subAna = 0;
                $subSum = 0;
                $total = 0;
                $subAvg = 0;

                if (!empty($analyses)) {
                    foreach ($analyses as $analyse) {
                        foreach ($analyse as $print) {

                            $anaid = $print->id;
                            $strP = $print->name;
                            $bioryth = $print->bioryth;
                            $anaids[] = $anaid;


                            if ($print->bioryth == 0) $bioryth = 28;
                            elseif ($print->bioryth < 0) $bioryth = 28;
                            else  $bioryth = $print->bioryth;

                            #Analysis/Male/Heart/Oneday Calculation
                            $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                            $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                            $ma = round($beerg, 0);
                            $beergK = round($beergK, 0);
                            $beergS = round($beergS, 0);
                            $beergod = round($beergod, 00);

                            if (!empty($ranValue)) {

                                if (array_key_exists($print->id, $ranValue)) {
                                    $ma = $ranValue[$print->id];
                                }
                            }

                            $subSum = $subSum + $ma;
                            $subAna++;
                        }
                    }
                }

                if ($subSum != 0 && $subAna != 0) {
                    $total = $subSum / $subAna;
                    $subAvg = round($total);
                }

                $totalSum = $totalSum + $subAvg;
                $totalSub++;

                if ($subDetails->status == 1) {
                    if ($print->user_submenu_id && $productColor[$print->user_submenu_id])
                        $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                    else if ($print->submenu_id && $productColor[$print->submenu_id])
                        $colorValue = $productColor[$print->submenu_id] ?? null;
                } else {
                    $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                }

                #Analysis Value Color
                if (isset($colorValue) && $colorValue->green_min == null && $colorValue->green_max == null) {
                    $colorValue = GlobalSetting::find(1);
                    if ($ma >= $colorValue->gs_red_min && $ma <= $colorValue->gs_red_max) $subColor = "#E84E1B";
                    elseif ($ma >= $colorValue->gs_orange_min && $ma <= $colorValue->gs_orange_max) $subColor = "#F8B133";
                    elseif ($ma >= $colorValue->gs_green_min && $ma <= $colorValue->gs_green_max) $subColor = "#2FAB66";
                } else {
                    if (isset($colorValue) && $ma >= $colorValue->red_min && $ma <= $colorValue->red_max) $subColor = "#E84E1B";
                    elseif (isset($colorValue) && $ma >= $colorValue->orange_min && $ma <= $colorValue->orange_max) $subColor = "#F8B133";
                    elseif (isset($colorValue) && $ma >= $colorValue->green_min && $ma <= $colorValue->green_max) $subColor = "#2FAB66";
                }

                $anaCount[] = array(

                    'submenuId' => $submenu->id,
                    'submenuName' => $submenu->name,
                    'subcolor' => $subColor,
                    'subAvg' => $subAvg,
                );
            }
        }

        if ($totalSub != 0 && $totalSum != 0) {
            $total1 = (100 / ($totalSub * 100)) * $totalSum;
            $totalAvg = round($total1);
        }

        #Analysis Value Color
        if (isset($colorValue) && $colorValue->green_min == null && $colorValue->green_max == null) {
            $colorValue = GlobalSetting::find(1);
            if ($ma >= $colorValue->gs_red_min && $ma <= $colorValue->gs_red_max) $totalColor = "#E84E1B";
            elseif ($ma >= $colorValue->gs_orange_min && $ma <= $colorValue->gs_orange_max) $totalColor = "#F8B133";
            elseif ($ma >= $colorValue->gs_green_min && $ma <= $colorValue->gs_green_max) $totalColor = "#2FAB66";
        } else {
            if (isset($colorValue) && $ma >= $colorValue->red_min && $ma <= $colorValue->red_max) $totalColor = "#E84E1B";
            elseif (isset($colorValue) && $ma >= $colorValue->orange_min && $ma <= $colorValue->orange_max) $totalColor = "#F8B133";
            elseif (isset($colorValue) && $ma >= $colorValue->green_min && $ma <= $colorValue->green_max) $totalColor = "#2FAB66";
        }

        $anaTotalCount = array(
            'proid' => $ownid,
            'proName' => $submenus->name,
            'totalAvg' => $totalAvg,
            'color' => $totalColor,
        );

        $data['product'] = $anaTotalCount;
        $data['submenu'] = $anaCount;

        return $data;
    }

    public function farbklangSubmenuAnalyses($proid, $subid)
    {
        $userid = getUserId();
        $sortBy = "";
        $user = User::with(["useroption:user_id,ran_ana,pattern_switch", "userfilter"])->find($userid);
        $productColors = productColors($proid);
        $priceValue = null;

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        $poolid = poolsIds($subid) ?? [];
        // $anaids = array();
        $redCollection = array();
        $orangeCollection = array();
        $greenCollection = array();

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        if ($user->userfilter->filter_type == 1) $sortBy = "asc";
        if ($user->userfilter->filter_type == 2) $sortBy = "desc";

        // if (app()->getLocale() == 'en') {
        //     $analyses = EnAnalyses::select('en_analyses.*', 'analyses.name as de_name')
        //         ->join('analyses', 'en_analyses.id', 'analyses.id')
        //         ->whereHas('pools', function ($query) use ($poolid) {
        //             $query->whereIn('pool_id', $poolid);
        //         })->when($sortBy, function ($query, $sortBy) {
        //             return $query->orderBy('name', $sortBy);
        //         })->get();
        // } else {
        //     $analyses = Analyse::whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query, $sortBy) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // }
        $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
            $query->whereIn('pool_id', $poolid);
        })->when($sortBy, function ($query, $sortBy) {
            return $query->orderBy('name', $sortBy);
        });
        if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
        $analyses = $anaQuery->get();

        if (count($analyses) > 0 && !empty($poolid)) {

            $all_analysis_id = array();

            foreach ($analyses as $analysis)
                $all_analysis_id[] = $analysis->id;

            #random causes
            $todayCause = savedCauses($subid, $all_analysis_id, $user->id);
            $randomCauses = randomCauses($subid);

            #random anaval query
            $random_values = randomValue($user->id);

            foreach ($analyses as $print) {

                $anaid = $print->id;
                $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                $bioryth = $print->bioryth;
                $bodydesc = $print->body_desc;
                $mentaldesc = $print->mental_desc;
                $desc = $print->description;

                $anaids[] = $anaid;
                if ($print->bioryth == 0) $bioryth = 28;
                elseif ($print->bioryth < 0) $bioryth = 28;
                else  $bioryth = $print->bioryth;

                #Analysis/Male/Heart/Oneday Calculation
                $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                $beerg = calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                $ma = round($beerg, 0);
                $beergK = round($beergK, 0);
                $beergS = round($beergS, 0);
                $beergod = round($beergod, 00);

                if (!empty($random_values) && $user->useroption->pattern_switch == 1) {
                    if (array_key_exists($print->id, $random_values)) {
                        $ma = $random_values[$print->id];
                    }
                }

                // #Male/Heart Color
                // if ($beergS >= 50) $heart = "color:red";
                // else if ($beergS <= 49) $heart = "color:gray";
                // if ($beergK <= 30) $male = "color:red";
                // else if ($beergK >= 31) $male = "color:gray";

                #Analysis Value Color
                if (isset($productColors) && $ma >= $productColors->red_min && $ma <= $productColors->red_max) $class_skill = "#E84E1B";
                elseif (isset($productColors) && $ma >= $productColors->orange_min && $ma <= $productColors->orange_max) $class_skill = "#F8B133";
                elseif (isset($productColors) && $ma >= $productColors->green_min && $ma <= $productColors->green_max) $class_skill = "#2FAB66";
                elseif (isset($productColors) && $ma >= $productColors->custom_min && $ma <= $productColors->custom_max) $class_skill = $productColors->custom_color;

                #Cause/Medium/Tipp
                if (!empty($todayCause['causes'])) {
                    if (array_key_exists($print->id, $todayCause['causes']))
                        $causeGroupid = $todayCause['causes'][$print->id];
                    else if (count($randomCauses['causes']) > 0)
                        $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];

                } else if (count($randomCauses['causes']) > 0) {
                    $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                }

                if (!empty($todayCause['medium'])) {
                    if (array_key_exists($print->id, $todayCause['medium']))
                        $mediumGroupid = $todayCause['medium'][$print->id];
                    else if (count($randomCauses['medium']) > 0)
                        $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];

                } else if (count($randomCauses['medium']) > 0) {

                    $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                }

                if (!empty($todayCause['tipp'])) {
                    if (array_key_exists($print->id, $todayCause['tipp']))
                        $tippGroupid = $todayCause['tipp'][$print->id];
                    else if (count($randomCauses['tipp']) > 0)
                        $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];

                } else if (count($randomCauses['tipp']) > 0) {

                    $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                }

                #Random Price For Cart
                if ($priceValue == null) {
                    $priceValue = priceValue($proid, $subid, $anaid);
                }

                $randPrice = rand($priceValue['min_price'], $priceValue['max_price']);
                $collectionType = null;
                $collection = array();

                if (isset($productColors) && $ma >= $productColors->red_min && $ma <= $productColors->red_max) $collectionType = "redCollection";
                elseif (isset($productColors) && $ma >= $productColors->orange_min && $ma <= $productColors->orange_max) $collectionType = "orangeCollection";
                elseif (isset($productColors) && $ma >= $productColors->green_min && $ma <= $productColors->green_max) $collectionType = "greenCollection";

                #all Analysis
                if ($collectionType != null) {
                    $collection = array(
                        "ana_val" => $ma,
                        "ana_id" => $anaid,
                        "ana_name" => $strP,
                        "desc" => $desc,
                        "body_desc" => $bodydesc,
                        "mental_desc" => $mentaldesc,
                        "url_name" => $print->url_name,
                        "url_link" => $print->url_link,
                        "anaColor" => $class_skill,
                        "male_val" => $beergK,
                        "heart_val" => $beergS,
                        "causes" => $causeGroupid,
                        "medium" => $mediumGroupid,
                        "tipp" => $tippGroupid,
                        "price" => $randPrice
                    );

                    if ($collectionType == "redCollection") {
                        $redCollection[$anaid] = (object)$collection;
                    } else if ($collectionType == "orangeCollection") {
                        $orangeCollection[$anaid] = (object)$collection;
                    } else {
                        $greenCollection[$anaid] = (object)$collection;
                    }
                }
            }
        } else {
            $analyses = "No Analyses Available";
        }

        if (!empty($redCollection)) {
            sort($redCollection);
        }
        if (!empty($orangeCollection)) {
            sort($orangeCollection);
        }
        if (!empty($greenCollection)) {
            sort($greenCollection);
        }

        $output['red_analyses'] = empty($redCollection) ? null : $redCollection;
        $output['orange_analyses'] = empty($orangeCollection) ? null : $orangeCollection;
        $output['green_analyses'] = empty($greenCollection) ? null : $greenCollection;

        return $output;
    }

    public function premiumPro()
    {
        if (app()->getLocale() == '' || app()->getLocale() == 'de') {
            $product_name = Product::find(request()->segment(3))->product_name;
        } else {
            $product_column_name = app()->getLocale() . "_product_name";
            $product = Product::select($product_column_name . ' as product_name')->find(request()->segment(3));
            $product_name = $product->product_name;
        }
        return view('Frontend.dashboard.premium_view', ['name' => $product_name]);
    }

    public function oldPackageView()
    {
        return view('Frontend.dashboard.old_packages');
    }

    public function productsProgressbar()
    {
        return view('Frontend.dashboard.products_progressbar');
    }

    public function pdfs($user = null, $admin = null)
    {
        $pdf = '';
        if ($user->boss_id == 0)
            $pdf = Pdf::where('user_id', getUserId())->get();
        elseif ($admin->user_type == 2) {
            $pdf = Pdf::where('user_id', getUserId())->get();
        } else
            $pdf = Pdf::where('subuser_id', getUserId())->get();

        if (!empty($pdf->toArray()))
            return $pdf->sortByDesc('id');
        else
            return $pdf;
    }

    public function activityLog()
    {
        return view('Frontend.dashboard.activity_log');
    }


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    public function paginate($items, $perPage = 12, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, ['path' => Paginator::resolveCurrentPath()]);
    }

    public function array_sort($array, $on, $order = 'SORT_DESC')
    {
        $new_array = array();
        $sortable_array = array();

        if (is_array($array) && count($array) > 0) {
            foreach ($array as $k => $v) {

                if (is_array($v)) {

                    foreach ($v as $k2 => $v2) {

                        if ($k2 == $on) {
                            $sortable_array[$k] = $v2;

                        }
                    }
                } else {
                    $sortable_array[$k] = $v;
                }
            }
            switch ($order) {
                case 'SORT_ASC':
                    asort($sortable_array);
                    break;
                case 'SORT_DESC':
                    arsort($sortable_array);
                    break;
            }

            foreach ($sortable_array as $k => $v) {
                array_push($new_array, $array[$k]);
            }
        }

        return $new_array;
    }

    public function changerandomstatus(Request $request)
    {
        $option = DB::table('user_options')->where('user_id', getUserId())->first();
        if ($option->ran_ana == 1) {
            DB::table('user_options')
                ->where("user_id", '=', getUserId())
                ->update(['ran_ana' => false]);
        } else {
            DB::table('user_options')
                ->where("user_id", '=', getUserId())
                ->update(['ran_ana' => true]);
        }
        Cache::put('get_ran_status' . getUserId(), true);
        if ($request->ajax()) {
            return response()->json(['success' => true]);
        } else {
            return Redirect::back()->with('message', __('action.successfullychange'));
        }
    }


    //chnages 03.11.2020

    public function longProduct(Request $request, $proid, $subid, $days)
    {
        $user_id = session()->get('id');
        $filterId = userFilterid($user_id);
        $userDetails = $filterId;
        $data['user'] = $filterId;
        if (app()->getLocale() == '' || app()->getLocale() == 'de') {
            $product_name = Product::find($proid)->product_name;
            $submenu_name = Submenu::find($subid)->menu_name;
        } else {
            $product_column_name = app()->getLocale() . "_product_name";
            $product = Product::select($product_column_name . ' as product_name')->find($proid);
            $product_name = $product->product_name;

            $submenu_column_name = app()->getLocale() . "_menu_name";
            $submenu = Submenu::select('id', 'product_id', 'sub_min', 'sub_max', 'show_icon', 'show_button', 'icon', 'type', 'slide_speed', 'direction_status', $submenu_column_name . " as menu_name")->find($subid);
            $submenu_name = $submenu->menu_name;
        }
        $data['proName'] = $product_name;
        $data['subName'] = $submenu_name;
        $data['proid'] = $proid;
        $data['subid'] = $subid;
        $data['filterid'] = $filterId->userfilter->filter_type;
        $package_list = User::find($user_id);
        $cartpackagelist = getCartPackagesById($package_list->id);
        $packages = array();
        if (count($cartpackagelist) > 0) {
            foreach ($cartpackagelist as $key => $packId) {
                $packages[] = array(
                    'id' => $packId->id,
                    'name' => $packId->package_name,
                    'date' => $packId->created_at,
                );
            }
        }
        $data['lastpage'] = 1;
        $days = request()->days;

        $thema_speichern = ($userDetails->thema_speichern) ? str_replace(' ', '', $userDetails->thema_speichern) : '';
        $calculation_with = ($userDetails->calculation_with) ? $userDetails->calculation_with : '';
        $datumcore = ($userDetails->datumcore) ? $userDetails->datumcore : '';
        $locale = app()->getLocale() ?? config('app.locale');
        $dynamic_cache_key = 'LTAanalysis_' . $user_id . '_' . $proid . '_' . $subid . '_' . $calculation_with . '_' . $datumcore . '_' . $thema_speichern;

        if (Cache::has('get_ran_status' . $user_id)) {
            Cache::pull('get_ran_status' . $user_id);
            Cache::forget($dynamic_cache_key);
        }

        if (Cache::has($dynamic_cache_key) && $request->ajax()) {
            $analyses = Cache::get($dynamic_cache_key);
            $sorted_analyses = $analyses['analyses'];

            #checking random values
            $random_values = randomValue($userDetails->id);
            if (!empty($random_values)) {
                foreach ($random_values as $key => $val) {
                    if (array_key_exists($key, $sorted_analyses)) {
                        $curAnaVal = $sorted_analyses[$key]['ma'];
                        if ($curAnaVal != $val) {
                            if ($val >= 0 && $val <= 10) $color = "#E84E1B";
                            else if ($val >= 11 && $val <= 69) $color = "#F8B133";
                            else if ($val >= 70 && $val <= 100) $color = "#2FAB66";
                            $sorted_analyses[$key]['anaVal'] = $val;
                            $sorted_analyses[$key]['anaColor'] = $color;
                            $sorted_analyses[$key]['ranValStatus'] = 1;
                        }
                    }
                }
            }

            $sorted_analyses = $this->array_sort($sorted_analyses, "anaVal_red", $order = 'SORT_DESC');
            $analyses_paginate = $this->paginate($sorted_analyses);

            #$analyses_paginate = $analyses['analyses'];
            $bodyimages = $analyses['bodyimages'];
            $mentalimages = $analyses['mentalimages'];

        } else {
            $analyses = $this->longProductAnalyses($proid, $subid, $userDetails, $days);
            if ($userDetails->userOption->ran_ana == 1) Cache::forget($dynamic_cache_key);

            Cache::forever($dynamic_cache_key, $analyses);
            $sorted_analyses = $analyses['analyses'];
            $sorted_analyses = $this->array_sort($sorted_analyses, "anaVal_red", $order = 'SORT_DESC');

            $analyses_paginate = $this->paginate($sorted_analyses);
            #$analyses_paginate = $analyses['analyses'];
            $bodyimages = $analyses['bodyimages'];
            $mentalimages = $analyses['mentalimages'];

        }

        if ($request->ajax()) {

            if ($userDetails->useroption->dashboard_option == 11) {
                return [
                    'posts' => view('Frontend.dashboard.longterm_product_progressbar_view', ['data' => $data, 'analyses' => $analyses_paginate, 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages])->render()
                ];
            } else {
                return [
                    'posts' => view('Frontend.dashboard.longterm_product_view', ['data' => $data, 'analyses' => $analyses_paginate, 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages])->render()
                ];
            }

        }

        if ($userDetails->useroption->ran_ana == 1) {
            DB::table('user_options')->where("user_id", '=', $user_id)->update(['ran_ana' => false]);
        }

        if (($userDetails->useroption->dashboard_option == 11 and $subid == 20) or $subid == 20)
            return view('Frontend.dashboard.longterm_chakra', ['data' => $data, 'analyses' => $analyses_paginate, 'anaids' => $analyses['anaids'], 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages, 'start_date' => $analyses['start_date'], 'end_date' => $analyses['end_date']]);
        else if ($userDetails->useroption->dashboard_option == 11)
            return view('Frontend.dashboard.longterm_product_progressbar', ['data' => $data, 'analyses' => $analyses_paginate, 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages, 'start_date' => $analyses['start_date'], 'end_date' => $analyses['end_date']]);
        else
            return view('Frontend.dashboard.longterm_product', ['data' => $data, 'analyses' => $analyses_paginate, 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages, 'start_date' => $analyses['start_date'], 'end_date' => $analyses['end_date']]);
    }


    public function longProductAnalyses($proid, $subid, $filterId = null, $days)
    {
        $userid = session()->get('id');
        $total_day = $days;
        // $days  = $days/30;
        $all_beergS_sum = array();
        $all_beergS_red = array();
        $all_beergS_gray = array();
        $all_beergK_sum = array();
        $all_beergK_red = array();
        $all_beergK_gray = array();
        $all_beerg_sum = array();
        $all_analysis_red = array();
        $all_analysis_orange = array();
        $all_analysis_green = array();
        $last_date = null;
        $sortBy = "";
        $causeGroupid = null;
        $mediumGroupid = null;
        $tippGroupid = null;
        $ranValStatus = 0;
        $anaids = array();
        $start_date = null;

        if ($filterId == null)
            $filterId = userFilterid($userid);

        $user = $filterId;
        $productColors = productColors($proid);
        $priceValue = priceValue($proid, $subid, 0);

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        $poolid = poolsIds($subid) ?? [];

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        if ($filterId->userfilter->filter_type == 1) $sortBy = "asc";
        if ($filterId->userfilter->filter_type == 2) $sortBy = "desc";
        if ($subid == 20) $sortBy = "desc";

        $anaids = array();

        //$analyses = Pool::with("analyses")->whereIn('id', $poolid->pools)->get();

        // if (app()->getLocale() == 'en') {
        //     $analyses = EnAnalyses::select('en_analyses.*', 'analyses.name as de_name')
        //     ->join('analyses', 'en_analyses.id', 'analyses.id')
        //     ->whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query,
        //         $sortBy
        //     ) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // } else {
        //     $analyses = Analyse::whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query, $sortBy) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // }
        $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
            $query->whereIn('pool_id', $poolid);
        })->when($sortBy, function ($query, $sortBy) {
            return $query->orderBy('name', $sortBy);
        });
        if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
        $analyses = $anaQuery->get();

        if (count($analyses) > 0 && !empty($poolid)) {

            $all_analysis_id = array();

            foreach ($analyses as $analysis) {
                $all_analysis_id[] = $analysis->id;
                $all_analysis_red["{$analysis->id}_red"] = 0;
                $all_analysis_orange["{$analysis->id}_orange"] = 0;
                $all_analysis_green["{$analysis->id}_green"] = 0;

                $all_beergS_sum[$analysis->id] = 0;
                $all_beergK_sum[$analysis->id] = 0;
                $all_beerg_sum[$analysis->id] = 0;
                $all_beergS_red[$analysis->id] = 0;
                $all_beergS_gray[$analysis->id] = 0;
                $all_beergK_red[$analysis->id] = 0;
                $all_beergK_gray[$analysis->id] = 0;
            }

            //body and mental images
            $body_images = getBodyImage($all_analysis_id);
            $mental_images = getMentalImage($all_analysis_id);

            //random causes
            $todayCause = savedCauses($subid, $all_analysis_id, $user->id);
            $randomCauses = randomCauses($subid);

            //random anaval query
            $random_values = randomValue($user->id);

            //start date;
            $start_date = $user->datumcore;

            //long term analysis start
            for ($i = 0; $i < $days; $i++) {

                $curdate = date('Y-m-d', strtotime($start_date . '+' . $i . ' days'));
                $user->datumcore = $curdate;
                $last_date = $curdate;

                foreach ($analyses as $print) {

                    $anaid = $print->id;
                    $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                    $bioryth = (int)$print->bioryth;
                    $bodydesc = $print->body_desc;
                    $mentaldesc = $print->mental_desc;

                    $anaids[] = $anaid;
                    $ranValStatus = 0;
                    if ($bioryth <= 0) $bioryth = 28;

                    //Analysis/Male/Heart/Oneday Calculation
                    $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beerg = ($user->userOption->ran_ana == 1) ? rand(0, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                    $ma = round($beerg, 0);
                    $beergK = round($beergK, 0);
                    $beergS = round($beergS, 0);
                    $beergod = round($beergod, 00);

                    if (!empty($random_values) && $user->useroption->pattern_switch == 1) {

                        if (array_key_exists($print->id, $random_values)) {
                            $ma = $random_values[$print->id];
                            $ranValStatus = 1;
                        }
                    }

                    if (isset($productColors) && $ma >= $productColors->red_min && $ma <= $productColors->red_max) $all_analysis_red["{$anaid}_red"] += 1;
                    elseif (isset($productColors) && $ma >= $productColors->orange_min && $ma <= $productColors->orange_max) $all_analysis_orange["{$anaid}_orange"] += 1;
                    elseif (isset($productColors) && $ma >= $productColors->green_min && $ma <= $productColors->green_max) $all_analysis_green["{$anaid}_green"] += 1;

                    $all_beergS_sum[$anaid] += $beergS;
                    $all_beergK_sum[$anaid] += $beergK;
                    $all_beerg_sum[$anaid] += $beergod;

                    if ($beergS >= 50) $all_beergS_red[$anaid]++;
                    else if ($beergS <= 49) $all_beergS_gray[$anaid]++;
                    if ($beergK <= 30) $all_beergK_red[$anaid]++;
                    else if ($beergK >= 31) $all_beergK_gray[$anaid]++;
                }
            }
        }

        if (count($analyses) > 0 && !empty($poolid)) {

            foreach ($analyses as $print) {

                $anaid = $print->id;
                $strP = $print->name;
                $bioryth = $print->bioryth;
                $bodydesc = $print->body_desc;
                $mentaldesc = $print->mental_desc;

                $anaids[] = $anaid;
                $ranValStatus = 0;

                if ($print->bioryth == 0) $bioryth = 28;
                elseif ($print->bioryth < 0) $bioryth = 28;
                else  $bioryth = $print->bioryth;

                // $ma = round($all_analysis_sum[$anaid]/$total_day);
                $beergK = 0;
                $beergS = 0;
                $beergod = 0;

                // Assuming $all_beergK_sum, $all_beergS_sum, and $all_beerg_sum are arrays with numeric values
                if (isset($all_beergK_sum[$anaid]) && is_numeric($all_beergK_sum[$anaid]) && $total_day !== 0) {
                    $beergK = round($all_beergK_sum[$anaid] / $total_day, 0);
                }
                if (isset($all_beergS_sum[$anaid]) && is_numeric($all_beergS_sum[$anaid]) && $total_day !== 0) {
                    $beergS = round($all_beergS_sum[$anaid] / $total_day, 0);
                }
                if (isset($all_beerg_sum[$anaid]) && is_numeric($all_beerg_sum[$anaid]) && $total_day !== 0) {
                    $beergod = round($all_beerg_sum[$anaid] / $total_day, 0);
                }

                #Male/Heart Color
                if ($beergS >= 50) $heart = "color:red";
                else if ($beergS <= 49) $heart = "color:gray";
                if ($beergK <= 30) $male = "color:red";
                else if ($beergK >= 31) $male = "color:gray";

                #Analysis Value Color
                // if ($ma >= $productColors->red_min && $ma <= $productColors->red_max) $class_skill = "#E84E1B";
                // elseif ($ma >= $productColors->orange_min && $ma <= $productColors->orange_max) $class_skill = "#F8B133";
                // elseif ($ma >= $productColors->green_min && $ma <= $productColors->green_max) $class_skill = "#2FAB66";
                // elseif ($ma >= $productColors->custom_min && $ma <= $productColors->custom_max) $class_skill = $productColors->custom_color;

                #Cause/Medium/Tipp
                if (!empty($todayCause['causes'])) {
                    if (array_key_exists($print->id, $todayCause['causes']))
                        $causeGroupid = $todayCause['causes'][$print->id];
                    else if (count($randomCauses['causes']) > 0)
                        $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];

                } else if (count($randomCauses['causes']) > 0) {
                    $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                }

                if (!empty($todayCause['medium'])) {
                    if (array_key_exists($print->id, $todayCause['medium']))
                        $mediumGroupid = $todayCause['medium'][$print->id];
                    else if (count($randomCauses['medium']) > 0)
                        $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];

                } else if (count($randomCauses['medium']) > 0) {
                    $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                }

                if (!empty($todayCause['tipp'])) {
                    if (array_key_exists($print->id, $todayCause['tipp']))
                        $tippGroupid = $todayCause['tipp'][$print->id];
                    else if (count($randomCauses['tipp']) > 0)
                        $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];

                } else if (count($randomCauses['tipp']) > 0) {
                    $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                }

                #Random Price For Cart
                if (empty($priceValue)) {
                    $priceValue = priceValue($proid, $subid, $anaid);
                }

                $randPrice = rand($priceValue['min_price'], $priceValue['max_price']);

                $allRed = ($all_analysis_red["{$anaid}_red"] == null) ? 0 : $all_analysis_red["{$anaid}_red"];
                $allOrange = ($all_analysis_orange["{$anaid}_orange"] == null) ? 0 : $all_analysis_orange["{$anaid}_orange"];
                $allGreen = $all_analysis_green["{$anaid}_green"];
                $allRedPercentage = 0;
                $allGreenPercentage = 0;
                $pointer = 0;

                if (($allRed + $allOrange) > 0) $allRedPercentage = round((($allRed + $allOrange) * 100) / $total_day, 0);
                if ($allGreen > 0) $allGreenPercentage = round(($allGreen * 100) / $total_day, 0);

                if ($allRedPercentage > 0) {
                    $pointer = floor((60 * ($allRedPercentage / 100))) + 21;
                } else {
                    $pointer = floor((60 * ($allGreenPercentage / 100))) + 21;
                }

                // $calculationRed = round((100/$days)*($allRed +$allOrange));

                #All Analysis
                $allAnalyses[$anaid] = array(
                    "anaVal_red" => ($allRed + $allOrange),
                    "anaVal_green" => ($all_analysis_green["{$anaid}_green"] == null) ? 0 : $all_analysis_green["{$anaid}_green"],
                    "anaVal" => $days,
                    // "anaVal"            => $calculationRed,
                    "redBar" => $allRedPercentage,
                    "greenBar" => $allGreenPercentage,
                    "anaid" => $anaid,
                    "anaName" => $strP,
                    "url_name" => $print->url_name,
                    "url_link" => $print->url_link,
                    "desc" => $print->description,
                    "desc_img" => $print->desc_image,
                    "bodyDesc" => $bodydesc,
                    "mentalDesc" => $mentaldesc,
                    "bioyrth" => $bioryth,
                    "anaColor" => "#E84E1B",
                    "maleVal" => $beergK,
                    "heartVal" => $beergS,
                    "maleRed" => $all_beergK_red[$anaid],
                    "maleGray" => $all_beergK_gray[$anaid],
                    "heartRed" => $all_beergS_red[$anaid],
                    "heartGray" => $all_beergS_gray[$anaid],
                    "maleColor" => $male,
                    "heartColor" => $heart,
                    "beergod" => $beergod,
                    "causes" => $causeGroupid,
                    "medium" => $mediumGroupid,
                    "tipp" => $tippGroupid,
                    "poolid" => 0,
                    "randPrice" => $randPrice,
                    "ranValStatus" => $ranValStatus,
                    "pointer" => $pointer,
                );

            }
        } else {
            $analyses = "No Analyses Available";
        }

        $data['analyses'] = $allAnalyses;
        $data['start_date'] = $start_date;
        $data['end_date'] = $last_date;
        $data['bodyimages'] = $body_images;
        $data['mentalimages'] = $mental_images;

        if ($subid == 20) $data['anaids'] = $anaids;

        return $data;
    }

    public function longOwnProduct(Request $request, $ownid, $ownsubid)
    {
        $userDetails = userFilterid(getUserId());
        $data['proName'] = getFirst(['id' => $ownid], 'menus')->name;
        $data['subName'] = getFirst(['id' => $ownsubid], 'user_submenus')->name;
        $data['user'] = $userDetails;
        $data['proid'] = $ownid;
        $data['subid'] = $ownsubid;

        $data['filterid'] = $userDetails->userfilter->filter_type;

        $package_list = User::find(getUserId());
        $cartpackagelist = getCartPackagesById($package_list->id);
        $packages = array();
        if (count($cartpackagelist) > 0) {
            foreach ($cartpackagelist as $key => $packId) {
                $packages[] = array(
                    'id' => $packId->id,
                    'name' => $packId->package_name,
                    'date' => $packId->created_at,
                );
            }
        }

        $days = request()->days;
        $analyses = $this->longUserOwnProduct($ownid, $ownsubid, $userDetails, $days);
        $analyses_paginate = $this->paginate($analyses['analyses']);
        $bodyimages = $analyses['bodyimages'];
        $mentalimages = $analyses['mentalimages'];

        if ($request->ajax()) {

            if ($userDetails->useroption->dashboard_option == 11) {
                return [
                    'posts' => view('Frontend.dashboard.longterm_ownproduct_progressbar_view', ['data' => $data, 'analyses' => $analyses_paginate, 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages])->render()
                ];
            } else {
                return [
                    'posts' => view('Frontend.dashboard.longterm_ownproduct_view', ['data' => $data, 'analyses' => $analyses_paginate, 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages])->render()
                ];
            }
        }

        if ($userDetails->useroption->ran_ana == 1) {
            DB::table('user_options')->where("user_id", '=', $userDetails->id)->update(['ran_ana' => false]);
        }

        if ($userDetails->useroption->dashboard_option == 11)
            return view('Frontend.dashboard.longterm_ownproduct_progressbar', ['data' => $data, 'analyses' => $analyses_paginate, 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages, 'start_date' => $analyses['start_date'], 'end_date' => $analyses['end_date']]);
        else
            return view('Frontend.dashboard.longterm_ownproduct', ['data' => $data, 'analyses' => $analyses_paginate, 'packages' => $packages, 'bodyimages' => $bodyimages, 'mentalimages' => $mentalimages, 'start_date' => $analyses['start_date'], 'end_date' => $analyses['end_date']]);
    }

    public function longUserOwnProduct($ownid, $ownsubid, $user, $days)
    {

        $total_day = $days;
        // $days  = $days/30;
        $all_beergS_sum = array();
        $all_beergS_red = array();
        $all_beergS_gray = array();
        $all_beergK_sum = array();
        $all_beergK_red = array();
        $all_beergK_gray = array();
        $all_beerg_sum = array();
        $all_analysis_red = array();
        $all_analysis_orange = array();
        $all_analysis_green = array();
        $last_date = null;

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        $ranValues = randomValue($user->id);
        $anaids = array();

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        $subDetails = UserSubMenu::with('linkSubMenus')->where('id', $ownsubid)->first();
        $analyses = array();

        if ($subDetails->status == 1) {
            $normalSubMenus = array();
            $ownSubMenus = array();
            $ownSubMenus[] = $subDetails->id;

            foreach ($subDetails->linkSubMenus as $linksub) {
                if ($linksub->type == 1) $normalSubMenus[] = $linksub->link_submenu_id;
                else $ownSubMenus[] = $linksub->link_submenu_id;
            }

            if (!empty($normalSubMenus)) {
                $productColor = getProductSettingBySubmenu($normalSubMenus);
                $randomCauses = randomCauses($normalSubMenus);
            }
            if (!empty($ownSubMenus)) {
                $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
            }

            if (!empty($normalSubMenus)) {
                $pools = poolsIdsOwn($normalSubMenus);
                $poolid = $pools;

                $analyses[] = DB::table('analyse_pools')
                    ->join('pool_submenus', 'pool_submenus.pool_id', '=', 'analyse_pools.pool_id')
                    ->join('analyses', 'analyses.id', '=', 'analyse_pools.analyse_id')
                    ->whereIn('pool_submenus.submenu_id', $normalSubMenus)
                    ->whereIn('analyse_pools.pool_id', $poolid)->get();
            }

            if (!empty($ownSubMenus)) {
                $analyses[] = DB::table('analyse_user_submenus')
                    ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                    ->whereIn('analyse_user_submenus.user_submenu_id', $ownSubMenus)->get();
            }

        } else {
            $analyses[] = DB::table('analyse_user_submenus')
                ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                ->where('analyse_user_submenus.user_submenu_id', $ownsubid)->get();

            $ownSubMenus[] = $ownsubid;
            $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
        }


        //long term
        if (!empty($analyses)) {

            $all_analysis_id = array();

            foreach ($analyses as $analyse) {
                foreach ($analyse as $analysis) {
                    $all_analysis_id[] = $analysis->id;
                    $all_analysis_red["{$analysis->id}_red"] = 0;
                    $all_analysis_orange["{$analysis->id}_orange"] = 0;
                    $all_analysis_green["{$analysis->id}_green"] = 0;

                    $all_beergS_sum[$analysis->id] = 0;
                    $all_beergK_sum[$analysis->id] = 0;
                    $all_beerg_sum[$analysis->id] = 0;

                    $all_beergS_red[$analysis->id] = 0;
                    $all_beergS_gray[$analysis->id] = 0;
                    $all_beergK_red[$analysis->id] = 0;
                    $all_beergK_gray[$analysis->id] = 0;
                }


                #body and mental images
                $body_images = getBodyImage($all_analysis_id);
                $mental_images = getMentalImage($all_analysis_id);

                //random anaval query
                $random_values = randomValue($user->id);

                //start date;
                $start_date = $user->datumcore;

                //long term analysis start
                for ($i = 0; $i < $days; $i++) {
                    // $month = date('m', strtotime($start_date. '+'. $i .' month'));
                    // $year  = date('Y', strtotime($start_date. '+'. $i .' month'));
                    // $curMonthDays = cal_days_in_month(CAL_GREGORIAN, $month, $year);

                    //for($j = 0; $j < $curMonthDays; $j++){

                    $curdate = date('Y-m-d', strtotime($start_date . '+' . $i . ' days'));
                    $user->datumcore = $curdate;
                    $last_date = $curdate;

                    foreach ($analyse as $print) {

                        $anaid = $print->id;
                        $strP = $print->name;
                        $bioryth = $print->bioryth;
                        $bodydesc = $print->body_desc;
                        $mentaldesc = $print->mental_desc;

                        $anaids[] = $anaid;
                        $ranValStatus = 0;
                        if ($print->bioryth == 0) $bioryth = 28;
                        elseif ($print->bioryth < 0) $bioryth = 28;
                        else  $bioryth = $print->bioryth;

                        //Analysis/Male/Heart/Oneday Calculation
                        $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beerg = calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                        $ma = round($beerg, 0);
                        $beergK = round($beergK, 0);
                        $beergS = round($beergS, 0);
                        $beergod = round($beergod, 00);

                        if (!empty($random_values) && $user->useroption->pattern_switch == 1) {

                            if (array_key_exists($print->id, $random_values)) {
                                $ma = $random_values[$print->id];
                                $ranValStatus = 1;
                            }
                        }

                        if ($subDetails->status == 1) {
                            if (isset($print->user_submenu_id) && $print->user_submenu_id && $productColor[$print->user_submenu_id])
                                $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                            else if (isset($print->submenu_id) && $print->submenu_id && $productColor[$print->submenu_id])
                                $colorValue = $productColor[$print->submenu_id] ?? null;
                        } else {
                            if (isset($print->user_submenu_id))
                                $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                        }

                        $color = colorCalculationHelper($colorValue, $ma, $biorythDetail);
                        $arrayName = "all_analysis_{$color}";
                        $key = "{$anaid}_{$color}";
                        $$arrayName[$key] = ($$arrayName[$key] ?? 0) + 1;

                        $all_beergS_sum[$anaid] += $beergS;
                        $all_beergK_sum[$anaid] += $beergK;
                        $all_beerg_sum[$anaid] += $beergod;

                        if ($beergS >= 50) $all_beergS_red[$anaid]++;
                        else if ($beergS <= 49) $all_beergS_gray[$anaid]++;
                        if ($beergK <= 30) $all_beergK_red[$anaid]++;
                        else if ($beergK >= 31) $all_beergK_gray[$anaid]++;
                    }
                    //}
                }
            }
        }

        //long term


        if (!empty($analyses)) {
            foreach ($analyses as $analyse) {
                foreach ($analyse as $print) {

                    $anaid = $print->id;
                    $strP = $print->name;
                    $bioryth = $print->bioryth;
                    $bodydesc = $print->body_desc;
                    $mentaldesc = $print->mental_desc;
                    $anaids[] = $anaid;
                    $ranValStatus = 0;

                    $causeGroupid = null;
                    $mediumGroupid = null;
                    $tippGroupid = null;

                    if ($print->bioryth == 0) $bioryth = 28;
                    elseif ($print->bioryth < 0) $bioryth = 28;
                    else  $bioryth = $print->bioryth;

                    //$ma      = round($all_analysis_sum[$anaid]/$total_day);
                    $beergK = round($all_beergK_sum[$anaid] / $total_day);
                    $beergS = round($all_beergS_sum[$anaid] / $total_day);
                    $beergod = round($all_beerg_sum[$anaid] / $total_day);

                    if (!empty($ranValues)) {
                        if (array_key_exists($print->id, $ranValues)) {
                            $ma = $ranValues[$print->id];
                            $ranValStatus = 1;
                        }
                    }

                    #Male/Heart Color
                    if ($beergS >= 50) $heart = "color:red";
                    else if ($beergS <= 49) $heart = "color:gray";
                    if ($beergK <= 30) $male = "color:red";
                    else if ($beergK >= 31) $male = "color:gray";

                    if ($subDetails->status == 1) {
                        if (isset($print->submenu_id)) {
                            $colorValue = $productColor[$print->submenu_id] ?? null;

                            if (count($randomCauses['causes']) > 0) {
                                $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                            }

                            if (count($randomCauses['medium']) > 0) {
                                $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                            }

                            if (count($randomCauses['tipp']) > 0) {
                                $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                            }
                        }
                    }


                    #Random Price For Cart
                    $randPrice = rand(10, 100);

                    $allRed = ($all_analysis_red["{$anaid}_red"] == null) ? 0 : $all_analysis_red["{$anaid}_red"];
                    $allOrange = ($all_analysis_orange["{$anaid}_orange"] == null) ? 0 : $all_analysis_orange["{$anaid}_orange"];
                    $allGreen = $all_analysis_green["{$anaid}_green"];
                    $allRedPercentage = 0;
                    $allGreenPercentage = 0;
                    $pointer = 0;

                    if (($allRed + $allOrange) > 0) $allRedPercentage = round((($allRed + $allOrange) * 100) / $total_day, 0);
                    if ($allGreen > 0) $allGreenPercentage = round(($allGreen * 100) / $total_day, 0);

                    if ($allRedPercentage > 0) {
                        $pointer = floor((60 * ($allRedPercentage / 100))) + 21;
                    } else {
                        $pointer = floor((60 * ($allGreenPercentage / 100))) + 21;
                    }
                    // $calculationRed = round((100/$days)*($allRed +$allOrange));
                    #All Analysis
                    $allAnalyses[] = array(
                        "anaVal_red" => ($allRed + $allOrange),
                        "anaVal_green" => ($all_analysis_green["{$anaid}_green"] == null) ? 0 : $all_analysis_green["{$anaid}_green"],
                        "redBar" => $allRedPercentage,
                        "greenBar" => $allGreenPercentage,
                        "anaVal" => $days,
                        "anaid" => $anaid,
                        "anaName" => $strP,
                        "url_name" => $print->url_name,
                        "url_link" => $print->url_link,
                        "desc" => $print->description,
                        "desc_img" => $print->desc_image,
                        "bodyDesc" => $bodydesc,
                        "mentalDesc" => $mentaldesc,
                        "bioyrth" => $print->bioryth,
                        "anaColor" => "#E84E1B",
                        "maleVal" => $beergK,
                        "heartVal" => $beergS,
                        "maleColor" => $male,
                        "heartColor" => $heart,
                        "maleRed" => $all_beergK_red[$anaid],
                        "maleGray" => $all_beergK_gray[$anaid],
                        "heartRed" => $all_beergS_red[$anaid],
                        "heartGray" => $all_beergS_gray[$anaid],
                        "beergod" => $beergod,
                        "causes" => ($causeGroupid == null) ? "" : $causeGroupid,
                        "medium" => ($mediumGroupid == null) ? "" : $mediumGroupid,
                        "tipp" => ($tippGroupid == null) ? "" : $tippGroupid,
                        "randPrice" => $randPrice,
                        "ranValStatus" => $ranValStatus,
                        "pointer" => $pointer,
                    );

                }
            }
        } else {
            $analyses = "No Analyses Available";
        }

        $allAnalyses = $this->array_sort($allAnalyses, "anaVal_red", $order = 'SORT_DESC');

        $data['analyses'] = $allAnalyses;
        $data['bodyimages'] = $body_images;
        $data['mentalimages'] = $mental_images;
        $data['start_date'] = $start_date;
        $data['end_date'] = $last_date;

        return $data;
    }

    public function allRedValues(Request $request)
    {
        $status = true;
        $msg = null;
        $sortBy = "";
        $collection = array();
        $filterId = userFilterid(getUserId());
        $proid = $request->proid;
        $subid = $request->subid;
        $availableCartCount = getCartCount();

        $user = $filterId;
        $thema_speichern = str_replace(' ', '', $user->thema_speichern ?? '');
        $calculation_with = ($user->calculation_with) ?? '';
        $datumcore = $user->datumcore ?? '';
        $dynamic_cache_key = 'analysis_' . $user->id . '_' . $proid . '_' . $subid . '_' . $calculation_with . '_' . $datumcore . '_' . $thema_speichern;
        if (Cache::has($dynamic_cache_key)) {
            $red = 0;
            $analysisCache = Cache::get($dynamic_cache_key);
            if (is_array($analysisCache['analyses'])) {
                foreach (Cache::get($dynamic_cache_key)['analyses'] as $ana) {
                    if ($availableCartCount >= 200) return response()->json([
                        'success' => false,
                        '_alert_type' => 'warning',
                        '_alert' => trans('action.warning'),
                        'message' => trans('action.cart_max_allow_alert')
                    ]);
                    if ($ana['anaColor'] == '#E84E1B' && !checkCartAnalysis($subid, $ana['anaid'])) {
                        $red++;
                        #All Analysis
                        $data['userID'] = $user->id;
                        $data['analysisID'] = $ana['anaid'];
                        $data['analysisName'] = $ana['anaName'];
                        $data['submenu_id'] = 'sub-' . $subid;
                        $data['productID'] = $proid;
                        $data['calculation'] = $ana['anaVal'];
                        $data['male'] = $ana['maleVal'];
                        $data['heart'] = $ana['heartVal'];
                        $data['price'] = $ana['randPrice'];
                        $data['causes_id'] = $ana['causes']->id ?? 0;
                        $data['medium_id'] = $ana['medium']->id ?? 0;
                        $data['tipp_id'] = $ana['tipp']->id ?? 0;
                        $data['color'] = $ana['anaColor'];
                        $data['type'] = "Analysis";
                        $data['type_id'] = 1;
                        $data['minute'] = gmdate('i:s', $ana['randPrice']);
                        $availableCartCount++;
                        $insertdata = Cart::add(['id' => $user->id, 'name' => 'Analysis', 'qty' => $ana['anaid'], 'price' => $ana['randPrice'], 'weight' => 1, 'options' => $data]);
                        $data['cart_id'] = $insertdata->rowId;
                        $collection[] = $data;
                    } else {
                        $msg = "No analysis found.";
                    }
                }
            }

            // if (count($collection) == 0 || !checkCartExist('submenu_id', $subid, $red)) {
            if (count($collection) == 0) {
                $status = false;
                $msg = "No red values found.";
            }

            return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
        }

        $productColors = productColors($proid);
        $priceValue = priceValue($proid, $subid, 0);

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        $poolid = poolsIds($subid) ?? [];

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        if ($filterId->userfilter->filter_type == 1) $sortBy = "asc";
        if ($filterId->userfilter->filter_type == 2) $sortBy = "desc";
        if ($subid == 20) $sortBy = "desc";

        // $anaids = array();

        #$analyses = Pool::with("analyses")->whereIn('id', $poolid->pools)->get();
        // if(app()->getLocale() == 'en'){
        //     $analyses = EnAnalyses::select('en_analyses.*', 'analyses.name as de_name')
        //     ->join('analyses', 'en_analyses.id', 'analyses.id')
        //     ->whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query, $sortBy) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // }else{
        //     $analyses = Analyse::whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query, $sortBy) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // }
        $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
            $query->whereIn('pool_id', $poolid);
        })->when($sortBy, function ($query, $sortBy) {
            return $query->orderBy('name', $sortBy);
        });
        if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
        $analyses = $anaQuery->get();

        if (count($analyses) > 0 && !empty($poolid)) {

            $all_analysis_id = array();

            foreach ($analyses as $analysis) {
                $all_analysis_id[] = $analysis->id;
            }

            #random causes
            $todayCause = savedCauses($subid, $all_analysis_id, $user->id);
            $randomCauses = randomCauses($subid);

            #random anaval query
            $random_values = randomValue($user->id);
            $redCount = 0;

            foreach ($analyses as $print) {
                if ($availableCartCount >= 200) return response()->json([
                    'success' => false,
                    '_alert_type' => 'warning',
                    '_alert' => trans('action.warning'),
                    'message' => trans('action.cart_max_allow_alert')
                ]);
                $anaid = $print->id;
                $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                $bioryth = $print->bioryth;

                $anaids[] = $anaid;
                // $ranValStatus = 0;
                if ($print->bioryth == 0) $bioryth = 28;
                elseif ($print->bioryth < 0) $bioryth = 28;
                else  $bioryth = $print->bioryth;

                #Analysis/Male/Heart/Oneday Calculation
                $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                $beerg = (isset($user->userODption->ran_ana) && $user->userODption->ran_ana == 1) ? rand(0, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beerg = calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                $ma = round($beerg, 0);
                $beergK = round($beergK, 0);
                $beergS = round($beergS, 0);
                $beergod = round($beergod, 00);

                if (!empty($random_values) && $user->useroption->pattern_switch == 1) {
                    if (array_key_exists($print->id, $random_values)) {
                        $ma = $random_values[$print->id];
                        $ranValStatus = 1;
                    }
                }

                #Analysis Value Color
                if (isset($productColors) && $ma >= $productColors->red_min && $ma <= $productColors->red_max) {
                    $class_skill = "#E84E1B";
                    $redCount++;
                }

                #Cause/Medium/Tipp
                if (!empty($todayCause['causes'])) {
                    if (array_key_exists($print->id, $todayCause['causes']))
                        $causeGroupid = $todayCause['causes'][$print->id];
                    else if (count($randomCauses['causes']) > 0)
                        $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                } else if (count($randomCauses['causes']) > 0) {
                    $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                }

                if (!empty($todayCause['medium'])) {
                    if (array_key_exists($print->id, $todayCause['medium']))
                        $mediumGroupid = $todayCause['medium'][$print->id];
                    else if (count($randomCauses['medium']) > 0)
                        $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                } else if (count($randomCauses['medium']) > 0) {
                    $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                }

                if (!empty($todayCause['tipp'])) {
                    if (array_key_exists($print->id, $todayCause['tipp']))
                        $tippGroupid = $todayCause['tipp'][$print->id];
                    else if (count($randomCauses['tipp']) > 0)
                        $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                } else if (count($randomCauses['tipp']) > 0) {
                    $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                }

                #Random Price For Cart
                if (empty($priceValue)) {
                    $priceValue = priceValue($proid, $subid, $anaid);
                }

                $randPrice = rand($priceValue['min_price'], $priceValue['max_price']);
                if (!checkCartAnalysis($subid, $anaid)) {
                    #All Analysis
                    if (isset($productColors) && $ma >= $productColors->red_min && $ma <= $productColors->red_max) {
                        $data['userID'] = getUserId();
                        $data['analysisID'] = $anaid;
                        $data['analysisName'] = $print->name;
                        $data['submenu_id'] = 'sub-' . $subid;
                        $data['productID'] = $proid;
                        $data['calculation'] = $ma;
                        $data['male'] = $beergK;
                        $data['heart'] = $beergS;
                        $data['price'] = $randPrice;
                        $data['causes_id'] = $causeGroupid->id ?? null;
                        $data['medium_id'] = $mediumGroupid->id ?? null;
                        $data['tipp_id'] = $tippGroupid->id ?? null;
                        $data['color'] = $class_skill;
                        $data['type'] = "Analysis";
                        $data['type_id'] = 1;
                        $data['minute'] = gmdate('i:s', $randPrice);
                        $availableCartCount++;
                        $insertdata = Cart::add(['id' => getUserId(), 'name' => (request()->type == '') ? 'Cart' : 'Analysis', 'qty' => (request()->type == 'Analysis') ? request()->ana_id : 1, 'price' => $randPrice, 'weight' => 1, 'options' => $data]);
                        $data['cart_id'] = $insertdata->rowId;
                        $collection[] = $data;
                    }
                }
            }
        } else {
            $analyses = [];
            $msg = "No analysis found.";
        }

        if (count($collection) == 0 || !checkCartExist('submenu_id', $subid, $redCount, AnalysisColorEnum::RED_COLOR)) {
            $status = false;
            $msg = "No red values found.";
        }

        return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
    }

    public function allOwnRedValues(Request $request)
    {
        $sortBy = "";
        $status = true;
        $msg = null;
        $collection = array();
        $redCount = 0;
        $availableCartCount = getCartCount();

        $ownsubid = $request->subid;
        $ownproid = $request->proid;
        $user = userFilterid(getUserId());

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        if ($user->userfilter->filter_type == 1) $sortBy = "asc";
        if ($user->userfilter->filter_type == 2) $sortBy = "desc";

        $ranValues = randomValue($user->id);
        $anaids = array();

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        $subDetails = UserSubMenu::with('linkSubMenus')->where('id', $ownsubid)->first();
        $analyses = array();
        $ownPrice = null;
        $globalValues = GlobalSetting::find(1);

        if ($subDetails->status == 1) {
            $normalSubMenus = array();
            $ownSubMenus = array();
            $ownSubMenus[] = $ownsubid;

            foreach ($subDetails->linkSubMenus as $linksub) {
                if ($linksub->type == 1) $normalSubMenus[] = $linksub->link_submenu_id;
                else $ownSubMenus[] = $linksub->link_submenu_id;
            }

            if (!empty($normalSubMenus)) {
                $productColor = getProductSettingBySubmenu($normalSubMenus);
                $randomCauses = randomCauses($normalSubMenus);
            }
            if (!empty($ownSubMenus)) {
                $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
            }

            if (!empty($normalSubMenus)) {

                $pools = poolsIdsOwn($normalSubMenus);
                $poolid = $pools;

                $analyses[] = DB::table('analyse_pools')
                    ->join('pool_submenus', 'pool_submenus.pool_id', '=', 'analyse_pools.pool_id')
                    ->join('analyses', 'analyses.id', '=', 'analyse_pools.analyse_id')
                    ->whereIn('pool_submenus.submenu_id', $normalSubMenus)
                    ->whereIn('analyse_pools.pool_id', $poolid)->get();
            }

            if (!empty($ownSubMenus)) {
                $analyses[] = DB::table('analyse_user_submenus')
                    ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                    ->whereIn('analyse_user_submenus.user_submenu_id', $ownSubMenus)->get();
            }

        } else {
            $analyses[] = DB::table('analyse_user_submenus')
                ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                ->where('analyse_user_submenus.user_submenu_id', $ownsubid)->get();

            $ownSubMenus[] = $ownsubid;
            $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
        }

        if (!empty($analyses) > 0) {
            foreach ($analyses as $analyse) {

                foreach ($analyse as $print) {
                    $anaid = $print->id;
                    $strP = $print->name;
                    $bioryth = $print->bioryth;
                    $bodydesc = $print->body_desc;
                    $mentaldesc = $print->mental_desc;
                    $anaids[] = $anaid;
                    $ranValStatus = 0;
                    $causeGroupid = null;
                    $mediumGroupid = null;
                    $tippGroupid = null;

                    if ($print->bioryth == 0) $bioryth = 28;
                    elseif ($print->bioryth < 0) $bioryth = 28;
                    else  $bioryth = $print->bioryth;

                    #Analysis/Male/Heart/Oneday Calculation
                    $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergod = calculationoneday($strM, $strP, $user, $bioryth);

                    $ma = round($beerg, 0);
                    $beergK = round($beergK, 0);
                    $beergS = round($beergS, 0);
                    $beergod = round($beergod, 00);

                    if (!empty($ranValues)) {
                        if (array_key_exists($print->id, $ranValues)) {
                            $ma = $ranValues[$print->id];
                            $ranValStatus = 1;
                        }
                    }

                    #Male/Heart Color
                    if ($beergS >= 50) $heart = "color:red";
                    else if ($beergS <= 49) $heart = "color:gray";
                    if ($beergK <= 30) $male = "color:red";
                    else if ($beergK >= 31) $male = "color:gray";

                    if ($subDetails->status == 1) {
                        if ($print->user_submenu_id && $productColor[$print->user_submenu_id])
                            $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                        else if ($print->submenu_id && $productColor[$print->submenu_id]) {
                            $colorValue = $productColor[$print->submenu_id] ?? null;

                            if (count($randomCauses['causes']) > 0) {
                                $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                            }

                            if (count($randomCauses['medium']) > 0) {
                                $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                            }

                            if (count($randomCauses['tipp']) > 0) {
                                $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                            }
                        }
                    } else {
                        $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                        $ownPrice = $ownColor[$print->user_submenu_id] ?? null;
                    }

                    $redFrom = null;
                    $redTo = null;

                    #Analysis Value Color
                    if ($colorValue->green_min == null && $colorValue->green_max == null) {
                        $colorValue = GlobalSetting::find(1);
                        if (isset($colorValue) && $ma >= $colorValue->gs_red_min && $ma <= $colorValue->gs_red_max) {
                            $class_skill = "#E84E1B";
                            $redCount++;
                            $redFrom = $colorValue->gs_red_min;
                            $redTo = $colorValue->gs_red_max;
                        }

                    } else {
                        if (isset($colorValue) && $ma >= $colorValue->red_min && $ma <= $colorValue->red_max) {
                            $class_skill = "#E84E1B";
                            $redCount++;
                            $redFrom = $colorValue->red_min;
                            $redTo = $colorValue->red_max;
                        }
                    }

                    #Get Price for cart
                    if ($ownPrice != null && ($print->ana_min_price == '' && $print->ana_max_price == '')) {
                        if ($ownPrice->gs_min_price != null && $ownPrice->gs_max_price != null)
                            $randPrice = rand($ownPrice->gs_min_price, $ownPrice->gs_max_price);
                        else
                            $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);

                    } elseif ($print->ana_min_price != '' && $print->ana_max_price != '') {
                        $randPrice = rand($print->ana_min_price, $print->ana_max_price);
                    } else {
                        #Random Price For Cart
                        $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);
                    }
                    if ($availableCartCount >= 200) return response()->json([
                        'success' => false,
                        '_alert_type' => 'warning',
                        '_alert' => trans('action.warning'),
                        'message' => trans('action.cart_max_allow_alert')
                    ]);
                    #All Analysis
                    if (!checkCartAnalysis($ownsubid, $anaid)) {
                        if ($ma >= $redFrom && $ma <= $redTo) {
                            $data['userID'] = getUserId();
                            $data["analysisID"] = $anaid;
                            $data["analysisName"] = $strP;
                            $data['submenu_id'] = 'own-' . $ownsubid;
                            $data['productID'] = $ownproid;
                            $data["calculation"] = $ma;
                            $data['male'] = $beergK;
                            $data['heart'] = $beergS;
                            $data['price'] = $randPrice;
                            $data['causes_id'] = ($causeGroupid == null) ? "" : $causeGroupid;
                            $data['medium_id'] = ($mediumGroupid == null) ? "" : $mediumGroupid;
                            $data['tipp_id'] = ($tippGroupid == null) ? "" : $tippGroupid;
                            $data['color'] = $class_skill;
                            $data["desc"] = $print->description;
                            $data["desc_img"] = $print->desc_image;
                            $data['type'] = "Analysis";
                            $data['type_id'] = 1;
                            $data['minute'] = gmdate('i:s', $randPrice);
                            $availableCartCount++;
                            $insertdata = Cart::add(['id' => getUserId(), 'name' => (request()->type == '') ? 'Cart' : 'Analysis', 'qty' => (request()->type == 'Analysis') ? request()->ana_id : 1, 'price' => $randPrice, 'weight' => 1, 'options' => $data]);
                            $data['cart_id'] = $insertdata->rowId;
                            $collection[] = $data;
                        }
                    }
                }
            }
        } else {
            $analyses = "No Analyses Available";
        }

        if (count($collection) == 0 || !checkCartExist('submenu_id', $ownsubid, $redCount, AnalysisColorEnum::RED_COLOR)) {
            $status = false;
            $msg = "No red values found.";
        }

        return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
    }


    public function allOrangeValues(Request $request)
    {
        $status = true;
        $msg = null;
        $sortBy = "";
        $collection = array();
        $filterId = userFilterid(getUserId());
        $proid = $request->proid;
        $subid = $request->subid;
        $availableCartCount = getCartCount();
        $user = $filterId;
        $thema_speichern = str_replace(' ', '', $user->thema_speichern ?? '');
        $calculation_with = ($user->calculation_with) ?? '';
        $datumcore = $user->datumcore ?? '';
        $dynamic_cache_key = 'analysis_' . $user->id . '_' . $proid . '_' . $subid . '_' . $calculation_with . '_' . $datumcore . '_' . $thema_speichern;
        if (Cache::has($dynamic_cache_key)) {
            $orange = 0;
            $analysisCache = Cache::get($dynamic_cache_key);
            if (is_array($analysisCache['analyses'])) {
                foreach (Cache::get($dynamic_cache_key)['analyses'] as $ana) {
                    if ($availableCartCount >= 200) return response()->json([
                        'success' => false,
                        '_alert_type' => 'warning',
                        '_alert' => trans('action.warning'),
                        'message' => trans('action.cart_max_allow_alert')
                    ]);
                    if ($ana['anaColor'] == '#F8B133' && !checkCartAnalysis($subid, $ana['anaid'])) {
                        $orange++;
                        #All Analysis
                        $data['userID'] = $user->id;
                        $data['analysisID'] = $ana['anaid'];
                        $data['analysisName'] = $ana['anaName'];
                        $data['submenu_id'] = 'sub-' . $subid;
                        $data['productID'] = $proid;
                        $data['calculation'] = $ana['anaVal'];
                        $data['male'] = $ana['maleVal'];
                        $data['heart'] = $ana['heartVal'];
                        $data['price'] = $ana['randPrice'];
                        $data['causes_id'] = $ana['causes']->id ?? 0;
                        $data['medium_id'] = $ana['medium']->id ?? 0;
                        $data['tipp_id'] = $ana['tipp']->id ?? 0;
                        $data['color'] = $ana['anaColor'];
                        $data['type'] = "Analysis";
                        $data['type_id'] = 1;
                        $data['minute'] = gmdate('i:s', $ana['randPrice']);
                        $availableCartCount++;
                        $insertdata = Cart::add(['id' => $user->id, 'name' => 'Analysis', 'qty' => $ana['anaid'], 'price' => $ana['randPrice'], 'weight' => 1, 'options' => $data]);
                        $data['cart_id'] = $insertdata->rowId;
                        $collection[] = $data;
                    } else {
                        $msg = "No analysis found.";
                    }
                }
            }

            // if (count($collection) == 0 || !checkCartExist('submenu_id', $subid, $red)) {
            if (count($collection) == 0) {
                $status = false;
                $msg = "No orange values found.";
            }

            return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
        }

        $productColors = productColors($proid);
        $priceValue = priceValue($proid, $subid, 0);

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        $poolid = poolsIds($subid) ?? [];

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        if ($filterId->userfilter->filter_type == 1) $sortBy = "asc";
        if ($filterId->userfilter->filter_type == 2) $sortBy = "desc";
        if ($subid == 20) $sortBy = "desc";

        // $poolid = $pools ?? [];
        // $anaids = array();

        #$analyses = Pool::with("analyses")->whereIn('id', $poolid->pools)->get();
        // if(app()->getLocale() == 'en'){
        //     $analyses = EnAnalyses::select('en_analyses.*', 'analyses.name as de_name')
        //     ->join('analyses', 'en_analyses.id', 'analyses.id')
        //     ->whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query, $sortBy) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // }else{
        //     $analyses = Analyse::whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query, $sortBy) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // }
        $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
            $query->whereIn('pool_id', $poolid);
        })->when($sortBy, function ($query, $sortBy) {
            return $query->orderBy('name', $sortBy);
        });
        if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
        $analyses = $anaQuery->get();

        if (count($analyses) > 0 && !empty($poolid)) {

            $all_analysis_id = array();

            foreach ($analyses as $analysis) {
                $all_analysis_id[] = $analysis->id;
            }

            #random causes
            $todayCause = savedCauses($subid, $all_analysis_id, $user->id);
            $randomCauses = randomCauses($subid);

            #random anaval query
            $random_values = randomValue($user->id);
            $orangeCount = 0;

            foreach ($analyses as $print) {
                if ($availableCartCount >= 200) return response()->json([
                    'success' => false,
                    '_alert_type' => 'warning',
                    '_alert' => trans('action.warning'),
                    'message' => trans('action.cart_max_allow_alert')
                ]);
                $anaid = $print->id;
                $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                $bioryth = $print->bioryth;

                $anaids[] = $anaid;
                // $ranValStatus = 0;
                if ($print->bioryth == 0) $bioryth = 28;
                elseif ($print->bioryth < 0) $bioryth = 28;
                else  $bioryth = $print->bioryth;

                #Analysis/Male/Heart/Oneday Calculation
                $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                $beerg = isset($user->userODption->ran_ana) && ($user->userODption->ran_ana == 1) ? rand(0, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beerg = calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                $ma = round($beerg, 0);
                $beergK = round($beergK, 0);
                $beergS = round($beergS, 0);
                $beergod = round($beergod, 00);

                if (!empty($random_values) && $user->useroption->pattern_switch == 1) {
                    if (array_key_exists($print->id, $random_values)) {
                        $ma = $random_values[$print->id];
                        $ranValStatus = 1;
                    }
                }

                #Analysis Value Color
                if (isset($productColors) && $ma >= $productColors->orange_min && $ma <= $productColors->orange_max) {
                    $class_skill = "#F8B133";
                    $orangeCount++;
                }

                #Cause/Medium/Tipp
                if (!empty($todayCause['causes'])) {
                    if (array_key_exists($print->id, $todayCause['causes']))
                        $causeGroupid = $todayCause['causes'][$print->id];
                    else if (count($randomCauses['causes']) > 0)
                        $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                } else if (count($randomCauses['causes']) > 0) {
                    $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                }

                if (!empty($todayCause['medium'])) {
                    if (array_key_exists($print->id, $todayCause['medium']))
                        $mediumGroupid = $todayCause['medium'][$print->id];
                    else if (count($randomCauses['medium']) > 0)
                        $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                } else if (count($randomCauses['medium']) > 0) {
                    $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                }

                if (!empty($todayCause['tipp'])) {
                    if (array_key_exists($print->id, $todayCause['tipp']))
                        $tippGroupid = $todayCause['tipp'][$print->id];
                    else if (count($randomCauses['tipp']) > 0)
                        $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                } else if (count($randomCauses['tipp']) > 0) {
                    $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                }

                #Random Price For Cart
                if (empty($priceValue)) {
                    $priceValue = priceValue($proid, $subid, $anaid);
                }

                $randPrice = rand($priceValue['min_price'], $priceValue['max_price']);
                if (!checkCartAnalysis($subid, $anaid)) {
                    #All Analysis
                    if (isset($productColors) && $ma >= $productColors->orange_min && $ma <= $productColors->orange_max) {
                        $data['userID'] = getUserId();
                        $data['analysisID'] = $anaid;
                        $data['analysisName'] = $print->name;
                        $data['submenu_id'] = 'sub-' . $subid;
                        $data['productID'] = $proid;
                        $data['calculation'] = $ma;
                        $data['male'] = $beergK;
                        $data['heart'] = $beergS;
                        $data['price'] = $randPrice;
                        $data['causes_id'] = $causeGroupid->id ?? null;
                        $data['medium_id'] = $mediumGroupid->id ?? null;
                        $data['tipp_id'] = $tippGroupid->id ?? null;
                        $data['color'] = $class_skill;
                        $data['type'] = "Analysis";
                        $data['type_id'] = 1;
                        $data['minute'] = gmdate('i:s', $randPrice);
                        $availableCartCount++;
                        $insertdata = Cart::add(['id' => getUserId(), 'name' => (request()->type == '') ? 'Cart' : 'Analysis', 'qty' => (request()->type == 'Analysis') ? request()->ana_id : 1, 'price' => $randPrice, 'weight' => 1, 'options' => $data]);
                        $data['cart_id'] = $insertdata->rowId;
                        $collection[] = $data;
                    }
                }
            }
        } else {
            $analyses = [];
            $msg = "No analysis found.";
        }

        if (count($collection) == 0 || !checkCartExist('submenu_id', $subid, $orangeCount, AnalysisColorEnum::ORANGE_COLOR)) {
            $status = false;
            $msg = "No orange values found.";
        }

        return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
    }

    public function allOwnOrangeValues(Request $request)
    {
        $sortBy = "";
        $status = true;
        $msg = null;
        $collection = array();
        $orangeCount = 0;
        $availableCartCount = getCartCount();
        $ownsubid = $request->subid;
        $ownproid = $request->proid;
        $user = userFilterid(getUserId());

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        if ($user->userfilter->filter_type == 1) $sortBy = "asc";
        if ($user->userfilter->filter_type == 2) $sortBy = "desc";

        $ranValues = randomValue($user->id);
        $anaids = array();

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        $subDetails = UserSubMenu::with('linkSubMenus')->where('id', $ownsubid)->first();
        $analyses = array();
        $ownPrice = null;
        $globalValues = GlobalSetting::find(1);

        if ($subDetails->status == 1) {
            $normalSubMenus = array();
            $ownSubMenus = array();
            $ownSubMenus[] = $ownsubid;

            foreach ($subDetails->linkSubMenus as $linksub) {
                if ($linksub->type == 1) $normalSubMenus[] = $linksub->link_submenu_id;
                else $ownSubMenus[] = $linksub->link_submenu_id;
            }

            if (!empty($normalSubMenus)) {
                $productColor = getProductSettingBySubmenu($normalSubMenus);
                $randomCauses = randomCauses($normalSubMenus);
            }
            if (!empty($ownSubMenus)) {
                $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
            }

            if (!empty($normalSubMenus)) {

                $pools = poolsIdsOwn($normalSubMenus);
                $poolid = $pools;

                $analyses[] = DB::table('analyse_pools')
                    ->join('pool_submenus', 'pool_submenus.pool_id', '=', 'analyse_pools.pool_id')
                    ->join('analyses', 'analyses.id', '=', 'analyse_pools.analyse_id')
                    ->whereIn('pool_submenus.submenu_id', $normalSubMenus)
                    ->whereIn('analyse_pools.pool_id', $poolid)->get();
            }

            if (!empty($ownSubMenus)) {
                $analyses[] = DB::table('analyse_user_submenus')
                    ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                    ->whereIn('analyse_user_submenus.user_submenu_id', $ownSubMenus)->get();
            }

        } else {
            $analyses[] = DB::table('analyse_user_submenus')
                ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                ->where('analyse_user_submenus.user_submenu_id', $ownsubid)->get();

            $ownSubMenus[] = $ownsubid;
            $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
        }

        if (!empty($analyses) > 0) {
            foreach ($analyses as $analyse) {

                foreach ($analyse as $print) {
                    $anaid = $print->id;
                    $strP = $print->name;
                    $bioryth = $print->bioryth;
                    $bodydesc = $print->body_desc;
                    $mentaldesc = $print->mental_desc;
                    $anaids[] = $anaid;
                    $ranValStatus = 0;
                    $causeGroupid = null;
                    $mediumGroupid = null;
                    $tippGroupid = null;

                    if ($print->bioryth == 0) $bioryth = 28;
                    elseif ($print->bioryth < 0) $bioryth = 28;
                    else  $bioryth = $print->bioryth;

                    #Analysis/Male/Heart/Oneday Calculation
                    $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergod = calculationoneday($strM, $strP, $user, $bioryth);

                    $ma = round($beerg, 0);
                    $beergK = round($beergK, 0);
                    $beergS = round($beergS, 0);
                    $beergod = round($beergod, 00);

                    if (!empty($ranValues)) {
                        if (array_key_exists($print->id, $ranValues)) {
                            $ma = $ranValues[$print->id];
                            $ranValStatus = 1;
                        }
                    }

                    #Male/Heart Color
                    if ($beergS >= 50) $heart = "color:red";
                    else if ($beergS <= 49) $heart = "color:gray";
                    if ($beergK <= 30) $male = "color:red";
                    else if ($beergK >= 31) $male = "color:gray";

                    if ($subDetails->status == 1) {
                        if ($print->user_submenu_id && $productColor[$print->user_submenu_id])
                            $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                        else if ($print->submenu_id && $productColor[$print->submenu_id]) {
                            $colorValue = $productColor[$print->submenu_id] ?? null;

                            if (count($randomCauses['causes']) > 0) {
                                $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                            }

                            if (count($randomCauses['medium']) > 0) {
                                $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                            }

                            if (count($randomCauses['tipp']) > 0) {
                                $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                            }
                        }
                    } else {
                        $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                        $ownPrice = $ownColor[$print->user_submenu_id] ?? null;
                    }

                    $orangeFrom = null;
                    $orangeTo = null;

                    #Analysis Value Color
                    if ($colorValue->green_min == null && $colorValue->green_max == null) {
                        $colorValue = GlobalSetting::find(1);
                        if (isset($colorValue) && $ma >= $colorValue->gs_orange_min && $ma <= $colorValue->gs_orange_max) {
                            $class_skill = "#F8B133";
                            $orangeCount++;
                            $orangeFrom = $colorValue->gs_orange_min;
                            $orangeTo = $colorValue->gs_orange_max;
                        }

                    } else {
                        if (isset($colorValue) && $ma >= $colorValue->orange_min && $ma <= $colorValue->orange_max) {
                            $class_skill = "#F8B133";
                            $orangeCount++;
                            $orangeFrom = $colorValue->orange_min;
                            $orangeTo = $colorValue->orange_max;
                        }
                    }

                    #Get Price for cart
                    if ($ownPrice != null && ($print->ana_min_price == '' && $print->ana_max_price == '')) {
                        if ($ownPrice->gs_min_price != null && $ownPrice->gs_max_price != null)
                            $randPrice = rand($ownPrice->gs_min_price, $ownPrice->gs_max_price);
                        else
                            $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);

                    } elseif ($print->ana_min_price != '' && $print->ana_max_price != '') {
                        $randPrice = rand($print->ana_min_price, $print->ana_max_price);
                    } else {
                        #Random Price For Cart
                        $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);
                    }
                    if ($availableCartCount >= 200) return response()->json([
                        'success' => false,
                        '_alert_type' => 'warning',
                        '_alert' => trans('action.warning'),
                        'message' => trans('action.cart_max_allow_alert')
                    ]);
                    #All Analysis
                    if (!checkCartAnalysis($ownsubid, $anaid)) {
                        if ($ma >= $orangeFrom && $ma <= $orangeTo) {
                            $data['userID'] = getUserId();
                            $data["analysisID"] = $anaid;
                            $data["analysisName"] = $strP;
                            $data['submenu_id'] = 'own-' . $ownsubid;
                            $data['productID'] = $ownproid;
                            $data["calculation"] = $ma;
                            $data['male'] = $beergK;
                            $data['heart'] = $beergS;
                            $data['price'] = $randPrice;
                            $data['causes_id'] = ($causeGroupid == null) ? "" : $causeGroupid;
                            $data['medium_id'] = ($mediumGroupid == null) ? "" : $mediumGroupid;
                            $data['tipp_id'] = ($tippGroupid == null) ? "" : $tippGroupid;
                            $data['color'] = $class_skill;
                            $data["desc"] = $print->description;
                            $data["desc_img"] = $print->desc_image;
                            $data['type'] = "Analysis";
                            $data['type_id'] = 1;
                            $data['minute'] = gmdate('i:s', $randPrice);
                            $availableCartCount++;
                            $insertdata = Cart::add(['id' => getUserId(), 'name' => (request()->type == '') ? 'Cart' : 'Analysis', 'qty' => (request()->type == 'Analysis') ? request()->ana_id : 1, 'price' => $randPrice, 'weight' => 1, 'options' => $data]);
                            $data['cart_id'] = $insertdata->rowId;
                            $collection[] = $data;
                        }
                    }
                }
            }
        } else {
            $analyses = "No Analyses Available";
        }

        if (count($collection) == 0 || !checkCartExist('submenu_id', $ownsubid, $orangeCount, AnalysisColorEnum::ORANGE_COLOR)) {
            $status = false;
            $msg = "No orange values found.";
        }

        return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
    }

    public function allGreenValues(Request $request)
    {
        $status = true;
        $msg = null;
        $collection = array();
        $filterId = userFilterid(getUserId());
        $proid = $request->proid;
        $subid = $request->subid;
        $availableCartCount = getCartCount();
        $user = $filterId;
        $thema_speichern = str_replace(' ', '', $user->thema_speichern ?? '');
        $calculation_with = ($user->calculation_with) ?? '';
        $datumcore = $user->datumcore ?? '';
        $dynamic_cache_key = 'analysis_' . $user->id . '_' . $proid . '_' . $subid . '_' . $calculation_with . '_' . $datumcore . '_' . $thema_speichern;
        if (Cache::has($dynamic_cache_key)) {
            $green = 0;
            $analysisCache = Cache::get($dynamic_cache_key);
            if (is_array($analysisCache['analyses'])) {
                foreach (Cache::get($dynamic_cache_key)['analyses'] as $ana) {
                    if ($availableCartCount >= 200) return response()->json([
                        'success' => false,
                        '_alert_type' => 'warning',
                        '_alert' => trans('action.warning'),
                        'message' => trans('action.cart_max_allow_alert')
                    ]);
                    if ($ana['anaColor'] == '#2FAB66' && !checkCartAnalysis($subid, $ana['anaid'])) {
                        $green++;
                        #All Analysis
                        $data['userID'] = $user->id;
                        $data['analysisID'] = $ana['anaid'];
                        $data['analysisName'] = $ana['anaName'];
                        $data['submenu_id'] = 'sub-' . $subid;
                        $data['productID'] = $proid;
                        $data['calculation'] = $ana['anaVal'];
                        $data['male'] = $ana['maleVal'];
                        $data['heart'] = $ana['heartVal'];
                        $data['price'] = $ana['randPrice'];
                        $data['causes_id'] = $ana['causes']->id ?? 0;
                        $data['medium_id'] = $ana['medium']->id ?? 0;
                        $data['tipp_id'] = $ana['tipp']->id ?? 0;
                        $data['color'] = $ana['anaColor'];
                        $data['type'] = "Analysis";
                        $data['type_id'] = 1;
                        $data['minute'] = gmdate('i:s', $ana['randPrice']);
                        $availableCartCount++;
                        $insertdata = Cart::add(['id' => $user->id, 'name' => 'Analysis', 'qty' => $ana['anaid'], 'price' => $ana['randPrice'], 'weight' => 1, 'options' => $data]);
                        $data['cart_id'] = $insertdata->rowId;
                        $collection[] = $data;
                    } else {
                        $msg = "No analysis found.";
                    }
                }
            }

            // if (count($collection) == 0 || !checkCartExist('submenu_id', $subid, $red)) {
            if (count($collection) == 0) {
                $status = false;
                $msg = "No green values found.";
            }

            return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
        }

        $productColors = productColors($proid);
        $priceValue = priceValue($proid, $subid, 0);

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        $poolid = poolsIds($subid) ?? [];

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        if ($filterId->userfilter->filter_type == 1) $sortBy = "asc";
        if ($filterId->userfilter->filter_type == 2) $sortBy = "desc";
        if ($subid == 20) $sortBy = "desc";

        // $anaids = array();

        #$analyses = Pool::with("analyses")->whereIn('id', $poolid->pools)->get();
        // if(app()->getLocale() == 'en'){
        //     $analyses = EnAnalyses::select('en_analyses.*', 'analyses.name as de_name')
        //     ->join('analyses', 'en_analyses.id', 'analyses.id')
        //     ->whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query, $sortBy) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // }else{
        //     $analyses = Analyse::whereHas('pools', function ($query) use ($poolid) {
        //         $query->whereIn('pool_id', $poolid);
        //     })->when($sortBy, function ($query, $sortBy) {
        //         return $query->orderBy('name', $sortBy);
        //     })->get();
        // }
        $anaQuery = Analyse::whereHas('pools', function ($query) use ($poolid) {
            $query->whereIn('pool_id', $poolid);
        })->when($sortBy, function ($query, $sortBy) {
            return $query->orderBy('name', $sortBy);
        });
        if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.*', 'analyses.name as de_name')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
        $analyses = $anaQuery->get();

        if (count($analyses) > 0 && !empty($poolid)) {

            $all_analysis_id = array();

            foreach ($analyses as $analysis) {
                $all_analysis_id[] = $analysis->id;
            }

            #random causes
            $todayCause = savedCauses($subid, $all_analysis_id, $user->id);
            $randomCauses = randomCauses($subid);

            #random anaval query
            $random_values = randomValue($user->id);
            $greenCount = 0;

            foreach ($analyses as $print) {
                if ($availableCartCount >= 200) return response()->json([
                    'success' => false,
                    '_alert_type' => 'warning',
                    '_alert' => trans('action.warning'),
                    'message' => trans('action.cart_max_allow_alert')
                ]);
                $anaid = $print->id;
                $strP = (app()->getLocale() == '' || app()->getLocale() == 'de') ? $print->name : $print->de_name;
                $bioryth = $print->bioryth;

                $anaids[] = $anaid;
                // $ranValStatus = 0;
                if ($print->bioryth == 0) $bioryth = 28;
                elseif ($print->bioryth < 0) $bioryth = 28;
                else  $bioryth = $print->bioryth;

                #Analysis/Male/Heart/Oneday Calculation
                $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                $beerg = ($user->userODption->ran_ana == 1) ? rand(0, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beerg = calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                $ma = round($beerg, 0);
                $beergK = round($beergK, 0);
                $beergS = round($beergS, 0);
                $beergod = round($beergod, 00);

                if (!empty($random_values) && $user->useroption->pattern_switch == 1) {
                    if (array_key_exists($print->id, $random_values)) {
                        $ma = $random_values[$print->id];
                        $ranValStatus = 1;
                    }
                }

                #Analysis Value Color
                if (isset($productColors) && $ma >= $productColors->green_min && $ma <= $productColors->green_max) {
                    $class_skill = "#2FAB66";
                    $greenCount++;
                }

                #Cause/Medium/Tipp
                if (!empty($todayCause['causes'])) {
                    if (array_key_exists($print->id, $todayCause['causes']))
                        $causeGroupid = $todayCause['causes'][$print->id];
                    else if (count($randomCauses['causes']) > 0)
                        $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                } else if (count($randomCauses['causes']) > 0) {
                    $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                }

                if (!empty($todayCause['medium'])) {
                    if (array_key_exists($print->id, $todayCause['medium']))
                        $mediumGroupid = $todayCause['medium'][$print->id];
                    else if (count($randomCauses['medium']) > 0)
                        $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                } else if (count($randomCauses['medium']) > 0) {
                    $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                }

                if (!empty($todayCause['tipp'])) {
                    if (array_key_exists($print->id, $todayCause['tipp']))
                        $tippGroupid = $todayCause['tipp'][$print->id];
                    else if (count($randomCauses['tipp']) > 0)
                        $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                } else if (count($randomCauses['tipp']) > 0) {
                    $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                }

                #Random Price For Cart
                if (empty($priceValue)) {
                    $priceValue = priceValue($proid, $subid, $anaid);
                }

                $randPrice = rand($priceValue['min_price'], $priceValue['max_price']);
                if (!checkCartAnalysis($subid, $anaid)) {
                    #All Analysis
                    if (isset($productColors) && $ma >= $productColors->green_min && $ma <= $productColors->green_max) {
                        $data['userID'] = getUserId();
                        $data['analysisID'] = $anaid;
                        $data['analysisName'] = $print->name;
                        $data['submenu_id'] = 'sub-' . $subid;
                        $data['productID'] = $proid;
                        $data['calculation'] = $ma;
                        $data['male'] = $beergK;
                        $data['heart'] = $beergS;
                        $data['price'] = $randPrice;
                        $data['causes_id'] = $causeGroupid->id;
                        $data['medium_id'] = $mediumGroupid->id;
                        $data['tipp_id'] = $tippGroupid->id;
                        $data['color'] = $class_skill;
                        $data['type'] = "Analysis";
                        $data['type_id'] = 1;
                        $data['minute'] = gmdate('i:s', $randPrice);
                        $availableCartCount++;
                        $insertdata = Cart::add(['id' => getUserId(), 'name' => (request()->type == '') ? 'Cart' : 'Analysis', 'qty' => (request()->type == 'Analysis') ? request()->ana_id : 1, 'price' => $randPrice, 'weight' => 1, 'options' => $data]);
                        $data['cart_id'] = $insertdata->rowId;
                        $collection[] = $data;
                    }
                }
            }
        } else {
            $analyses = [];
            $msg = "No analysis found.";
        }

        if (count($collection) == 0 || !checkCartExist('submenu_id', $subid, $greenCount, AnalysisColorEnum::GREEN_COLOR)) {
            $status = false;
            $msg = "No green values found.";
        }

        return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
    }

    public function allOwnGreenValues(Request $request)
    {
        $status = true;
        $msg = null;
        $collection = array();
        $greenCount = 0;
        $availableCartCount = getCartCount();
        $ownsubid = $request->subid;
        $ownproid = $request->proid;
        $user = userFilterid(getUserId());

        $biorythDetail = biorythVisibleDetails();
        $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

        $ranValues = randomValue($user->id);
        $anaids = array();

        $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

        $subDetails = UserSubMenu::with('linkSubMenus')->where('id', $ownsubid)->first();
        $analyses = array();
        $ownPrice = null;
        $globalValues = GlobalSetting::find(1);

        if ($subDetails->status == 1) {
            $normalSubMenus = array();
            $ownSubMenus = array();
            $ownSubMenus[] = $ownsubid;

            foreach ($subDetails->linkSubMenus as $linksub) {
                if ($linksub->type == 1) $normalSubMenus[] = $linksub->link_submenu_id;
                else $ownSubMenus[] = $linksub->link_submenu_id;
            }

            if (!empty($normalSubMenus)) {
                $productColor = getProductSettingBySubmenu($normalSubMenus);
                $randomCauses = randomCauses($normalSubMenus);
            }
            if (!empty($ownSubMenus)) {
                $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
            }

            if (!empty($normalSubMenus)) {

                $pools = poolsIdsOwn($normalSubMenus);
                $poolid = $pools;

                $analyses[] = DB::table('analyse_pools')
                    ->join('pool_submenus', 'pool_submenus.pool_id', '=', 'analyse_pools.pool_id')
                    ->join('analyses', 'analyses.id', '=', 'analyse_pools.analyse_id')
                    ->whereIn('pool_submenus.submenu_id', $normalSubMenus)
                    ->whereIn('analyse_pools.pool_id', $poolid)->get();
            }

            if (!empty($ownSubMenus)) {
                $analyses[] = DB::table('analyse_user_submenus')
                    ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                    ->whereIn('analyse_user_submenus.user_submenu_id', $ownSubMenus)->get();
            }

        } else {
            $analyses[] = DB::table('analyse_user_submenus')
                ->join('analyses', 'analyses.id', '=', 'analyse_user_submenus.analyse_id')
                ->where('analyse_user_submenus.user_submenu_id', $ownsubid)->get();

            $ownSubMenus[] = $ownsubid;
            $ownColor = getUserOwnMenuSettingBySubId($ownSubMenus);
        }

        if (!empty($analyses) > 0) {
            foreach ($analyses as $analyse) {

                foreach ($analyse as $print) {
                    $anaid = $print->id;
                    $strP = $print->name;
                    $bioryth = $print->bioryth;
                    $anaids[] = $anaid;
                    $causeGroupid = null;
                    $mediumGroupid = null;
                    $tippGroupid = null;

                    if ($print->bioryth == 0) $bioryth = 28;
                    elseif ($print->bioryth < 0) $bioryth = 28;
                    else  $bioryth = $print->bioryth;

                    #Analysis/Male/Heart/Oneday Calculation
                    $beergS = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergK = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beerg = ($user->userOption->ran_ana == 1) ? rand(10, 100) : calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                    $beergod = calculationoneday($strM, $strP, $user, $bioryth);

                    $ma = round($beerg, 0);
                    $beergK = round($beergK, 0);
                    $beergS = round($beergS, 0);
                    $beergod = round($beergod, 00);

                    if (!empty($ranValues) && array_key_exists($print->id, $ranValues)) {
                        $ma = $ranValues[$print->id];
                    }


                    if ($subDetails->status == 1) {
                        if ($print->user_submenu_id && $productColor[$print->user_submenu_id])
                            $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                        else if ($print->submenu_id && $productColor[$print->submenu_id]) {
                            $colorValue = $productColor[$print->submenu_id] ?? null;

                            if (count($randomCauses['causes']) > 0) {
                                $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                            }

                            if (count($randomCauses['medium']) > 0) {
                                $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                            }

                            if (count($randomCauses['tipp']) > 0) {
                                $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                            }
                        }
                    } else {
                        $colorValue = $ownColor[$print->user_submenu_id] ?? null;
                        $ownPrice = $ownColor[$print->user_submenu_id] ?? null;
                    }

                    $greenFrom = null;
                    $greenTo = null;

                    #Analysis Value Color
                    if ($colorValue->red_min == null && $colorValue->red_max == null) { //changed green_min to red_min
                        $colorValue = GlobalSetting::find(1);
                        if (isset($colorValue) && $ma >= $colorValue->gs_green_min && $ma <= $colorValue->gs_green_max) {
                            $class_skill = "#2FAB66";
                            $greenCount++;
                            $greenFrom = $colorValue->gs_green_min;
                            $greenTo = $colorValue->gs_green_max;
                        }

                    } else {
                        if (isset($colorValue) && $ma >= $colorValue->green_min && $ma <= $colorValue->green_max) {
                            $class_skill = "#2FAB66";
                            $greenCount++;
                            $greenFrom = $colorValue->green_min;
                            $greenTo = $colorValue->green_max;
                        }
                    }

                    #Get Price for cart
                    if ($ownPrice != null && ($print->ana_min_price == '' && $print->ana_max_price == '')) {
                        if ($ownPrice->gs_min_price != null && $ownPrice->gs_max_price != null)
                            $randPrice = rand($ownPrice->gs_min_price, $ownPrice->gs_max_price);
                        else
                            $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);

                    } elseif ($print->ana_min_price != '' && $print->ana_max_price != '') {
                        $randPrice = rand($print->ana_min_price, $print->ana_max_price);
                    } else {
                        #Random Price For Cart
                        $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);
                    }
                    if ($availableCartCount >= 200) return response()->json([
                        'success' => false,
                        '_alert_type' => 'warning',
                        '_alert' => trans('action.warning'),
                        'message' => trans('action.cart_max_allow_alert')
                    ]);
                    #All Analysis
                    if (!checkCartAnalysis($ownsubid, $anaid)) {
                        if ($ma >= $greenFrom && $ma <= $greenTo) {
                            $data['userID'] = getUserId();
                            $data["analysisID"] = $anaid;
                            $data["analysisName"] = $strP;
                            $data['submenu_id'] = 'own-' . $ownsubid;
                            $data['productID'] = $ownproid;
                            $data["calculation"] = $ma;
                            $data['male'] = $beergK;
                            $data['heart'] = $beergS;
                            $data['price'] = $randPrice;
                            $data['causes_id'] = ($causeGroupid == null) ? "" : $causeGroupid;
                            $data['medium_id'] = ($mediumGroupid == null) ? "" : $mediumGroupid;
                            $data['tipp_id'] = ($tippGroupid == null) ? "" : $tippGroupid;
                            $data['color'] = $class_skill;
                            $data["desc"] = $print->description;
                            $data["desc_img"] = $print->desc_image;
                            $data['type'] = "Analysis";
                            $data['type_id'] = 1;
                            $data['minute'] = gmdate('i:s', $randPrice);
                            $availableCartCount++;
                            $insertdata = Cart::add(['id' => getUserId(), 'name' => (request()->type == '') ? 'Cart' : 'Analysis', 'qty' => (request()->type == 'Analysis') ? request()->ana_id : 1, 'price' => $randPrice, 'weight' => 1, 'options' => $data]);
                            $data['cart_id'] = $insertdata->rowId;
                            $collection[] = $data;
                        }
                    }
                }
            }
        } else {
            $analyses = "No Analyses Available";
        }

        if (count($collection) == 0 || !checkCartExist('submenu_id', $ownsubid, $greenCount, AnalysisColorEnum::GREEN_COLOR)) {
            $status = false;
            $msg = "No green values found.";
        }

        return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
    }

    public function getDashboardWidget()
    {
        $dashboardWidgetSetting = request()->all();

        $user = User::with('userfilter:user_id,filter_type')->find(getUserDetails()->id, ['id', 'language_id', 'first_name', 'last_name', 'gebort', 'gebdatum', 'datumcore', 'calculation_with', 'thema_speichern']);

        $anaQuery = Analyse::whereHas('pools', function ($query) use ($dashboardWidgetSetting) {
            $query->where('pool_id', $dashboardWidgetSetting['pool_id']);
        });
        if (app()->getLocale() != '' && app()->getLocale() != 'de') $anaQuery->select(app()->getLocale() . '_analyses.id', app()->getLocale() . '_analyses.name', 'analyses.name as de_name', 'analyses.ana_min_price', 'analyses.ana_max_price')->join('analyses', 'analyses.id', app()->getLocale() . '_analyses.id');
        else $anaQuery->select('analyses.id', 'analyses.name', 'analyses.ana_min_price', 'analyses.ana_max_price');

        $analyses = $anaQuery->orderBy('id', 'asc')->take(7)->get();
        $langBaseAnalyses = $analyses->pluck(app()->getLocale() == 'de' ? 'name' : 'de_name', 'id')->toArray() ?? [];
        $langBaseLabels = $analyses->pluck('name')->toArray() ?? [];
        // Assuming you have an array of data from the user

        $get_resp = app('App\Services\Calculation\CalculationService')->calculationFromUser($this->createNewRequestForcalculation($user, $langBaseAnalyses));
        $final_response = $this->processChartCard($get_resp, $langBaseLabels, $user->userfilter->filter_type ?? 3, request()->diagram_type);

        return response()->json([
            'success' => true,
            'results' => $final_response[1],
            'labels' => $final_response[0],
            'redValues' => $this->getRedValuesResul($get_resp, $langBaseAnalyses) ?? [],
        ]);
    }

    private function processChartCard($result, array $langBaseLabels, $sorting, $diagram_type)
    {
        $haveSortedDiagram = cache()->get('dashboard_widget_setting_exists') ?? [];
        $sortedDiagram = $diagram_type == 'bar' ? $haveSortedDiagram->bar_sorted_diagram : $haveSortedDiagram->sorted_diagram;
        $sortedDiagramEn = $diagram_type == 'bar' ? $haveSortedDiagram->bar_sorted_diagram_en : $haveSortedDiagram->sorted_diagram_en;
        $sortrdDiagramIsEmpty = empty($sortedDiagram);
        $sortResult = !$sortrdDiagramIsEmpty ? $this->processSortedDiagram($result->toArray(), $sortedDiagram) : $result->toArray();
        $langBaseLabelses = !$sortrdDiagramIsEmpty
            ? array_values(
                app()->getLocale() == 'de' ? $sortedDiagram : $sortedDiagramEn
            )
            : $langBaseLabels;

        $sortResult = $this->userLangBaseDataProcess($sortResult, $langBaseLabelses);
        if ($sortrdDiagramIsEmpty) {
            usort($sortResult, fn($a, $b) => $sorting == 4 ? $b['val'] - $a['val'] : $a['val'] - $b['val']);
        }
        return collect([
            array_map(fn($item) => $item['name'] . ': ' . $item['val'], $sortResult),
            array_column($sortResult, 'val'),
        ]);
    }

    private function userLangBaseDataProcess($result, array $langBaseLabels): array
    {
        return array_map(fn($item, $userValue) => ['name' => $userValue] + $item, $result, $langBaseLabels);
    }

    private function processSortedDiagram($sortResult, array $haveSortedDiagram): array
    {
        $assocArray1 = array_column($sortResult, null, 'name');
        return array_map(function ($name) use ($assocArray1) {
            return $assocArray1[$name] ?? null; // '?? null' is optional, it's just for safety in case a name doesn't exist
        }, $haveSortedDiagram);
    }

    private function getRedValuesResul($get_resp, array $langBaseAnalyses): array
    {
        return array_values(array_map(function ($item) use ($langBaseAnalyses) {
            $name = $item['name'];
            return array_merge($item, ['analysis_id' => $langBaseAnalyses[$name], 'ana_min_price' => $item['ana_min_price'], 'ana_max_price' => $item['ana_max_price']]);
        }, array_filter($get_resp->toArray(), function ($item) {
            return $item['color'] == '#E84E1B';
        })));
    }


    public function add2CartDashboardWidgetRedValues()
    {
        if (!UserService::checkUserAccess()) return response()->json([
            'success' => false,
            '_alert_type' => 'warning',
            '_alert' => trans('action.warning'),
            'message' => trans('action.payment_due_message', ['siteName' => env('APP_NAME')])
        ]);
        $collection = array();
        $allRedValues = request()->redValues;
        $globalValues = GlobalSetting::find(1);
        foreach ($allRedValues as $redValue) {
            #Get Price for cart
            if ($redValue['ana_min_price'] != '' && $redValue['ana_max_price'] != '') {
                $randPrice = rand($redValue['ana_min_price'], $redValue['ana_max_price']);
            } else {
                #Random Price For Cart
                $randPrice = rand($globalValues->gs_min_price, $globalValues->gs_max_price);
            }
            $data['userID'] = getUserId();
            $data["analysisID"] = $redValue['analysis_id'];
            $data["analysisName"] = $redValue['name'];
            $data['submenu_id'] = (empty(request()->submenu_id)) ? 0 : request()->submenu_id;
            $data['productID'] = (empty(request()->proID)) ? 0 : request()->proID;
            $data["calculation"] = round($redValue['val'], 1);
            $data['male'] = (empty(request()->male)) ? 0 : request()->male;
            $data['heart'] = (empty(request()->heart)) ? 0 : request()->heart;
            $data['price'] = $randPrice;
            $data['causes_id'] = (empty(request()->causes_id)) ? 0 : request()->causes_id;
            $data['medium_id'] = (empty(request()->medium_id)) ? 0 : request()->medium_id;
            $data['tipp_id'] = (empty(request()->tipp_id)) ? 0 : request()->tipp_id;
            $data['color'] = $redValue['color'];
            $data["desc"] = "";
            $data["desc_img"] = "";
            $data['type'] = "Analysis";
            $data['type_id'] = CausesType::Dashboard_widget;
            $data['minute'] = gmdate('i:s', $randPrice);
            $insertdata = Cart::instance('wishlist')->add(['id' => getUserId(), 'name' => 'Analysis', 'qty' => $redValue['analysis_id'], 'price' => $randPrice, 'weight' => 1, 'options' => $data]);
            $data['cart_id'] = $insertdata->rowId;
            $collection[] = $data;
        }
        $status = true;
        if (count($collection) == 0) {
            $status = false;
            $msg = "No red values found.";
        }

        return response()->json(['status' => $status, 'msg' => $msg, 'data' => $collection, 'cart_updated' => count($collection) > 0]);
    }

    private function checkMissingFields($color,$ownproid, $requiredFields)
    {
        foreach ($requiredFields as $field) {
            if ($color->$field === null || $color->$field === '') {
                return response()->json([
                    'status' => false,
                    'missing_fields' => true,
                    'edit_url' => url("/menu/edit_menu/{$ownproid}?open_settings=1"),
                    'msg' => trans('action.missing_fields'),
                ]);
            }
        }
        return null;
    }
}
