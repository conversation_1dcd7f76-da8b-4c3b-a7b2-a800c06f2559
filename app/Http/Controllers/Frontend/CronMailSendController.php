<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Model\CronSetup;
use App\Model\CronsetupTimes;
use App\Services\Mail\MailService;
use App\Services\Users\Subscribtion\GetUserByCryptedId;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CronMailSendController extends Controller
{
    public function unsubscribe(Request $request)
    {
        $action = $request->get('unsubsribe');
        $cryptedUserId = $request->get('user_id');
        $url = route('unsubscribe', ['user_id' => $cryptedUserId, 'unsubsribe' => 1]);

        if(!$action) {
            return view('Frontend.cron.unsubscribe', compact('url', 'action'));
        }

        try{
            $user = (new GetUserByCryptedId())->execute($cryptedUserId);

            if(!$user) {
                return view('Frontend.cron.unsubscribe')->with('error', 'User not found.');
            }

            $user->email_subscription = 0;
            $user->save();
        } catch (\Exception $e) {
            Log::error('Unsubscribe email error: ' . $e->getMessage());
            return redirect()->route('home')->with('error', 'Something went wrong. Please try again.');
        }

        return view('Frontend.cron.unsubscribe', compact( 'action'));
    }

    public function index()
    {
        $filehandle = fopen(base_path()."/public/lock.txt", "r+");

        if (flock($filehandle, LOCK_EX | LOCK_NB)) {

            fwrite($filehandle, "job_running\n");

            #cron mail send function
            $this->CronMailSend();

            flock($filehandle, LOCK_UN);
        }
    }


    public function CronMailSend()
    {
        #default_timezone
        date_default_timezone_set('Europe/Vienna');

        $time1 = 3600;
		$dt1 = date("Y-m-d H:i:s");
		$dt2 = date("Y-m-d H:i:s", strtotime($dt1) + $time1);

		#crontimes
        $crontime = DB::table("cronsetup_times")->where("start_time", ">=", $dt1)->where("start_time", "<=", $dt2)
        ->orWhere(function($query) use($dt1, $dt2) {
            $query->where('end_time', ">=", $dt1)
                  ->where('end_time', '<=', $dt2);
        })->where("status", "!=", 2)->get();

        #email placeholder
        $eph = DB::table("email_placeholders")->get();
        $mailreport = array();
        $send_status = false;

        foreach($crontime as $ctkey => $ctvalues)
        {
            $time_start = microtime(true);

            ### QUERY START ###

            #alltimes
            $crontimes = DB::table("cronsetup_times")
                        ->select("id", "end_time")->where("status", "!=", 2)
                        ->where("cronsetup_id", $ctvalues->cronsetup_id)->get();

            $timescnt = count($crontimes);

            #admin info
            $admin  = DB::table("cron_setups")
                        ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cron_setups.id")
                        ->join("cron_settings", "cron_setups.user_id", "=", "cron_settings.user_id")
                        ->join("users", "users.id", "=", "cron_setups.user_id")
                        ->join("smtps", "smtps.user_id", "=", "cron_setups.user_id")
                        ->join("email_templates", "email_templates.cron_setting_id", "=", "cron_settings.id")
                        ->select(
                                'cron_setups.*',
                                'cronsetup_options.*',
                                'cron_settings.id', 'cron_settings.send_email_time',
                                'users.first_name', 'users.last_name', 'users.cron_email', 'users.user_status', 'users.language_id',
                                'smtps.*',
                                'email_templates.*'
                        )->where("cron_setups.id", $ctvalues->cronsetup_id)->first();

            #subuser info
            $subuser  = DB::table("users")->select("id", "first_name", "language_id","last_name", "cron_email", "user_status", "email_subscription")->where("id", $admin->selected_user)->first();

            //check if user is unsubscribed
            if($subuser->email_subscription == 0) continue;

            #analyses info
            $analyses = DB::table("cronsetup_analyses")
                            ->where("start_time", $ctvalues->start_time)
                            ->where("cron_setup_id", $ctvalues->cronsetup_id)->first();
            $lang = 'de';
            if($subuser && $subuser->language_id != '2') $lang = DB::table('languages')->find($subuser->language_id)->short_code;
            setServerLocal($lang);

            ### QUERY END ###

            #before send time
	        $beforesend = 300;
	        if($admin->send_email_time != "" || $admin->send_email_time != 0)
	        	$beforesend = $admin->send_email_time*60;

            #time info
	        $start_time = strtotime($ctvalues->start_time);
            $end_time   = $crontimes[$timescnt-1]->end_time;
            $nowtime    = strtotime(date('Y-m-d H:i:s'));
            $endtime    = strtotime($end_time);
	        $diff = $start_time - $nowtime;
	        $end_diff = $endtime - $nowtime;

            #conditions to ignore sendmail
            if($diff > $beforesend) continue;
            if($diff < 0 && $end_diff > 120) continue;
            if($admin->user_status == 0 || $subuser->user_status == 0) continue;
            if($admin->is_deleted == 1 || $admin->as_draft == 1 || $admin->is_stop == 1) continue;

            $sender_name = $admin->fullName;
            #reciver info
			$receiver_email = ($admin->email == null)? $subuser->cron_email : $admin->email;
            $reciver_name = $subuser->fullName;

            #smtp configuration
            $smtp_config = $this->smtpConfig($admin, $sender_name);

            #PDF link setup
	        $pdf_status = false;
            $pdflink = "";
            $click = ($admin->language_id == 2) ? "drücke hier" : "Click Here";

	        if($admin->pdf_export == 1)
	        {
	        	$pdflink = url('/treatment/view_pdf',[$admin->selected_user, $ctvalues->cronsetup_id, $ctvalues->id, $admin->unique_id]);
	        	$pdf_status = true;
            }

            if(($end_diff > 0 && $end_diff < 120) || !$analyses) $pdf_status = false;

            #array in for checking condition
            $others = [
                'diff' => $diff,
                'end_diff' => $end_diff,
                'before' => $beforesend,
                'start_time' => $ctvalues->start_time,
                'end_time' => $ctvalues->end_time,
                'nowtime' => $nowtime,
                'status' => $ctvalues->status,
                'cronsetup_id' => $ctvalues->cronsetup_id,
                'crontime_id' => $ctvalues->id,
                'click' => $click,
                'pdf_link' => $pdflink
            ];

            #emailtemplate setup
            $emailTemplate = $this->EmailTemplate($admin, $analyses, $others, $eph);

            #template structure converated
            $templateStucture = $this->mailTamplate($emailTemplate['temp_body'], $subuser, $admin, $others);

            #mail template array
            $mailTamplate = ['mail' => true, 'mail_subject' => $emailTemplate['temp_title'], 'mail_body' => $templateStucture, 'mail_footer' => $emailTemplate['temp_footer'], "pdf_status" => $pdf_status, "pdf" => $pdflink, 'user_id' => $subuser->id];

            if($receiver_email != "" && ($diff > 0 && $diff <= $beforesend) && $ctvalues->status == 0 && (($admin->due_power == 0 && $admin->start_email == 1) || ($admin->due_power == 1 && $admin->start_email_duepower == 1))){

                $recipient['mail'] = $receiver_email;
                $recipient['name'] = $reciver_name;
                $datetime = date("Y-m-d H:i:s");

                $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate,$lang);

                $time_end = microtime(true);
                $ex_time = ($time_end - $time_start)/60;
                $runtime = round($ex_time, 3);

                $mailstatus = 1;
                if($crontimes[$timescnt-1]->id != $ctvalues->id) $mailstatus = 2;

                #updating cronsetuptime/treatment_time status
                $statusUpdate = CronsetupTimes::find($ctvalues->id);
                $statusUpdate->update(array('status' => $mailstatus));

                #cronmail record
                $mailquery = [
                    'admin_id' => $admin->user_id,
                    'user_id' => $admin->selected_user,
                    'user_email' => $receiver_email,
                    'execution_time' => $runtime,
                    'status' => $mailsend_status,
                    'mail_type' => 0,
                    'created_at' => $datetime
                ];

                $mailreport[] = $mailquery;
                $send_status = true;

            }else if($receiver_email != "" && ($end_diff > 0 && $end_diff <= 120) && ($ctvalues->status == 0 || $ctvalues->status == 1) && (($admin->due_power == 0 && $admin->end_email == 1) || ($admin->due_power == 1 && $admin->end_email_duepower == 1))){

                $recipient['mail'] = $receiver_email;
                $recipient['name'] = $reciver_name;
                $datetime = date("Y-m-d H:i:s");

                $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate,$lang);

                $time_end = microtime(true);
                $ex_time = ($time_end - $time_start)/60;
                $runtime = round($ex_time, 2);

                #updating cronsetuptime/treatment_time status
                $statusUpdate = CronsetupTimes::find($ctvalues->id);
                $statusUpdate->update(array('status' => 2));

                //cronmail record
                $mailquery = [
                    'admin_id' => $admin->user_id,
                    'user_id' => $admin->selected_user,
                    'user_email' => $receiver_email,
                    'execution_time' => $runtime,
                    'status' => $mailsend_status,
                    'mail_type' => 1,
                    'created_at' => $datetime
                ];

                $mailreport[] = $mailquery;
                $send_status = true;
            }
        }

        #storing mail report
        if($send_status == true)
        {
            DB::table("cronmail_record")->insert($mailreport);
        }
    }


    public function resendTreatMail(Request $request)
    {
        $crontime_id = $request->timeid;
        $crontime = DB::table("cronsetup_times")->find($crontime_id);

        $admin  = DB::table("cron_setups")
                        ->select(
                            'cron_setups.*',
                            'cron_setups.email as receiver_email',
                            'cronsetup_options.*',
                            'cron_settings.id',
                            'cron_settings.id as cronsettingid',
                            'cron_settings.send_email_time',
                            DB::raw("CONCAT(first_name, ' ', last_name) AS fullName"),
                            'users.first_name',
                            'users.last_name',
                            'users.cron_email',
                            'users.user_status',
                            "users.language_id",
                            'smtps.*'
                        )->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cron_setups.id")
                        ->join("cron_settings", "cron_setups.user_id", "=", "cron_settings.user_id")
                        ->join("users", "users.id", "=", "cron_setups.user_id")#sender
                        ->leftJoin("smtps", "smtps.user_id", "=", "cron_setups.user_id")
                        ->where("cron_setups.id", $crontime->cronsetup_id)->first();

        # Default Template
        $eph = DB::table("email_placeholders")->get();

        #Receiver details
        $receiver = DB::table("users")->select("ID",DB::raw("CONCAT(first_name, ' ', last_name) AS fullName"),"language_id" ,"first_name", "last_name", "cron_email", "user_status", "email_subscription")->where("id", $admin->selected_user)->first();

        //check if user is unsubscribed
        if($receiver->email_subscription === 0) return response()->json(['status' => false, 'title' => 'User is unsubscribed', 'msg' => 'Please subscribe to receive emails']);

        #analyses info
        $analyses = DB::table("cronsetup_analyses")
                        ->where("start_time", $crontime->start_time)
                        ->where("cron_setup_id", $crontime->cronsetup_id)->first();
        $lang = 'de';
        if($receiver->language_id != '2') $lang = DB::table('languages')->find($receiver->language_id)->short_code;
        setServerLocal($lang);

        $tableName = ($lang != 'de')? $lang.'_email_templates' : 'email_templates';
        $adminTemplate = DB::table($tableName)->where('cron_setting_id',$admin->cronsettingid)->first();
        ### QUERY END ###

        #reciver info
        $receiver_email = $admin->receiver_email ?? $receiver->cron_email;
        $reciver_name = $receiver->fullName;

        #smtp configuration
        $smtp_config = $this->smtpConfig($admin, $admin->fullName);

        #PDF link setup
        $pdf_status = false;
        $pdflink = "";
        // $click = ($admin->language_id == 2) ? "Klick hier" : "Click Here";
        $click = trans('action.click_here');

        if($admin->pdf_export == 1)
        {
            $pdflink = url('/treatment/view_pdf',[$admin->selected_user, $crontime->cronsetup_id, $crontime->id, $admin->unique_id]);
            $pdf_status = true;
        }

        #array in for checking condition
        $others = [
            'start_time' => date_change_with_time($crontime->start_time),
            'end_time' => date_change_with_time($crontime->end_time),
            'cronsetup_id' => $crontime->cronsetup_id,
            'crontime_id' => $crontime->id,
            'click' => $click,
            'pdf_link' => $pdflink
        ];

        #emailtemplate setup
        // $emailTemplate = $this->resendEmailTemplate($admin, $analyses, $eph);
        $emailTemplate = $this->EmailContentTemplate($receiver, $adminTemplate, $eph, $lang);
        #template structure converated
        $templateStucture = $this->mailTamplate($emailTemplate['temp_body'], $receiver, $admin, $others);

        #mail template array
        $mailTamplate = [
            'mail'          => true,
            'mail_subject'  => $emailTemplate['temp_title'],
            'mail_body'     => $templateStucture,
            "pdf_status"    => $pdf_status,
            "pdf"           => $pdflink,
            'click'         => $click,
            'mail_footer'   => $emailTemplate['temp_footer'],
            'user_id'       => $admin->selected_user,
        ];

        $recipient['mail'] = $receiver_email;
        $recipient['name'] = $reciver_name;
        $recipient['lang_id'] = $receiver->language_id;
        #For mail send header
        $cronsetupInfo['cronsetup_id'] = $crontime->cronsetup_id ?? 0;
        $cronsetupInfo['cronsetup_times_id'] = $crontime->id ?? 0;
        $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate,$lang,$cronsetupInfo);

        activityLogTrack(4,'resend email to '.$receiver->fullName, 'E-Mail an '.$receiver->fullName.' erneut versendet', 'Remote Treatment', 'remote_treatment');#AL

        return response()->json($mailsend_status);
    }


    public function sendMail($config, $recipient, $template, $lang, $cronsetupInfo = [])
    {
        // Create instance of MailService
        $mailService = new MailService();

        // Prepare custom headers for Mailgun if cronsetup info is provided
        $customHeaders = null;
        if (isset($cronsetupInfo['cronsetup_id']) && isset($cronsetupInfo['cronsetup_times_id'])) {
            $customHeaders = [
                'X-Mailgun-Variables' => [
                    'cronsetup_id' => $cronsetupInfo['cronsetup_id'],
                    'cronsetup_times_id' => $cronsetupInfo['cronsetup_times_id']
                ]
            ];
        }

        // Use the service to send the email
        return $mailService->send(
            $config,
            $recipient,
            $template,
            $lang,
            null,           // no PDF attachment
            $customHeaders  // Mailgun variables if present
        );
    }

    public function smtpConfig($adminsmtp, $sender_name)
	{
        if (env('APP_ENV') !== 'production' || $adminsmtp->smtp_email == null || $adminsmtp->smtp_user_name == null || $adminsmtp->smtp_password == null || $adminsmtp->smtp_port == null || $adminsmtp->smtp_host == null) {
            return [
                'smtp_host'       => env('MAIL_HOST'),
                'smtp_port'       => env('MAIL_PORT'),
                'smtp_username'   => env('MAIL_USERNAME'),
                'smtp_password'   => env('MAIL_PASSWORD'),
                'smtp_encryption' => env('MAIL_ENCRYPTION'),
                'from_email'      => env('MAIL_FROM_ADDRESS'),
                'from_name'       => env('MAIL_FROM_NAME')
            ];
        }
        #smtp config
        if($adminsmtp->smtp_host == "ssl://smtp.gmail.com" || $adminsmtp->smtp_host == "tls://smtp.gmail.com")
        {
            $tmphost  = explode("://", $adminsmtp->smtp_host);
            $smtphost = $tmphost[1];
        }else
            $smtphost = $adminsmtp->smtp_host;

        if($adminsmtp->smtp_ssl == "ssl://smtp.gmail.com" || $adminsmtp->smtp_ssl == "tls://smtp.gmail.com")
        {
            $tmpencryp  = explode("://", $adminsmtp->smtp_ssl);
            $encryption = $tmpencryp[0];
        }
        else
            $encryption = $adminsmtp->smtp_ssl;

        $smtpconfig = [

            'smtp_host'       => $smtphost,
            'smtp_port'       => $adminsmtp->smtp_port,
            'smtp_username'   => trim($adminsmtp->smtp_user_name),
            'smtp_password'   => $adminsmtp->smtp_password,
            'smtp_encryption' => $encryption,
            'from_email'      => $adminsmtp->smtp_email,
            'from_name'       => ($adminsmtp->sender_name == "") ? $sender_name : $adminsmtp->sender_name
        ];

        return $smtpconfig;
    }

    protected function EmailContentTemplate($receiver,$template,$defaultTemp,$lang){
        if($template){
            $emailTemplate = [
                'temp_title' => strip_tags(($template->email_template_title_resend == '') ? ($defaultTemp->where('type','email_template_title_resend')->where('language',$lang)->first()->message ?? '' ) : $template->email_template_title_resend ?? ''),
                'temp_body' => ($template->email_template_resend == '') ? ($defaultTemp->where('type','email_template_resend')->where('language',$lang)->first()->message ?? '' ) : $template->email_template_resend ?? '',
                'temp_footer' => ($template->email_template_footer == '') ? ($defaultTemp->where('type','global_footer_'.$lang)->first()->message ?? '') : $template->email_template_footer ?? ''
            ];
        }else{
            $emailTemplate = [
                'temp_title' => strip_tags($defaultTemp->where('type','email_template_title_resend')->where('language',$lang)->first()->message ?? ''),
                'temp_body' => $defaultTemp->where('type','email_template_resend')->where('language',$lang)->first()->message ?? '',
                'temp_footer' => $defaultTemp->where('type','global_footer_'.$lang)->first()->message ?? ''
            ];
        }
        return $emailTemplate;

    }
    // public function resendEmailTemplate($admintemp, $analyses, $eph)
	// {
	// 	#email template
    //     if(!$analyses){
    //     	$emailTemplate = [
    //     	    'temp_title' => ($admintemp->email_template_title_blank == "")? (($admintemp->language_id == 1) ? $eph[10]->message : $eph[11]->message) : $admintemp->email_template_title_blank,
    //             'temp_body' => ($admintemp->email_template_blank == "")? (($admintemp->language_id == 1) ? $eph[8]->message : $eph[9]->message) : $admintemp->email_template_blank,
    //             'temp_footer' => ($admintemp->email_template_footer != "") ? $admintemp->email_template_footer : (($admintemp->language_id == 1) ? $eph[12]->message : $eph[13]->message)
    //         ];
    //     } else{
    //         #Strrt Email with cron link
    //         $emailTemplate = [
    //             'temp_title' => ($admintemp->email_template_title_start == "") ? (($admintemp->language_id == 1) ? $eph[4]->message : $eph[5]->message) : $admintemp->email_template_title_start,
    //             'temp_body' => ($admintemp->email_template_start == "") ? (($admintemp->language_id == 1) ? $eph[0]->message : $eph[2]->message) : $admintemp->email_template_start,
    //             'temp_footer' => ($admintemp->email_template_footer != "") ? $admintemp->email_template_footer : (($admintemp->language_id == 1) ? $eph[12]->message : $eph[13]->message)
    //         ];

    //     }

    //     return $emailTemplate;
	// }


    public function EmailTemplate($admintemp, $analyses, $others, $eph)
	{
		#email template
        if(($others['end_diff'] > 0 && $others['end_diff'] <= 120) && ($others['status'] == 0 || $others['status'] == 1) && (($admintemp->due_power == 0 && $admintemp->end_email == 1) || ($admintemp->due_power == 1 && $admintemp->end_email_duepower == 1))){

            #END email greeting
            $emailTemplate = [
            	'temp_title' => ($admintemp->email_template_title_stop == "") ? (($admintemp->language_id == 1) ? $eph[6]->message : $eph[7]->message) : $admintemp->email_template_title_stop,
                'temp_body' => ($admintemp->email_template_stop == "") ? (($admintemp->language_id == 1) ? $eph[1]->message : $eph[3]->message) : $admintemp->email_template_stop,
                'temp_footer' => ($admintemp->email_template_footer != "") ? $admintemp->email_template_footer : (($admintemp->language_id == 1) ? $eph[12]->message : $eph[13]->message)
            ];
        }
        else if(!$analyses){

        	$emailTemplate = [
        	    'temp_title' => ($admintemp->email_template_title_blank == "") ? (($admintemp->language_id == 1) ? $eph[10]->message : $eph[11]->message) : $admintemp->email_template_title_blank,
                'temp_body' => ($admintemp->email_template_blank == "") ? (($admintemp->language_id == 1) ? $eph[8]->message : $eph[9]->message) : $admintemp->email_template_blank,
                'temp_footer' => ($admintemp->email_template_footer != "") ? $admintemp->email_template_footer : (($admintemp->language_id == 1) ? $eph[12]->message : $eph[13]->message)
            ];
        }
        else if(($others['diff'] > 0 && $others['diff'] <= $others['before']) && $others['status'] == 0 && (($admintemp->due_power == 0 && $admintemp->start_email == 1) || ($admintemp->due_power == 1 && $admintemp->start_email_duepower == 1))){

            #Strrt Email with cron link
            $emailTemplate = [
                'temp_title' => ($admintemp->email_template_title_start == "") ? (($admintemp->language_id == 1) ? $eph[4]->message : $eph[5]->message) : $admintemp->email_template_title_start,
                'temp_body' => ($admintemp->email_template_start == "") ? (($admintemp->language_id == 1) ? $eph[0]->message : $eph[2]->message) : $admintemp->email_template_start,
                'temp_footer' => ($admintemp->email_template_footer != "") ? $admintemp->email_template_footer : (($admintemp->language_id == 1) ? $eph[12]->message : $eph[13]->message)
            ];

        }

        return $emailTemplate;
	}

    protected function mailTamplate($string, $subuser, $admin, $others){

        if ($subuser->language_id == 2) {
            $overviewText = "Übersicht";
        }
        else {
            $overviewText = "Overview";
        }

        $variables = array(
            '{first_name}' => $subuser->first_name,
            '{last_name}' => $subuser->last_name,
            '{start_date_time}' => $others['start_time'] ?? '',
            '{end_date_time}' => $others['end_time'] ?? '',
            '{link}' => ($admin->customer_link == 1) ? "<a href='".url('cron/open_treat',[$others['cronsetup_id'],$others['crontime_id'],$admin->unique_id])."'><b>".$others['click']."</b></a>" : "",
            '{client_note}' => $others['client_note'] ?? '',
            '{pdf_link}' => '<a href="'.($others['pdf_link'] ?? '').'"><b>'.($others['click'] ?? '').'</b></a>',
            '{overview_link}' => "<a href='".url('cron/overview/' . $others['cronsetup_id'] . '/' . rand()) . "'><b>".$overviewText."</b></a>",
            '{remote_id}' => $others['cronsetup_id'] ?? ''
        );

        $datas = strtr($string, $variables);
        return $datas;
    }
    public function resendTreatMailById($timeid)
    {
        $crontime_id = $timeid;
        $crontime = DB::table("cronsetup_times")->find($crontime_id);

        $admin  = DB::table("cron_setups")#sender
                        ->select(
                            'cron_setups.*',
                            'cron_setups.email as receiver_email',
                            'cronsetup_options.*',
                            'cron_settings.id',
                            'cron_settings.id as cronsettingid',
                            'cron_settings.send_email_time',
                            DB::raw("CONCAT(first_name, ' ', last_name) AS fullName"),
                            'users.first_name',
                            'users.last_name',
                            'users.cron_email',
                            'users.user_status',
                            "users.language_id",
                            'smtps.*'
                        )->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cron_setups.id")
                        ->join("cron_settings", "cron_setups.user_id", "=", "cron_settings.user_id")
                        ->join("users", "users.id", "=", "cron_setups.user_id")#sender
                        ->join("smtps", "smtps.user_id", "=", "cron_setups.user_id")
                        ->where("cron_setups.id", $crontime->cronsetup_id)->first();

        # Default Template
        $eph = DB::table("email_placeholders")->get();

        #Receiver details
        $receiver = DB::table("users")->select("ID",DB::raw("CONCAT(first_name, ' ', last_name) AS fullName"),"language_id" ,"first_name", "last_name", "cron_email", "user_status", "email_subscription")->where("id", $admin->selected_user)->first();

        //check if user is unsubscribed
        if($receiver->email_subscription == 0) return response()->json(['status' => false, 'title' => 'User is unsubscribed', 'msg' => 'Please subscribe to receive emails']);

        #analyses info
        $analyses = DB::table("cronsetup_analyses")
                        ->where("start_time", $crontime->start_time)
                        ->where("cron_setup_id", $crontime->cronsetup_id)->first();
        $lang = 'de';
        if($receiver->language_id != '2') $lang = DB::table('languages')->find($receiver->language_id)->short_code;
        setServerLocal($lang);

        $tableName = ($lang != 'de')? $lang.'_email_templates' : 'email_templates';
        $adminTemplate = DB::table($tableName)->where('cron_setting_id',$admin->cronsettingid)->first();
        ### QUERY END ###

        #reciver info
        $receiver_email = $admin->receiver_email ?? $receiver->cron_email;
        $reciver_name = $receiver->fullName;

        #smtp configuration
        $smtp_config = $this->smtpConfig($admin, $admin->fullName);

        #PDF link setup
        $pdf_status = false;
        $pdflink = "";
        // $click = ($admin->language_id == 2) ? "Klick hier" : "Click Here";
        $click = trans('action.click_here');

        if($admin->pdf_export == 1)
        {
            $pdflink = url('/treatment/view_pdf',[$admin->selected_user, $crontime->cronsetup_id, $crontime->id, $admin->unique_id]);
            $pdf_status = true;
        }

        #array in for checking condition
        $others = [
            'start_time' => $crontime->start_time,
            'end_time' => $crontime->end_time,
            'cronsetup_id' => $crontime->cronsetup_id,
            'crontime_id' => $crontime->id,
            'click' => $click,
            'pdf_link' => $pdflink
        ];

        #emailtemplate setup
        // $emailTemplate = $this->resendEmailTemplate($admin, $analyses, $eph);
        $emailTemplate = $this->EmailContentTemplate($receiver, $adminTemplate, $eph, $lang);
        #template structure converated
        $templateStucture = $this->mailTamplate($emailTemplate['temp_body'], $receiver, $admin, $others);

        #mail template array
        $mailTamplate = [
            'mail'          => true,
            'mail_subject'  => $emailTemplate['temp_title'],
            'mail_body'     => $templateStucture,
            "pdf_status"    => $pdf_status,
            "pdf"           => $pdflink,
            'click'         => $click,
            'mail_footer'   => $emailTemplate['temp_footer'],
            'user_id'       => $admin->selected_user,
        ];

        $recipient['mail'] = $receiver_email;
        $recipient['name'] = $reciver_name;
        $recipient['lang_id'] = $receiver->language_id;
        $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate,$lang);

        activityLogTrack(4,'resend email to '.$receiver->fullName, 'E-Mail an '.$receiver->fullName.' erneut versendet', 'Remote Treatment', 'remote_treatment');#AL

        return response()->json($mailsend_status);
    }

    public function resendMailByUser($userid,$from,$to){
        $crons = CronSetup::select('cronsetup_times.*')->join('cronsetup_times','cron_setups.id','cronsetup_times.cronsetup_id')
            ->where('cron_setups.user_id',$userid)
            ->whereBetween('cronsetup_times.start_send_mail',[$from, $to])
            ->where('cronsetup_times.status','!=',2)
            ->get();
        foreach ($crons as $key => $cron) {
            echo '<pre>';
            echo $cron->id;
            // $this->resendTreatMailById($cron->id);
        }
        dd($userid,$from,$to);
    }
}
