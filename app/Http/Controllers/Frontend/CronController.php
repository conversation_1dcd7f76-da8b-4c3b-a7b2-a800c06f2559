<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Process\CronPreviewProcess;
use App\Http\Controllers\Process\CronSetupProcess;
use App\Http\Requests\StoreCronSetupRequest;
use App\Jobs\RemoteTreatementJob;
use App\Model\Analyse;
use App\Model\BodyImage;
use App\Model\CartContent;
use App\Model\CartPackage;
use App\Model\Cause;
use App\Model\Causes;
use App\Model\CronmailSetup;
use App\Model\CronmailSetupTimes;
use App\Model\CronSetting;
use App\Model\CronsettingOption;
use App\Model\CronSetup;
use App\Model\CronsetupAnalysis;
use App\Model\CronsetupTimes;
use App\Model\Feedback;
use App\Model\GlobalSetting;
use App\Model\Language;
use App\Model\MentalImage;
use App\Model\OptimizedAnalysesValues;
use App\Model\Pdf;
use App\Model\Smtp;
use App\Model\Timezone;
use App\Model\Treatment_custom_image;
use App\Model\User;
use App\Services\Calculation\CalculationService;
use App\Services\Treatment\SessionIdManager;
use Carbon\Carbon;
use Dompdf\Dompdf;
use Dompdf\Options;
use Gloudemans\Shoppingcart\Facades\Cart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\View;

class CronController extends Controller
{

    public function index(Request $request)
    {
        $debug=$request->debug;
        if ($request->ajax()) {
            return app('App\Services\CronModule\CronControllerService')->index($request);
        }

        $subuser = User::where('boss_id', getAuthID())->orderBy('first_name', 'ASC')->orWhere('id',getAuthID())->get();

        if ($request->archive == "archive") {
            return view('Frontend.cron.cron_archive', ['subuser' => $subuser]);
        }else if($debug=="true"){
            return view('Frontend.cron.cron_debug',['subuser' => $subuser]);
        }else {
            return view('Frontend.cron.cron', ['subuser' => $subuser]);
        }
    }

    public function cron_show()
    {
        $cron = app('App\Services\CronModule\CronControllerService')->getCronById(request()->id, ['cronsetup_option', 'cronsetuptimes', 'selecteduser']);

        return view('Frontend.cron.cron_show', ['cron' => $cron, 'cron_times' => $cron->cronsetuptimes]);
    }

    public function showCronSetting($userid)
    {
        if(strlen($userid) === 0 || md5(getAuthID()) !== $userid){
            return Redirect::back();
        }else $userid = getAuthID();

        $setting = app('App\Services\CronModule\CronControllerService')->getCronSetting($userid);

        // return view('Frontend.cron.cron_setting', ['setting' => $setting['cron']->cronsetting,'langs'=>$setting['languages'], 'products' => $setting['menus'], 'placeholder' => $setting['emailplaceholder'], 'template' => $setting['cron']->cronsetting->emailtemplate, 'default' => $setting['cron']->cronsetting->cronsettingoption, 'smtp' => $setting['cron']->smtp, 'preselected' => $setting['cron']->cronsetting->preassignsubmenus, 'userlang' => $setting['userlang'], 'nextMonth' => $setting['daysOfNextMonth']]);
        return view('Frontend.cron.cron_setting', ['setting' => $setting['cron']->cronsetting,'langs'=>$setting['languages'], 'products' => $setting['menus'], 'default' => $setting['cron']->cronsetting->cronsettingoption, 'smtp' => $setting['cron']->smtp, 'preselected' => $setting['cron']->cronsetting->preassignsubmenus, 'userlang' => $setting['userlang'], 'nextMonth' => $setting['daysOfNextMonth']]);
    }

    public function storeCronSetting(Request $request)
    {
        $setting = app('App\Services\CronModule\CronControllerService')->storeCronSetting($request);

        if($setting) $notify = array(
                'alert-type' =>  "success",
                'message' => trans('action.update_successfully')
            );
        else $notify = array(
                'alert-type' =>  "warning",
                'message' => trans('action.something_went_wrong')
            );


        return redirect()->back()->with($notify);
    }

    public function storeSMTP(Request $request){

        $this->smtp(getAuthID(),  $request);
        $notify = array(
            'alert-type' =>  "success",
            'message' => trans('action.smtp_connection_success')
        );

        return redirect()->back()->with($notify);
    }

    protected function smtp($userid, $request) {

        if (!Smtp::where('user_id', $userid)->exists()) {

            $smtp = new Smtp;
            $smtp->user_id = $userid;
            $smtp->sender_name = $request->sender_name;
            $smtp->smtp_email = $request->smtp_email;
            $smtp->smtp_host = $request->smtp_host;
            $smtp->smtp_port = $request->smtp_port;
            $smtp->smtp_ssl = $request->smtp_ssl;
            $smtp->smtp_user_name = $request->smtp_user_name;
            $smtp->smtp_password = $request->smtp_password;
            $smtp->status = ($request->status == true) ? true : false;

            $smtp->save();
            activityLogTrack(1, 'at SMTP for Remote Treatment Setting', 'im SMTP für Fernbehandlung Einstellungen hinzugefügt', 'Remote Treatment', 'remote_treatment'); #AL
        } else {
            $smtp = Smtp::where('user_id', $userid)->first();
            $smtp->sender_name = $request->sender_name;
            $smtp->smtp_email = $request->smtp_email;
            $smtp->smtp_host = $request->smtp_host;
            $smtp->smtp_port = $request->smtp_port;
            $smtp->smtp_ssl = $request->smtp_ssl;
            $smtp->smtp_user_name = $request->smtp_user_name;
            if(isset($request->smtp_password)) $smtp->smtp_password = $request->smtp_password;
            $smtp->status = ($request->status == true) ? true : false;
            $smtp->update();
            activityLogTrack(2, 'at SMTP for Remote Treatment Setting', 'im SMTP für Fernbehandlung Einstellungen', 'Remote Treatment', 'remote_treatment'); #AL
        }
    }

    public function storeMailTemplates(Request $request){
        $setting = app('App\Services\CronModule\CronControllerService')->storeMailTemplates($request);
        if($setting) return response()->json([
            'success' => true,
            'message' => $request->from .' '.trans('action.added_successfully')
        ]);
        else return response()->json([
            'success' => false,
            'message' => $request->from .' '.trans('action.something_went_wrong')
        ]);
    }

    public function getTemplate(Request $request)
    {
        $setting = app('App\Services\CronModule\CronControllerService')->getTemplate($request);
        return response()->json([
            'success' => true,
            'subject' => $setting['subject']??'',
            'content'=>$setting['content']??''
        ]);
    }

    public function storeCronSetup(Request $request)
    {
        if(!$request['isDuplicate']) {
            StoreCronSetupRequest::capture();
        }

        $data = $request->all();

        CalculationService::setCalculationWidth(isset($data['cal_type']));

        if (isset($data['due_power'])) {
            unset($data['start_email']);
            unset($data['end_email']);
        } else {
            unset($data['start_time_duepower']);
            unset($data['end_time_duepower']);
        }
        $start_time = date("Y-m-d H:i:s", strtotime($request->crondate));
        if (isset($data['due_power']))
            $start_time = date("Y-m-d H:i:s", strtotime($data['start_time_duepower']));

        $treatTimes = array();

        $checkTimeZone = DB::table('timezones')->where('timezone',$data['timezone_id'])->exists();
        if(!$checkTimeZone) $data['timezone_id'] = 'Europe/Vienna';

        if (!isset($data['due_power'])) {
            $curDate = date("Y-m-d", strtotime($request->crondate));
            $time2 = ($request->time2 == null) ? null : date("Y-m-d H:i:s", strtotime($curDate . " " . $request->time2));
            $time3 = ($request->time3 == null) ? null : date("Y-m-d H:i:s", strtotime($curDate . " " . $request->time3));
            array_push($treatTimes, $start_time, $time2, $time3);
        } else {
            array_push($treatTimes, $start_time);
        }

        $checkSetupTime = CronSetup::whereHas('cronsetuptimes', function ($query) use ($treatTimes) {
            $query->whereIn('start_time', $treatTimes)->where('status', 0);
        })->where('user_id', getAuthID())->where('selected_user', $request->cronuser)->where('is_deleted', 0)->first();

        if ($checkSetupTime) {
            $notify = array(
                'alert-type' =>  "error",
                'message' => trans('action.already_have_treatment_alert'),
            );
            return redirect()->back()->withInput()->with($notify);
        } else if (!$request->due_power && ($start_time == $time2 || $start_time == $time3 || ($time2 != null && $time2 == $time3))) {
            $notify = array(
                'alert-type' =>  "error",
                'message' => trans('action.already_have_treatment_alert'),
            );
            return redirect()->back()->withInput()->with($notify);
        }

        $data['segment'] = request()->segments();
        $data['ip_address'] = $this->getIPAddress();
        $data['adminid']  = getAuthID();
        $data['session_id'] = SessionIdManager::invalidateSessionId();

        if (isset($data['segment'][2]) && $data['segment'][2] == "remote") {
            $data['cart'] = getCartData();
            $data['isRemote'] = true;
            $this->add2Package(trans('action.autosave'), $data['adminid']);
            // app('App\Services\CronModule\CronSetupService')->storeRemoteCronSetup($data);
        }else if($request->cron_duplicate == "active"){
            $data['isDuplicate'] = true;
            // app('App\Services\CronModule\CronSetupService')->storeDuplicateCronSetup($data);
        }else{
            $data['isNormal'] = true;
            // app('App\Services\CronModule\CronSetupService')->storeNormalCronSetup($data);
        }

        if($request->cronlength < 3 || $request->due_power) {
            $this->storeCronSetupBackground($data,$data['adminid']);
        }
        else {
            $this->dispatch(new RemoteTreatementJob($data, $data['adminid']));
        }
        // $this->dispatch(new RemoteTreatementJob($data, $data['adminid']));
        // $this->storeCronSetupBackground($data,$data['adminid']);

        $notify = array(
            'alert-type' =>  "success",
            'message' => trans('action.cron_prgress'),
        );

        return redirect()->route('cron.cronSetup')->with($notify);
    }

    public function storeCronSetupBackground($request, $adminid){
        ini_set('max_execution_time','0');
        if (isset($request['isRemote']) && $request['isRemote']) {
            app('App\Services\CronModule\CronSetupService')->storeRemoteCronSetup($request);
        }elseif(isset($request['isDuplicate']) && $request['isDuplicate']){
            app('App\Services\CronModule\CronSetupService')->storeDuplicateCronSetup($request);
        }elseif(isset($request['isNormal']) && $request['isNormal']){
            app('App\Services\CronModule\CronSetupService')->storeNormalCronSetup($request);
        }

        $notify = array(
            'alert-type' =>  "success",
            'message' => __('action.cron_setup_successfully')
        );
        return json_encode(['success' => true, 'notify' => $notify]);
    }

    public function storeCronSetupOld(Request $request)
    {
        $data = $request->all();

        if (isset($data['due_power'])) {
            unset($data['start_email']);
            unset($data['end_email']);
        } else {
            unset($data['start_time_duepower']);
            unset($data['end_time_duepower']);
        }
        $start_time = date("Y-m-d H:i:s", strtotime($request->crondate));
        if (isset($data['due_power']))
            $start_time = date("Y-m-d H:i:s", strtotime($data['start_time_duepower']));

        $treatTimes = array();

        $checkTimeZone = DB::table('timezones')->where('timezone',$data['timezone_id'])->exists();
        if(!$checkTimeZone) $data['timezone_id'] = 'Europe/Vienna';

        if (!isset($data['due_power'])) {
            $curDate = date("Y-m-d", strtotime($request->crondate));
            $time2 = ($request->time2 == null) ? null : date("Y-m-d H:i:s", strtotime($curDate . " " . $request->time2));
            $time3 = ($request->time3 == null) ? null : date("Y-m-d H:i:s", strtotime($curDate . " " . $request->time3));
            array_push($treatTimes, $start_time, $time2, $time3);
        } else {
            array_push($treatTimes, $start_time);
        }

        $checkSetupTime = CronSetup::whereHas('cronsetuptimes', function ($query) use ($treatTimes) {
            $query->whereIn('start_time', $treatTimes)->where('status', 0);
        })->where('user_id', getAuthID())->where('selected_user', $request->cronuser)->where('is_deleted', 0)->first();

        if ($checkSetupTime) {
            $notify = array(
                'alert-type' =>  "error",
                'message' => trans('action.already_have_treatment_alert'),
            );
            return redirect()->back()->withInput()->with($notify);
        } else if (!$request->due_power && ($start_time == $time2 || $start_time == $time3 || ($time2 != null && $time2 == $time3))) {
            $notify = array(
                'alert-type' =>  "error",
                'message' => trans('action.already_have_treatment_alert'),
            );
            return redirect()->back()->withInput()->with($notify);
        }
        $data['timezone_id'] = $data['timezone_id'] ?? "Europe/Vienna";
        $data['segment'] = request()->segments();
        $data['ip_address'] = $this->getIPAddress();
        $data['adminid']  = getAuthID();

        if ($data['segment'][2] == "remote") {
            $data['cart'] = getCartData();
            $this->add2Package(trans('action.autosave'), $data['adminid']);
        }
        ini_set('max_execution_time','0');
        // dd($data, $request->all());
        if($request->cronlength < 3 || $request->due_power) $this->storeCronSetupBackground($data,$data['adminid']);
        else $this->dispatch(new RemoteTreatementJob($data, $data['adminid']));
        // $this->storeCronSetupBackground($data,$data['adminid']);

        $notify = array(
            'alert-type' =>  "success",
            'message' => trans('action.cron_prgress'),
        );

        return redirect()->route('cron.cronSetup')->with($notify);
    }

    public function storeCronSetupBackgroundOld($request, $adminid)
    {
        ini_set('max_execution_time','0');
        $data = array();
        $request = json_decode(json_encode($request), FALSE);
        $status = false;

        $data['type'] = 0;
        $data['adminid'] = $adminid;

        $data['therapist']      = User::with('cronsetting')->find($adminid);
        $data['subuser']        = User::with('useroption:user_id,pattern_switch')->find($request->cronuser);
        $data['start_date']     = Carbon::parse($request->crondate)->format('Y-m-d');
        $data['startdue_date']  = Carbon::parse($request->start_time_duepower)->format('Y-m-d');
        $data['today']          = Carbon::now()->format('Y-m-d');

        if ($request->segment[2] == "remote") {
            $data['cart'] = $request->cart;
            activityLogTrack(1, "at remote treatment via cart for {$data['subuser']->first_name} {$data['subuser']->last_name}", "Fernbehandlung Behandlungskorb für Benutzer {$data['subuser']->first_name} {$data['subuser']->last_name} hinzugefügt", 'Remote Treatment', 'remote_treatment', $data['adminid'], $request->ip_address);
        } else if ($request->cron_cart == "active") {
            $data['dup_ana'] = DB::table('cronsetup_analyses')->where('cron_setup_id', $request->duplicate_cronid)->get();
            $data['dup_sub'] = DB::table('cron_setup_submenus')->where('cron_setup_id', $request->duplicate_cronid)->get();
            activityLogTrack(1, "at remote treatment via duplicate(Ref. ID:{$request->duplicate_cronid}) for {$data['subuser']->first_name} {$data['subuser']->last_name}", "Fernbehandlung Duplikat(Ref. ID:{$request->duplicate_cronid}) für Benutzer {$data['subuser']->first_name} {$data['subuser']->last_name} hinzugefügt", 'Remote Treatment', 'remote_treatment', $data['adminid'], $request->ip_address);
        } else if ($request->due_power) {
            activityLogTrack(1, "at remote treatment duepower for {$data['subuser']->first_name} {$data['subuser']->last_name}", "Fernbehandlung duepower für Benutzer {$data['subuser']->first_name} {$data['subuser']->last_name} hinzugefügt", 'Remote Treatment', 'remote_treatment', $data['adminid'], $request->ip_address);
        } else {
            if($request->cron_duplicate == "active") {
                activityLogTrack(1, "at remote treatment via duplicate(Ref. ID:{$request->duplicate_cronid}) for {$data['subuser']->first_name} {$data['subuser']->last_name}", "Fernbehandlung Duplikat(Ref. ID:{$request->duplicate_cronid}) für Benutzer {$data['subuser']->first_name} {$data['subuser']->last_name} hinzugefügt", 'Remote Treatment', 'remote_treatment', $data['adminid'], $request->ip_address);
            } else {
                activityLogTrack(1, "at remote treatment normal for {$data['subuser']->first_name} {$data['subuser']->last_name}", "Fernbehandlung normale für Benutzer {$data['subuser']->first_name} {$data['subuser']->last_name} hinzugefügt", 'Remote Treatment', 'remote_treatment', $data['adminid'], $request->ip_address);
            }
        }
        // CronProcess instance
        $cronProcess = new CronSetupProcess();
        // cron setup
        $cronSetup = $cronProcess->StoreCronSetup($data, $request);
        $data['cronsetup'] = $cronSetup;
        $data['unique_id'] = $cronSetup->unique_id;
        // cron options
        $cronProcess->StoreCronOptions($data, $request);
        // cron submenus
        $cronSubmenus = $cronProcess->StoreCronSubmenus($data, $request);
        $data['submenus'] = $cronSubmenus;

        if ($request->preview == "active" && $request->preview_status == 0) {
            if ($request->segment[2] != "remote") {
                $data['preCauses'] = (!empty($request->causesid) && $request->causes) ? explode(",", $request->causesid) : null;
                $data['preMedium'] = (!empty($request->mediumid) && $request->medium) ? explode(",", $request->mediumid) : null;
                $data['preTipp']   = (!empty($request->tippid) && $request->tipp) ? explode(",", $request->tippid) : null;
                $data['preCnt']    = 0;
            }
        }

        // all pre-allocated values for setup
        $data['ranValue']         = randomValue($data['subuser']->id);
        $data['analyses']         = array();
        $data['biorythsystem']    = biorythVisibleDetails();
        $data['gsPrice']          = $data['biorythsystem'];
        $data['selectedTimezone'] = $request->timezone_id;
        if ($request->due_power == "") {
            for ($len = 0; $len < $request->cronlength; $len++) {
                $data['len'] = $len;
                for ($freq = 0; $freq < $request->cronfreq; $freq++) {
                    //__start time__//
                    $data['time']       = $request->crondate;
                    // $data['startTime']  = getConvertedDate($data['time'], $data['selectedTimezone'],$data['subuser']);
                    //__timezone__//
                    $data['timezone']   = $data['selectedTimezone'];
                    $data['freq']       = $freq;
                    // save cron time
                    $data['time_type']  = 0;
                    $cronTime           = $cronProcess->StoreCronTimes($data, $request);
                    $data['cron_time']  = $cronTime;

                    // store cron setup analyses
                    if ($request->segment[2] == "remote") {
                        $causeTime              = $cronProcess->StoreRemoteCronAnalyses($data, $request);
                        $data['causes_times']   = $causeTime;
                    } else if ($request->cron_cart == "active") {
                        $causeTime              = $cronProcess->StoreCronDuplicateAnalyses($data, $request);
                        $data['causes_times']   = $causeTime;
                    } else {
                        $causeTime              = $cronProcess->StoreNormalCronAnalyses($data, $request);

                        $data['causes_times']   = $causeTime['causeTimes'];
                        $data['red_day']        = $causeTime['red_day'];
                        $data['analyses']       = $causeTime['analyses'];
                        $data['copy_analyses']  = $causeTime['copy_analyses'];
                        $data['copy_red_day']   = $causeTime['copy_red_day'];
                    }

                    // update cron time
                    $data['time_type']  = 1;
                    $cronTime           = $cronProcess->StoreCronTimes($data, $request);
                    $data['cron_time']  = $cronTime;

                    $data['time_type']  = 0;
                }
            }
        } else {
            //__start time__//
            $data['time'] = $request->start_time_duepower;
            //__timezone__//
            $data['timezone'] = $data['selectedTimezone'];
            // save cron time
            $cronTime = $cronProcess->StoreCronTimes($data, $request);
            $data['cron_time'] = $cronTime;

            // store cron setup analyses
            if ($request->segment[2] == "remote") {
                $causeTime = $cronProcess->StoreRemoteCronAnalyses($data, $request);
                $data['causes_times'] = $causeTime;
            } else if ($request->cron_cart == "active") {
                $causeTime = $cronProcess->StoreCronDuplicateAnalyses($data, $request);
                $data['causes_times'] = $causeTime;
            } else {
                $causeTime = $cronProcess->StoreNormalCronAnalyses($data, $request);
                $data['causes_times'] = $causeTime['causeTimes'];
            }
        }

        $status = true;
        $update = DB::table('cron_setups')->where('id', $cronSetup->id)->update(['setup_status' => 0]);

        $notify = array(
            'alert-type' =>  "success",
            'message' => __('action.cron_setup_successfully')
        );
        return json_encode(['success' => $status, 'notify' => $notify]);
    }



    public function getCronInCart($cron_id, $crontime_id, $unique)
    {
        $crondetails = DB::table("cronsetup_times")
            ->join("cron_setups", "cron_setups.id", "=", "cronsetup_times.cronsetup_id")
            ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cronsetup_times.cronsetup_id")
            ->select("cronsetup_times.*", "cronsetup_times.status as crontime_status", "cron_setups.*", "cronsetup_options.causes", "cronsetup_options.medium", "cronsetup_options.tipp", "cronsetup_options.any_time_cron", "cronsetup_options.pdf_export", "cronsetup_options.ra_status", "cronsetup_options.pdf_status")
            ->where("cronsetup_times.id", $crontime_id)->first();

        $analyses   = DB::table("cronsetup_analyses")
            ->join("analyses", "analyses.id", "=", "cronsetup_analyses.analyse_id")
            ->select("cronsetup_analyses.*", "cronsetup_analyses.id as cronanalyse_id", "analyses.*")
            ->whereIn("type_status", [0,6])
            ->where("cronsetup_analyses.cron_setup_id", $cron_id)
            ->where("cronsetup_analyses.start_time", Carbon::parse($crondetails->start_time)->format('Y-m-d H:i'))->get();

        $causes = DB::table("cronsetup_analyses")
            ->join('causes', function ($join) {
                $join->on('causes.id', '=', 'cronsetup_analyses.causes')
                    ->orOn('causes.id', '=', 'cronsetup_analyses.medium')
                    ->orOn('causes.id', '=', 'cronsetup_analyses.tipp');
            })
            ->select("cronsetup_analyses.*", "cronsetup_analyses.id as cronanalyse_id", "causes.*")
            ->whereIn("type_status", [1, 2, 3, 4])
            ->where("cronsetup_analyses.cron_setup_id", $cron_id)
            ->where("cronsetup_analyses.start_time", Carbon::parse($crondetails->start_time)->format('Y-m-d H:i'))->get();

        $userdetails = User::find(getUserId());
        $data['creator_role'] = ($userdetails->boss_id != 0) ? User::find($userdetails->boss_id)->user_type : $userdetails->user_type;
        $data['userDetails'] = $userdetails;
        $data['status'] = 1;
        $data['pdf_export'] = $crondetails->pdf_export;
        $data['userid'] = $userdetails->id;
        $data['cronid'] = $cron_id;
        $data['ctimeid'] = $crontime_id;
        $data['cuniqueid'] = $crondetails->unique_id;

        if (count($analyses) > 0) {

            if ($crondetails->causes == 1 || $crondetails->medium == 1 || $crondetails->tipp == 1) {
                $cronCauses = array();
                $all_pro = array();
                foreach ($analyses as $cronana) {
                    $cronCauses[] = ($crondetails->causes == 1) ? $cronana->causes : 0;
                    $cronCauses[] = ($crondetails->medium == 1) ? $cronana->medium : 0;
                    $cronCauses[] = ($crondetails->tipp == 1) ? $cronana->tipp : 0;
                    $all_pro[] = $cronana->analyse_id;
                }

                if (!empty($cronCauses))
                    $all_causes = getAllCause($cronCauses);
                if (!empty($all_pro))
                    $all_proids = getProductByAnaId($all_pro);
            } else {

                $all_pro = array();
                foreach ($analyses as $cronana)
                    $all_pro[] = $cronana->analyse_id;

                if (!empty($all_pro))
                    $all_proids = getProductByAnaId($all_pro);
            }
        }

        if ($this->saveDataOnCart($userdetails, $analyses, $all_causes, $causes, $all_proids, 'wishlist')) {
            return response()->json([
                'success' => true,
                'message' => __('action.cart_save'),
                'cart_data' => getCartData()
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong'
            ]);
        }
    }

    public function updateCronOption($cron_setup_id)
    {
        $setting = app('App\Services\CronModule\CronControllerService')->updateCronOption($cron_setup_id);
        return redirect()->back();
    }

    public function showCronSetup()
    {
        $subuser = User::where('boss_id', getAuthID())->orderBy('first_name', 'ASC')->orWhere('id',getAuthID())->get();

        $cronsetting = CronSetting::with('preassignsubmenus', 'cronsettingoption')->where('user_id', getAuthID())->first();
        $pre_datetime = ($cronsetting->cronsettingoption->pre_time != null) ? Carbon::now()->format('Y-m-d') . " " . $cronsetting->cronsettingoption->pre_time : null;
        $pre_datetime = ($pre_datetime != null && $pre_datetime < Carbon::now()) ? Carbon::parse($pre_datetime)->addDays(1)->format('Y-m-d H:i') : $pre_datetime;
        $pre_datetime = ($pre_datetime != null) ? date_change_with_time($pre_datetime) : null;
        $defaultSetting = $cronsetting->cronsettingoption;
        $daysOfNextMonth = Carbon::now()->addMonth()->daysInMonth;

        $product = __getMenus();
        $productSub = $product->whereIn('status',[1,2])->where('sp',false)->map(function($prod){

            return [
                'proId' => $prod->id,
                'proName' => $prod->product_name,
                // 'submenu' => $prod->submenus,
                'submenus' => ($prod->status == 2 && $prod->submenus) ?
                $prod->submenus->map(function($submenu){
                    $submenu->product_id = $submenu->menu_id;
                    return $submenu;
                }) :
                $prod->submenus,
                'ownmenu' => ($prod->status == 2) ? 'yes':'no'
            ];
        });

        $timezones=DB::table('timezones')->select('id','name','timezone')->get();
        return view('Frontend.cron.cron_setup', ['cronsetting' => $cronsetting, 'default' => $defaultSetting, 'subuser' => $subuser, 'product' => $productSub, 'preselected' => $cronsetting->preassignsubmenus, 'pre_datetime' => $pre_datetime, 'nextMonth' => $daysOfNextMonth,'timezones'=>$timezones]);
    }

    public function add2Package($package_name, $adminid=null)
    {
        $package =  new CartPackage;
        $package->package_name = $package_name;
        $package->save();
        $package->users()->attach($adminid);

        $package_id =  $package->id;
        $ip_address = $this->getIPAddress();

        foreach (getCartData() as $key => $cart) {
            $__data = $cart->options->toArray();

            $cartContent = new CartContent();

            $cartContent->product_id  = $__data['productID'];
            $cartContent->analyse_id  = $__data['analysisID'];
            $cartContent->submenu_id  = $__data['submenu_id'];
            $cartContent->price       = $__data['price'];
            $cartContent->calculation = $__data['calculation'];
            $cartContent->male      = $__data['male'];
            $cartContent->heart     = $__data['heart'];
            $cartContent->name      = $__data['analysisName'];
            $cartContent->causes_id = is_object($__data['causes_id']) ? $__data['causes_id']->id : $__data['causes_id'];
            $cartContent->medium_id = is_object($__data['medium_id']) ? $__data['medium_id']->id : $__data['medium_id'];
            $cartContent->tipp_id   = is_object($__data['tipp_id']) ? $__data['tipp_id']->id : $__data['tipp_id'];
            $cartContent->color     = $__data['color'];
            $cartContent->type      = $__data['type'];
            $cartContent->others    = $__data['others'] ?? [];
            if (!empty($__data)) {
                if ($cartContent->save()) {
                    $cartContent->cartpackges()->attach($package_id);
                    Cart::remove($cart->rowId);
                }
            }
        }
        activityLogTrack(1, $package->package_name . ' at Cart Package', $package->package_name . ' im Behandlungskorb paket hinzugefügt', 'Cart Package', 'cart_package', $adminid, $ip_address); #AL
        $notify = array(
            'alert-type' =>  "success",
            'message' => __('action.cart_package_added_successfully')
        );
    }

    public function deleteCronSetup($id)
    {
        $cron = CronSetup::find($id);
        if (!$cron) {
            return response()->json([
                'success' => false,
                'message' => __('action.something_went_wrong')
            ]);
        }

        $treat_user = getUserByid($cron->selected_user);
        $cron->update(['is_deleted' => 1]);

        $cronmail = CronmailSetup::find($id);
        if ($cronmail) {
            $cronmail->update(['is_deleted' => 1]);
        }

        activityLogTrack(3, "at remote treatment for {$treat_user->first_name} {$treat_user->last_name}", "Fernbehandlung gestopped für {$treat_user->first_name} {$treat_user->last_name}", 'Remote Treatment', 'remote_treatment');

        return response()->json([
            'success' => true,
            'id' => $id,
            'message' => __('action.item_moved_to_trash')
        ]);
    }

    public function deleteCronTime($id)
    {
        $cronTime = CronsetupTimes::find($id);
        $cronmailTime = CronmailSetupTimes::find($id);
        if ($cronTime) {
            DB::table('cronsetup_analyses')->where('cron_setup_id', $cronTime->cronsetup_id)->where('start_time', $cronTime->start_time)->delete();
            $cronTime->delete();
            if ($cronmailTime != null) {
                DB::table('cronmail_setup_analyses')->where('cron_setup_id', $cronmailTime->cronsetup_id)->where('start_time', $cronmailTime->start_time)->delete();
                $cronmailTime->delete();
            }

            return response()->json([
                'success' => true,
                'id' => $id,
                'message' => __('action.removed_successfully')
            ]);
        } else {
            return response()->json([
                'success' => true,
                'message' => __('action.something_went_wrong')
            ]);
        }
    }

    public function stop($id)
    {
        $cron = CronSetup::find($id);
        $cronmail = CronmailSetup::find($id);
        $treat_user = getUserByid($cron->selected_user);

        if ($cron) {
            if ($cron->status == 0) {
                $cron->update(['status' => 1]);
                if ($cronmail != null) {
                    $cronmail->update(['status' => 1]);
                }
                activityLogTrack(4, "Remote treatment stopped for {$treat_user->first_name} {$treat_user->last_name}", "Fernbehandlung unterbrochen für {$treat_user->first_name} {$treat_user->last_name}", 'Remote Treatment', 'remote_treatment'); #AL
            } else {
                $cron->update(['status' => 0]);
                if ($cronmail != null) {
                    $cronmail->update(['status' => 0]);
                }
                activityLogTrack(4, "Remote treatment activated for {$treat_user->first_name} {$treat_user->last_name}", "Fernbehandlung aktiviert für {$treat_user->first_name} {$treat_user->last_name}", 'Remote Treatment', 'remote_treatment'); #AL
            }

            return response()->json([
                'success' => true,
                'id' => $id,
                'message' => ($cron->status == 1) ? __('action.stop_remote_treat') : __('action.active_remote_treat'),
                'button' => ($cron->status == 1) ? __('action.start_cron') : __('action.stop_cron'),
                'color' => ($cron->status == 1) ? 'success' : 'danger',
                'title' => ($cron->status == 1) ? __('action.breakupTitle') : __('action.breakupTitle2'),
                'msg' => ($cron->status == 1) ? __('action.breakupContent') : __('action.breakupContent2'),
            ]);
        } else {
            return response()->json([
                'success' => true,
                'message' => __('action.something_went_wrong')
            ]);
        }
    }

    public function cronTrashRestore($id)
    {
        $cron = CronSetup::find($id);
        if ($cron && ($cron->is_deleted)) {
            $cron->update(['is_deleted' => 0]);
            activityLogTrack(4, 'restore at Temote Rreatment from Recycle Bin', 'Fernbehandlung wiederhergestellt', 'Remote Treatment', 'remote_treatment'); #AL
            return response()->json([
                'success' => true,
                'message' => __('action.ttem_restored_successfully')
            ]);
        } else {
            return response()->json([
                'success' => true,
                'message' => __('action.something_went_wrong')
            ]);
        }
    }

    public function cronTrash(Request $request)
    {
        if ($request->ajax()) {
            return app('App\Services\CronModule\CronControllerService')->getTrush($request);
        }
        return view('Frontend.cron.cron_trash');
    }

    #Remote Analysis with analysis data from Cart
    public function remoteAnalysis()
    {
        $cart_content = getCartData();
        $cronsetting = CronSetting::where('user_id', getAuthID())->first();
        $default = CronsettingOption::where('cron_setting_id', $cronsetting->id)->first();
        $subusers = User::where('boss_id', getAuthID())->orWhere('id',getAuthId())->orderBy('first_name', 'ASC')->get();

        $pre_datetime = ($default->pre_time != null) ? Carbon::now()->format('Y-m-d') . " " . $default->pre_time : null;
        $pre_datetime = ($pre_datetime != null && $pre_datetime < Carbon::now()) ? Carbon::parse($pre_datetime)->addDays(1)->format('Y-m-d H:i') : $pre_datetime;
        $pre_datetime = ($pre_datetime != null) ? date_change_with_time($pre_datetime) : null;
        $timezones=Timezone::all();
        return view('Frontend.cron.remote_analysis', compact('cart_content', 'default', 'subusers', 'cronsetting', 'pre_datetime','timezones'));
    }


    public function cronPreview(Request $request)
    {
        $data['admin_id'] = getAuthID();
        $data['cron_setting'] = CronSetting::with('cronsettingoption')->where('user_id', $data['admin_id'])->first();
        $data['user'] = userFilterid($request->cronuser);
        // $data['user'] = Auth::user()->useroption;
        $data['ran_value'] = randomValue($data['user']->id);
        $data['bioryth'] = biorythVisibleDetails();

        $anaPreview = null;
        $results = null;
        $submenus = null;
        $tippid = null;
        $startTime = ($request->crondate == null) ? date_change_with_time(date('d-m-Y H:i', strtotime($request->start_time_duepower))) : date_change_with_time(date('d-m-Y H:i', strtotime($request->crondate)));
        $preview = $data['cron_setting']->cronsettingoption->preview_status;

        // CronPreview Instance
        $cronPreview = new CronPreviewProcess();

        if ($request->type == "remote") {
            $cart = getCartData();
            $anaPreview = $cronPreview->cartCronPreview($cart, $request);
        } else if ($request->cron_cart == "active") {
            $cronsetup = CronSetup::with("analyses", "cronsubmenu")->where('id', $request->duplicate_cronid)->first();
            $submenus = $cronsetup->cronsubmenu;
            $anaPreview = $cronPreview->duplicateCronPreview($cronsetup, $request);
        } else {
            $results = $cronPreview->normalCronPreview($data, $request);
            $anaPreview = $results['anaPreview'];
            $causesid = $results['causesid'];
            $mediumid = $results['mediumid'];
            $tippid = $results['tippid'];
        }

        $ures = collect($data['user']->toArray())->only(['first_name', 'last_name', 'gebdatum', 'gebort', 'cron_email', 'gender'])->all();
        $response = $request->except(['_token', '_', 'cronuser']);
        $others = ['preview' => $preview, 'start_time' => $startTime, 'dob' => date_change($ures['gebdatum'])];
        $predata = [
            'cronuser'          => $request->cronuser,
            'submenu'           => $request->cronsubmenu,
            'start_due_power'   => $request->start_time_duepower,
            'end_due_power'     => $request->end_time_duepower,
            'crondate'          => $request->crondate,
            'time2'             => $request->time2,
            'time3'             => $request->time3,
            'cronlength'        => $request->cronlength,
            'cronday'           => $request->cronday,
            'cronfreq'          => $request->cronfreq,
            'topic'             => $request->topic,
            'note'              => $request->note,
            'client_note'       => $request->client_note,
            'img_src'           => ($ures['gender'] == 2) ? asset('images/female.jpg') : asset('images/avatar.png'),
            'timezone_id'       => $request->timezone_id,
            'cal_type'          => $request->cal_type
        ];

        return response()->json(compact('response', 'predata', 'ures', 'anaPreview', 'causesid', 'mediumid', 'tippid', 'submenus', 'others'));
    }
    public function cronShowAnalysis()
    {
        $crontimeid = request()->crontimeid;
        $crondetails = CronsetupTimes::where("id", $crontimeid)
            ->with(['cronsetup' => function($q){
                $q->with(['cronsetup_option','user.useroption:user_id,custom_link,pdf_logo,view_pdf','user.smtp']);
            }])->first();
        $response = app('App\Services\CronModule\CronControllerService')->cronShowAnalysis($crondetails);

        $allSubmenus = $response['allSubmenus'];
        $topicnames = $response['allTopics'];

        if (!empty($allSubmenus) || !empty($topicnames)) $status = true;
        else $status = false;

        return response()->json(['success' => $status, 'allsubmenus' => $allSubmenus, 'causes' => $crondetails->cronsetup->cronsetup_option->causes, 'medium' => $crondetails->cronsetup->cronsetup_option->medium, 'tipp' => $crondetails->cronsetup->cronsetup_option->tipp, 'topicnames' => $topicnames]);

    }


    public function cronShowAnalysis1()
    {
        $crontimeid = request()->crontimeid;

        $crondetails = DB::table('cronsetup_times')
            ->join("cron_setups", "cron_setups.id", "=", "cronsetup_times.cronsetup_id")
            ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cronsetup_times.cronsetup_id")
            ->select("cronsetup_times.*", "cron_setups.*", "cron_setups.status as cron_status", "cronsetup_options.causes", "cronsetup_options.medium", "cronsetup_options.tipp")
            ->where("cronsetup_times.id", $crontimeid)->first();

        $analyses = DB::table("cronsetup_analyses")
            ->where("cron_setup_id", $crondetails->cronsetup_id)
            ->where("start_time", $crondetails->start_time)
            ->orWhere("start_time", Carbon::parse($crondetails->start_time)->format('Y-m-d H:i'))
            // ->limit(10)
            ->get();

        $subMenuQuery = DB::table('cron_setup_submenus')->where('cron_setup_id', $crondetails->cronsetup_id)->get();

        $tmp_cau = [];

        if ($analyses >= 0) {
            foreach ($analyses as $aval) {
                $ana_ids[] = $aval->analyse_id;

                if ($aval->type_status == 0) {
                    if ($aval->causes != 0 && $crondetails->causes == 1) $causes[] = $aval->causes;
                    if ($aval->medium != 0 && $crondetails->medium == 1) $causes[] = $aval->medium;
                    if ($aval->tipp != 0 && $crondetails->tipp == 1) $causes[] = $aval->tipp;
                }

                if ($aval->type_status == 1) $causes[] = $aval->causes;
                else if ($aval->type_status == 2) $causes[] = $aval->medium;
                else if ($aval->type_status == 3) $causes[] = $aval->tipp;
                else if ($aval->type_status == 4) $causes[] = $aval->causes;
                else if ($aval->type_status == 5) $causes[] = $aval->causes;
            }
        }

        if (!empty($ana_ids)) {
            // $all_analyses = DB::table("analyses")->whereIn('id', $ana_ids)->get();
            $all_analyses = Analyse::whereIn('id', $ana_ids)->get();
            foreach ($all_analyses as $aval)
                $tmp_ana[$aval->id] = $aval;
        }

        if (!empty($causes)) {
            // $all_causes = DB::table("causes")->whereIn('id', $causes)->get();
            $all_causes = Causes::whereIn('id', $causes)->get();
            foreach ($all_causes as $caval)
                $tmp_cau[$caval->id] = $caval;
        }

        $allSubmenus = array();
        $topicnames = array();
        $langId =app()->getLocale()??'de';
        if (count($analyses) >= 0) {
            if (count($subMenuQuery) > 0) {
                foreach ($subMenuQuery as $keys => $subm) {

                    $allana = array();
                    $anaQuery = "";
                    $subName = "";

                    foreach ($analyses as $key => $anaval) {

                        if ($anaval->color == "done") continue;

                        $tmpana = array();
                        $causesNames = array();
                        $name = "";

                        if (!empty($tmp_ana) && $anaval->type_status == 0) {
                            if (array_key_exists($anaval->analyse_id, $tmp_ana)) {

                                if ($subm->submenu_type == 0) {
                                    $anaQuery = DB::table('analyse_pools')
                                        ->join('pool_submenus', "pool_submenus.pool_id", "=", "analyse_pools.pool_id")
                                        ->join('submenus', "submenus.id", "=", "pool_submenus.submenu_id")
                                        ->where('pool_submenus.submenu_id', $subm->submenu_id)
                                        ->where('analyse_pools.analyse_id', $anaval->analyse_id)->first();

                                    if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                                    if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                                    else $subName = $anaQuery->menu_name;
                                    $anaval->color = "done";
                                } else if ($subm->submenu_type == 1) {
                                    $anaQuery = DB::table('analyse_user_submenus')
                                        ->join('user_submenus', "user_submenus.id", "=", "analyse_user_submenus.user_submenu_id")
                                        ->where('user_submenus.id', $subm->submenu_id)
                                        ->where('analyse_user_submenus.analyse_id', $anaval->analyse_id)->first();

                                    if ($anaQuery->user_submenu_id != $subm->submenu_id) continue;
                                    $subName = $anaQuery->name;
                                    $anaval->color = "done";
                                }

                                $analyses_details = $tmp_ana[$anaval->analyse_id];
                                $name = $analyses_details->name;
                            }
                        }

                        if ($anaval->type_status == 0) {
                            if ($crondetails->causes == 1 && count($tmp_cau) > 0) if (array_key_exists($anaval->causes, $tmp_cau)) $causesNames[0] = ($crondetails->causes == 1) ? $tmp_cau[$anaval->causes] : "";
                            if ($crondetails->medium == 1 && count($tmp_cau) > 0) if (array_key_exists($anaval->medium, $tmp_cau)) $causesNames[1] = ($crondetails->medium == 1) ? $tmp_cau[$anaval->medium] : "";
                            if ($crondetails->tipp == 1 && count($tmp_cau) > 0) if (array_key_exists($anaval->tipp, $tmp_cau))   $causesNames[2] = ($crondetails->tipp == 1) ? $tmp_cau[$anaval->tipp] : "";
                        }
                        // if ($anaval->type_status == 0) {
                        //     $fields = [
                        //         'causes' => 0,
                        //         'medium' => 1,
                        //         'tipp' => 2,
                        //     ];

                        //     foreach ($fields as $field => $index) {
                        //         if ($crondetails->$field != 1 || count($tmp_cau) <= 0 || !array_key_exists($anaval->$field, $tmp_cau)) {
                        //             continue;
                        //         }
                        //         $causesNames[$index] = $tmp_cau[$anaval->$field];
                        //     }
                        // }


                        if ($anaval->type_status == 1) {
                            $causesNames[0] = $tmp_cau[$anaval->causes];
                            $anaQuery = DB::table('causes')
                                ->join('group_causes', "group_causes.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_causes.submenu_id")
                                ->where('group_causes.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->causes)->first();

                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 2) {
                            $causesNames[1] = $tmp_cau[$anaval->medium];
                            $anaQuery = DB::table('causes')
                                ->join('group_mediums', "group_mediums.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_mediums.submenu_id")
                                ->where('group_mediums.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->medium)->first();
                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 3) {
                            $causesNames[2] = $tmp_cau[$anaval->tipp];
                            $anaQuery = DB::table('causes')
                                ->join('group_tips', "group_tips.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_tips.submenu_id")
                                ->where('group_tips.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->tipp)->first();
                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 4) {
                            $causesNames[3] = $tmp_cau[$anaval->causes];
                            $anaQuery = DB::table('causes')
                                ->join('group_einfluss', "group_einfluss.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_einfluss.submenu_id")
                                ->where('group_einfluss.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->causes)->first();

                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 5) {
                            $causesNames[4] = $tmp_cau[$anaval->causes];
                            $anaQuery = DB::table('causes')
                                ->join('group_focus', "group_focus.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_focus.submenu_id")
                                ->where('group_focus.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->causes)->first();
                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 6) {
                            $tmpTopic = array();

                            if ($anaval->color != "done") {
                                $tmpTopic["topic"] = $anaval->topic;
                                $tmpTopic["time"] = $anaval->time;
                                $topicnames[] = $tmpTopic;
                                $anaval->color = "done";
                            }
                            continue;
                        }

                        $tmpana = [
                            'ana_name' => $name,
                            'ana_val' => $anaval->time,
                            'calculation' => $anaval->calculation,
                            'cau_title' => ($causesNames[0] != "") ? $causesNames[0]->title : "",
                            'cau_price' => ($causesNames[0] != "") ? $anaval->cau_price : "",
                            'mid_title' => ($causesNames[1] != "") ? $causesNames[1]->title : "",
                            'mid_price' => ($causesNames[1] != "") ? $anaval->mid_price : "",
                            'tip_title' => ($causesNames[2] != "") ? $causesNames[2]->title : "",
                            'tip_price' => ($causesNames[2] != "") ? $anaval->tipp_price : "",
                            'ein_title' => ($causesNames[3] != "") ? $causesNames[3]->title : "",
                            'ein_price' => ($causesNames[3] != "") ? $anaval->time : "",
                            'foc_title' => ($causesNames[4] != "") ? $causesNames[4]->title : "",
                            'foc_price' => ($causesNames[4] != "") ? $anaval->time : "",
                            'type' => $anaval->type_status,
                        ];

                        $allana[] = $tmpana;
                    }
                    if(!empty($allana)){
                        $tmpSubmenus['submenu_name'] = $subName;
                        $tmpSubmenus['analyses'] = $allana;

                        $allSubmenus[] = $tmpSubmenus;
                    }
                }
            } else {
                $topics = DB::table("cronsetup_analyses")
                    ->where(["cron_setup_id" => $crondetails->cronsetup_id, "start_time" => $crondetails->start_time])
                    ->where('type_status', 6)->get();

                if (count($topics) > 0) {
                    foreach ($topics as $topic) {
                        $tmpTopic = array();
                        $tmpTopic["topic"] = $topic->topic;
                        $tmpTopic["time"] = $topic->time;
                        $topicnames[] = $tmpTopic;
                    }
                }
            }
        }

        if (!empty($allSubmenus) || !empty($topicnames)) $status = true;
        else $status = false;

        return response()->json(['success' => $status, 'allsubmenus' => $allSubmenus, 'causes' => $crondetails->causes, 'medium' => $crondetails->medium, 'tipp' => $crondetails->tipp, 'topicnames' => $topicnames]);
    }

    public function cronUpdateEmail()
    {
        $email = request()->email;
        $cronid = request()->cronid;

        $cron = CronSetup::find($cronid);
        $cronmail = CronmailSetup::find($cronid);
        $user = User::find($cron->selected_user);
        //Update cronsetup_times table when its blocked by valid email check webhook.
        if (!hash_equals(($cron->email ?? ''), ($email ?? ''))) {
            cronsetupTimesUpdate((int)$cronid);
        }
        $cron->update(array('email' => $email));
        if ($cronmail != null) {
            $cronmail->update(array('email' => $email));
        }
        activityLogTrack(2, "{$user->first_name} {$user->last_name} Email at Remote Treatment", "{$user->first_name} {$user->last_name} E-Mail geändert", 'Remote Treatment', 'remote_treatment'); #AL
        return response()->json(['status' => true, 'message'   =>  'E-Mail erfolgreich aktualisiert']);
    }

    public function stopEmail()
    {
        $cronid = request()->cronid;
        $cron = CronSetup::find($cronid);
        $cronmail = CronmailSetup::find($cronid);
        $treat_user = getUserByid($cron->selected_user);

        if ($cron) {
            if ($cron->is_stop == 0) {
                $cron->update(array('is_stop' => 1));
                if ($cronmail != null) {
                    $cronmail->update(array('is_stop' => 1));
                }
                activityLogTrack(4, 'Stopped remote treatment sending email for ' . $treat_user->first_name . ' ' . $treat_user->last_name, 'Fernbehandlung E-Mail stopeen für ' . $treat_user->first_name . ' ' . $treat_user->last_name, 'Remote Treatment', 'remote_treatment'); #AL
            } else {
                $cron->update(array('is_stop' => 0));
                if ($cronmail != null) {
                    $cronmail->update(array('is_stop' => 0));
                }
                activityLogTrack(4, 'Activated remote treatment sending email for ' . $treat_user->first_name . ' ' . $treat_user->last_name, 'Fernbehandlung E-Mail Versand aktiviert für ' . $treat_user->first_name . ' ' . $treat_user->last_name, 'Remote Treatment', 'remote_treatment'); #AL
            }
        }
        $msg = ($cron->is_stop == 1) ? __('action.stop_remote_email') :  __('action.active_remote_email');
        $button = ($cron->is_stop == 1) ? __('action.start_sending_email') :  __('action.stop_sending_email');
        $color = ($cron->is_stop == 1) ? 'success' :  'warning';
        return response()->json(['success' => true, 'message' => $msg, 'button' => $button, 'color' => $color]);
    }

    public function oldcron()
    {
        return view('Frontend.cron.oldcron');
    }

    function openTreatmentAnyTime($cron_id, $crontime_id, $unique){
        $response = app('App\Services\CronModule\CronControllerService')->cronTreatmentAnyTime($cron_id, $crontime_id, $unique);
        // DB::table('cronsetup_times')->where('id', $crontime_id)->update(['email_open_status' => true]);
        switch ($response['status']) {
            case '1':
                return view('Frontend.cron.no_treatment', ['data' => $response]);
                break;
            case '2':
                return view('Frontend.cron.countdown', ['data' => $response]);
                break;
            case '3':
                return view('Frontend.cron.treat_finish', ['data' => $response]);
                break;
            case '4':
                return view('Frontend.treat.cron_due_treatment', ['data' => $response]);
                // return view('Frontend.treat.cron_due_treatment', ['data' => $response]);
                break;
            case '5':
                return view('Frontend.treat.cron_single_treatment', ['data' => $response]);
                break;

            default:
                return back()->with('error', __('action.no_access1'));
                break;
        }
    }

    public function openTreatmentByEmail($cron_id, $crontime_id, $unique)
    {
        /**
         * Distroy old cart record of
         */
        #destroy cart
        Cart::instance("CronEMailCart")->destroy();
        $response = app('App\Services\CronModule\CronControllerService')->cronTreatment($cron_id, $crontime_id, $unique);
        // dd($response);
        DB::table('cronsetup_times')->where('id', $crontime_id)->update(['email_open_status' => true]);
        switch ($response['status']) {
            case '1':
                return view('Frontend.cron.no_treatment', ['data' => $response]);
                break;
            case '2':
                return view('Frontend.cron.countdown', ['data' => $response]);
                break;
            case '3':
                return view('Frontend.cron.treat_finish', ['data' => $response]);
                break;
            case '4':
                return view('Frontend.treat.cron_due_treatment', ['data' => $response]);
                // return view('Frontend.treat.cron_due_treatment', ['data' => $response]);
                break;
            case '5':
                return view('Frontend.treat.cron_single_treatment', ['data' => $response]);
                break;

            default:
                return back()->with('error', __('action.no_access1'));
                break;
        }

        dd($response);
        $crondetails = DB::table("cronsetup_times")
            ->join("cron_setups", "cron_setups.id", "=", "cronsetup_times.cronsetup_id")
            ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cronsetup_times.cronsetup_id")
            ->select("cronsetup_times.*", "cronsetup_times.status as crontime_status", "cron_setups.*", "cronsetup_options.causes", "cronsetup_options.medium", "cronsetup_options.tipp", "cronsetup_options.any_time_cron", "cronsetup_options.pdf_export", "cronsetup_options.ra_status", "cronsetup_options.pdf_status")
            ->where("cronsetup_times.id", $crontime_id)->first();

        if ($crondetails->unique_id != $unique) return back()->with('error', __('action.no_access1'));
        elseif ($crondetails->red_day == 1) return view('Frontend.cron.no_treatment');

        // $all_times  = DB::table("cronsetup_times")->where("cronsetup_id", $cron_id)->get();

        $userdetails = User::find($crondetails->selected_user);
        #language show as user language
        setServerLocal(Language::find($userdetails->language_id)->short_code);

        $analyses_name = (App::getLocale() == '' || App::getLocale() == 'de') ? 'analyses' : App::getLocale().'_analyses';

        $analyses   = DB::table("cronsetup_analyses")
            ->join($analyses_name, $analyses_name.".id", "=", "cronsetup_analyses.analyse_id")
            ->select("cronsetup_analyses.*", "cronsetup_analyses.id as cronanalyse_id", $analyses_name.".*")
            ->where("type_status", 0)
            ->where("cronsetup_analyses.cron_setup_id", $cron_id)
            ->where("cronsetup_analyses.start_time", $crondetails->start_time)
            ->get();

        $causes_name = (App::getLocale() == '' || App::getLocale() == 'de') ? 'causes' : App::getLocale().'_causes';

        $causes = DB::table("cronsetup_analyses")
            ->join($causes_name, function ($join) use($causes_name){
                $join->on($causes_name.'.id', '=', 'cronsetup_analyses.causes')
                    ->orOn($causes_name.'.id', '=', 'cronsetup_analyses.medium')
                    ->orOn($causes_name.'.id', '=', 'cronsetup_analyses.tipp');
            })
            ->select("cronsetup_analyses.*", "cronsetup_analyses.id as cronanalyse_id", $causes_name.".*")
            ->whereIn("type_status", [1, 2, 3, 4, 5])
            ->where("cronsetup_analyses.cron_setup_id", $cron_id)
            ->where("cronsetup_analyses.start_time", $crondetails->start_time)->get();

        $topics = DB::table("cronsetup_analyses")
            ->where("type_status", 6)
            ->where("cron_setup_id", $cron_id)
            ->where("start_time", $crondetails->start_time)->get();

        // dd($topics,$causes,$analyses);
        // $userdetails = User::find($crondetails->selected_user);
        $data['creator_role'] = ($userdetails->boss_id != 0) ? User::find($userdetails->boss_id)->user_type : $userdetails->user_type;
        #language show as user language
        // setServerLocal(Language::find($userdetails->language_id)->short_code);

        $data['status']  = 1;
        $data['pdf_export'] = $crondetails->pdf_export;
        $data['userid']  = $userdetails->id;
        $data['cronid']  = $cron_id;
        $data['ctimeid'] = $crontime_id;
        $data['cuniqueid'] = $unique;
        $anytime = date('Y-m-d', strtotime($crondetails->end_time));
        $today   = date("Y-m-d");

        $lastTreatment = DB::table("cronsetup_times")->select(['end_time'])->where("cronsetup_id", $cron_id)->latest()->first();

        #time details
        $timezone = "Europe/Vienna"; #"Asia/Dhaka";
        $startTime = $crondetails->start_time;
        $endTime = $crondetails->end_time;

        #time differences
        $startDiff = $this->getTimeDifference($startTime, $timezone);

        // $endDiff   = $this->getTimeDifference($endTime, $timezone);
        $endDiff   = 1;

        // if ($endDiff <= 0 && $crondetails->any_time_cron) return view('Frontend.cron.treat_finish', $data);

        $lastDiff  = $this->getTimeDifference($lastTreatment->end_time, $timezone);

        if (count($analyses) > 0) {
            if ($crondetails->causes == 1 || $crondetails->medium == 1 || $crondetails->tipp == 1) {
                $causesIds = array();
                $anaIds = array();

                ($crondetails->causes == 1) ? $causesIds = $analyses->pluck('causes')->toArray() : 0;
                ($crondetails->medium == 1) ?  $causesIds = array_merge($causesIds,$analyses->pluck('medium')->toArray()) : 0;
                ($crondetails->tipp == 1) ?  $causesIds = array_merge($causesIds,$analyses->pluck('tipp')->toArray()) : 0;
                $anaIds = $analyses->pluck('analyse_id')->toArray();

                if (!empty($causesIds)){
                    $causesIds = array_unique($causesIds);
                    $all_causes = array();
                    DB::table("causes")->whereIn("id", $causesIds)->get(['id', 'title', 'description'])->map(function ($cause) use (&$all_causes) {
                        $all_causes[$cause->id] = $cause;
                    });
                }
                if (!empty($anaIds)){
                    $anaIds = array_unique($anaIds);
                    $proIds = [];
                    DB::table("analyse_pools")
                        ->join("pool_submenus", "pool_submenus.pool_id", "=", "analyse_pools.pool_id")
                        ->join("submenus", "submenus.id", "=", "pool_submenus.submenu_id")
                        ->whereIn("analyse_pools.analyse_id", $anaIds)
                        ->groupBy('product_id')->get(['analyse_id','product_id'])->each(function ($ana) use (&$proIds) {
                            $proIds[$ana->analyse_id] = $ana->product_id;
                    });
                }
            } else {
                $anaIds = $analyses->pluck('analyse_id')->toArray();

                if (!empty($anaIds)) DB::table("analyse_pools")
                        ->join("pool_submenus", "pool_submenus.pool_id", "=", "analyse_pools.pool_id")
                        ->join("submenus", "submenus.id", "=", "pool_submenus.submenu_id")
                        ->whereIn("analyse_pools.analyse_id", $anaIds)
                        ->groupBy('product_id')->get(['analyse_id','product_id'])->each(function ($ana) use (&$proIds) {
                                $proIds[$ana->analyse_id] = $ana->product_id;
                        });
            }
            $bodyImages = $mentalImages = $globalImages = collect();
            if(!empty($proIds)) {
                $bodyImages = DB::table('body_images')->whereIn('analyse_id', $proIds)->get(['analyse_id', 'image']);
                $mentalImages = DB::table('mental_images')->whereIn('analyse_id', $proIds)->get(['analyse_id', 'image']);
                $globalImages = Analyse::whereIn('id', $proIds)->get(['id', 'desc_image']);
            }
        }
        $data['userDetails'] = $userdetails;
        $this->saveDataOnCart($userdetails, $analyses, $all_causes, $causes, $proIds);
        $data['cartDatas'] = $this->getCronEmailCart($userdetails->id);

        if ($lastDiff <= 0)
            DB::table("cron_setups")->where("id", $cron_id)->update(["status" => 1, "open_status" => 1]);


        if ($startDiff > 0 && $crondetails->any_time_cron != 1) {
            $data = [
                'startTime' => $startTime,
                'endTime' => $endTime,
                'zone' => $timezone,
                'userDetails' => $userdetails
            ];
            return view('Frontend.cron.countdown', ['data' => $data]);
        } elseif ($crondetails->any_time_cron == 1 && $anytime >= $today) {
            if ($crondetails->length == 0) {

                $timeFirst  = strtotime($startTime);
                $timeSecond = strtotime($endTime);
                $timeinSeconds = $timeSecond - $timeFirst;
                return view('Frontend.cron.due_treatment', ['data' => $data, 'analyses' => $analyses, 'all_causes' => $all_causes, 'causes_types' => $causes, 'all_proids' => $proIds,  'causesop' => $crondetails->causes, 'mediumop' => $crondetails->medium, 'tippop' => $crondetails->tipp, 'times' => $timeinSeconds, 'topics' => $topics]);
            } else {

                return view('Frontend.cron.single_treatment', ['data' => $data, 'analyses' => $analyses, 'all_causes' => $all_causes, 'causes_types' => $causes, 'all_proids' => $proIds, 'causesop' => $crondetails->causes, 'mediumop' => $crondetails->medium, 'tippop' => $crondetails->tipp, 'topics' => $topics, 'ra_status' => $crondetails->ra_status, 'pdf_status' => $crondetails->pdf_status, 'bodyImages' => $bodyImages, 'mentalImages' => $mentalImages, 'globalImages' => $globalImages]);
            }
        } elseif ($endDiff <= 0) {
            return view('Frontend.cron.treat_finish', $data);
        } else {
            if ($crondetails->length == 0) {

                $timeFirst  = strtotime($startTime);
                $timeSecond = strtotime($endTime);
                $timeinSeconds = $timeSecond - $timeFirst;
                return view('Frontend.cron.due_treatment', ['data' => $data, 'analyses' => $analyses, 'all_causes' => $all_causes, 'causes_types' => $causes, 'all_proids' => $proIds, 'causesop' => $crondetails->causes, 'mediumop' => $crondetails->medium, 'tippop' => $crondetails->tipp, 'times' => $timeinSeconds, 'topics' => $topics]);
            } else {
                return view('Frontend.cron.single_treatment', ['data' => $data, 'analyses' => $analyses, 'all_causes' => $all_causes, 'causes_types' => $causes, 'all_proids' => $proIds, 'causesop' => $crondetails->causes, 'mediumop' => $crondetails->medium, 'tippop' => $crondetails->tipp, 'topics' => $topics, 'ra_status' => $crondetails->ra_status, 'pdf_status' => $crondetails->pdf_status, 'bodyImages' => $bodyImages, 'mentalImages' => $mentalImages, 'globalImages' => $globalImages]);
            }
        }
    }

    public function openTreatment($cron_id, $crontime_id, $unique)
    {
        #destroy cart
        Cart::instance("CronEMailCart")->destroy();
        $response = app('App\Services\CronModule\CronControllerService')->cronTreatment($cron_id, $crontime_id, $unique);
        // dd($response);
        switch ($response['status']) {
            case '1':
                return view('Frontend.cron.no_treatment', ['data' => $response]);
                break;
            case '2':
                return view('Frontend.cron.countdown', ['data' => $response]);
                break;
            case '3':
                return view('Frontend.cron.treat_finish', ['data' => $response]);
                break;
            case '4':
                return view('Frontend.treat.cron_due_treatment', ['data' => $response]);
                // return view('Frontend.treat.cron_due_treatment', ['data' => $response]);
                break;
            case '5':
                return view('Frontend.treat.cron_single_treatment', ['data' => $response]);
                break;

            default:
                return back()->with('error', __('action.no_access1'));
                break;
        }
        dd("END");
        $crondetails = DB::table("cronsetup_times")
            ->join("cron_setups", "cron_setups.id", "=", "cronsetup_times.cronsetup_id")
            ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cronsetup_times.cronsetup_id")
            ->select("cronsetup_times.*", "cronsetup_times.status as crontime_status", "cron_setups.*", "cronsetup_options.causes", "cronsetup_options.medium", "cronsetup_options.tipp", "cronsetup_options.any_time_cron", "cronsetup_options.pdf_export", "cronsetup_options.ra_status", "cronsetup_options.pdf_status")
            ->where("cronsetup_times.id", $crontime_id)->first();

        if ($crondetails->unique_id != $unique) return back()->with('error', __('action.no_access1'));
        elseif ($crondetails->red_day == 1) return view('Frontend.cron.no_treatment');

        $all_times  = DB::table("cronsetup_times")->where("cronsetup_id", $cron_id)->get();

        $userdetails = User::find($crondetails->selected_user);
        #language show as user language
        setServerLocal(Language::find($userdetails->language_id)->short_code);

        $analyses_name = (App::getLocale() == '' || App::getLocale() == 'de') ? 'analyses' : App::getLocale().'_analyses';

        $analyses   = DB::table("cronsetup_analyses")
            ->join($analyses_name, $analyses_name.".id", "=", "cronsetup_analyses.analyse_id")
            ->select("cronsetup_analyses.*", "cronsetup_analyses.id as cronanalyse_id", $analyses_name.".*")
            ->where("type_status", 0)
            ->where("cronsetup_analyses.cron_setup_id", $cron_id)
            ->where("cronsetup_analyses.start_time", $crondetails->start_time)
            ->get();
            // dd($crondetails,$analyses);
        $causes_name = (App::getLocale() == '' || App::getLocale() == 'de') ? 'causes' : App::getLocale().'_causes';
        $causes = DB::table("cronsetup_analyses")
            ->join($causes_name, function ($join) use($causes_name){
                $join->on($causes_name.'.id', '=', 'cronsetup_analyses.causes')
                    ->orOn($causes_name.'.id', '=', 'cronsetup_analyses.medium')
                    ->orOn($causes_name.'.id', '=', 'cronsetup_analyses.tipp');
            })
            ->select("cronsetup_analyses.*", "cronsetup_analyses.id as cronanalyse_id", $causes_name.".*")
            ->whereIn("type_status", [1, 2, 3, 4, 5])
            ->where("cronsetup_analyses.cron_setup_id", $cron_id)
            ->where("cronsetup_analyses.start_time", $crondetails->start_time)->get();


        $topics = DB::table("cronsetup_analyses")
            ->where("type_status", 6)
            ->where("cron_setup_id", $cron_id)
            ->where("start_time", $crondetails->start_time)->get();

        // $userdetails = User::find($crondetails->selected_user);
        $data['creator_role'] = ($userdetails->boss_id != 0) ? User::find($userdetails->boss_id)->user_type : $userdetails->user_type;
        #language show as user language
        // setServerLocal(Language::find($userdetails->language_id)->short_code);

        $data['status']  = 1;
        $data['pdf_export'] = $crondetails->pdf_export;
        $data['userid']  = $userdetails->id;
        $data['cronid']  = $cron_id;
        $data['ctimeid'] = $crontime_id;
        $data['cuniqueid'] = $unique;
        $anytime = date('Y-m-d', strtotime($crondetails->end_time));
        $today   = date("Y-m-d");

        if (count($all_times) > 0) $lastTreatment = $all_times->last();

        #time details
        $timezone = "Europe/Vienna"; #"Asia/Dhaka";
        $startTime = $crondetails->start_time;
        $endTime = $crondetails->end_time;

        #time differences
        $startDiff = $this->getTimeDifference($startTime, $timezone);

        // $endDiff   = $this->getTimeDifference($endTime, $timezone);
        $endDiff   = 1;

        // if ($endDiff <= 0 && $crondetails->any_time_cron) return view('Frontend.cron.treat_finish', $data);

        $lastDiff  = $this->getTimeDifference($lastTreatment->end_time, $timezone);
        if (count($analyses) > 0) {
            if ($crondetails->causes == 1 || $crondetails->medium == 1 || $crondetails->tipp == 1) {
                $causesIds = array();
                $anaIds = array();

                ($crondetails->causes == 1) ? $causesIds = $analyses->pluck('causes')->toArray() : 0;
                ($crondetails->medium == 1) ?  $causesIds = array_merge($causesIds,$analyses->pluck('medium')->toArray()) : 0;
                ($crondetails->tipp == 1) ?  $causesIds = array_merge($causesIds,$analyses->pluck('tipp')->toArray()) : 0;
                $anaIds = $analyses->pluck('analyse_id')->toArray();

                if (!empty($causesIds)){
                    $causesIds = array_unique($causesIds);
                    $all_causes = array();
                    DB::table("causes")->whereIn("id", $causesIds)->get(['id', 'title', 'description'])->map(function ($cause) use (&$all_causes) {
                        $all_causes[$cause->id] = $cause;
                    });
                }
                if (!empty($anaIds)){
                    $anaIds = array_unique($anaIds);
                    $proIds = [];
                    DB::table("analyse_pools")
                        ->join("pool_submenus", "pool_submenus.pool_id", "=", "analyse_pools.pool_id")
                        ->join("submenus", "submenus.id", "=", "pool_submenus.submenu_id")
                        ->whereIn("analyse_pools.analyse_id", $anaIds)
                        ->groupBy('product_id')->get(['analyse_id','product_id'])->each(function ($ana) use (&$proIds) {
                            $proIds[$ana->analyse_id] = $ana->product_id;
                    });
                }
            } else {
                $anaIds = $analyses->pluck('analyse_id')->toArray();

                if (!empty($anaIds)) DB::table("analyse_pools")
                        ->join("pool_submenus", "pool_submenus.pool_id", "=", "analyse_pools.pool_id")
                        ->join("submenus", "submenus.id", "=", "pool_submenus.submenu_id")
                        ->whereIn("analyse_pools.analyse_id", $anaIds)
                        ->groupBy('product_id')->get(['analyse_id','product_id'])->each(function ($ana) use (&$proIds) {
                                $proIds[$ana->analyse_id] = $ana->product_id;
                        });
            }
            $bodyImages = $mentalImages = $globalImages = collect();
            if(!empty($proIds)) {
                $bodyImages = DB::table('body_images')->whereIn('analyse_id', $proIds)->get(['analyse_id', 'image']);
                $mentalImages = DB::table('mental_images')->whereIn('analyse_id', $proIds)->get(['analyse_id', 'image']);
                $globalImages = Analyse::whereIn('id', $proIds)->get(['id', 'desc_image']);
            }
        }
        $data['userDetails'] = $userdetails;
        $this->saveDataOnCart($userdetails, $analyses, $all_causes, $causes, $proIds);

        $data['cartDatas'] = $this->getCronEmailCart($userdetails->id);

        if ($lastDiff <= 0)
            DB::table("cron_setups")->where("id", $cron_id)->update(["status" => 1, "open_status" => 1]);


        if ($startDiff > 0 && $crondetails->any_time_cron != 1) {
            $data = [
                'startTime' => $startTime,
                'endTime' => $endTime,
                'zone' => $timezone,
                'userDetails' => $userdetails
            ];
            return view('Frontend.cron.countdown', ['data' => $data]);
        } elseif ($crondetails->any_time_cron == 1 && $anytime >= $today) {
            if ($crondetails->length == 0) {

                $timeFirst  = strtotime($startTime);
                $timeSecond = strtotime($endTime);
                $timeinSeconds = $timeSecond - $timeFirst;
                return view('Frontend.cron.due_treatment', ['data' => $data, 'analyses' => $analyses, 'all_causes' => $all_causes, 'causes_types' => $causes, 'all_proids' => $proIds,  'causesop' => $crondetails->causes, 'mediumop' => $crondetails->medium, 'tippop' => $crondetails->tipp, 'times' => $timeinSeconds, 'topics' => $topics]);
            } else {

                return view('Frontend.cron.single_treatment', ['data' => $data, 'analyses' => $analyses, 'all_causes' => $all_causes, 'causes_types' => $causes, 'all_proids' => $proIds, 'causesop' => $crondetails->causes, 'mediumop' => $crondetails->medium, 'tippop' => $crondetails->tipp, 'topics' => $topics, 'ra_status' => $crondetails->ra_status, 'pdf_status' => $crondetails->pdf_status, 'bodyImages' => $bodyImages, 'mentalImages' => $mentalImages, 'globalImages' => $globalImages]);
            }
        } elseif ($endDiff <= 0) {
            return view('Frontend.cron.treat_finish', $data);
        } else {
            if ($crondetails->length == 0) {

                $timeFirst  = strtotime($startTime);
                $timeSecond = strtotime($endTime);
                $timeinSeconds = $timeSecond - $timeFirst;
                return view('Frontend.cron.due_treatment', ['data' => $data, 'analyses' => $analyses, 'all_causes' => $all_causes, 'causes_types' => $causes, 'all_proids' => $proIds, 'causesop' => $crondetails->causes, 'mediumop' => $crondetails->medium, 'tippop' => $crondetails->tipp, 'times' => $timeinSeconds, 'topics' => $topics]);
            } else {
                return view('Frontend.cron.single_treatment', ['data' => $data, 'analyses' => $analyses, 'all_causes' => $all_causes, 'causes_types' => $causes, 'all_proids' => $proIds, 'causesop' => $crondetails->causes, 'mediumop' => $crondetails->medium, 'tippop' => $crondetails->tipp, 'topics' => $topics, 'ra_status' => $crondetails->ra_status, 'pdf_status' => $crondetails->pdf_status, 'bodyImages' => $bodyImages, 'mentalImages' => $mentalImages, 'globalImages' => $globalImages]);
            }
        }
    }

    public function saveBadfeedback(Request $request){
       $feedbackdata = $request->all();

        $request->validate([
            'dislike_feedback' => 'required',
            'user_id' => 'required|numeric',
            'url' => 'required|url',
        ]);


        // dd($feedbackdata);

        Feedback::insert([
            'user_id' => $request->input('user_id'),
            'feedback' => $request->input('dislike_feedback'),
            'status' => true,
            'url' => $request->input('url'),
            'created_at' => now(),
        ]);

        return response()->json(['message' => 'Feedback submitted successfully']);
    }


    #all cart save process
    public function saveDataOnCart($user, $analysis, $allCauses, $causes, $pId, $instance_name = 'CronEMailCart')
    {
        $proids = Cache::rememberForever('_productIds', function (){
            return DB::table('products')->pluck('id')->toArray();
        });

        if (count($analysis) > 0) {
            foreach ($analysis as $cronana) {
                $anaName = ($cronana->type_status == 6 && $cronana->analyse_id == 1) ? $cronana->topic : $cronana->name;
                $causeTypeStatus = $cronana->type_status == 6 ? 6 : 0;
                $this->create_cart($user->id, $cronana->analyse_id,$anaName, 0, $proids[rand(0, count($proids) - 1)], $cronana->calculation, $cronana->male, $cronana->heart, $cronana->time, 0, 0, 0, $cronana->color, 'Analysis', false, 0, true, $instance_name,$causeTypeStatus);
                if (!empty($allCauses)) {
                    if ($cronana->causes != 0) {
                        $name = $allCauses[$cronana->causes]->title;
                        $this->create_cart($user->id, $cronana->analyse_id, $name, 0, $proids[rand(0, count($proids) - 1)], $cronana->calculation, $cronana->male, $cronana->heart, $cronana->cau_price, 0, 0, 0, $cronana->color, 'Causes', false, 0, true, $instance_name);
                    }
                    if ($cronana->medium != 0) {
                        $name = $allCauses[$cronana->medium]->title;
                        $this->create_cart($user->id, $cronana->analyse_id, $name, 0, $proids[rand(0, count($proids) - 1)], $cronana->calculation, $cronana->male, $cronana->heart, $cronana->mid_price, 0, 0, 0, $cronana->color, 'Medium', false, 0, true, $instance_name);
                    }
                    if ($cronana->tipp != 0) {
                        $name = $allCauses[$cronana->tipp]->title;
                        $this->create_cart($user->id, $cronana->analyse_id, $name, 0, $proids[rand(0, count($proids) - 1)], $cronana->calculation, $cronana->male, $cronana->heart, $cronana->tipp_price, 0, 0, 0, $cronana->color, 'Tipp', false, 0, true, $instance_name);
                    }
                }
            }
        }
        if (count($causes) > 0) {
            foreach ($causes as $key => $cause) {
                if (($pId != 0 || $pId != null) && $key == 0) {
                    $keyName = key($pId);
                    $proId = $pId[$keyName];
                } else $proId = $proids[rand(0, count($proids) - 1)];
                if ($cause->type_status == 1) $causes_type = 'Causes';
                if ($cause->type_status == 2) $causes_type = 'Medium';
                if ($cause->type_status == 3) $causes_type = 'Tipp';
                if ($cause->type_status == 4) $causes_type = 'Einfluss';
                if ($cause->type_status == 5) $causes_type = 'Fokus';
                if ($cause->type_status == 6) $causes_type = 'Topic';
                $this->create_cart($user->id, $cause->analyse_id, $cause->title, 0, $proId, $cause->calculation, $cause->male, $cause->heart, $cause->time, $cause->causes, $cause->medium, $cause->tipp, $cause->color, $causes_type, false, 0, true, $instance_name);
            }
        }
        #save cart data on DB
        // $this->addToCartRA($user);

        return true;
    }

    #create single cart for RA remote treatment
    public function create_cart($user_id, $anaID, $name, $subID, $proID, $cal, $male, $heart, $price, $cID, $mID, $tID, $color, $type, $opt, $per, $op_status, $instance_name = 'CronEMailCart',$cause_type_status = 0)
    {
        $gsSetting = Cache::rememberForever('_globaSetting', function () {
            return DB::table('global_settings')->where('gs_type', 'other')->first();
        });

        $randPrice  = rand($gsSetting->gs_min_price, $gsSetting->gs_max_price);
        $submenuId = $subID && $subID != 0 ? $subID : (($anaID ? Analyse::find($anaID)->defaultSubmenuId() :  0));
        $cart_data = array(
            'user_id' => $user_id,
            'analysisID' => $anaID,
            'analysisName' => $name,
            'submenu_id' => $submenuId,
            'productID' => $proID,
            'calculation' => $cal,
            'male' => $male,
            'heart' => $heart,
            'price' => $price == 0 ? $randPrice : $price,
            'causes_id' => $cID,
            'medium_id' => $mID,
            'tipp_id' => $tID,
            'color' => $color,
            'type' => $type,
            'optimized' => $opt,
            'percentage' => $per,
            'op_status' => $op_status,
            'time' => gmdate('i:s', $price == 0 ? $randPrice : $price)
        );
        
        if($cause_type_status == 6){
            $cart_data['analysisID'] = substr(uniqid('', true), -8);
            $cart_data['submenu_id'] = substr(uniqid('', true), -8);
        }

        if ($instance_name == 'wishlist') {
            Cart::instance($instance_name)->add(['id' => $user_id, 'name' => $name, 'qty' =>  1, 'price' =>  1, 'weight' => 1, 'options' => $cart_data]);
        } else
            Cart::instance($instance_name)->add(['id' => $user_id . 'RA', 'name' => $name, 'qty' =>  1, 'price' =>  1, 'weight' => 1, 'options' => $cart_data]);

    }

    public function optimizedTreat($user_id, $cron_id, $timeid, $uniqueid)
    {

        $user = User::find($user_id);
        $cartData = $this->getCronEmailCart($user_id);
        $ana_ids = array();
        // $cartData = $this->getFromCartRA($user);
        if (!empty($cartData)) {
            foreach ($cartData as $cart) {
                $rowId      = $cart->rowId;
                $updateCart = array();
                if ($cart->options->price == 0) continue;
                $updateCart['userID'] = $cart->id;
                $updateCart['analysisID']   = $cart->options->analysisID;
                $updateCart['analysisName'] = $cart->options->analysisName;
                $updateCart['submenu_id']   = $cart->options->submenu_id;
                $updateCart['productID']    = $cart->options->productID;
                $updateCart['calculation']  = $cart->options->calculation;
                $updateCart['male']  = $cart->options->male;
                $updateCart['heart'] = $cart->options->heart;
                $updateCart['price'] = $cart->options->price;
                $updateCart['causes_id']  = $cart->options->causes_id;
                $updateCart['medium_id']  = $cart->options->medium_id;
                $updateCart['tipp_id']    = $cart->options->tipp_id;
                $updateCart['color'] = $cart->options->color;
                $updateCart['type']  = $cart->options->type;
                $updateCart['optimized'] = false;
                $updateCart['percentage'] = $cart->options->percentage;
                $updateCart['op_status'] = false;
                Cart::update($rowId, ['options' => $updateCart]);
                $ana_ids[] = $cart->options->analysisID;
            }
        }
        $cart_result = array();
        foreach (Cart::instance('CronEMailCart')->content()->where('id', $user_id . 'RA') as $key => $cartValue) {
            $cart_result[] = $cartValue;
        }
        $bodyImages = $mentalImages = $globalImages = collect();
        if(!empty($ana_ids)) {
            $bodyImages = BodyImage::whereIn('analyse_id', $ana_ids)->select('analyse_id', 'image')->get();
            $mentalImages = MentalImage::whereIn('analyse_id', $ana_ids)->select('analyse_id', 'image')->get();
            $globalImages = Analyse::whereIn('id', $ana_ids)->select('id', 'desc_image')->get();
        }
        $status = 0;
        #cart save to shopping cart
        // $this->addToCartRA($user);
        $cronoption = DB::table('cronsetup_options')->where('cron_setup_id', $cron_id)->first();
        $ra_status = $cronoption->ra_status;
        $pdf_status = $cronoption->pdf_status;
        $data['pdf_export'] = $cronoption->pdf_export;
        $data['cronid'] = $cron_id;
        $data['userid'] = $user_id;
        $data['ctimeid'] = $timeid;
        $data['cuniqueid'] = $uniqueid;
        $data['userDetails'] = $user;
        $data['creator_role'] = ($data['userDetails']->boss_id != 0) ? User::find($user->boss_id)->user_type : $data['userDetails']->user_type;

        return view('Frontend.cron.cronReactionPage', compact('status', 'data', 'cart_result', 'ra_status', 'pdf_status', 'bodyImages', 'mentalImages', 'globalImages'));
    }

    public function optimizedCorn($user_id)
    {
        $data = array();
        $data['userDetails']    = User::with('useroption:user_id,ran_ana,ra_status,ra_pdf_status')->find($user_id);
        $data['randomAnalysis'] = $data['userDetails']->userOption->ran_ana;
        $cartRecords = getCartDataInstance(4, $user_id);

        $result = app('App\Services\Treatment\TreatmentService')->optimizeBeforePageLoad($cartRecords,'CronEMailCart');

        return response()->json(['status' => $result['status'], 'carts' => $result['cartRecords'], 'randomValues' => $result['randomValues'], 'anaNames' => $result['anaNames'], 'newValues' => $result['newValues'], 'colors' => $result['colors'], 'img' => $result['img'], 'data' => $data]);
        // $result = $this->cart_update($user_id);
        // return response()->json(['status' => $result['status'], 'randomValues' => $result['randomValues'], 'anaNames' => $result['anaNames'], 'newValues' => $result['newValues'], 'colors' => $result['colors'], 'img' => $result['img'], 'data' => $result['data'], 'allBGImage' => $result['allBGImage']]);
    }

    public function cart_update($user_id)
    {
        $user = User::find($user_id);
        $cartData = $this->getCronEmailCart($user_id);
        // $cartData = $this->getFromCartRA($user);
        $newValues  = array();
        $anaNames   = array();
        $colors     = array();
        $randValues = array();
        $gsSetting  = biorythVisibleDetailsByName('Reaction');
        $allBGImage = Treatment_custom_image::get(['id','image']);
        $data = array();
        $data['userDetails']    = User::with('useroption')->find($user_id);
        $data['randomAnalysis'] = $data['userDetails']->userOption->ran_ana;
        $total_cart = 0;
        if (!empty($cartData)) {
            $total_cart = count($cartData);
            foreach ($cartData as $cart) {
                $rowId = $cart->rowId;
                if ($cart->options->price >= 1) {

                    if ($cart->options->type == 'Causes') {
                        $optimizedResult = $this->checkOptimizedValue($user_id, $cart->options->causes_id, $cart->options->type);
                    } elseif ($cart->options->type == 'Medium') {
                        $optimizedResult = $this->checkOptimizedValue($user_id, $cart->options->medium_id, $cart->options->type);
                    } elseif ($cart->options->type == 'Tipp') {
                        $optimizedResult = $this->checkOptimizedValue($user_id, $cart->options->tipp_id, $cart->options->type);
                    } elseif ($cart->options->type == 'Fokus') {
                        $optimizedResult = $this->checkOptimizedValue($user_id, $cart->options->causes_id, $cart->options->type);
                    } elseif ($cart->options->type == 'Einfluss') {
                        $optimizedResult = $this->checkOptimizedValue($user_id, $cart->options->causes_id, $cart->options->type);
                    } elseif ($cart->options->type == 'analyse') {
                        $optimizedResult = $this->checkOptimizedValue($user_id, $cart->options->causes_id, $cart->options->type);
                    } elseif ($cart->options->type == 'Analysis') {
                        $optimizedResult = $this->checkOptimizedValue($user_id, $cart->options->analysisID, $cart->options->type);
                    }

                    if ($optimizedResult != null && $cart->options->op_status) {
                        $percentage = ($optimizedResult->percentage == 0) ?  rand(20, 50) : $optimizedResult->percentage;
                        $newValue1 = $optimizedResult->price;

                        #Get color useing GS-Setting and Percentage
                        $color = $this->getColor($gsSetting, $percentage);

                        $this->updateCartData($cart, $rowId, $percentage, $newValue1, $color, ($newValue1 == 0) ? true : false);

                        $randValues[]   = $percentage;
                        $newValues[]    = $newValue1;
                        $anaNames[]     = $cart->options->analysisName;
                        $colors[] = $color;
                    } else {
                        $percentage = $cart->options->percentage;
                        $randValue  =  rand($percentage, 100);
                        $curValue   = $cart->options->price;

                        $newValue   = $curValue - ($curValue * ($randValue / 100));
                        if ($newValue <= 0) $newValue = 0;
                        $newValue1  = (int)$newValue;

                        $randValues[]   = $randValue;
                        $newValues[]    = (int)$newValue1;
                        $anaNames[]     = $cart->options->analysisName;

                        #Get color useing GS-Setting and Percentage
                        $color = $this->getColor($gsSetting, $randValue);
                        $colors[] = $color;

                        #update Cart data with the required fileds
                        $this->updateCartData($cart, $rowId, $randValue, $newValue1, $color, ($newValue1 == 0) ? true : false);
                    }
                } else {

                    $randValues[] = $cart->options->percentage;
                    $newValues[] = $cart->options->price;
                    $anaNames[]  = $cart->options->analysisName;

                    #Get color useing GS-Setting and Percentage
                    $color = $this->getColor($gsSetting, $cart->options->percentage);
                    $colors[] = $color;

                    #update Cart data with the required fileds
                    $this->updateCartData($cart, $rowId, $cart->options->percentage, $cart->options->price, $color, true);
                    $total_cart--;
                }
            }
        }

        $status = true;
        if ($total_cart <= 0) {
            $status = false;
            $img = public_path('images/happy.png');
        }
        #cart save to shopping cart
        // $this->addToCartRA($user);

        return $result = [
            'status' => $status,
            'randomValues' => $randValues,
            'anaNames' => $anaNames,
            'newValues' => $newValues,
            'colors' => $colors,
            'img' => $img,
            'data' => $data,
            'allBGImage' => $allBGImage
        ];
    }
    #update cart with change price and color and percentage
    private function updateCartData($cart, $rowId, $percentage, $price, $color, $optimize_status)
    {
        $updateCart['userID'] = $cart->options->userID;
        $updateCart['analysisID']   = $cart->options->analysisID;
        $updateCart['analysisName'] = $cart->options->analysisName;
        $updateCart['submenu_id']   = $cart->options->submenu_id;
        $updateCart['productID']    = $cart->options->productID;
        $updateCart['calculation']  = $cart->options->calculation;
        $updateCart['male']         = $cart->options->male;
        $updateCart['heart']        = $cart->options->heart;
        $updateCart['price']        = $price;
        $updateCart['causes_id']    = $cart->options->causes_id;
        $updateCart['medium_id']    = $cart->options->medium_id;
        $updateCart['tipp_id']      = $cart->options->tipp_id;
        $updateCart['color']        = $color;
        $updateCart['type']         = $cart->options->type;
        $updateCart['optimized']    = $optimize_status;
        $updateCart['percentage']   = $percentage;
        $updateCart['op_status']    = $cart->options->op_status;
        $cart_id = Cart::update($rowId, ['options' => $updateCart]);
    }
    #get The color with GS (Glonal Setting) Reaction Settings
    private function getColor($gsSetting, $percentage)
    {
        if ($percentage >= $gsSetting->gs_red_min && $percentage <= $gsSetting->gs_red_max)
            $color = "#E84E1B";
        else if ($percentage >= $gsSetting->gs_orange_min && $percentage <= $gsSetting->gs_orange_max)
            $color = "#F8B133";
        else if ($percentage >= $gsSetting->gs_green_min && $percentage <= $gsSetting->gs_green_max)
            $color = "#2FAB66";

        return $color;
    }


    public function cronDuplicate($cronid)
    {
        $analyses = null;
        $causes = null;
        $uniqueNsub = [];
        $uniqueOsub = [];
        $productSub = [];
        $cronsetup = CronSetup::find($cronid);
        if ($cronsetup->cron_type == 1) {
            $crondetails = CronSetup::with("cronsetupoption", "cronsubmenu")->where('id', $cronid)->first();
            
            $firstCronTime = $cronsetup->cronsetuptimes()->orderBy('start_time', 'asc')->first();
            
            $cronsetupAnalyses = CronsetupAnalysis::where('cron_setup_id', $cronid)
                ->where('start_time', $firstCronTime->start_time)
                ->orderBy('id', 'asc')
                ->get();
            
            $analysesData = collect();
            $causesData = collect();
            $allData = collect();
            
            foreach ($cronsetupAnalyses as $item) {
                if (in_array($item->type_status, [0, 6])) {
                    if ($item->type_status == 6) {
                        $topicAnalysis = (object)[
                            'id' => 1, 
                            'name' => $item->topic, 
                            'pivot' => $item
                        ];
                        $analysesData->push($topicAnalysis);
                        $allData->push($topicAnalysis);
                    } elseif ($item->analyse_id > 0) {
                        $analysis = Analyse::find($item->analyse_id);
                        if ($analysis) {
                            $analysis->pivot = $item;
                            $analysesData->push($analysis);
                            $allData->push($analysis);
                        }
                    }
                } else {
                    $causeId = null;
                    if ($item->type_status == 1 || $item->type_status == 4 || $item->type_status == 5) {
                        $causeId = $item->causes;
                    } elseif ($item->type_status == 2) {
                        $causeId = $item->medium;
                    } elseif ($item->type_status == 3) {
                        $causeId = $item->tipp;
                    }
                    
                    if ($causeId > 0) {
                        $cause = Causes::find($causeId);
                        if ($cause) {
                            $cause->pivot = $item;
                            $cause->name = $cause->title;
                            $causesData->push($cause);
                            $allData->push($cause);
                        }
                    }
                }
            }
            
            $analyses = $analysesData;
            $causes = $causesData;
        } else {
            $crondetails = CronSetup::with("cronsetupoption", "cronsubmenu")->where('id', $cronid)->first();
        }

        $submenus = $crondetails->cronsubmenu;
        $normalsubs = [];
        $ownsubs = [];
        $uniqueOsub = [];
        if($submenus != null){
            foreach ($submenus as $csub) {
                if ($csub->pivot->submenu_type == 0)
                    $normalsubs[] = $csub->id;
                else
                    $ownsubs[] = $csub->id;
            }
        }

        if (!empty($normalsubs))
            $uniqueNsub = array_unique($normalsubs);
        if (!empty($ownsubs))
            $uniqueOsub = array_unique($ownsubs);
        $subusers = User::where('boss_id', getAuthID())->orWhere('id',getAuthId())->orderBy('first_name', 'ASC')->get();

        if(!$cronsetup->cron_type){
            $product = __getMenus();
            $productSub = $product->whereIn('status',[1,2])->map(function($prod){
                return [
                    'proId' => $prod->id,
                    'proName' => $prod->product_name,
                    'submenu' => $prod->submenus,
                    'ownmenu' => ($prod->status == 2) ? 'yes':'no'
                ];
            });
        }

        $selectedTimezone = $cronsetup->cronsetuptimes[0]->timezone;
        $timezones=Timezone::get()->each(function ($zone) use ($selectedTimezone){
            $zone->isSelected = ($selectedTimezone == $zone->timezone)?true:false;
        })->values();
        
        return view('Frontend.cron.cron_duplicate', ['user' => $cronsetup->selecteduser, 'timezones'=>$timezones,'default' => $crondetails->cronsetupoption, 'subuser' => $subusers, 'product' => $productSub, 'norsubs' => $uniqueNsub, 'ownsubs' => $uniqueOsub, 'cronsetup' => $cronsetup, 'analyses' => $analyses, 'causes' => $causes, 'allData' => $allData ?? collect()]);
    }

    function checkOptimizedValue($user_id, $id, $type)
    {
        $optimizeResult = DB::table('optimized_analyses_values')
            ->whereDate('created_at', Carbon::today())
            ->where('user_id', $user_id)
            ->where('types', $type)
            ->where('treat_id', $id)
            ->orderBy('created_at', 'desc')
            ->first();
        $price = ($optimizeResult != null) ? $optimizeResult : null;
        return $price;
    }

    public function treatFinish()
    {
        return view('Frontend.cron.treat_finish');
    }

    public function getTimeDifference($cronTime, $timezone)
    {
        date_default_timezone_set($timezone);
        $cronTime = strtotime($cronTime);
        $dateNow = new \DateTime();
        $nowTimeStamp = $dateNow->getTimestamp();
        $diff = $cronTime - $nowTimeStamp;
        return $diff;
    }

    public function cronPDFview($timeid)
    {
        $crondetails = CronsetupTimes::where("id", $timeid)
            ->with(['cronsetup' => function($q){
                $q->with(['cronsetup_option','user.useroption:user_id,custom_link,pdf_logo,view_pdf','user.smtp']);
            }])->first();

        $new = collect([
            'first_name'    => $crondetails->cronsetup->selecteduser->first_name,
            'last_name'     => $crondetails->cronsetup->selecteduser->last_name,
            'cron_email'    => $crondetails->cronsetup->selecteduser->cron_email,
            'causes'        => $crondetails->cronsetup->cronsetup_option->causes,
            'medium'        => $crondetails->cronsetup->cronsetup_option->medium,
            'tipp'          => $crondetails->cronsetup->cronsetup_option->tipp,
            'language_id'   => $crondetails->cronsetup->selecteduser->language_id,
            'custom_link'   => $crondetails->cronsetup->user->useroption->custom_link,
            'pdf_logo'      => $crondetails->cronsetup->user->useroption->pdf_logo,
            'view_pdf'      => $crondetails->cronsetup->user->useroption->view_pdf
        ]);
        $cronsetup = (object)collect($crondetails->cronsetup)->merge($new)->merge(collect($crondetails->cronsetup->user->smtp))->all();

        $subuser = $crondetails->cronsetup->selecteduser;

        setServerLocal(Language::find($subuser->language_id,['short_code'])->short_code);

        $langId = App::getLocale();

        $response = app('App\Services\CronModule\CronControllerService')->getPDFDatas($crondetails);
        $allSubmenus = $response['allSubmenus'];
        $topicnames = $response['allTopics'];

        if ($cronsetup->pdf_logo != '') {
            $logo = public_path('/users/pdf_logo/' . $cronsetup->pdf_logo);
        } else {
            $logo = public_path('/energetisch_fit.png');
        }

        $data['page_title'] = "Cron PDF";
        $pdf_title = "cronPDF";
        $type = "CronPDF";
        $pdfstatus = "cron";

        $options = new Options();
        // $options->set('tempDir', __DIR__ . '/site_uploads/dompdf_temp');
        $options->set('isRemoteEnabled', TRUE);
        $options->set('debugKeepTemp', TRUE);
        $options->set('chroot', '/'); // Just for testing :)
        $options->set('isHtml5ParserEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4');
        $font = $dompdf->getFontMetrics()->get_font("roboto", "bold");
        $pdfName = $type.'_'.uniqid(5) . ".pdf";

        $column = ($langId == 'de') ? 'pdf_second_page' : $langId.'_pdf_second_page';
        $templates = DB::table('pdf_templates')->select([$column])->where('user_id', $cronsetup->user_id)->first();

        $pdfSecondPage = null;
        if ($templates != null)  $pdfSecondPage = $templates->{$column};
        if(!$pdfSecondPage) $pdfSecondPage = DB::table('email_placeholders')->where('type', 'pdf_second_page_'.$langId)->first()->message;


        $pdf_layout_datas = [
            'logo' => $logo,
            'subuser_full_name' => $subuser->first_name." ".$subuser->last_name,
            'dob' => $subuser->gebdatum,
            'place_of_birth' => $subuser->gebort,
            'address' => $subuser->address,
            'post_code' => $subuser->post_code,
            'village' => $subuser->village,
            'pdf_second_page' => str_replace('\r\n', "<br>", $pdfSecondPage),
            'today' => date('d-m-Y'),
            'time' => date('H:i'),
            'custom_link' => $crondetails->cronsetup->user->useroption->custom_link,
            'admin_full_name' => $crondetails->cronsetup->user->first_name." ".$crondetails->cronsetup->user->last_name
        ];

        #Loads an HTML string
        $fileContent = (string) View::make("NewPDF.pdf_index", compact('pdf_layout_datas', 'cronsetup', 'allSubmenus', 'topicnames'));
        // $fileContent = (string) View::make("Frontend.cron.cron_pdf_test", compact('pdfSecondPage','cronsetup', 'subuser', 'crontime', 'allSubmenus', 'logo', 'topicnames'));

        $dompdf->loadHtml($fileContent);

        #Create the PDF
        $dompdf->render();

        $dompdf->stream($pdfName, array("Attachment" => false));
        exit(0);
    }

    public function cronPDFviewOld($timeid)
    {
        $crontime = DB::table("cronsetup_times")->where("id", $timeid)->first();

        $cron_analyses = DB::table("cronsetup_analyses")
            ->where("cron_setup_id", $crontime->cronsetup_id)
            ->where("start_time", $crontime->start_time)->get();

        $cronsetup = DB::table("cron_setups")
            ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cron_setups.id")
            ->join("users", "users.id", "=", "cron_setups.user_id")
            ->join("user_options", "user_options.user_id", "=", "cron_setups.user_id")
            ->join("smtps", "smtps.user_id", "=", "cron_setups.user_id")
            ->select(
                "cron_setups.*",
                "cronsetup_options.pdf_export",
                "cronsetup_options.causes",
                "cronsetup_options.medium",
                "cronsetup_options.tipp",
                "users.first_name",
                "users.last_name",
                "users.cron_email",
                "users.language_id",
                "user_options.custom_link",
                "user_options.pdf_logo",
                "user_options.view_pdf",
                "smtps.*"
            )->where("cron_setups.id", $crontime->cronsetup_id)->first();

        $subuser = DB::table("users")->select("users.first_name", "users.last_name","users.language_id", "users.cron_email", "users.gebdatum", "users.gebort", "users.address", "users.post_code", "users.village")->where("id", $cronsetup->selected_user)->first();
        $pro_submenus = DB::table('cron_setup_submenus')->where('cron_setup_id', $crontime->cronsetup_id)->get();
        setServerLocal(DB::table('languages')->find(($subuser->language_id??2),['short_code'])->short_code);
        $langId = app()->getLocale()??'de';
        $normalSub = array();
        $ownSub = array();

        foreach ($pro_submenus as $prosub) {
            if ($prosub->submenu_type == 0) $normalSub[] = $prosub->submenu_id;
            else $ownSub[] = $prosub->submenu_id;
        }
        $systemProColor = array();
        $ownProColor = array();

        if (!empty($normalSub)) $systemProColor = getProductSettingBySubmenu($normalSub);
        else if (!empty($ownSub)) $ownProColor = getUserOwnMenuSettingBySubId($ownSub);

        $ana_ids = array();
        $causes = array();
        $tmp_ana = array();
        $tmp_cau = array();
        $pdfdata = array();

        if (count($cron_analyses) > 0) {
            foreach ($cron_analyses as $cval) {
                $ana_ids[] = $cval->analyse_id;

                if ($cval->type_status == 0) {
                    if ($cval->causes != 0 && $cronsetup->causes) $causes[] = $cval->causes;
                    if ($cval->medium != 0 && $cronsetup->medium) $causes[] = $cval->medium;
                    if ($cval->tipp != 0 && $cronsetup->tipp) $causes[] = $cval->tipp;
                }

                if ($cval->type_status == 1) $causes[] = $cval->causes;
                else if ($cval->type_status == 2) $causes[] = $cval->medium;
                else if ($cval->type_status == 3) $causes[] = $cval->tipp;
                else if ($cval->type_status == 4) $causes[] = $cval->causes;
                else if ($cval->type_status == 5) $causes[] = $cval->causes;
            }
        }

        if (!empty($ana_ids)) {
            // $all_analyses = DB::table("analyses")->whereIn('id', $ana_ids)->get();
            $all_analyses = Analyse::whereIn('id', $ana_ids)->get();
            foreach ($all_analyses as $aval)
                $tmp_ana[$aval->id] = $aval;
        }

        if (!empty($causes)) {
            // $all_causes = DB::table("causes")->whereIn('id', $causes)->get();
            $all_causes = Causes::whereIn('id', $causes)->get();
            foreach ($all_causes as $caval)
                $tmp_cau[$caval->id] = $caval;
        }

        $subMenuQuery = DB::table('cron_setup_submenus')->where('cron_setup_id', $crontime->cronsetup_id)->get();

        $allSubmenus = array();
        $topicnames = array();
        if (count($cron_analyses) > 0) {

            if (count($subMenuQuery) > 0) {

                foreach ($subMenuQuery as $keys => $subm) {

                    $pdfdata = array();
                    $subName = "";

                    foreach ($cron_analyses as $anaval) {

                        $anaQuery = "";
                        if ($anaval->color == "done") continue;

                        $temp = array();
                        $causesNames = array();
                        $name = "";

                        if (!empty($tmp_ana) && $anaval->type_status == 0) {

                            if ($subm->submenu_type == 0) {
                                $anaQuery = DB::table('analyse_pools')
                                    ->join('pool_submenus', "pool_submenus.pool_id", "=", "analyse_pools.pool_id")
                                    ->join('submenus', "submenus.id", "=", "pool_submenus.submenu_id")
                                    ->where('submenus.id', $subm->submenu_id)
                                    ->where('pool_submenus.submenu_id', $subm->submenu_id)
                                    ->where('analyse_pools.analyse_id', $anaval->analyse_id)->first();

                                if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                                if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                                else $subName = $anaQuery->menu_name;
                                $anaval->color = "done";
                            } else if ($subm->submenu_type == 1) {
                                $anaQuery = DB::table('analyse_user_submenus')
                                    ->join('user_submenus', "user_submenus.id", "=", "analyse_user_submenus.user_submenu_id")
                                    ->where('user_submenus.id', $subm->submenu_id)
                                    ->where('analyse_user_submenus.analyse_id', $anaval->analyse_id)->first();

                                if ($anaQuery->user_submenu_id != $subm->submenu_id) continue;
                                $subName = $anaQuery->name;
                                $anaval->color = "done";
                            }

                            if (array_key_exists($anaval->analyse_id, $tmp_ana)) {
                                $analyses = $tmp_ana[$anaval->analyse_id];
                                $name   = $analyses->name;
                                $body   = $analyses->body_desc;
                                $mental = $analyses->mental_desc;
                                $desc = $analyses->description;
                            }
                        }

                        if ($anaval->type_status == 0) {
                            if (array_key_exists($anaval->causes, $tmp_cau)) $causesNames[0] = ($cronsetup->causes == 1) ? $tmp_cau[$anaval->causes] : 0;
                            if (array_key_exists($anaval->medium, $tmp_cau)) $causesNames[1] = ($cronsetup->medium == 1) ? $tmp_cau[$anaval->medium] : 0;
                            if (array_key_exists($anaval->tipp, $tmp_cau)) $causesNames[2] = ($cronsetup->tipp == 1) ? $tmp_cau[$anaval->tipp] : 0;
                        }


                        if ($anaval->type_status == 1) $causesNames[0] = $tmp_cau[$anaval->causes];
                        else if ($anaval->type_status == 2) $causesNames[1] = $tmp_cau[$anaval->medium];
                        else if ($anaval->type_status == 3) $causesNames[2] = $tmp_cau[$anaval->tipp];
                        else if ($anaval->type_status == 4) $causesNames[3] = $tmp_cau[$anaval->causes];
                        else if ($anaval->type_status == 5) $causesNames[4] = $tmp_cau[$anaval->causes];

                        if ($anaval->type_status == 1) {
                            $causesNames[0] = $tmp_cau[$anaval->causes];
                            $anaQuery = DB::table('causes')
                                ->join('group_causes', "group_causes.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_causes.submenu_id")
                                ->where('group_causes.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->causes)->first();

                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 2) {
                            $causesNames[1] = $tmp_cau[$anaval->medium];
                            $anaQuery = DB::table('causes')
                                ->join('group_mediums', "group_mediums.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_mediums.submenu_id")
                                ->where('group_mediums.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->medium)->first();
                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 3) {
                            $causesNames[2] = $tmp_cau[$anaval->tipp];
                            $anaQuery = DB::table('causes')
                                ->join('group_tips', "group_tips.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_tips.submenu_id")
                                ->where('group_tips.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->tipp)->first();
                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 4) {
                            $causesNames[3] = $tmp_cau[$anaval->causes];
                            $anaQuery = DB::table('causes')
                                ->join('group_einfluss', "group_einfluss.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_einfluss.submenu_id")
                                ->where('group_einfluss.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->causes)->first();

                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 5) {
                            $causesNames[4] = $tmp_cau[$anaval->causes];
                            $anaQuery = DB::table('causes')
                                ->join('group_focus', "group_focus.group_id", "=", "causes.group_id")
                                ->join('submenus', "submenus.id", "=", "group_focus.submenu_id")
                                ->where('group_focus.submenu_id', $subm->submenu_id)
                                ->where('causes.id', $anaval->causes)->first();
                            if ($anaQuery->submenu_id != $subm->submenu_id) continue;
                            if($langId != 'de')$subName = $anaQuery->{$langId.'_menu_name'};
                            else $subName = $anaQuery->menu_name;
                            $anaval->color = "done";
                        } else if ($anaval->type_status == 6) {
                            $tmpTopic = array();

                            if ($anaval->color != "done") {
                                $tmpTopic["topic"] = $anaval->topic;
                                $tmpTopic["time"] = $anaval->time;
                                $topicnames[] = $tmpTopic;
                                $anaval->color = "done";
                            }
                            continue;
                        }

                        if ($anaval->male < 31) $male_img = "man-red.png";
                        else $male_img = "man-gray.png";

                        if ($anaval->heart >= 50) $heart_img = "heart-red.png";
                        else $heart_img = "heart-gray.png";

                        if ($subm->submenu_type == 0) {
                            $colorValue = $systemProColor[$subm->submenu_id];
                            if ($anaval->calculation >= $colorValue->red_min && $anaval->calculation <= $colorValue->red_max) $color = "#E84E1B";
                            else if ($anaval->calculation >= $colorValue->orange_min && $anaval->calculation <= $colorValue->orange_max) $color = "#F8B133";
                            else if ($anaval->calculation >= $colorValue->green_min && $anaval->calculation <= $colorValue->green_max) $color = "#2FAB66";
                        } else {
                            $colorValue = $ownProColor[$subm->submenu_id];

                            $colorStatus = false;

                            if ($colorValue != null) {
                                if ($colorValue->red_min == null && $colorValue->red_max == null) {
                                    if ($colorValue->orange_min == null && $colorValue->orange_max == null) {
                                        if ($colorValue->green_min == null && $colorValue->green_max == null) {
                                            $colorValue = GlobalSetting::find(1);
                                            $colorStatus = true;
                                        }
                                    }
                                }
                            } else {
                                $colorValue = GlobalSetting::find(1);
                                $colorStatus = true;
                            }

                            #Analysis Value Color
                            if ($colorStatus == true) {
                                if ($anaval->calculation >= $colorValue->gs_red_min && $anaval->calculation <= $colorValue->gs_red_max) $color = "#E84E1B";
                                elseif ($anaval->calculation >= $colorValue->gs_orange_min && $anaval->calculation <= $colorValue->gs_orange_max) $color = "#F8B133";
                                elseif ($anaval->calculation >= $colorValue->gs_green_min && $anaval->calculation <= $colorValue->gs_green_max) $color = "#2FAB66";
                            } else {
                                if ($anaval->calculation >= $colorValue->red_min && $anaval->calculation <= $colorValue->red_max) $color = "#E84E1B";
                                elseif ($anaval->calculation >= $colorValue->orange_min && $anaval->calculation <= $colorValue->orange_max) $color = "#F8B133";
                                elseif ($anaval->calculation >= $colorValue->green_min && $anaval->calculation <= $colorValue->green_max) $color = "#2FAB66";
                            }
                        }

                        $temp = [
                            'name'   => $name,
                            'cal'    => $anaval->calculation,
                            'color'  => $color,
                            'male_val' => $anaval->male,
                            'male'   => $male_img,
                            'heart_val'  => $anaval->heart,
                            'heart'  => $heart_img,
                            'desc'   => $desc,
                            'body'   => $body,
                            'mental' => $mental,
                            'cau_title' => ($causesNames[0] != "") ? $causesNames[0]->title : "",
                            'mid_title' => ($causesNames[1] != "") ? $causesNames[1]->title : "",
                            'tip_title' => ($causesNames[2] != "") ? $causesNames[2]->title : "",
                            'ein_title' => ($causesNames[3] != "") ? $causesNames[3]->title : "",
                            'foc_title' => ($causesNames[4] != "") ? $causesNames[4]->title : "",
                            'cau_desc'  => ($causesNames[0] != "") ? $causesNames[0]->description : "",
                            'mid_desc'  => ($causesNames[1] != "") ? $causesNames[1]->description : "",
                            'tip_desc'  => ($causesNames[2] != "") ? $causesNames[2]->description : "",
                            'ein_desc'  => ($causesNames[3] != "") ? $causesNames[3]->description : "",
                            'foc_desc'  => ($causesNames[4] != "") ? $causesNames[4]->description : "",
                            'type' => $anaval->type_status,
                            'others' => json_decode($anaval->others)
                        ];

                        $pdfdata[] = $temp;
                    }

                    $tmpSubmenus['submenu_name'] = $subName;
                    $tmpSubmenus['analyses'] = $pdfdata;

                    $allSubmenus[] = $tmpSubmenus;
                }
            }
        }

        if ($cronsetup->pdf_logo != '') {
            $logo = public_path('/users/pdf_logo/' . $cronsetup->pdf_logo);
        } else {
            $logo = public_path('/energetisch_fit.png');
        }

        $data['page_title'] = "Cron PDF";
        $pdf_title = "cronPDF";
        $type = "CronPDF";
        $pdfstatus = "cron";

        $options = new Options();
        // $options->set('tempDir', __DIR__ . '/site_uploads/dompdf_temp');
        $options->set('isRemoteEnabled', TRUE);
        $options->set('debugKeepTemp', TRUE);
        $options->set('chroot', '/'); // Just for testing :)
        $options->set('isHtml5ParserEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4');
        $font = $dompdf->getFontMetrics()->get_font("roboto", "bold");
        $pdfName = $type.'_'.uniqid(5) . ".pdf";
        $templates = DB::table('pdf_templates')->where('user_id', $cronsetup->user_id)->first();

        if ($templates != null) {
            $column = ($langId == 'de') ? 'pdf_second_page' : $langId.'_pdf_second_page';
            $pdfSecondPage = $templates->$column;

            if ($pdfSecondPage == null || $pdfSecondPage == "") {
                $placeholder_id = ($subuser->language_id == 2) ? 16 : 15;
                $pdfSecondPage = DB::table('email_placeholders')->where('id', $placeholder_id)->first(['message'])->message;
            }
        } else {
            $placeholder_id = ($subuser->language_id == 2) ? 16 : 15;
            $pdfSecondPage = DB::table('email_placeholders')->where('id', $placeholder_id)->first(['message'])->message;
        }

        $pdfSecondPage = str_replace('\r\n', "<br>", $pdfSecondPage);

        #Loads an HTML string
        $fileContent = (string) View::make("Frontend.cron.cron_pdf", compact('pdfSecondPage','cronsetup', 'subuser', 'crontime', 'allSubmenus', 'logo', 'topicnames'));

        $dompdf->loadHtml($fileContent);

        #Create the PDF
        $dompdf->render();

        $dompdf->stream("cron_pdf.pdf", array("Attachment" => false));
        exit(0);

        // file_put_contents(public_path('/cronpdf/') . $pdfName, $dompdf->output());
        // $createData = [
        //     'user_id' => $cronsetup->selected_user,
        //     'name' => $pdf_title,
        //     'pdf' => $pdfName,
        //     'username' => "{$subuser->first_name} {$subuser->last_name}",
        //     'type' => $type,
        // ];

        // Pdf::create($createData);
        // return redirect()->intended(request()->root() . '/cronpdf/' . $pdfName);
    }


    public function viewCronPDF($userid, $cronid, $timeid, $uniqid)
    {
        $crontime = DB::table("cronsetup_times")->where("id", $timeid)->first();

        $crondetails = CronsetupTimes::where("id", $timeid)
            ->with(['cronsetup' => function($q){
                $q->with(['cronsetup_option','user.useroption:user_id,custom_link,pdf_logo,view_pdf','user.smtp']);
            }])->first();

        $subuser = $crondetails->cronsetup->selecteduser;
        setServerLocal(Language::find($subuser->language_id,['short_code'])->short_code);

        $langId = App::getLocale() ?? "de";

        if($crondetails->cronsetup->unique_id !== $uniqid){
            $notify = array(
                'alert-type' =>  "warning",
                'message' => trans('action.something_went_wrong')
            );
            return redirect()->back()->with($notify);
        }

        $new = collect([
            'first_name'    => $crondetails->cronsetup->selecteduser->first_name,
            'last_name'     => $crondetails->cronsetup->selecteduser->last_name,
            'cron_email'    => $crondetails->cronsetup->selecteduser->cron_email,
            'causes'        => $crondetails->cronsetup->cronsetup_option->causes,
            'medium'        => $crondetails->cronsetup->cronsetup_option->medium,
            'tipp'          => $crondetails->cronsetup->cronsetup_option->tipp,
            'language_id'   => $crondetails->cronsetup->selecteduser->language_id,
            'custom_link'   => $crondetails->cronsetup->user->useroption->custom_link,
            'pdf_logo'      => $crondetails->cronsetup->user->useroption->pdf_logo,
            'view_pdf'      =>$crondetails->cronsetup->user->useroption->view_pdf
        ]);
        $cronsetup = (object)collect($crondetails->cronsetup)->merge($new)->merge(collect($crondetails->cronsetup->user->smtp))->all();

        $response = app('App\Services\CronModule\CronControllerService')->getPDFDatas($crondetails);
        $allSubmenus = $response['allSubmenus'];
        $topicnames = $response['allTopics'];

        $logo = ($cronsetup->pdf_logo != '') ?
            public_path('/users/pdf_logo/' . $cronsetup->pdf_logo) :
            public_path('/energetisch_fit.png');

        $data['page_title'] = "Cron PDF";
        $pdf_title = "cronPDF";
        $type = "CronPDF";
        $pdfstatus = "cron";

        $pdfName = $type.'_'.uniqid(5) . ".pdf";

        $column = ($langId == 'de') ? 'pdf_second_page' : $langId.'_pdf_second_page';
        $templates = DB::table('pdf_templates')->select([$column])->where('user_id', $cronsetup->user_id)->first();

        $pdfSecondPage = null;
        if ($templates != null)  $pdfSecondPage = $templates->{$column};
        if(!$pdfSecondPage) $pdfSecondPage = DB::table('email_placeholders')->where('type', 'pdf_second_page_'.$langId)->first()->message;

        $options = new Options();
        $options->set('isRemoteEnabled', TRUE);
        $options->set('debugKeepTemp', TRUE);
        $options->set('chroot', '/'); // Just for testing :)
        $options->set('isHtml5ParserEnabled', true);
        $dompdf = new Dompdf($options);

        $pdf_layout_datas = [
            'logo' => $logo,
            'subuser_full_name' => $subuser->first_name." ".$subuser->last_name,
            'dob' => $subuser->gebdatum,
            'place_of_birth' => $subuser->gebort,
            'address' => $subuser->address,
            'post_code' => $subuser->post_code,
            'village' => $subuser->village,
            'pdf_second_page' => str_replace('\r\n', "<br>", $pdfSecondPage),
            'today' => date('d-m-Y'),
            'time' => date('H:i'),
            'custom_link' => $crondetails->cronsetup->user->useroption->custom_link,
            'admin_full_name' => $crondetails->cronsetup->user->first_name." ".$crondetails->cronsetup->user->last_name
        ];

        #Loads an HTML string
        $fileContent = (string) View::make("NewPDF.pdf_index", compact('pdf_layout_datas', 'cronsetup', 'allSubmenus', 'topicnames'));
        // $fileContent = (string) View::make("Frontend.cron.cron_pdf", compact('pdfSecondPage', 'cronsetup', 'subuser', 'crontime', 'allSubmenus', 'logo', 'topicnames'));
        $dompdf->loadHtml($fileContent);
        $dompdf->setPaper('A4');
        $font = $dompdf->getFontMetrics()->get_font("roboto", "bold");
        #Create the PDF
        $dompdf->render();

        $dompdf->stream($pdfName, array("Attachment" => false));
        exit(0);

        file_put_contents(public_path('/cronpdf/') . $pdfName, $dompdf->output());
        $createData = [
            'user_id' => $cronsetup->selected_user,
            'name' => $pdf_title,
            'pdf' => $pdfName,
            'username' => "{$subuser->first_name} {$subuser->last_name}",
            'type' => $type,
        ];

        Pdf::create($createData);
        return redirect()->intended(request()->root() . '/cronpdf/' . $pdfName);
    }

    public function addToCartRA($user)
    {
        #delete if shopping cart already exist
        if (DB::table('shoppingcart')->where('identifier', $user->id . 'RA')->exists())
            DB::table('shoppingcart')->where('identifier', $user->id . 'RA')->delete();
        #store session data on DB->shoppingcart
        Cart::instance('Reaction_data')->store($user->id . 'RA');
        #destroy cart
        Cart::destroy();
    }

    private function getFromCartRA($user)
    {
        #destroy cart
        Cart::destroy();
        #restore from DB->shoppingcart
        Cart::instance('Reaction_data')->restore($user->id . 'RA');
        #delete if shopping cart already exist
        if (DB::table('shoppingcart')->where('identifier', $user->id . 'RA')->exists())
            DB::table('shoppingcart')->where('identifier', $user->id . 'RA')->delete();
        #store session data on DB->shoppingcart
        $cart_result = array();
        foreach (Cart::instance('Reaction_data')->content()->where('id', $user->id . 'RA') as $key => $cartValue) {
            $cart_result[] = $cartValue;
        }
        return $cart_result;
    }

    public function optimizedResultSave($user_id)
    {
        $user = User::find($user_id);
        // $cart_data = $this->getFromCartRA($user);
        $cart_data = $this->getCronEmailCart($user_id);

        foreach ($cart_data as $key => $cart) {
            $id = 0;
            if ($cart->options->type == 'Causes') {
                $id = $cart->options->causes_id;
                $delete = OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->get();
                if (!empty($delete[0])) OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->delete();
            }
            if ($cart->options->type == 'Medium') {
                $id = $cart->options->medium_id;
                $delete = OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->get();
                if (!empty($delete[0])) OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->delete();
            }
            if ($cart->options->type == 'Tipp') {
                $id = $cart->options->tipp_id;
                $delete = OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->get();
                if (!empty($delete[0])) OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->delete();
            }
            if ($cart->options->type == 'Analysis') {
                $id = $cart->options->analysisID;
                $delete = OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->get();
                if (!empty($delete[0])) OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->delete();
            }
            if ($cart->options->type == 'Einfluss') {
                $id = $cart->options->causes_id;
                $delete = OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->get();
                if (!empty($delete[0])) OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->delete();
            }
            if ($cart->options->type == 'analse') {
                $id = $cart->options->causes_id;
                $delete = OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->get();
                if (!empty($delete[0])) OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->delete();
            }
            if ($cart->options->type == 'Fokus') {
                $id = $cart->options->causes_id;
                $delete = OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->get();
                if (!empty($delete[0])) OptimizedAnalysesValues::where('treat_id', $id)->where('types', $cart->options->type)->delete();
            }
            if ($id) {
                $checkOptimize = new OptimizedAnalysesValues();
                $checkOptimize->user_id = (int) $user_id;
                $checkOptimize->names = $cart->options->analysisName;
                $checkOptimize->treat_id = $id;
                $checkOptimize->types = $cart->options->type;
                $checkOptimize->price = $cart->options->price;
                $checkOptimize->percentage = $cart->options->percentage;
                $checkOptimize->status = request()->status ?? true;
                $checkOptimize->save();
            }

            Cart::remove($cart->rowId);
        }
        #delete shoping cart record
        if (DB::table('shoppingcart')->where('identifier', $user_id . 'RA')->exists())
            DB::table('shoppingcart')->where('identifier', $user_id . 'RA')->delete();
        #destroy cart
        Cart::instance('CronEMailCart')->destroy();

        return response()->json([
            'success' => true,
            'cart_data' => $cart_data
        ]);
    }



    #create pdf for reaction value
    public function createPDF($user_id, $creator_id)
    {
        $name = request()->name;
        $gsSetting = biorythVisibleDetailsByName('Reaction');
        $admin = User::with('useroption', 'pdf_template')->find($creator_id);
        $user = User::with('useroption', 'language')->find($user_id);

        date_default_timezone_set('Europe/Vienna');

        $cart_data = array();
        $carts = $this->getCronEmailCart($user_id);

        $cart_data = $this->getCartDataForPdf($user->language->short_code, $carts, $gsSetting);

        if ($admin->pdf_template != null) {
            $column = ($user->language_id == 'de') ? 'pdf_second_page' : $user->language_id.'_pdf_second_page';
            $pdf_second_page = $admin->pdf_template->$column;

            if ($pdf_second_page == null || $pdf_second_page == "") {
                $placeholder_id = ($user->language_id == 2) ? 16 : 15;
                $pdf_second_page = DB::table('email_placeholders')->where('id', $placeholder_id)->first(['message'])->message;
            }
        } else {
            $placeholder_id = ($user->language_id == 2) ? 16 : 15;
            $pdf_second_page = DB::table('email_placeholders')->where('id', $placeholder_id)->first(['message'])->message;
        }

        $pdf_layout_datas = [
            'logo' => ($user->useroption->pdf_logo == '') ? public_path('/energetisch_fit.png') : public_path('/users/pdf_logo/' . $user->useroption->pdf_logo),
            'subuser_full_name' => $user->first_name." ".$user->last_name,
            'dob' => $user->gebdatum,
            'place_of_birth' => $user->gebort,
            'address' => $user->address,
            'post_code' => $user->post_code,
            'village' => $user->village,
            'pdf_second_page' => $pdf_second_page,
            'today' => Carbon::now()->format('d-m-Y'),
            'time' => Carbon::now()->format('H:i'),
            'custom_link' => $user->useroption->custom_link,
            'admin_full_name' => $admin->first_name." ".$admin->last_name
        ];

        $options = new Options();
        $options->set('isRemoteEnabled', TRUE);
        $options->set('debugKeepTemp', TRUE);
        $options->set('chroot', '/');
        $options->set('isHtml5ParserEnabled', true);
        $dompdf = new Dompdf($options);

        // $fileContent = (string) View::make("generatePDF/ReactionAnalysis", ['pdf_template'=>$admin->pdf_template,'data' => $data, 'startAnalysis' => $save_cart, 'start_topic' => $topic, 'endAnalysis' => $cart_data['allSubmenus'], 'end_topic' => $cart_data['topic']]);

        // $fileContent = (string) View::make("NewPDF.reaction_analysis", ['pdf_layout_datas' => $pdf_layout_datas, 'startAnalysis' => $save_cart, 'start_topic' => $topic, 'endAnalysis' => $cart_data['allSubmenus'], 'end_topic' => $cart_data['topic']]);
        $fileContent = (string) View::make("NewPDF.reaction_analysis", ['pdf_layout_datas' => $pdf_layout_datas, 'startAnalysis' => $cart_data['allSubmenus'], 'end_topic' => $cart_data['topic']]);


        $dompdf->loadHtml($fileContent);
        $dompdf->setPaper('A4');
        $dompdf->render();
        $dompdf->getFontMetrics()->get_font("roboto", "bold");
        $pdfName = 'Reaction_'.uniqid(5) . ".pdf";
        file_put_contents(public_path('gpdf/') . $pdfName, $dompdf->output());

        $createData = [
            'user_id' => $creator_id,
            'subuser_id' => $user_id,
            'name' => $name,
            'pdf' => $pdfName,
            'username' => ($creator_id == $user_id) ? "{$admin->first_name} {$admin->last_name}" : "{$user->first_name} {$user->last_name}",
            'type' => 'Reaction'
        ];

        Pdf::create($createData);
        return response()->json([
            'success' => true,
            'pdf' => asset("gpdf\\") . $pdfName
        ]);
    }

    public function getCartDataForPdf($langCode, $carts, $gsSetting)
    {
        $submenus = array();
        $subid = 0;
        $subname = "";
        $topic = array();

        foreach ($carts as $cart) {
            if ($cart->name == 'Analysis') {
                $tmp = explode('-', $cart->options->submenu_id);
                $subid = $tmp[1] ?? 0;
                if ($tmp[0] == 'sub') {
                    $subname  = DB::table('submenus')->where('id', $subid)->first();
                    $sub_name = ($langCode == 'de') ? $subname->menu_name : $subname->{$langCode.'_menu_name'};
                } else {
                    $subname = DB::table('user_submenus')->where('id', $subid)->first();
                    $sub_name = $subname->name ?? '';
                }
            } else {
                $subid = $cart->options->submenu_id;
                $subname  = DB::table('submenus')->where('id', $subid)->first();
                $sub_name = ($langCode == 'de') ? $subname->menu_name : $subname->{$langCode.'_menu_name'};
            }

            if (!in_array($subid, $submenus)) {
                $submenus[] = $subid;
                $submenuNames[] = $sub_name;
            }
        }

        $allSubmenus = array();

        foreach ($submenus as $key => $sub) {
            $analyses = array();

            foreach ($carts as $ckey => $cart) {

                if ($cart->name == 'Topic') {
                    array_push($topic, $cart);
                    unset($carts[$ckey]);
                    continue;
                }

                if ($cart->name == 'Analysis') {
                    $tmp = explode('-', $cart->options->submenu_id);
                    $subids = $tmp[1] ?? 0;
                } else {
                    $subids = $cart->options->submenu_id;
                }

                if ($subids == $sub) {
                    if ($cart->options->heart >= 50) $heart = "heart-red.png";
                    else if ($cart->options->heart <= 49) $heart = "heart-gray.png";
                    if ($cart->options->male <= 30) $male = "man-red.png";
                    else if ($cart->options->male >= 31) $male = "man-gray.png";

                    $bar_color = $cart->options->color;

                    $causesName = array();

                    if ($cart->name == 'Analysis') {
                        foreach ($carts as $count => $cau) {
                            if ($cau->options->causes_id != 0 && $subids == $sub && $cau->options->analysisID == $cart->options->analysisID && $cau->options->type == "Causes") {
                                $causesName[0] = $cau->options->analysisName;
                                $cart->name = 'done';
                                unset($carts[$count]);
                            } else if ($cau->options->causes_id != 0 && $subids == $sub && $cau->options->analysisID == $cart->options->analysisID && $cau->options->type == "Medium") {
                                $causesName[1] = $cau->options->analysisName;
                                $cart->name = 'done';
                                unset($carts[$count]);
                            } else if ($cau->options->causes_id != 0 && $subids == $sub && $cau->options->analysisID == $cart->options->analysisID && $cau->options->type == "Tipp") {
                                $causesName[2] = $cau->options->analysisName;
                                $cart->name = 'done';
                                unset($carts[$count]);
                            }
                        }
                        $desc = Analyse::where('id', $cart->options->analysisID)->first();
                    } else {

                        if ($cart->options->type == "Causes" && $subids == $sub) {
                            $causesName[0] = $cart->options->causes_id;
                            $tmpdata['discription'] = Cause::find($cart->options->causes_id);
                            $cart->name = 'done';
                            unset($carts[$ckey]);
                        } else if ($cart->options->type == "Medium" && $subids == $sub) {
                            $causesName[1] = $cart->options->medium_id;
                            $tmpdata['discription'] = Cause::find($cart->options->medium_id);
                            $cart->name = 'done';
                            unset($carts[$ckey]);
                        } else if ($cart->options->type == "Tipp" && $subids == $sub) {
                            $causesName[2] = $cart->options->tipp_id;
                            $tmpdata['discription'] = Cause::find($cart->options->tipp_id);
                            $cart->name = 'done';
                            unset($carts[$ckey]);
                        } else if ($cart->options->type == "Einfluss" && $subids == $sub) {
                            $causesName[3] = $cart->options->causes_id;
                            $tmpdata['discription'] = Cause::find($cart->options->causes_id);
                            $cart->name = 'done';
                            unset($carts[$ckey]);
                        } else if ($cart->options->type == "Fokus" && $subids == $sub) {
                            $causesName[4] = $cart->options->causes_id;
                            $tmpdata['discription'] = Cause::find($cart->options->causes_id);
                            $cart->name = 'done';
                            unset($carts[$ckey]);
                        }
                    }
                    $color = $this->getColor($gsSetting,$cart->options->calculation);
                    $percentageColor = $this->getColor($gsSetting,$cart->options->percentage);

                    $tmpdata['name']  = $cart->options->analysisName;
                    $tmpdata['calculation'] = (int)$cart->options->calculation;
                    $tmpdata['color'] = $color;
                    $tmpdata["heart_img"] = $heart;
                    $tmpdata["male_img"]  = $male;
                    $tmpdata["description"]  = ($desc->description == "") ? "" : $desc->description;
                    $tmpdata['body_desc']   = ($desc->body_desc == "") ? "" : $desc->body_desc;
                    $tmpdata['mental_desc'] = ($desc->mental_desc == "") ? "" : $desc->mental_desc;
                    $tmpdata['cause']  = isset($causesName[0]) ? $causesName[0] : "";
                    $tmpdata['medium'] = isset($causesName[1]) ? $causesName[1] : "";
                    $tmpdata['tipp']   = isset($causesName[2]) ? $causesName[2] : "";
                    $tmpdata['einflu'] = isset($causesName[3]) ? $causesName[3] : "";
                    $tmpdata['focus']  = isset($causesName[4]) ? $causesName[4] : "";                    
                    $tmpdata['type']   = $cart->options->type;
                    $tmpdata['percentage']   = (int)$cart->options->percentage;
                    $tmpdata['percentageColor']   = $percentageColor;
                    $tmpdata['male_val']   = (int)$cart->options->male;
                    $tmpdata['heart_val']   = (int)$cart->options->heart;
                    $tmpdata['others']   = $cart->options->others;

                    $analyses[] = $tmpdata;
                }
            }

            $tmpsubmenu = array();
            $tmpsubmenu['submenu_name'] = $submenuNames[$key];
            $tmpsubmenu['analyses'] = $analyses;

            $allSubmenus[] = $tmpsubmenu;
        }
        $data['allSubmenus'] = $allSubmenus;
        $data['topic'] = $topic;
        return $data;
    }

    public function getCronProgress(Request $request) {
        $progress = DB::table('cron_setups')->select(['id','setup_status'])->whereIn('id', $request->id)->get();
        $status = [];
        foreach ($progress as $row) {
            $status[$row->id] = $row->setup_status;
        }

        return response()->json(['success' => true, 'status' => $status]);
    }

    public function saveCartToPackage($user_id)
    {
        #check Package name exist or not
        date_default_timezone_set('Europe/Vienna');
        // $getCartData = $this->getFromCartRA($user);
        $getCartData = $this->getCronEmailCart($user_id);

        if (empty($getCartData))
            return response()->json(['success' => true]);

        $package =  new CartPackage;
        $package->package_name = request()->package_name??trans('action.auto_save');
        $package->unique_id = SessionIdManager::invalidateSessionId();
        $package->save();
        $package->users()->attach($user_id);
        $package_id =  $package->id;

        #cart content
        foreach ($getCartData as $key => $cart) {
            $__data = $cart->options->toArray();
            $cartContent = new CartContent();
            $cartContent->product_id     = $__data['productID'];
            $cartContent->analyse_id     = $__data['analysisID'];
            $cartContent->submenu_id     = $__data['submenu_id'];
            $cartContent->price          = $__data['price'];
            $cartContent->calculation    = $__data['calculation'];
            $cartContent->male           = $__data['male'];
            $cartContent->heart          = $__data['heart'];
            $cartContent->name           = $__data['analysisName'];
            $cartContent->causes_id      = $__data['causes_id'];
            $cartContent->medium_id      = $__data['medium_id'];
            $cartContent->tipp_id        = $__data['tipp_id'];
            $cartContent->color          = $__data['color'];
            $cartContent->type           = $__data['type'];
            if (!empty($__data)) {
                if ($cartContent->save()) { #data Save to Cart Content Table
                    $cartContent->cartpackges()->attach($package_id);
                }
            }
        }
        // $this->addToCartRA($user);
        return response()->json([
            'success' => true
        ]);
    }

    public function empty_trash()
    {
        $trush = app('App\Services\CronModule\CronControllerService')->cleanTrush(getAuthID());

        return response()->json([
            'success' => $trush
        ]);
    }

    /**
     * Does distroy CronEmailCart record from cart instance
     * @return status
     */
    public function clearCronEamilCart()
    {
        if (Cart::instance("CronEMailCart")->destroy()) return true;
        else return false;
    }
    /**
     * Does serve all cart data from Shopping Cart
     * @param userID
     * @return array
     */
    private function getCronEmailCart($uID)
    {
        #store session data on DB->shoppingcart
        $cart_result = array();
        foreach (Cart::instance('CronEMailCart')->content()->where('id', $uID . 'RA') as $key => $cartValue) {
            $cart_result[] = $cartValue;
        }
        return $cart_result;
    }

    public function getIPAddress() {
        return $_SERVER['REMOTE_ADDR'] ?? ($_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['HTTP_CLIENT_IP']);
    }

    public function getAvilableDateTime(Request $request)
    {
        try {
            $timeZone = $request->timezone;
            $format = (app()->getLocale() == 'de') ? 'd.m.Y H:i' : 'Y/m/d H:i';

            $cronSetting = DB::table('cron_settings')
                ->select('cronsetting_options.pre_time')
                ->join('cronsetting_options', 'cron_settings.id', 'cronsetting_options.cron_setting_id')
                ->where('cron_settings.user_id', getAuthID())
                ->first();

            if (!$cronSetting) {
                throw new \Exception('Cron settings not found');
            }

            $viennaTime = Carbon::now('Europe/Vienna');
            $userTime = Carbon::now($timeZone);

            // Calculate time difference between Vienna and user's timezone
            $hoursDiff = (int) $viennaTime->diffInHours($userTime);

            // Set default time based on Vienna time adjusted for user's timezone
            $defaultTime = Carbon::now('Europe/Vienna')->addHours($hoursDiff);

            // Apply pre-time settings
            if ($cronSetting->pre_time === '00:00') {
                $defaultTime->addMinutes(5);
            } else {
                $defaultTime->setTimeFromTimeString($cronSetting->pre_time);
            }

            // If the calculated time is in the past, move it to next day
            if ($defaultTime->lt($userTime)) {
                $defaultTime->addDay();
            }

            return [
                'success' => true,
                'minDate' => $userTime->format($format),
                'format' => $format,
                'setDefault' => $defaultTime->format($format),
                'time' => $defaultTime->format('H:i'),
                'timeAlert' => trans('action.timeChangeAlert') . $userTime->format('H:i')
            ];
        } catch (\Throwable $th) {
            // Fallback to a simple 5-minutes-from-now time if anything fails
            $fallbackTime = Carbon::now($timeZone)->addMinutes(5);

            return [
                'success' => true,
                'minDate' => Carbon::now($timeZone)->format($format),
                'format' => $format,
                'setDefault' => $fallbackTime->format($format),
                'time' => $fallbackTime->format('H:i'),
                'timeAlert' => trans('action.timeChangeAlert') . $fallbackTime->format('H:i')
            ];
        }
    }

    public function show_treat_overview($cronid, $uniqueid)
    {
        $cron = app('App\Services\CronModule\CronControllerService')->getCronById($cronid, ['cronsetuptimes', 'get_therapist', 'selecteduser']);

        $treatment = collect();
        $remaining_crons = ($cron->cronsetuptimes ?? collect())->sort(function ($a, $b) {
            $aIsToday = Carbon::parse($a->start_time)->isSameDay(Carbon::now());
            $bIsToday = Carbon::parse($b->start_time)->isSameDay(Carbon::now());

            if ($aIsToday && !$bIsToday) {
                return -1; // $a is today, so it comes first
            } elseif (!$aIsToday && $bIsToday) {
                return 1; // $b is today, so it comes first
            } else {
                // If both are today or both are not today, sort in descending order of start_time
                return Carbon::parse($b->start_time)->timestamp - Carbon::parse($a->start_time)->timestamp;
            }
        });

        // Collect today's data
        $todayData = $remaining_crons->filter(function ($cron) {
            return Carbon::parse($cron->start_time)->isSameDay(Carbon::now());
        });

        // Push today's data to $treatment
        $treatment = $treatment->concat($todayData);

        // Remaining data is already sorted in descending order
        // $remaining_crons now contains today's data at the beginning and others sorted in descending order
        $data['userDetails'] = User::find($cron->selected_user);

        return view('Frontend.partials.includes.treatment-overview', compact('data','cron', 'treatment', 'remaining_crons'));
    }

    public function clearSmptSetting()
    {
        try{
            $smtp = Smtp::where('user_id', getAuthID())->first();
            $smtp->sender_name    = NULL;
            $smtp->smtp_email     = NULL;
            $smtp->smtp_host      = NULL;
            $smtp->smtp_port      = NULL;
            $smtp->smtp_ssl       = NULL;
            $smtp->smtp_user_name = NULL;
            $smtp->smtp_password  = NULL;
            $smtp->update();
            return response()->json(['success' => true]);
        }catch(\Exception $e){
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Save submenu selections to the session
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveSubmenuSelections(Request $request)
    {
        $selections = $request->input('selections', []);
        Session::put('savedCronSubmenus', $selections);
        
        return response()->json(['success' => true]);
    }

    /**
     * Get submenu selections from the session
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSubmenuSelections()
    {
        $selections = Session::get('savedCronSubmenus');
        
        return response()->json([
            'success' => true,
            'selections' => $selections
        ]);
    }

    /**
     * Clear submenu selections from the session
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function clearSubmenuSelections()
    {
        Session::forget('savedCronSubmenus');
        
        return response()->json(['success' => true]);
    }
}
