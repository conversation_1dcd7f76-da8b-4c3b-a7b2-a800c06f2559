<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Model\Analyse;
use App\Model\BodyImage;
use App\Model\MentalImage;
use App\Model\Treatment_custom_image;
use App\Model\User;
use App\Services\EnfitGPT\TreatGenerator;
use App\Services\Treatment\Interfaces\TreatmentServiceInterfaces;
use Gloudemans\Shoppingcart\Facades\Cart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class TreatController extends Controller
{
    protected $treatmentService;
    protected $cache_time = 1440; // 1 day in minutes

    function __construct(TreatmentServiceInterfaces $treatmentService)
    {
        $this->treatmentService = $treatmentService;
    }

    public function treat()
    {
        $data = array();
        $data['userDetails'] = User::with('useroption:user_id,ran_ana,ra_status,ra_pdf_status')->find(getUserId());
        $data['random_status'] = $data['userDetails']->userOption->ran_ana;
        $allBGImage = Treatment_custom_image::get(['id', 'image']);
        $cartData = getCartData();
        $response = $this->treatmentService->treatStart($cartData);

        return view('Frontend.treat.treat', ['data' => $data, 'status' => $response['status'], 'cartRecords' => $response['cartRecords'], 'allBGImage' => $allBGImage, 'bodyImages' => $response['bodyImages'], 'mentalImages' => $response['mentalImages'], 'globalImages' => $response['globalImages']]);
    }


    public function durPower()
    {
        $data = array();
        $userid = getUserId();
        $allBGImage = Treatment_custom_image::get(['id', 'image']);
        $data['userDetails'] = User::with('useroption')->find($userid);
        $data['random_status'] = DB::table('treatment_users_images')->where('user_id', $data['userDetails']->id)->first()->rn_status;

        return view('Frontend.treat.dur_power', compact('allBGImage', 'data'));
    }

    public function optimizedAnalysis()
    {
        $data = array();
        $data['userDetails']    = User::with('useroption:user_id,ran_ana,ra_status,ra_pdf_status')->find(getUserId());
        $data['randomAnalysis'] = $data['userDetails']->userOption->ran_ana;
        // if (Cache::has('treatment' . getAuthId())) Cache::forget('treatment' . getAuthId());
        $cartRecords = Cart::instance('wishlist')->content()->where('id', getUserId());
        $result = $this->treatmentService->optimizeBeforePageLoad($cartRecords);
        $allBGImage = Treatment_custom_image::all();

        Cache::put('treatment' . getUserId(), $result['cartRecords'], 600);
        // if (!Cache::has(('treatment' . getAuthId()))) {
        //     $result = $this->treatmentService->optimizeBeforePageLoad($cartRecords);
        //     Cache::put('treatment' . getAuthId(), $result['cartRecords'], 600);
        // }
        return response()->json(['status' => $result['status'], 'carts' => $result['cartRecords'], 'randomValues' => $result['randomValues'], 'anaNames' => $result['anaNames'], 'newValues' => $result['newValues'], 'colors' => $result['colors'], 'img' => $result['img'], 'data' => $data, 'allBGImage' => $allBGImage]);
    }
    public function optimizedTreat()
    {
        $data = array();
        $allBGImage = Treatment_custom_image::get(['id', 'image']);
        $data['userDetails'] = User::with('useroption:user_id,ran_ana,ra_status,ra_pdf_status')->find(getUserId());
        $data['random_status'] = $data['userDetails']->userOption->ran_ana;
        $cartRecords = Cache::has('treatment' . getUserId()) ? Cache::get('treatment' . getUserId()) : $this->treatmentService->optimizeBeforePageLoad(Cart::instance('wishlist')->content()->where('id', getUserId()))['cartRecords'];
        $anaIds = array();
        if (!empty($cartRecords)) {
            foreach ($cartRecords as $cart) {
                if ($cart->options->price >= 1 && $cart->options->type == "Analysis") $anaIds[] = $cart->options->analysisID;
            }

            if (!empty($anaIds)) {
                $bodyImages = BodyImage::whereIn('analyse_id', $anaIds)->select('analyse_id', 'image')->get();
                $mentalImages = MentalImage::whereIn('analyse_id', $anaIds)->select('analyse_id', 'image')->get();
                $globalImages = Analyse::whereIn('id', $anaIds)->select('id', 'desc_image')->get();
            }
        }
        $status = 0;
        return view('Frontend.treat.treat', compact('cartRecords', 'status', 'allBGImage', 'bodyImages', 'mentalImages', 'globalImages', 'data'));
    }

    /**
     * optimize result save
     * @return json response true
     */
    public function optimizedResultSave()
    {
        $result = $this->treatmentService->optimizedResultSave(getCartData(), request());
        if (Cache::has(('treatment' . getAuthId()))) Cache::forget('treatment' . getAuthId());
        return response()->json(['success' => $result]);
    }
    /**
     * create pdf with reaction result
     * @return pdf-view
     */
    public function createPDF()
    {
        $response = $this->treatmentService->createPDF(getCartData(), request());
        return response()->json([
            'success' => $response['success'],
            'message' => $response['message'],
            'pdf' => $response['pdf']
        ]);
    }


    #create pdf send as a email with reaction
    public function createandsand()
    {
        $response = $this->treatmentService->createandsand(getCartData(), request());
        return response()->json([
            'status' => $response['mail_status'],
            'receiverinfo' => $response['receiver']
        ]);

    }


    /**
     * remove all data from cart list
     * @return success
     */
    public function clean_cart()
    {
        if (Cache::has(('treatment' . getAuthId()))) Cache::forget('treatment' . getAuthId());
        foreach (getCartData() as $key => $cart) {
            Cart::remove($cart->rowId);
        }
        Cart::destroy();
        return response()->json(['success' => true]);
    }


    /**
     * Save user feedback
     */
    public function saveFeedback(Request $req){
        
        $user_id = $req->usid;
        $ques_id = $req->qid;
        $status = $req->status;

        $currentTimestamp = now();
        $fiveMinutesAgo = now()->subMinutes(5);

        // Check if a record exists for the same user and question within the last 5 minutes
        $existingRecord = DB::table('pvot_treatment_feedback')
            ->where('user_id', $user_id)
            ->where('ques_id', $ques_id)
            ->where('created_at', '>', $fiveMinutesAgo)
            ->first();

        if ($existingRecord) {
            // Update the existing record
            DB::table('pvot_treatment_feedback')
                ->where('user_id', $user_id)
                ->where('ques_id', $ques_id)
                ->where('created_at', '>', $fiveMinutesAgo)
                ->update([
                    'status' => $status
                ]);
        } else {
            // Insert a new record
            DB::table('pvot_treatment_feedback')->insert([
                'user_id' => $user_id,
                'ques_id' => $ques_id,
                'status' => $status,
                'created_at' => $currentTimestamp
            ]);
        }
        
        return response()->json(['success' => true,"msg" => trans('action.thanks')]);
    }

    public function enfitgpt(Request $request)
    {
        $userId = getUserId();
        $data = (new TreatGenerator())->getCachedTreatmentPlan($userId);
        $chatHistory = Cache::get('enfitgpt_chat_history_' . $userId, []);

        return view('Frontend.treat.enfitgpt', [
            'data' => $data,
            'chatHistory' => $chatHistory
        ]);
    }

    public function enfitgptCreate(Request $request)
    {
        $userId = getUserId();
        // When generating a new plan, clear the old chat history
        Cache::forget('enfitgpt_chat_history_' . $userId);
        $data = (new TreatGenerator())->execute($userId);

        return response()->json(['data' => $data]);
    }

    public function chat(Request $request)
    {
        $request->validate([
            'message' => 'required|string',
            'history' => 'nullable|array'
        ]);

        $userId = getUserId();
        $message = $request->input('message');
        $history = $request->input('history', []);

        // Get AI response
        $response = (new TreatGenerator())->chat($history, $message, $userId);

        // Update history with the new exchange
        $history[] = ['role' => 'user', 'content' => $message];
        $history[] = ['role' => 'assistant', 'content' => $response];

        // Store the updated history in the cache
        Cache::put('enfitgpt_chat_history_' . $userId, $history, $this->cache_time);

        return response()->json(['reply' => $response]);
    }

    public function clearEnfitgptChatHistory()
    {
        Cache::forget('enfitgpt_chat_history_' . getUserId());
        return response()->json(['success' => true, 'message' => 'Chat history cleared.']);
    }
}