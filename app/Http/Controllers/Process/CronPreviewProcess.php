<?php

namespace App\Http\Controllers\Process;

use App\Http\Controllers\Controller;
use App\Model\Analyse;
use App\Model\CronsetupAnalysis;
use App\Model\GlobalSetting;
use App\Model\MenuSetting;
use App\Model\UserSubMenu;

class CronPreviewProcess extends Controller
{
    /**
     * Cron Selected Submenus
     *
     * @param  array  $data
     * @param  object $request
     * @return array
     */

    public function cronSubmens($data, $request)
    {
        $submenus = array_unique($request->cronsubmenu);
        $ownSubmenus = array();
        $normalSubmenus = array();
        $proSub = array();

        if (!empty($submenus)) {
            foreach ($submenus as $sub) {

                $subidEx = explode('-', $sub);
                $subtype = ($subidEx[1] == "own") ? 1 : 0;

                // submenus array;
                if ($subtype == 0) $normalSubmenus[] =  $subidEx[0];
                else $ownSubmenus[] =  $subidEx[0];
            }

            if (!empty($normalSubmenus))
                $proSub['normalSubPro'] = getProductBySubmenu($normalSubmenus);
            if (!empty($ownSubmenus))
                $proSub['ownSubPro'] = getUserOwnMenuBySubId($ownSubmenus);
        }

        return $proSub;
    }

    /**
     * Cron Preview via Cart
     *
     * @param  array  $cart
     * @param  object $request
     * @return array
     */

    public function cartCronPreview($cart, $request)
    {
        $types = array('Analysis', 'Causes', 'Medium', 'Tipp', 'Einfluss', 'Fokus', 'Topic');
        $anaPreview = array();
        $cauIds = array();

        foreach ($cart as $key => $car_item) {
            if ($request->causes) $cauIds[] = $car_item->options->causes_id;
            if ($request->medium) $cauIds[] = $car_item->options->medium_id;
            if ($request->tipp) $cauIds[] = $car_item->options->tipp_id;
        }

        $causes = getAllCause($cauIds);

        foreach ($cart as $key => $car_item) {

            $typeIn = array_search($car_item->options->type, $types);

            $cauTitle = ($typeIn == 0 && $request->causes) ? $causes[$car_item->options->causes_id]->title : null;
            $midTitle = ($typeIn == 0 && $request->medium) ? $causes[$car_item->options->medium_id]->title : null;
            $tipTitle = ($typeIn == 0 && $request->tipp) ? $causes[$car_item->options->tipp_id]->title : null;

            $analyses[] = array(
                "ana_val"  => $car_item->options->calculation,
                "ana_id"   => $car_item->options->analysisID,
                "ana_name" => $car_item->options->analysisName,
                "price"    => $car_item->options->price,
                "causes"   => $cauTitle,
                "medium"   => $midTitle,
                "tipp"     => $tipTitle,
                "type"     => $typeIn,
            );

            $anaPreview[] = array(
                'sub_name' => $car_item->options->analysisName,
                'sub_id'   => $car_item->options->submenu_id,
                'pro_id'   => $car_item->options->analysisID,
                'analyses' => $analyses
            );

            $analyses = array();
        }

        return $anaPreview;
    }

    /**
     * Cron Preview via Duplicate
     *
     * @param  object $cronsetup
     * @param  object $request
     * @return array
     */

    public function duplicateCronPreview($cronsetup, $request)
    {
        $anaPreview = array();
        $cauIds = array();
        
        $firstCronTime = $cronsetup->cronsetuptimes()->orderBy('start_time', 'asc')->first();
        $setupAnalyses = CronsetupAnalysis::where('cron_setup_id', $cronsetup->id)
            ->where('start_time', $firstCronTime->start_time)
            ->orderBy('id', 'asc')
            ->get();

        foreach ($setupAnalyses as $key => $car_item) {
            if ($request->causes) $cauIds[] = $car_item->causes;
            if ($request->medium) $cauIds[] = $car_item->medium;
            if ($request->tipp) $cauIds[] = $car_item->tipp;
        }

        $causes = getAllCause($cauIds);

        foreach ($setupAnalyses as $key => $car_item) {

            $cauTitle = ($car_item->type_status == 0 && $request->causes) ? $causes[$car_item->causes]->title : null;
            $midTitle = ($car_item->type_status == 0 && $request->medium) ? $causes[$car_item->medium]->title : null;
            $tipTitle = ($car_item->type_status == 0 && $request->tipp) ? $causes[$car_item->tipp]->title : null;

            if ($car_item->type_status == 6) {
                $analysisName = $car_item->topic;
                $analyseId = 1; 
            } else {
                $analysis = Analyse::find($car_item->analyse_id);
                $analysisName = $analysis ? $analysis->name : 'Unknown';
                $analyseId = $car_item->analyse_id;
            }

            // Handle cause names for non-analysis types
            if (in_array($car_item->type_status, [1, 2, 3, 4, 5])) {
                $causeId = null;
                if ($car_item->type_status == 1 || $car_item->type_status == 4 || $car_item->type_status == 5) {
                    $causeId = $car_item->causes;
                } elseif ($car_item->type_status == 2) {
                    $causeId = $car_item->medium;
                } elseif ($car_item->type_status == 3) {
                    $causeId = $car_item->tipp;
                }
                
                if ($causeId) {
                    $cause = getCauseNameByCauseid($causeId);
                    $analysisName = $cause;
                    $analyseId = 0;
                }
            }

            $analyses[] = array(
                "ana_val"  => (int)$car_item->calculation,
                "ana_id"   => $analyseId,
                "ana_name" => $analysisName,
                "price"    => (int)$car_item->time,
                "causes"   => $cauTitle,
                "medium"   => $midTitle,
                "tipp"     => $tipTitle,
                "type"     => $car_item->type_status,
            );

            $anaPreview[] = array(
                'sub_name' => $analysisName,
                'sub_id'   => 0,
                'pro_id'   => ($car_item->type_status == 6) ? 1 : $car_item->analyse_id,
                'analyses' => $analyses
            );

            $analyses = array();
        }

        return $anaPreview;
    }

    /**
     * Cron Preview via Normal Setup
     *
     * @param  array $data
     * @param  object $request
     * @return array
     */

    public function normalCronPreview($data, $request)
    {
        $submenus = array_unique($request->cronsubmenu);
        $subPro = $this->cronSubmens($data, $request);
        $cronSetting = $data['cron_setting'];
        $user = $data['user'];
        $anaPreview = array();
        $globalValues = GlobalSetting::find(1);
        $ranValue = $data['ran_value'];
        $causesids = array();
        $mediumids = array();
        $tippids = array();
        foreach ($submenus as $sub) {

            $subidEx = explode('-', $sub);

            if ($subidEx[1] == 'sub') {
                $subid  = $subidEx[0];
                $pro    = $subPro['normalSubPro'][$subid];
                $proid  = $pro->id;
                $productColors = productColors($proid);

                $pools  = poolsId($subid);
                $subd   = $pools;
                $poolid = poolsIds($subid);
                $priceValue = priceValue($proid, $subid, 0);
            } else {
                $subid  = $subidEx[0];
                $pro    = $subPro['ownSubPro'][$subid];
                $proid  = $pro->id;

                $subd = UserSubMenu::find($subid);
                $colorValue = MenuSetting::where('menu_id', $proid)->first();
                $ownPrice = $colorValue;
                $colorStatus = false;

                if ($colorValue != null) {
                    if ($colorValue->red_min == null && $colorValue->red_max == null) {
                        if ($colorValue->orange_min == null && $colorValue->orange_max == null) {
                            if ($colorValue->green_min == null && $colorValue->green_max == null) {
                                $colorValue = $globalValues;
                                $colorStatus = true;
                            }
                        }
                    }
                }
            }

            if ($cronSetting->cronsettingoption->preview_status != 1) {

                if ($cronSetting->red_value == 1 && $subidEx[1] == 'sub') {
                    $from = $productColors->red_min;
                    $to   = $productColors->red_max;
                } elseif ($cronSetting->red_value == 1) {
                    if ($colorStatus == false) {
                        $from = $colorValue->red_min;
                        $to   = $colorValue->red_max;
                    } else {
                        $from = $colorValue->gs_red_min;
                        $to   = $colorValue->gs_red_max;
                    }
                }else {
                    $from = $cronSetting->from;
                    $to   = $cronSetting->to;
                }

                $biorythsystem = array('p' => $data['bioryth']->gs_bioryth_p, 'm' => $data['bioryth']->gs_bioryth_m);

                if ($request->topic != "")
                    $user->thema_speichern = $request->topic;
                $strM = $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern;

                if ($user->userfilter?->filter_type == 1) $sortBy = "asc";
                if ($user->userfilter?->filter_type == 2) $sortBy = "desc";

                $crondate = ($request->crondate == null) ? date('Y-m-d', strtotime($request->start_time_duepower)) : date('Y-m-d', strtotime(request()->crondate));
                $user->datumcore = $crondate;
                $user->gebdatum = date_change(date('d-m-Y', strtotime($user->gebdatum)));

                // $analyses = Pool::with("analyses")->whereIn('id', $poolid->pools)->get();

                if ($subidEx[1] == 'sub') {
                    if (!empty($poolid)) {
                        $analyses = Analyse::whereHas('pools', function ($query) use ($poolid) {
                            $query->whereIn('pool_id', $poolid);
                        })->when(isset($sortBy), function ($query, $sortBy) {
                            return $query->orderBy('name', $sortBy);
                        })->get();
                    }
                } else {
                    $ownana = UserSubMenu::with('analyses')->where('id', $subid)->first();
                    $analyses = $ownana->analyses;
                }

                if (!empty($analyses)) {

                    if ($subidEx[1] == 'sub') {
                        $all_analysis_id = array();
                        foreach ($analyses as $analysis)
                            $all_analysis_id[] = $analysis->id;

                        $todayCause    = savedCauses($subid, $all_analysis_id, $user->id);
                        $randomCauses  = randomCauses($subid);
                    }

                    foreach ($analyses as $print) {
                        $anaid = $print->id;
                        $strP = $print->name;
                        $bioryth = $print->bioryth;

                        if ($print->bioryth == 0)  $bioryth = 28;
                        elseif ($print->bioryth < 0)  $bioryth = 28;
                        else  $bioryth = $print->bioryth;

                        #Analysis/Male/Heart/Oneday Calculation
                        $beergS  = calculationPS($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beergK  = calculationPK($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beerg   = calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                        $beergod = calculationoneday($strM, $strP, $user, $bioryth);


                        $ma      = round($beerg, 0);
                        $beergK  = round($beergK, 0);
                        $beergS  = round($beergS, 0);
                        $beergod = round($beergod, 00);


                        if (!empty($ranValue) && $user->useroption->pattern_switch == 1) {
                            if (array_key_exists($print->id, $ranValue)) {
                                $ma = $ranValue[$print->id];
                            }
                        }

                        $causeGroupid = null; $mediumGroupid = null; $tippGroupid = null;

                        if ($subidEx[1] == 'sub') {
                            // Cause/Medium/Tipp
                            if ($request->causes) {
                                if(!empty($todayCause['causes'])){
                                    if(array_key_exists($print->id, $todayCause['causes']))
                                        $causeGroupid = $todayCause['causes'][$print->id];
                                    else if(count($randomCauses['causes']) > 0)
                                        $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                                }else if(count($randomCauses['causes']) > 0) {
                                    $causeGroupid = $randomCauses['causes'][rand(0, count($randomCauses['causes']) - 1)];
                                }
                            }

                            if ($request->medium) {
                                if(!empty($todayCause['medium'])){
                                    if(array_key_exists($print->id, $todayCause['medium']))
                                        $mediumGroupid = $todayCause['medium'][$print->id];
                                    else if(count($randomCauses['medium']) > 0)
                                        $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                                }else if(count($randomCauses['medium']) > 0){
                                    $mediumGroupid = $randomCauses['medium'][rand(0, count($randomCauses['medium']) - 1)];
                                }
                            }

                            if ($request->tipp) {
                                if(!empty($todayCause['tipp'])){
                                    if(array_key_exists($print->id, $todayCause['tipp']))
                                        $tippGroupid = $todayCause['tipp'][$print->id];
                                    else if(count($randomCauses['tipp']) > 0)
                                        $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                                }else if(count($randomCauses['tipp']) > 0){
                                    $tippGroupid = $randomCauses['tipp'][rand(0, count($randomCauses['tipp']) - 1)];
                                }
                            }
                        }

                        // Random Price For Cart
                        if ($subidEx[1] == 'sub') {
                            if (empty($priceValue)) {
                                $priceValue = priceValue($proid, $subid, $anaid);
                            }
                            $randPrice  = rand($priceValue['min_price'], $priceValue['max_price']);
                        } else if($ownPrice){
                            if($ownPrice->gs_min_price == null) $ownPrice->gs_min_price = $globalValues->gs_min_price;
                            if($ownPrice->gs_max_price == null) $ownPrice->gs_max_price = $globalValues->gs_max_price;
                            $randPrice = rand($ownPrice->gs_min_price, $ownPrice->gs_max_price);
                        } else {
                            $randPrice  = rand(10, 100);
                        }

                        if ($ma >= $from && $ma <= $to) {
                            //all analyses
                            $analyse[] = array(
                                "ana_val" => $ma,
                                "ana_id" => $anaid,
                                "ana_name" => $strP,
                                "causes" => ($causeGroupid != null) ? $causeGroupid->title : null,
                                "medium" => ($mediumGroupid != null) ? $mediumGroupid->title : null,
                                "tipp"  => ($tippGroupid != null) ? $tippGroupid->title : null,
                                "price" => $randPrice,
                                "type"  => ($subidEx[1] == 'sub') ? 0 : "",
                            );

                            if ($request->causes) $causesids[] = $causeGroupid->id;
                            if ($request->medium) $mediumids[] = $mediumGroupid->id;
                            if ($request->tipp) $tippids[] = $tippGroupid->id;
                        }
                    }
                }
            }

            if ($subidEx[1] == 'sub') {
                $anaPreview[] = array(
                    'sub_name' => $subd->menu_name,
                    'sub_id' => $subd->id,
                    'pro_id' => $subd->product_id,
                    'analyses' => $analyse
                );
            } else {
                $anaPreview[] = array(
                    'sub_name' => $subd->name,
                    'sub_id' => $subd->id,
                    'pro_id' => $subd->menu_id,
                    'analyses' => $analyse
                );
            }

            $analyse = array();
        }

        $data['causesid'] = $causesids;
        $data['mediumid'] = $mediumids;
        $data['tippid'] = $tippids;
        $data['anaPreview'] = $anaPreview;
        return $data;
    }
}
