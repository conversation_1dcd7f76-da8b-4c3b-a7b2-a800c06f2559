<?php

namespace App\Model;

use App\Model\Analyse;
use App\Model\Causes;
use App\Model\CronsetupAnalysis;
use App\Model\UserAnalyse;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CronSetupTable extends Model
{
    protected $table = 'cron_setups';

    public function cronsetuptimes()
    {
        return $this->hasMany(CronsetupTimes::class, 'cronsetup_id', 'id')->orderBy('id','asc');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'selected_user','id');
    }

    public function cronsetup_option()
    {
        return $this->hasOne(CronsetupOption::class, 'cron_setup_id', 'id');
    }

    public function selecteduser()
    {
        return $this->belongsTo(User::class, 'selected_user', 'id');
    }

    public function get_therapist()
    {
        return $this->belongsTo(User::class, 'user_id','id');
    }

    public function causes()
    {
        $causes_name = (app()->getLocale() == '' || app()->getLocale() == 'de') ? 'causes' : app()->getLocale().'_causes';
        return $this->hasManyThrough(
            Causes::class,
            CronsetupAnalysis::class,
            'cron_setup_id',
            'id',
            'id',
            'causes'
        )->select([$causes_name.'.id', $causes_name.'.title']);
    }

    public function analyses()
    {
        $analyses_name = (app()->getLocale() == '' || app()->getLocale() == 'de') ? 'analyses' : app()->getLocale().'_analyses';
        return $this->hasManyThrough(
            Analyse::class,
            CronsetupAnalysis::class,
            'cron_setup_id',
            'id',
            'id',
            'analyse_id'
        )->select([$analyses_name.'.id', $analyses_name.'.name']);
    }

    public function getAnalysesAndCausesNameAttribute(): array
    {
        $allAnalyses = [];

        $cronAnalyses = CronsetupAnalysis::where('cron_setup_id', $this->id)->get();

        foreach ($cronAnalyses as $analyse) {
            if ($analyse->type_status == 6) {
                if (!empty($analyse->topic)) {
                    $allAnalyses[] = $analyse->topic;
                }
            } elseif ($analyse->type_status == 0) {
                if ($analyse->status == false) {
                    $triggeredAnalyse = Analyse::find($analyse->analyse_id);
                    if ($triggeredAnalyse) {
                        $allAnalyses[] = $triggeredAnalyse->name;
                    }
                } elseif ($analyse->status == true) {
                    $triggeredAnalyse = UserAnalyse::find($analyse->analyse_id);
                    if ($triggeredAnalyse) {
                        $allAnalyses[] = $triggeredAnalyse->name;
                    }
                }
            } elseif (in_array($analyse->type_status, [1, 2, 3, 4, 5])) {
                $causeId = $analyse->causes ?? $analyse->medium ?? $analyse->tipp;
                if ($causeId > 0) {
                    $triggeredCause = Causes::find($causeId);
                    if ($triggeredCause) {
                        $allAnalyses[] = $triggeredCause->title;
                    }
                }
            }
        }

        return collect($allAnalyses)->filter()->unique()->values()->all();
    }
}
