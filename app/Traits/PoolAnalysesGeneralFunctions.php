<?php

namespace App\Traits;

use App\Model\Analyse;
use App\Statics\Calculation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

trait PoolAnalysesGeneralFunctions
{

    public function processDiagram($OldsortedDiagram, $pool): array
    {
        return empty($OldsortedDiagram) ? ($pool ? $pool->analyses->pluck('name', 'id')->toArray() : []) : $OldsortedDiagram;
    }

    public function poolIdToUserLngBaseAnalysesGet(int $pool_id)
    {
        $locale = app()->getLocale();
        $anaQuery = Analyse::whereHas('pools', fn ($query) => $query->where('pool_id', $pool_id));

        if ($locale != '' && $locale != 'de') {
            $anaQuery->select($locale . '_analyses.id', $locale . '_analyses.name', 'analyses.name as de_name', 'analyses.ana_min_price', 'analyses.ana_max_price','analyses.description','analyses.desc_image','analyses.body_desc','analyses.mental_desc')
            ->join('analyses', 'analyses.id', $locale . '_analyses.id');
        } else {
            $anaQuery->select('analyses.id', 'analyses.name', 'analyses.ana_min_price', 'analyses.ana_max_price','analyses.description','analyses.desc_image','analyses.body_desc','analyses.mental_desc');
        }
        return $anaQuery->orderBy('id', 'asc')->get();
       
    }

    public function executeAnalysisCalculation($deAnalysesForCalculation, $analysisId = null)
    {
        $datumcore = Calculation::getDatumcore()?->format('Y-m-d H:i:s') ?? auth()->user()->datumcore;
        $cacheKey = 'analysis_calculation_' . $deAnalysesForCalculation . '_analysis_' . $analysisId . '_' . $datumcore;
        $cachedResult = Cache::get($cacheKey) ?? null;

        if ($cachedResult) {
            return $cachedResult;
        }

        $allAnalyses = app('App\Http\Controllers\Frontend\DashboardController')
            ->productAnalyses(null, null, null, [$deAnalysesForCalculation]);

        if (is_array($allAnalyses) && !empty($allAnalyses['analyses'])) {
            foreach ($allAnalyses['analyses'] as $analysis) {

                if($analysisId && $analysis['anaid'] != $analysisId) {
                    continue;
                }

                $output[] = [
                    'name'      => $analysis['anaName'],
                    'val'       => $analysis['anaVal'],
                    'color'     => $analysis['anaColor'],
                    'analysis_id' => $analysis['anaid'],
                ];
            }
        } else {
            // If no analyses were returned, fall back to a single “No Analyses Available”
            $output[] = [
                'name'      => 'No Analyses Available',
                'val'       => 0,
                'color'     => '#000000', // Default color for no analyses
                'analysis_id' => null,
            ];
        }

        Cache::put($cacheKey, $output, 60 * 60 * 12); // Cache for 24 hours

        return $output;
    }

    public function createNewRequestForcalculation(object $user, array $langBaseAnalyses)
    {
        // Create a new request instance
        return new Request([
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'birthplace' => $user->gebort,
            'birthdate' => $user->gebdatum,
            'cal_day' => $user->datumcore,
            'analyses' => json_encode(array_values($langBaseAnalyses), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            'topic' => str_replace(' ', '', $user->thema_speichern ?? ''),
            "cal_for_month" => $user->calculation_with == 1 ? 'true' : 'false',
        ]);
    }

    public function processAndSortLangBaseData(array $targetedLangbaseAnalysesName, array $deAnalysesForCalculation, array $analysisCalculationResponse)
    {
        // Sort the array of targeted language base analyses by the keys of the German analyses for calculation
        $sortedAnalyses = $this->sortArrayByKeyOrder($targetedLangbaseAnalysesName['targetedAnalysesName'], $deAnalysesForCalculation);

        $order = array_keys($sortedAnalyses);
        $positionMap = array_flip($order);
        usort($analysisCalculationResponse, function($a, $b) use ($positionMap) {
            $posA = $positionMap[$a['analysis_id']] ?? PHP_INT_MAX;
            $posB = $positionMap[$b['analysis_id']] ?? PHP_INT_MAX;
            return $posA <=> $posB;
        });
        // Process user language base data according to the sorted analysis calculation response
        // add analysis_id the sorted result based on the targeted language base analyses id
        return $this->getAnalysesByIdFilter(
            $analysisCalculationResponse,
            array_flip($sortedAnalyses),
            $targetedLangbaseAnalysesName['description'] ?? [],
            $targetedLangbaseAnalysesName['desc_img'] ?? [],
            $targetedLangbaseAnalysesName['body_desc'] ?? [],
            $targetedLangbaseAnalysesName['mental_desc'] ?? [],
        );
    }

    public function diagramTypeBaseFormat($result, $diagramType,$filter=null)
    {
        $diagramType = strtolower($diagramType);
        switch ($diagramType) {
            case 'progress':
                return $this->progressFormat($result, $diagramType, $filter);
            default:
                $final_response = $this->formatResultAsExpected($result, $filter);
                return [
                    'success' => true,
                    'filter' => $filter ? true : false,
                    'labels' => $final_response[0],
                    'results' => !$filter ? $final_response[1] : [$final_response[1], $final_response[2]],
                    'diagram_type' => $diagramType,
                ];
        }
    }

    public function progressFormat($result, $diagramType,$filter)
    {
        return [
            'success' => true,
            'filter'=> $filter ? true : false,
            'results' => $result,
            'diagram_type' => $diagramType,
        ];
    }

    public function formatResultAsExpected(array $result, $filter): array
    {
        return !$filter
            ? [
                array_map(fn ($item) => $item['name'] . ': ' . $item['val'], $result),
                array_column($result, 'val'),
            ] : [
                array_column($result, 'name'),
                array_column($result, 'RED'),
                array_column($result, 'GREEN'),
            ];
    }

    public function filterAndSortArray(array $array, $sortType): array
    {
        usort($array, function($a, $b) use ($sortType) {
            switch ($sortType) {
                case 1:
                    return strcmp($a['name'], $b['name']); // A-Z
                case 2:
                    return strcmp($b['name'], $a['name']); // Z-A
                case 3:
                    return $a['val'] - $b['val']; // 1-100
                case 4:
                    return $b['val'] - $a['val']; // 100-1
                default:
                    return [];
            }
        });
        return $array;
    }

    public function sortArrayByKeyOrder(array $arrayToSort, array $referenceArray): array
    {
        // Extract the values from $arrayToSort based on the keys of $referenceArray
        return array_replace(array_intersect_key($referenceArray, $arrayToSort), $arrayToSort);
    }

    public function getAnalysesByIdFilter($analysisCalculationResponse, array $targetedLangbaseAnalysesName, array $description, array $descImg, array $bodyDesc, array $mentalDesc): array
    {
        return array_map(function ($item) use ($targetedLangbaseAnalysesName, $description, $descImg, $bodyDesc, $mentalDesc) {
            if (isset($targetedLangbaseAnalysesName[$item['name']])) {
                $id = $item['analysis_id'];
                isset($description[$id]) ? $item['description'] = $description[$id] : '';
                isset($descImg[$id]) ? $item['desc_img'] = $descImg[$id] : '';
                isset($bodyDesc[$id]) ? $item['bodyDesc'] = $bodyDesc[$id] : '';
                isset($mentalDesc[$id]) ? $item['mentalDesc'] = $mentalDesc[$id] : '';
            }
            return $item;
        }, $analysisCalculationResponse);
    }

    public function userLangBaseDataProcess($result, array $langBaseLabels): array
    {
        return array_map(function ($item, $userValue) {
            if (is_null($userValue)) {
                return $item;  // Return item as is if $userValue is null
            }
            return ['name' => $userValue] + (is_array($item) ? $item : []);
        }, $result, $langBaseLabels);
    }    

    public function analysesColorCountAlgorithm($analysisArray): array
    {
        $colors = [
            "#E84E1B" => "RED",
            "#F8B133" => "YELLOW",
            "#2FAB66" => "GREEN"
        ];
        $nameCounts = [];
        // Loop through each entry in $analysisArray
        foreach ($analysisArray as $entry) {
            // Loop through each item in the entry
            foreach ($entry as $item) {
                // Get the color of the current item
                $color = $colors[$item['color']] ?? null;
                // If the color is either red or yellow, merge it into red
                if ($color === 'RED' || $color === 'YELLOW') {
                    $color = 'RED';
                }
                // Get the name of the current item
                $name = $item['name'];
                // Initialize the count for the name if it doesn't exist
                if (!isset($nameCounts[$name])) {
                    $nameCounts[$name] = ['name'=>$name,'RED' => 0, 'GREEN' => 0];
                }
                // Increment the count for the color
                $nameCounts[$name][$color]++;
            }
        }
        return $nameCounts;
    }

    public function getColorBaseAnalysesFilter($analysisCalculationResponse, array $deAnalyses, string $color): array
    {
        return array_values(array_filter($analysisCalculationResponse, function ($item) use ($color) {
            return  $color ? $item['color'] == $color : $item ;
        }));
    }

}