<?php

namespace App\Traits;

trait CartTimeParserTrait
{
    /**
     * Parse total time and update component state
     */
    private function updateTimeFromTotalTime()
    {
        $totalTime = $this->getTotalTimeProperty();
        $this->parseAndUpdateTimeState($totalTime);
    }

    /**
     * Parse time string (HH:MM:SS or MM:SS) and update state
     */
    private function parseAndUpdateTimeState(string $timeString)
    {
        $timeParts = explode(':', $timeString);
        
        if (count($timeParts) === 3) {
            $this->maxHour = (int)$timeParts[0];
            $this->maxMinute = (int)$timeParts[1];
            $this->maxSecond = (int)$timeParts[2];
        } elseif (count($timeParts) === 2) {
            $this->maxHour = 0;
            $this->maxMinute = (int)$timeParts[0];
            $this->maxSecond = (int)$timeParts[1];
        } else {
            $this->resetTimeToDefault();
        }
        
        $this->validateTimeValues();
    }

    /**
     * Validate time values based on cart item count
     */
    private function validateTimeValues()
    {
        $itemCount = count($this->cartData);
        
        if ($itemCount === 0) {
            $this->resetTimeToDefault();
            return;
        }
        
        $maxAllowedHour = max(1, $itemCount);
        $hasChanged = false;
        
        if ($this->maxHour === 0 && $this->maxMinute === 0 && $this->maxSecond === 0) {
            $this->maxMinute = 1;
            $hasChanged = true;
        }
        
        if ($this->maxHour > $maxAllowedHour) {
            $this->maxHour = $maxAllowedHour;
            $this->maxMinute = 0;
            $this->maxSecond = 0;
            $hasChanged = true;
        } elseif ($this->maxHour >= $maxAllowedHour) {
            $this->maxMinute = 0;
            $this->maxSecond = 0;
            $hasChanged = true;
        }
        
        if ($hasChanged) {
            $this->saveTimeToSession();
        }
    }

    /**
     * Reset time values to default and update session
     */
    private function resetTimeToDefault()
    {
        $this->maxHour = 0;
        $this->maxMinute = 10;
        $this->maxSecond = 0;
        $this->saveTimeToSession();
    }
} 