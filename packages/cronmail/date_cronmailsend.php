<?php

class CronMail{

    public $db_con;
    public $site_link;

    public function __construct($start_date, $end_date)
    {
        include_once 'env.php';
        #db connection
        $this->db_con = mysqli_connect(getenv('DB_HOST'), getenv('DB_USERNAME'), getenv('DB_PASSWORD'), getenv('DB_DATABASE'));
        #sitelink
        $this->site_link = getenv('APP_URL');

        #cron mail send function
        $this->cronMailSend($start_date, $end_date);
    }

    public function cronMailSend($dt1, $dt2)
    {
        #default_timezone
        date_default_timezone_set('Europe/Vienna');

        #crontimes
        $crontime = "SELECT * FROM cronsetup_times WHERE ((start_send_mail >= '".$dt1."' AND start_send_mail <= '".$dt2."') OR (end_send_mail >= '".$dt1."' AND end_send_mail <= '".$dt2."')) AND status = 0";

        $crontime_query = mysqli_query($this->db_con, $crontime);
        #email placeholder
        $eph = "SELECT id, message, type FROM email_placeholders";
        $eph_query = mysqli_query($this->db_con, $eph);
        $eph_row = mysqli_fetch_all($eph_query, MYSQLI_ASSOC);

        $mailreport  = array();
        $send_status = false;

        while($timerow = mysqli_fetch_object($crontime_query))
        {
            $time_start = microtime(true);

            ### QUERY START ###

            #alltimes
            $crontimes = "SELECT id,cronsetup_id, end_time, end_send_mail, start_send_mail FROM cronsetup_times WHERE cronsetup_id = $timerow->cronsetup_id ORDER BY id DESC";
            $crontimes_query = mysqli_query($this->db_con, $crontimes);
            $crontimes_row   = mysqli_fetch_all($crontimes_query, MYSQLI_ASSOC);
            // $timescnt  = mysqli_num_rows($crontimes_query);


            #admin info
            $admin_info  = "SELECT cron_setups.*, cronsetup_options.*,
                            cron_settings.id as cronsettingid, cron_settings.send_email_time,
                            users.first_name, users.last_name, users.cron_email, users.user_status, users.language_id,users.email as admin_email,
                            smtps.* FROM cron_setups
                            -- smtps.*, email_templates.* FROM cron_setups
                            LEFT JOIN cronsetup_options ON cronsetup_options.cron_setup_id = cron_setups.id
                            LEFT JOIN cron_settings ON cron_settings.user_id = cron_setups.user_id
                            LEFT JOIN users ON users.id = cron_setups.user_id
                            LEFT JOIN smtps ON smtps.user_id = cron_setups.user_id
                            -- LEFT JOIN email_templates ON email_templates.cron_setting_id = cron_settings.id
                            WHERE cron_setups.id = ".$timerow->cronsetup_id;

            $admin_query = mysqli_query($this->db_con, $admin_info);
            $admin_row = mysqli_fetch_object($admin_query);

            #subuser info
            $subuser  = "SELECT first_name, last_name, language_id, email, cron_email, user_status  FROM users WHERE id = ".$admin_row->selected_user;
            $subuser_query = mysqli_query($this->db_con, $subuser);
            $subuser_row = mysqli_fetch_object($subuser_query);

            #analyses info
            $analyses = "SELECT id, cron_setup_id, start_time  FROM cronsetup_analyses WHERE start_time = '".$timerow->start_time."' and cron_setup_id = ".$timerow->cronsetup_id;
            $analyses_query =  mysqli_query($this->db_con, $analyses);
            $analyses_row = mysqli_fetch_assoc($analyses_query);

            $lang = 'de';
            if(!empty($subuser_row) && $subuser_row->language_id != '2'){
                $langQuery = mysqli_query($this->db_con,"SELECT short_code FROM languages WHERE id ='".$subuser_row->language_id."'");
                $lang = mysqli_fetch_object($langQuery)->short_code;
            }
            // setServerLocal($lang);

            $tableName = ($lang != 'de')? $lang.'_email_templates' : 'email_templates';
            $adminTemplateQuery = mysqli_query($this->db_con,"SELECT * FROM ".$tableName." WHERE cron_setting_id = '".$admin_row->cronsettingid."'");
            $adminTemplate = mysqli_fetch_object($adminTemplateQuery);
            ### QUERY END ###

            #before send time
	        $beforesend = 300;
	        if($admin_row->send_email_time != 0)
                $beforesend = $admin_row->send_email_time*60;

            #time info
	        // $start_time = strtotime($timerow->start_time);
	        $start_time = strtotime($timerow->start_send_mail);
            // $end_time   = $crontimes_row[0]['end_time'];
            $end_time   = $crontimes_row[0]['end_send_mail'];
            $nowtime    = strtotime(date('Y-m-d H:i:s'));
            $endtime    = strtotime($end_time);
	        $start_diff = $start_time - $nowtime;
            $end_diff   = $endtime - $nowtime;

            #For mail send header
            $cronsetupInfo['cronsetup_id'] = $crontimes_row[0]['cronsetup_id'] ?? 0;
            $cronsetupInfo['cronsetup_times_id'] = $crontimes_row[0]['id'] ?? 0;

            if(!$subuser_row) {
                continue;
            }

            if($admin_row->user_status == 0 || $subuser_row->user_status == 0) continue;
            if($admin_row->is_deleted == 1 || $admin_row->as_draft == 1 || $admin_row->is_stop == 1) continue;
            if($admin_row->status == 2) continue;

            #reciver info
            $sender_name = $admin_row->first_name." ".$admin_row->last_name;
            $receiver_email = ($admin_row->email == null)? $subuser_row->cron_email : $admin_row->email;
            if($receiver_email == "") $receiver_email = $subuser_row->email;
            $reciver_name = $subuser_row->first_name." ". $subuser_row->last_name;

            #smtp configuration
            $smtp_config = $this->smtpConfig($admin_row, $sender_name);

            #PDF link setup
	        $pdf_status = false;
            $pdflink = "";
            $pdfmsg = "";
            $click = ($subuser_row->language_id == 2) ? "Klick hier" : "Click Here";

            if($admin_row->pdf_export == 1)
	        {
	        	$pdflink = $this->site_link.'/treatment/view_pdf/'.$admin_row->selected_user.'/'.$timerow->cronsetup_id.'/'.$timerow->id.'/'.$admin_row->unique_id;
                $pdf_status = true;

                if($subuser_row->language_id == 2)
                    $pdfmsg = "Hier finden Sie den PDF-Anhang";
                else
                    $pdfmsg = "Please Find PDF Attachment";
            }


            if(!$analyses_row) $pdf_status = false;


            #array in for checking condition
            $others = [
                'diff' => $start_diff,
                'end_diff' => $end_diff,
                'before' => $beforesend,
                'start_time' => $this->getLangBaseDate($timerow->start_time,$lang,true),
                'end_time' => $this->getLangBaseDate($timerow->end_time,$lang,true),
                // 'start_time' => $timerow->start_time,
                // 'end_time' => $timerow->end_time,
                'nowtime' => $nowtime,
                'status' => $timerow->status,
                'red_day' => $timerow->red_day,
                'cronsetup_id' => $timerow->cronsetup_id,
                'crontime_id' => $timerow->id,
                'client_note' => $admin_row->client_note,
                'click' => $click,
                'pdf_link' => $pdflink
            ];
            // var_dump('others', $admin_row, $admin_row->user_id) && die();
            if((int)$admin_row->user_id == 1288){
                #emailtemplate setup
                $emailTemplate = $this->EmailTemplateNew($admin_row, $subuser_row, $analyses_row, $others, $eph_row, $adminTemplate);
            }else{
                #emailtemplate setup
                $emailTemplate = $this->EmailTemplate($admin_row, $subuser_row, $analyses_row, $others, $eph_row, $adminTemplate);
            }
            // $emailTemplate = $this->EmailContentTemplate($subuser_row, $adminTemplate, $eph, $lang,$others);

            #template structure converated
            if(!empty($emailTemplate)) {
                $subuser_row->first_name = $this->encodeToUtf8($subuser_row->first_name);
                $subuser_row->last_name = $this->encodeToUtf8($subuser_row->last_name);
                $templateStucture = $this->mailTamplates($emailTemplate['temp_body'], $subuser_row, $admin_row, $others);
            }
            $recipient = [];
            // check if end email is enabled and send to end_email_therapist
            if ($admin_row->end_email_therapist == 1 && isset($emailTemplate['end_template']) && $emailTemplate['end_template'] == true) {
                $recipient['cc'] = $admin_row->cron_email ? $admin_row->cron_email : $admin_row->admin_email;
            }

            $recipient['user_id'] = ((int)$admin_row->user_id == (int)$admin_row->selected_user) ? $admin_row->user_id : $admin_row->selected_user;
            #mail template array
            if(!empty($emailTemplate))
                $mailTamplate = ['mail' => true, 'mail_subject' => $emailTemplate['temp_title'], 'mail_body' => $templateStucture, "pdf_status" => $pdf_status, "pdf" => $pdflink, "pdfmsg" => $pdfmsg, "click" => $click,'mail_footer' => $emailTemplate['temp_footer']];

            if($receiver_email != "" && $timerow->status == 0 && (($admin_row->due_power == 0 && $admin_row->start_email == 1) || ($admin_row->due_power == 1 && $admin_row->start_email_duepower == 1))){

                $recipient['mail'] = $receiver_email;
                $recipient['name'] = $reciver_name;
                $datetime = date("Y-m-d H:i:s");

                $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate, $cronsetupInfo);

                $time_end = microtime(true);
                $ex_time = ($time_end - $time_start)/60;
                $runtime = round($ex_time, 3);

                $mailstatus = 1;
                if($crontimes_row[0]['id'] != $timerow->id) $mailstatus = 2;

                #updating cronsetuptime/treatment_time status
                $update_status = "UPDATE cronsetup_times SET status = $mailstatus where id = ".$timerow->id;
                $query = mysqli_query($this->db_con, $update_status);

                if($mailsend_status['status'] == "" || $mailsend_status['status'] == false) $mailsend_status['status'] = 0;

                #cronmail record
                $mailreport[] = "('" . $admin_row->user_id . "', '" . $admin_row->selected_user . "', '" . $receiver_email . "', '" . $runtime . "', '" . $mailsend_status['status'] . "', '" . $mailsend_status['msg'] . "', '" . 0 . "', '" . $datetime . "')";

                $send_status = true;

            }else if($receiver_email != "" && ($end_diff > 0 && $end_diff <= 120) && ($timerow->status == 0 || $timerow->status == 1) && (($admin_row->due_power == 0 && $admin_row->end_email == 1) || ($admin_row->due_power == 1 && $admin_row->end_email_duepower == 1))){

                $recipient['mail'] = $receiver_email;
                $recipient['name'] = $reciver_name;
                $datetime = date("Y-m-d H:i:s");

                $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate, $cronsetupInfo);

                $time_end = microtime(true);
                $ex_time = ($time_end - $time_start)/60;
                $runtime = round($ex_time, 3);

                #updating cronsetuptime/treatment_time status
                $update_status = "UPDATE cronsetup_times SET status = 2 where id = ".$timerow->id;
                $query = mysqli_query($this->db_con, $update_status);

                if($mailsend_status['status'] == "" || $mailsend_status['status'] == false) $mailsend_status['status'] = 0;

                #cronmail record
                $mailreport[] = "('" . $admin_row->user_id . "', '" . $admin_row->selected_user . "', '" . $receiver_email . "', '" . $runtime . "', '" . $mailsend_status['status'] . "', '" . $mailsend_status['msg'] . "', '" . 1 . "', '" . $datetime . "')";

                $send_status = true;
            }
        }

        if($send_status == true)
        {
            $record = "INSERT INTO cronmail_record(admin_id, user_id, user_email, execution_time, status, mail_response, mail_type, created_at) VALUES ".implode(",", $mailreport);
            $record_query = mysqli_query($this->db_con, $record);
        }
    }


    public function sendMail($config, $recipient, $template, $cronsetupInfo = [])
    {
        require_once('vendor/autoload.php');

        $mail_subject = $this->encodeToUtf8($template['mail_subject']);
        $fromName = $this->encodeToUtf8($config['from_name']);
        $toName = $this->encodeToUtf8($recipient['name']);
        $encryptedData = $this->userIdEncrypt($recipient['user_id']);
        $unsubscribeLink = $encryptedData ? '<p style="text-align: center; font-size: 12px;"><a href="' . getenv('APP_URL') . '/unsubscribe?user_id=' . $encryptedData . '">' .'Click to Unsubscribe'. '</a></p>' : '';

        try {
            // Configure the transport
            $dsn = sprintf(
                '%s://%s:%s@%s:%d',
                $config['smtp_encryption'] === 'ssl' ? 'smtps' : 'smtp',
                $config['smtp_username'],
                urlencode($config['smtp_password']),
                $config['smtp_host'],
                $config['smtp_port']
            );

            $transport = \Symfony\Component\Mailer\Transport::fromDsn($dsn);
            $mailer = new \Symfony\Component\Mailer\Mailer($transport);

            // Create the email
            $email = new \Symfony\Component\Mime\Email();
            $email->subject($mail_subject);
            $email->from(new \Symfony\Component\Mime\Address($config['from_email'], $fromName));
            $email->to(new \Symfony\Component\Mime\Address($recipient['mail'], $toName));

            // Check if CC recipient information is available and then add CC
            if (isset($recipient['cc'])) {
                if (is_array($recipient['cc'])) {
                    foreach ($recipient['cc'] as $ccEmail => $ccName) {
                        $email->addCc(new \Symfony\Component\Mime\Address($ccEmail, $ccName));
                    }
                } else {
                    $email->addCc($recipient['cc']);
                }
            }

            // Set the body based on template settings
            if ($template['pdf_status'] == true) {
                $htmlContent = "<p>" . $this->encodeToUtf8($template['mail_body']) . "</p>"
                    . "<p>" . $template['pdfmsg'] . " <a href='" . $template['pdf'] . "'><b>" . $template['click'] . "</b></a></p>"
                    . "<br><p>" . $this->encodeToUtf8($template['mail_footer']) . "</p>"
                    . $unsubscribeLink;
            } else {
                $htmlContent = "<p>" . $this->encodeToUtf8($template['mail_body']) . "</p>"
                    . "<br><p>" . $this->encodeToUtf8($template['mail_footer']) . "</p>"
                    . $unsubscribeLink;
            }

            $email->html($htmlContent);

            // Add Mailgun variables if cronsetup information is available
            if (isset($cronsetupInfo['cronsetup_id']) && isset($cronsetupInfo['cronsetup_times_id'])) {
                $email->getHeaders()->add(new \Symfony\Component\Mime\Header\UnstructuredHeader(
                    'X-Mailgun-Variables',
                    json_encode([
                        'cronsetup_id' => $cronsetupInfo['cronsetup_id'],
                        'cronsetup_times_id' => $cronsetupInfo['cronsetup_times_id']
                    ])
                ));
            }

            // Send the email
            $mailer->send($email);

            $response = [
                'status' => true,
                'msg' => "success"
            ];

            return $response;

        } catch (\Exception $ex) {
            $error_msg = $ex->getMessage();

            $errorData = [
                'error' => $error_msg,
                'recipient' => $recipient,
                'template' => $template,
                'config' => $config,
                'cronsetupInfo' => $cronsetupInfo,
                'cronsetup_id' => $cronsetupInfo['cronsetup_id'] ?? 0,
                'cronsetup_times_id' => $cronsetupInfo['cronsetup_times_id'] ?? 0
            ];

            $this->log(json_encode($errorData));

            return [
                'status' => false,
                'msg' => $error_msg
            ];
        }
    }

    public function log($message) {
        $logPath = getenv('LOG_PATH') . 'cronmail-' . date('Y-m-d') . '.log';
        $log = date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL;
        file_put_contents($logPath, $log, FILE_APPEND);

        //remove log files older than 7 days
        $files = glob(getenv('LOG_PATH') . 'cronmail-*.log');
        $now = time();
        foreach ($files as $file) {
            if (is_file($file) && $now - filemtime($file) >= 7 * 24 * 60 * 60) {
                unlink($file);
            }
        }
    }


    public function smtpConfig($adminsmtp, $sender_name)
	{
        $smtpconfig = array();
        if (getenv('APP_ENV') !== 'production' || $adminsmtp->smtp_email == null || $adminsmtp->smtp_user_name == null || $adminsmtp->smtp_password == null || $adminsmtp->smtp_port == null || $adminsmtp->smtp_host == null) {
            return $smtpconfig = [
                'smtp_host'       => getenv('MAIL_HOST'),
                'smtp_port'       => getenv('MAIL_PORT'),
                'smtp_username'   => getenv('MAIL_USERNAME'),
                'smtp_password'   => getenv('MAIL_PASSWORD'),
                'smtp_encryption' => getenv('MAIL_ENCRYPTION'),
                'from_email'      => getenv('MAIL_FROM_ADDRESS'),
                'from_name'       => getenv('MAIL_FROM_NAME')
            ];
        }
        #smtp config
        if($adminsmtp->smtp_host == "ssl://smtp.gmail.com" || $adminsmtp->smtp_host == "tls://smtp.gmail.com")
        {
                $tmphost  = explode("://", $adminsmtp->smtp_host);
                $smtphost = $tmphost[1];
        }else
                $smtphost = $adminsmtp->smtp_host;

        if($adminsmtp->smtp_ssl == "ssl://smtp.gmail.com" || $adminsmtp->smtp_ssl == "tls://smtp.gmail.com")
        {
                $tmpencryp  = explode("://", $adminsmtp->smtp_ssl);
                $encryption = $tmpencryp[0];
        }
        else
            $encryption = $adminsmtp->smtp_ssl;

        $smtpconfig = [

            'smtp_host'       => $smtphost,
            'smtp_port'       => $adminsmtp->smtp_port,
            'smtp_username'   => trim($adminsmtp->smtp_user_name),
            'smtp_password'   => $adminsmtp->smtp_password,
            'smtp_encryption' => $encryption,
            'from_email'      => $adminsmtp->smtp_email,
            'from_name'       => ($adminsmtp->sender_name == "") ? $sender_name : $adminsmtp->sender_name
        ];

        return $smtpconfig;
    }

    public function EmailTemplate($admintemp, $subuser, $analyses, $others, $eph, $admintemplate)
	{
        $emailTemplate = array();

		if(($others['end_diff'] > 0 && $others['end_diff'] <= 120) && ($others['status'] == 0 || $others['status'] == 1) && (($admintemp->due_power == 0 && $admintemp->end_email == 1) || ($admintemp->due_power == 1 && $admintemp->end_email_duepower == 1))){

            #END email greeting
            // $eph[array_search('start_treatment_de', array_column($eph, 'type'))]['message'] -  Gettting defult template data
            $emailTemplate = [
                'temp_title' => ($admintemplate && $admintemplate->email_template_title_stop != "") ?
                    $admintemplate->email_template_title_stop : (($subuser->language_id == 1) ? $eph[6]['message'] : $eph[7]['message']),
                'temp_body' => ($admintemplate && $admintemplate->email_template_stop != "") ?
                    $admintemplate->email_template_stop : (($subuser->language_id == 1) ? $eph[1]['message'] : $eph[3]['message']),
                'temp_footer' => ($admintemplate && $admintemplate->email_template_footer != "") ?
                    $admintemplate->email_template_footer : (($subuser->language_id == 1) ? $eph[12]['message'] : $eph[13]['message']),
                'end_template' => true
            ];
        }
        else if(!$analyses || $others['red_day'] == 1){

            #red day mail
            $emailTemplate = [
                'temp_title' => ($admintemplate && $admintemplate->email_template_title_blank != "") ?
                    $admintemplate->email_template_title_blank : (($subuser->language_id == 1) ? $eph[10]['message'] : $eph[11]['message']),
                'temp_body' => ($admintemplate && $admintemplate->email_template_blank != "") ?
                    $admintemplate->email_template_blank : (($subuser->language_id == 1) ? $eph[8]['message'] : $eph[9]['message']),
                'temp_footer' => ($admintemplate && $admintemplate->email_template_footer != "") ?
                    $admintemplate->email_template_footer : (($subuser->language_id == 1) ? $eph[12]['message'] : $eph[13]['message'])
            ];
        }
        else if($others['status'] == 0 && (($admintemp->due_power == 0 && $admintemp->start_email == 1) || ($admintemp->due_power == 1 && $admintemp->start_email_duepower == 1))){

            #Start Email with cron link
            $emailTemplate = [
                'temp_title' =>
                    ($admintemplate && $admintemplate->email_template_title_start != "") ?  $admintemplate->email_template_title_start : (($subuser->language_id == 1) ? $eph[4]['message'] : $eph[5]['message']),
                'temp_body' => ($admintemplate && $admintemplate->email_template_start != "") ?
                    $admintemplate->email_template_start : (($subuser->language_id == 1) ? $eph[0]['message'] : $eph[2]['message']),
                'temp_footer' => ($admintemplate && $admintemplate->email_template_footer != "") ?
                    $admintemplate->email_template_footer  : (($subuser->language_id == 1) ? $eph[12]['message'] : $eph[13]['message'])
            ];

        }

        return $emailTemplate;
    }

    public function EmailTemplateNew($admintemp, $subuser, $analyses, $others, $eph, $admintemplate)
    {
        $emailTemplate = array();

        if (($others['end_diff'] > 0 && $others['end_diff'] <= 120) && ($others['status'] == 0 || $others['status'] == 1) && (($admintemp->due_power == 0 && $admintemp->end_email == 1) || ($admintemp->due_power == 1 && $admintemp->end_email_duepower == 1))) {
            #END email greeting
            // $eph[array_search('start_treatment_de', array_column($eph, 'type'))]['message'] -  Gettting defult template data
            $emailTemplate = [
                'temp_title' => ($admintemplate && $admintemplate->email_template_title_stop != "") ?
                    $admintemplate->email_template_title_stop : (($subuser->language_id == 1) ? $eph[6]['message'] : $eph[7]['message']),
                'temp_body' => ($admintemplate && $admintemplate->email_template_stop != "") ?
                    $admintemplate->email_template_stop : (($subuser->language_id == 1) ? $eph[1]['message'] : $eph[3]['message']),
                'temp_footer' => ($admintemplate && $admintemplate->email_template_footer != "") ?
                    $admintemplate->email_template_footer : (($subuser->language_id == 1) ? $eph[12]['message'] : $eph[13]['message']),
                'end_template' => true
            ];
        } else if ($others['red_day'] == 1) {
            #red day mail
            $emailTemplate = [
                'temp_title' => ($admintemplate && $admintemplate->email_template_title_blank != "") ?
                    $admintemplate->email_template_title_blank : (($subuser->language_id == 1) ? $eph[10]['message'] : $eph[11]['message']),
                'temp_body' => ($admintemplate && $admintemplate->email_template_blank != "") ?
                    $admintemplate->email_template_blank : (($subuser->language_id == 1) ? $eph[8]['message'] : $eph[9]['message']),
                'temp_footer' => ($admintemplate && $admintemplate->email_template_footer != "") ?
                    $admintemplate->email_template_footer : (($subuser->language_id == 1) ? $eph[12]['message'] : $eph[13]['message'])
            ];
        } else if ($others['red_day'] == 2) {
            #green day mail
            $emailTemplate = [
                'temp_title' => ($admintemplate && $admintemplate->email_template_title_green != "") ?
                    $admintemplate->email_template_title_green : (($subuser->language_id == 1) ? $eph[39]['message'] : $eph[40]['message']),
                'temp_body' => ($admintemplate && $admintemplate->email_template_green != "") ?
                    $admintemplate->email_template_green : (($subuser->language_id == 1) ? $eph[37]['message'] : $eph[38]['message']),
                'temp_footer' => ($admintemplate && $admintemplate->email_template_footer != "") ?
                    $admintemplate->email_template_footer : (($subuser->language_id == 1) ? $eph[12]['message'] : $eph[13]['message'])
            ];
        } else if ($others['status'] == 0 && (($admintemp->due_power == 0 && $admintemp->start_email == 1) || ($admintemp->due_power == 1 && $admintemp->start_email_duepower == 1))) {
            #Start Email with cron link
            $emailTemplate = [
                'temp_title' =>($admintemplate && $admintemplate->email_template_title_start != "") ?  $admintemplate->email_template_title_start : (($subuser->language_id == 1) ? $eph[4]['message'] : $eph[5]['message']),
                'temp_body' => ($admintemplate && $admintemplate->email_template_start != "") ?
                    $admintemplate->email_template_start : (($subuser->language_id == 1) ? $eph[0]['message'] : $eph[2]['message']),
                'temp_footer' => ($admintemplate && $admintemplate->email_template_footer != "") ?
                    $admintemplate->email_template_footer  : (($subuser->language_id == 1) ? $eph[12]['message'] : $eph[13]['message'])
            ];
        }

        return $emailTemplate;
    }
    // public function EmailTemplate($admintemp, $analyses, $others, $eph)
	// {
    //     $emailTemplate = array();

	// 	#email template
    //     if(($others['end_diff'] > 0 && $others['end_diff'] <= 120) && ($others['status'] == 0 || $others['status'] == 1) && (($admintemp->due_power == 0 && $admintemp->end_email == 1) || ($admintemp->due_power == 1 && $admintemp->end_email_duepower == 1))){

    //         #END email greeting
    //         $emailTemplate = [
    //             'temp_title' => ($admintemp->email_template_title_stop == "") ? (($admintemp->language_id == 1) ? $eph[6]['message'] : $eph[7]['message']) : $admintemp->email_template_title_stop,
    //             'temp_body' => ($admintemp->email_template_stop == "") ? (($admintemp->language_id == 1) ? $eph[1]['message'] : $eph[3]['message']) : $admintemp->email_template_stop,
    //             'temp_footer' => ($admintemp->email_template_footer != "") ? $admintemp->email_template_footer : (($admintemp->language_id == 1) ? $eph[12]['message'] : $eph[13]['message'])
    //         ];
    //     }
    //     else if(!$analyses || $others['red_day'] == 1){

    //         $emailTemplate = [
    //             'temp_title' => ($admintemp->email_template_title_blank == "") ? (($admintemp->language_id == 1) ? $eph[10]['message'] : $eph[11]['message']) : $admintemp->email_template_title_blank,
    //             'temp_body' => ($admintemp->email_template_blank == "") ? (($admintemp->language_id == 1) ? $eph[8]['message'] : $eph[9]['message']) : $admintemp->email_template_blank,
    //             'temp_footer' => ($admintemp->email_template_footer != "") ? $admintemp->email_template_footer : (($admintemp->language_id == 1) ? $eph[12]['message'] : $eph[13]['message'])
    //         ];
    //     }
    //     else if(($others['diff'] > 0 && $others['diff'] <= $others['before']) && $others['status'] == 0 && (($admintemp->due_power == 0 && $admintemp->start_email == 1) || ($admintemp->due_power == 1 && $admintemp->start_email_duepower == 1))){

    //         #Start Email with cron link
    //         $emailTemplate = [
    //             'temp_title' => ($admintemp->email_template_title_start == "") ? (($admintemp->language_id == 1) ? $eph[4]['message'] : $eph[5]['message']) : $admintemp->email_template_title_start,
    //             'temp_body' => ($admintemp->email_template_start == "") ? (($admintemp->language_id == 1) ? $eph[0]['message'] : $eph[2]['message']) : $admintemp->email_template_start,
    //             'temp_footer' => ($admintemp->email_template_footer != "") ? $admintemp->email_template_footer : (($admintemp->language_id == 1) ? $eph[12]['message'] : $eph[13]['message'])
    //         ];

    //     }

    //     return $emailTemplate;
    // }

    public function mailTamplates($string, $subuser, $admin, $others){

        if ($subuser->language_id == 2) {
            $overviewText = "Übersicht";
        }
        else {
            $overviewText = "Overview";
        }

        $variables = array(
            '{first_name}' => $subuser->first_name,
            '{last_name}' => $subuser->last_name,
            '{start_date_time}' => $others['start_time'],
            '{end_date_time}' => $others['end_time'],
            '{time_before}' => (int)$others['before']/60,
            '{link}' => ($admin->customer_link == 1) ? '<a href="'.$this->site_link.'/cron/open_treat/'.$others['cronsetup_id'].'/'.$others['crontime_id'].'/'.$admin->unique_id.'"><b>'.$others['click'].'</b></a>' : "",
            '{client_note}' => $others['client_note'],
            '{pdf_link}' => '<a href="'.$others['pdf_link'].'"><b>'.$others['click'].'</b></a>',
            '{overview_link}' => '<a href="'.$this->site_link.'/cron/overview/'.$others['cronsetup_id'].'/'. rand() .'"><b>'.$overviewText.'</b></a>',
            '{remote_id}' => $others['cronsetup_id']
        );

        $datas = strtr($string, $variables);
        return $datas;
    }

    public function encodeToUtf8($string) {
        return mb_convert_encoding($string, "UTF-8", mb_detect_encoding($string, "UTF-8, ISO-8859-1, ISO-8859-15", true));
    }

    private function getLangBaseDate($date,$lang,$time = false){
        $timestamp = strtotime($date);
        if($lang == 'de'){
            if($time) return date('d.m.Y H:i:s', $timestamp);
            return date('d.m.Y', $timestamp);
        }else{
            if($time) return date('Y/m/d H:i:s', $timestamp);
            return date('Y/m/d', $timestamp);
        }
    }

    public function userIdEncrypt($value, $serialize = true)
    {
        $cipher = 'AES-256-CBC';
        $iv = random_bytes(openssl_cipher_iv_length($cipher));
        $key = base64_decode(substr(getenv('APP_KEY'), strlen('base64:')));
        $value = \openssl_encrypt(
            $serialize ? serialize($value) : $value,
            $cipher,
            $key,
            0,
            $iv
        );
        if ($value === false) {
            return false;
        }
        $mac = $this->hash($iv = base64_encode($iv), $value, $key);
        $json = json_encode(compact('iv', 'value', 'mac'), JSON_UNESCAPED_SLASHES);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        return base64_encode($json);
    }

    public function hash($iv, $value, $key)
    {
        return hash_hmac('sha256', $iv . $value, $key);
    }
}
