@php
    use Illuminate\Support\Facades\Auth;
    use Illuminate\Support\Facades\Request;
    $userdata = (!isset($data['userDetails']) && !Auth::check())? $data['userDetails'] : Auth::user();
    if($userdata?->user_type == 0) $user_role = getUserByid(getID())?->user_type;
    else $user_role = $userdata?->user_type;
    // $getMenus = cache()->has('user_all_menus_'.Auth::id()) ? cache()->get('user_all_menus_'.Auth::id()) : __getMenus();
    $menu_lists = __getMenuCollectionDesign();

    $urlParam = Request::segments();

    $auto_menu_hide = $userdata->userOption->auto_menu_hide ?? true;
@endphp
<style>
    .font-bold{
        /* color: #000000cf; */
        font-weight: 600;
    }
    .font-bold-light{
        font-weight: 500;
    }
    .font-bold-strong{
        font-weight: 700;
    }
    .width-90{
        width: 85% !important;
    }
    .toggle-icon{
        margin-top: 7%;
    }
    #layout-sidenav {
        width: 300px !important;
    }
    .sidenav-item{
        position: relative !important;
    }
    a.sidenav-link.sideNavs:hover,
    a.sidenav-link.sidenav-toggle:hover{
        color: #2fab66 !important;
        transition: .5s;
    }
    a.sidenav-link.sidenav-toggle:hover {
        transition: .5s;
    }
    a.sidenav-link.sidenav-toggle:hover{
        transition: .5s;
    }
    .sidenav-menu .sidenav-item .sidenav-link:hover{
        color: #2fab66 !important;
    }
    a.sidenav-link.sideNavs {
        position: relative;
        width: 73%;
        z-index: 1024;
    }
    .sidenav-vertical .sidenav-icon {
        width: 20px;
    }
    .input-group-prepend > span {
        margin: 0px -10px;
    }
    .sidenav.bg-light .sidenav-header {
        color: #595959 !important;
        font-weight: 700 !important;
        padding: 12px 20px !important;
        border-bottom: 1px solid #ddd;
        border-top: 1px solid #ddd;
    }
    .sidenav-inner {
        margin: 0px !important;
        padding: 0px !important;
    }
    .sidenav-item .sidenav-link > div{
        max-width: 215px !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .arm {
        display: none;
    }

    .disarmed .arm {
        display: inline;
    }
    .disarmed .disarm {
        display: none;
    }
    .sidenav-vertical .sidenav-item .sidenav-toggle::after {
        right: 10px !important;
    }

    .custom-menu-active{
        color: #2fab66;
    }

    .main_menu_section {
        visibility: hidden;
        opacity: 0;
    }

    .sidebar-menu-preloader{
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        background-color: #969696;
        animation: movebg 30s infinite;
        opacity: 0.8;
        background-size: auto;
    }
    .sidebar-menu-preloader > div{
        display: flex;
        justify-content: center;
    }
    .sidebar-menu-preloader img{
        height: 160px;
        width: 160px;
        top: 100px;
        animation: rotating 5s linear infinite;
        position: absolute;
        content: '';
    }
    a.sidenav-link.sideNavs {
        width: 100%;
    }
    @media (min-width: 992px) {
        .layout-collapsed.layout-offcanvas #layout-sidenav.layout-sidenav {
            margin-right: -19rem;
        }
    }
    
    .skeleton {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        overflow: hidden;
        width: 100%;
        margin: 0px auto;
        height: 100%;
    }

    .skeleton.skeleton-header {
    padding: 20px;
    border-bottom: 1px solid #eaeaea;
    }

    .skeleton.skeleton-body {
    padding: 20px;
    }

    .skeleton-loader {
        height: 20px;
        width: 100%;
        background-color: #eaeaea;
        animation: loading 1s ease-in-out infinite;
        margin: 10px 0;
    }
    a.sidenav-link.sidenav-toggle.sideNavs.head-menu>i {
        display: none;
    }
    a.toggle-icon.sidenav-link.sidenav-toggle>i {
        display: none;
    }

    li.sidenav-item.open.current-active>a.toggle-icon.sidenav-link.sidenav-toggle,
    .current-active.open>a.toggle-icon.sidenav-link.sidenav-toggle.current-selected {
        position: absolute;
        left: 0px; 
        z-index: 9999 !important;
        height: 37px;
        width: 25px;
        text-align: center !important;
        padding: 0px !important;
        padding-left: 5px !important;
    }

    .current-active.open>a.sidenav-link.sideNavs {
        display: block !important;
        width: 100% !important; 
        text-align: left;
        padding-left: 40px !important;
    }
    li.sidenav-item.open.current-active>a.toggle-icon.sidenav-link.sidenav-toggle>i,
    .current-active.open>a.toggle-icon.sidenav-link.sidenav-toggle.current-selected>i {
        display: block;
        font-size: 20px;
    }
    li.sidenav-item.menu-heading.current-active.open>a.sidenav-link.sideNavs.sidenav-toggle.head-menu:after {
        display: none;
    }

    .current-active.open>a.sidenav-link.sideNavs>div {
        width: 100% !important;
        display: block;
        max-width: 100% !important;
    }




    li.sidenav-item.ui-sortable-handle.menu-heading.current-active.open>a.toggle-icon.sidenav-link.sidenav-toggle.head-menu {
        position: absolute;
        left: 3px;
        top: 0;
    }
    li.sidenav-item.ui-sortable-handle.menu-heading>a.toggle-icon.sidenav-link.sidenav-toggle.head-menu:after,
    li.sidenav-item.ui-sortable-handle.menu-heading>a.toggle-icon.sidenav-link.sidenav-toggle.head-menu i {
            display: none;
    }
    li.sidenav-item.ui-sortable-handle.menu-heading.current-active.open>a.toggle-icon.sidenav-link.sidenav-toggle.head-menu i {
        display: block;
    }

    li.sidenav-item.ui-sortable-handle.menu-heading.current-active.open>a.toggle-icon.sidenav-link.sidenav-toggle.head-menu:after {
        display: none;
    }

    li.sidenav-item.ui-sortable-handle.menu-heading.current-active.open>a.sidenav-link.sideNavs.sidenav-toggle.head-menu.current-selected {}

    li.sidenav-item.ui-sortable-handle.menu-heading.current-active.open>a.sidenav-link.sideNavs.sidenav-toggle.head-menu.current-selected::after {
        display: none;
    }


    li.sidenav-item.open.current-active {
        position: absolute !important;
        /* height: 100%; */
        height: 130%;
        background-color: #fff !important;
        z-index: 9999999 !important;
        width: 100%;
        display: inline-table !important;
        top: 0;
        left: 0px;
    }


    li.sidenav-item.open.current-active ~ li.sidenav-item {
        display: none !important;
    }

    .sidenav-item.open > .sidenav-menu {
        display: none;
    }

    li.sidenav-item.open.current-active>ul.sidenav-menu {
        display: -ms-flexbox;
        display: flex;
    }

    /* custom switch */
    #auto-hide[type=checkbox]{
        height: 0;
        width: 0;
        visibility: hidden;
    }

    .auto-hide-label {
        cursor: pointer;
        text-indent: -9999px;
        width: 42px;
        height: 25px;
        background: #a3a4a6;
        display: block;
        border-radius: 100vmax;
        position: relative;
    }

    .auto-hide-label:after {
        content: '';
        position: absolute;
        top: 4px;
        left: 5px;
        width: 16px;
        height: 16px;
        background: #fff;
        border-radius: 100vmax;
        transition: 0.3s;
    }

    #auto-hide:checked + .auto-hide-label {
        background: #02bc77;
    }

    #auto-hide:checked + .auto-hide-label:after {
        left: calc(100% - 5px);
        transform: translateX(-100%);
    }

    .auto-hide-label:active:after {
        width: 20px;
    }

    @keyframes loading {
        0% {
            opacity: 0.5;
        }
        50% {
            opacity: 1;
        }
        100% {
            opacity: 0.5;
        }
    }
</style>

<script>
    // menu auto hide start
    const menuAutoHideVal = '{{ $auto_menu_hide }}';

    (menuAutoHideVal == 1) ? $('html').addClass('layout-collapsed') : $('html').removeClass('layout-collapsed');
    // menu auto hide end

    let menustatus = '{{ session()->get("_menu_active" . getAuthID()) }}';
    let segment0 = '{{$urlParam[0]}}';
    let segment1 = '{{$urlParam[1] ?? 0}}';
    let segment2 = '{{$urlParam[2] ?? 0}}';
    let segment3 = '{{$urlParam[3] ?? 0}}';
    let segment4 = '{{$urlParam[4] ?? 0}}';
    let comid = {{ request()->comid??0 }}

    if(menustatus && segment0 != 'reset4me' && $.inArray(segment1,['fv','pp']) === -1 && !segment2) $('html').removeClass('layout-collapsed')
    $('#sortable').find('menu-active').last().focus()
    
    // sidebar preloader
    $(".skeleton").css({display:'block',opacity:1});
   
</script>
<div id="layout-sidenav" class="{{ isset($layout_sidenav_horizontal) ? 'layout-sidenav-horizontal sidenav-horizontal container-p-x flex-grow-0' : 'layout-sidenav sidenav-vertical' }} sidenav bg-light" style="max-width:300px;">
@if($user_role > 0)
    <div class="menubar-btn-contaier py-2 w-100 d-flex justify-content-end bg-whight align-items-center" style="border-top: 1px solid #ddd;border-bottom: 1px solid #ddd;">
        <button class="btn btn-success mr-2 btn-sm" id="resetmenu" data-toggle="tooltip" title="{{ trans('action.reset') }}"><i class="fas fa-redo"></i></button>
        <button class="btn btn-danger mr-2 btn-sm menu-sort-access" data-toggle="tooltip" title="Lock"><i class="fas fa-lock"></i></button>
        <input type="checkbox" id="auto-hide" {{ $auto_menu_hide == 1 ? "checked" : "" }} name="auto_hide"/><label for="auto-hide" class="auto-hide-label mr-2" data-toggle="tooltip" title="{{ trans('action.msg_menu_auto_hide') }}"></label>
    </div>
@endif
@if($user_role)
<div class="skeleton">
    <div class="skeleton-header">
      <div class="skeleton-loader"></div>
    </div>
    <div class="skeleton-body">
      <div class="skeleton-loader col-md-7"></div>
      <div class="skeleton-loader col-md-9"></div>
      <div class="skeleton-loader col-md-6"></div>
      <div class="skeleton-loader col-md-4"></div>
      <div class="skeleton-loader col-sm-10"></div>
      <div class="skeleton-loader col-sm-11"></div>
    </div>
</div>
@endif

<ul class="sidenav-inner py-1 ps @if($user_role){{ 'main_menu_section' }} @endif" id="sortable">
    @if($menu_lists)
        {{-- new menu item --}}
        @forelse ($menu_lists as $_key => $sys_menus)

            <li class="sidenav-item ui-sortable-handle menu-heading " level='1'>
                @php
                    $_head_name = explode('__',$_key);
                    $isModuleHead = isset($_head_name[2]) ? $_head_name[2] === 'module_head' : false;
                @endphp
                <a href="{{ $isModuleHead ? route('dashboard.module', ['moduleId'=> $_head_name[1]]) : 'javascript:void(0)' }}"
                    onclick="{{ $isModuleHead ? '' : 'event.preventDefault();' }} loadModule(this.getAttribute('href'));"
                    class="sidenav-link sideNavs sidenav-toggle head-menu">
                    <div class="font-bold">{{ $_head_name[0] }}</div>
                </a>
                <a href="javascript:void(0)" class="toggle-icon sidenav-link sidenav-toggle head-menu"></a> 
                {{-- multi dropdown 1 --}}
                <ul class="sidenav-menu sortable-menu-container"  module-head="{{ $_head_name[1] }}">
                    <div class="input-group search-div">
                        <div class="input-group-prepend">
                            <span class="input-group-text" id="basic-addon1"><i class="fa fa-search" aria-hidden="true"></i></span>
                        </div>
                        <input type="text" class="form-control" id="system-submenu-search" data-search placeholder="{{trans('action.search')}}..." aria-label="{{trans('action.search')}}..." aria-describedby="basic-addon1">
                    </div>

                    @foreach($menu_lists[$_key] as $sys_menu)
                        @continue($sys_menu->submenus === null && $sys_menu->menus === null && $sys_menu->package_name != 'Premium')
                        <li level='2' id='{{ $sys_menu->id }}' class="sidenav-item ui-sortable-handle  @if($sys_menu->isOn && (isset(request()->ownsubid)) || isset(request()->subid)) open @endif">
                            @if($sys_menu->package_name == 'Premium' || $sys_menu->package_name == 'Frabklang')
                                <a href="{{ $sys_menu->link }}" style="width: 100%;" nochildren="yes" class="sidenav-link sideNavs {{ $sys_menu->link !== 'javascript:void(0)' ? 'url-link' : '' }}">
                                    <div class="width-90 font-bold @if($sys_menu->isOn) {{'menu-active active_menu'}} @endif">
                                        {{ ucfirst($sys_menu->product_name) }}
                                    </div>
                                </a>
                            @elseif($sys_menu->sp)
                                <a href="{{ $sys_menu->link }}" class="sidenav-link sideNavs {{ $sys_menu->link !== 'javascript:void(0)' ? 'url-link' : '' }}" style="width: 100%;">
                                    <div class="width-90 font-bold @if($sys_menu->isOn) {{'menu-active active_menu'}} @endif">
                                        {{ ucfirst($sys_menu->product_name) }}
                                    </div>
                                </a>
                                <a href="javascript:void(0)" class="toggle-icon sidenav-link sidenav-toggle"></a>
                            @elseif(in_array($sys_menu->status,[3,4])) 
                                {{-- @continue --}}
                                <a href="{{ $sys_menu->link }}" class="sidenav-link sideNavs {{ $sys_menu->link !== 'javascript:void(0)' ? 'url-link' : '' }}">
                                    <div class="width-90 font-bold  @if($sys_menu->isOn) {{'menu-active active_menu'}} @endif">
                                        {{ ucfirst($sys_menu->product_name) }} <span class="text-danger">Combo</span>
                                    </div>
                                </a>
                                <a href="javascript:void(0)" class="toggle-icon sidenav-link sidenav-toggle"></a>
                            @elseif($sys_menu->status == 2)
                                <a href="{{ $sys_menu->link }}" class="sidenav-link sideNavs {{ $sys_menu->link !== 'javascript:void(0)' ? 'url-link' : '' }}">
                                    <div class="width-90 font-bold  @if($sys_menu->isOn) {{'menu-active active_menu'}} @endif">
                                        {{ ucfirst($sys_menu->product_name) }}
                                    </div>
                                </a>
                                <a href="javascript:void(0)" class="toggle-icon sidenav-link sidenav-toggle"></a>
                            @else
                                <a href="{{ $sys_menu->link }}" class="sidenav-link sideNavs {{ $sys_menu->link !== 'javascript:void(0)' ? 'url-link' : '' }}">
                                    <div class="width-90 font-bold  @if($sys_menu->isOn) {{'menu-active active_menu'}} @endif">
                                        {{ ucfirst($sys_menu->product_name) }}
                                    </div>
                                </a>
                                <a href="javascript:void(0)" class="toggle-icon sidenav-link sidenav-toggle"></a> 
                            @endif

                            {{-- multi dropdown 2 --}}
                            @if($sys_menu->menus)
                            {{-- @continue --}}
                                <ul class="sidenav-menu search-system-menu">
                                    <div class="input-group search-div">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="basic-addon1"><i class="fa fa-search" aria-hidden="true"></i></span>
                                        </div>
                                        <input type="text" class="form-control"  id="system-submenu-search" data-search placeholder="{{ trans('action.search') }}..." aria-label="{{ trans('action.search') }}..." aria-describedby="basic-addon1">
                                    </div>
                                    @foreach($sys_menu->menus as $menu)
                                        @continue($menu->submenus === null)
                                        <li class="sidenav-item ui-sortable-handle">
                                            <a href="{{ $menu->link }}" class="sidenav-link sideNavs">
                                                <div class="font-bold  @if($menu->isOn) {{'menu-active active_menu'}} @endif">{{ ucfirst($menu->product_name) }}</div>
                                            </a>
                                            <a href="javascript:void(0)" class="toggle-icon sidenav-link sidenav-toggle"></a> 
                                            {{-- Single Menu --}}
                                            <ul class="sidenav-menu">
                                                <div class="input-group search-div">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text" id="basic-addon1"><i class="fa fa-search" aria-hidden="true"></i></span>
                                                    </div>
                                                    <input type="text" class="form-control"  id="system-submenu-search" data-search placeholder="{{ trans('action.search') }}..." aria-label="{{ trans('action.search') }}..." aria-describedby="basic-addon1">
                                                </div>
                                                @foreach($menu->submenus as $key => $submenu)
                                                    <li class="sidenav-item ui-sortable-handle " level='4'>
                                                        
                                                        @if($menu->status == 1)
                                                            <a href="{{ ($submenu->type == 12) ? route('dashboard.combo.focusView',[$submenu->product_id,$submenu->id,$menu->id])."?menu_head=".($menu->sMenuHead ?? ($menu->module ?? $menu->parent_module)) : route('dashboard.combo.product', [$menu->id,$submenu->id,$sys_menu->id])."?menu_head=".($menu->sMenuHead ?? ($menu->module ?? $menu->parent_module)) }}" class="sidenav-link url-link" >
                                                        @elseif($menu->status == 2)
                                                            <a href="{{ ($submenu->type == 12) ? route('dashboard.combo.focusView',[$submenu->product_id,$submenu->id,$menu->id])."?menu_head=".($menu->sMenuHead ?? ($menu->module ?? $menu->parent_module)) : route('dashboard.combo.ownProduct', [$menu->id, $submenu->id,$sys_menu->id])."?menu_head=".($menu->sMenuHead ?? ($menu->module ?? $menu->parent_module)) }}" class="sidenav-link url-link">
                                                        @endif
                                                            <div class="font-bold-light  @if($submenu->isOn) {{'menu-active'}} @endif" data-subid='{{ $submenu->id }}'>{{ ucfirst($submenu->menu_name ?? $submenu->name) }}</div>
                                                        </a>
                                                    </li>
                                                @endforeach
                                            </ul>
                                            {{-- Single Menu / --}}
                                        </li>
                                    @endforeach
                                    
                                </ul>
                            @elseif($sys_menu->status === 1 && $sys_menu->submenus != null && $sys_menu->package_name != 'Frabklang')
                                {{-- Single Menu --}}
                                <ul class="sidenav-menu sortable-menu-container">
                                    <div class="input-group search-div" >
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="basic-addon1"><i class="fa fa-search" aria-hidden="true"></i></span>
                                        </div>
                                        <input type="text" class="form-control"  id="system-submenu-search" data-search placeholder="{{ trans('action.search') }}..." aria-label="{{ trans('action.search') }}..." aria-describedby="basic-addon1">
                                    </div>
                                    @foreach($sys_menu->submenus as $key => $submenu)
                                        <li class="sidenav-item ui-sortable-handle" data-subid="{{ $submenu->id }}" level='3'>
                                            @if($_head_name[1] == 'sys')
                                                <a href="{{ ($submenu->type == 12) ? route('dashboard.focusView',[$submenu->product_id,$submenu->id]): route('dashboard.product', [$sys_menu->id,$submenu->id]) }}" class="sidenav-link url-link" >
                                                    <div class="font-bold-light  @if($submenu->isOn) {{'menu-active'}} @endif" data-subid='{{ $submenu->id }}'>{{ ucfirst($submenu->menu_name) }}</div>
                                                </a>
                                            @else
                                            @php
                                              $menu_head = (!is_string($sys_menu->parent_module) 
                                                                ? $sys_menu->module 
                                                                : ($sys_menu->parent_module > 0 
                                                                    ? $sys_menu->parent_module 
                                                                    : $sys_menu->module
                                                                )
                                                            ) ?? $sys_menu->parent_module;
                                            @endphp
                                                <a data-check="{{ $sys_menu->module ."--".$sys_menu->parent_module."--".$submenu->isOn }}" href="{{ ($submenu->type == 12) 
                                                        ? route('dashboard.focusView',[$submenu->product_id,$submenu->id])."?menu_head=".$menu_head
                                                        : route('dashboard.product', [$sys_menu->id,$submenu->id])."?menu_head=".$menu_head }}"
                                                        class="sidenav-link url-link" >
                                                    <div class="font-bold-light  @if($submenu->isOn) {{'menu-active'}} @endif" data-subid='{{ $submenu->id }}'>{{ ucfirst($submenu->menu_name) }}</div>
                                                </a>
                                            @endif
                                        </li>
                                    @endforeach
                                </ul>
                                {{-- Single Menu / --}}
                            @elseif($sys_menu->status === 2 && $sys_menu->submenus != null)
                            {{-- Single Menu --}}
                                <ul class="sidenav-menu sortable-menu-container">
                                    <div class="input-group search-div">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="basic-addon1"><i class="fa fa-search" aria-hidden="true"></i></span>
                                        </div>
                                        <input type="text" class="form-control"  id="system-submenu-search" data-search placeholder="{{ trans('action.search') }}..." aria-label="{{ trans('action.search') }}..." aria-describedby="basic-addon1">
                                    </div>
                                    @foreach($sys_menu->submenus as $key => $submenu)
                                        <li class="sidenav-item ui-sortable-handle" data-subid="{{ $submenu->id }}" level='3'>
                                            <a href="{{ ($submenu->type == 12) ? route('dashboard.focusView',[$submenu->product_id,$submenu->id]) : route('dashboard.ownProduct', [$sys_menu->id, $submenu->id]) .$sys_menu->sMenuHead ?? '' }}" class="sidenav-link url-link">
                                                <div class="font-bold-light @if($submenu->isOn) menu-active @endif">{{ ucfirst($submenu->name) }}</div>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            {{-- Single Menu / --}}
                            @endif
                        </li>
                    @endforeach
                </ul>
                
            </li>


        @empty
            
        @endforelse
    
    @endif

</ul>

</div>


<script src="{{asset('vendor/libs/perfect-scrollbar/perfect-scrollbar.js')}}"></script>

<script type="text/javascript">

    $(document).ready(function(){        
        $(".skeleton").css({display:'none'});
        // $(".sortable-menu-container").sortable({
        //     cancel: ".search-div",
        //     disabled: true
        // });
    });


    // $(function() {
    //     $('.sortable-menu-container').sortable({
    //         placeholder:'ui-state-default',
    //         update: function(event,ui){
    //             var latestsort = [];

    //             var __menu_head = {};
    //             $('li.sidenav-item[level="2"]').parent().each(function() {
    //                 var __proids = {};
                    
    //                 $(this).children('li').each(function($MENUKEY) {
    //                     var __submenu = {};
    //                     $temp = {}
    //                     $(this).find('ul li').each(function($SUBKEY) {
    //                         __submenu[$SUBKEY] = $(this).data('subid');
    //                     });
    //                     $temp['proid'] = $(this).attr('id')
    //                     $temp['subids'] = __submenu
    //                     __proids[$MENUKEY] = $temp
    //                 });
                    
    //                 var __name = $(this).attr("module-head");
                    
    //                 __menu_head[__name] = __proids;
    //                 latestsort.push(__menu_head);
    //             });
    //             $.ajax({
    //                 type: "POST",
    //                 dataType: "json",
    //                 url: "{{ url('short') }}",
    //                 data: {
    //                     order: __menu_head,
    //                     _token: '{{csrf_token()}}'
    //                 },
    //                 success: function(response) {
    //                     toastr.success(`{{__('action.update_successfully')}}`);
    //                 }
    //             });
    //         }
    //     });
    // });

    $(document).on('click', ".menu-sort-access",async function() {
        $(this).toggleClass('access-guranted').toggleClass('btn-danger btn-success')
                .find('i')
                .toggleClass("fa fa-lock fa fa-unlock");

        if(!$(this).hasClass('access-guranted')) {
            toastr.success(`{{__('action.menu_locked')}}`);
            // $(".sortable-menu-container").sortable("disable")
            sortable('.sortable-menu-container', 'disable');
        }else {
            await activeSortable();
            toastr.success(`{{__('action.menu_unlocked')}}`);
            // $(".sortable-menu-container").sortable("enable")
           
        }
    });

    async function activeSortable(){
       sortable('.sortable-menu-container').forEach(function(sortableMenu) {
            sortableMenu.addEventListener('sortupdate', function(e) {
                var latestsort = [];
                var __menu_head = {};
                $('li.sidenav-item[level="2"]').parent().each(function() {
                    var __proids = {};
                    
                    $(this).children('li').each(function($MENUKEY) {
                        var __submenu = {};
                        $temp = {}
                        $(this).find('ul li').each(function($SUBKEY) {
                            __submenu[$SUBKEY] = $(this).data('subid');
                        });
                        $temp['proid'] = $(this).attr('id')
                        $temp['subids'] = __submenu
                        __proids[$MENUKEY] = $temp
                    });
                    
                    var __name = $(this).attr("module-head");
                    
                    __menu_head[__name] = __proids;
                    latestsort.push(__menu_head);
                });
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "{{ url('short') }}",
                    data: {
                        order: __menu_head,
                        _token: '{{csrf_token()}}'
                    },
                    success: function(response) {
                        toastr.success(`{{__('action.update_successfully')}}`);
                    }
                });
            });
        });
    }

    $(document).on('click', "#resetmenu", function() {
        var userid = "{{ getUserId() }}";
        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#337ab7',
            confirmButtonText: `{{__('action.yes')}}`,
            cancelButtonText: `{{__('action.cancel')}}`,
            cancelButtonColor: '#d33',

        }).then((result) => {

            if (result.value) {
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "{{ route('Aajax.resetmenu') }}",
                    data: {userid: userid},
                    success: function(response) {
                        toastr.success(response.message);
                        location.reload();
                    }

                });
            }
        })
    });

    // menu auto hide
    $(document).on('click', "#auto-hide", function() {
        const userid = "{{ getUserId() }}";
        let checkedVal;

        $(this).is(":checked") ? (checkedVal = 1, window.setTimeout(function(){$('html').addClass('layout-collapsed');}, 400)) : (checkedVal = 0, $('html').removeClass('layout-collapsed'));

        // $('.auto-hide-label').tooltip('option', 'hide');
        
        $.ajax({
            type: "POST",
            dataType: "json",
            url: "{{ route('Aajax.menuAutoHide') }}",
            data: {
                user_id: userid,
                checkbox_val: checkedVal
            },
            success: function(response) {
                toastr.success(response.message);
            }
        });
    });
    

    // pay btn script
    $(document).ready(function(){

        $('.menu-active.active_menu').parentsUntil("ul.sidenav-inner").addClass('current-active');
        $('.sidenav-item').removeClass('open');
        $('li.sidenav-item.current-active').addClass('open');

        $('[nochildren="yes"]').parent().removeClass('open');
        //if(menustatus == true) $('a.current-active').parent().removeClass('open');


        $('<i class="fa fa-angle-double-left" aria-hidden="true"></i>').appendTo('.sidenav-toggle');
        // $('ul.sidenav-inner>li').addClass('current-active');
        $('ul.sidenav-inner li .sidenav-toggle').on('click', function(){
            $('.sidenav-toggle').removeClass('current-selected');
            $(this).addClass('current-selected');

            $('.sidenav-toggle').parentsUntil("ul.sidenav-inner").removeClass('current-active'); 
            $(this).parentsUntil("ul.sidenav-inner").addClass('current-active');
            //alert('Menu Expand Executed');

        });

        $('.url-link').on('click', function(event) {
            // Get the href attribute of the link
            var href = $(this).attr('href');
            // If the href attribute is equal to 'javascript:void(0)', then execute the click event for the sidenav toggle element
            if (href && href.includes("javascript:void(0)")) {
                event.preventDefault();
                // Get the sibling of the url-link element with the class sidenav-toggle and trigger click event
                $(this).parent('li').addClass('current-active open')
                $(this).siblings('.sidenav-toggle').trigger('click');
                return false;
            }
        });



        $('[data-search]').on('keyup', function(evt) {
            var searchVal = $(evt.target).val();
            var filterItems = $(evt.target).parent().parent().children('li')
            filterItems.each(function () {
                if ($(this).find('a').text().toLowerCase().search(searchVal) > -1) {
                    $(this).find('ul .search-div').addClass('hide')
                    $(this).addClass('open').show();
                } else if ($(this).find('a').text().search(searchVal) > -1) {
                    $(this).find('ul .search-div').addClass('hide')
                    $(this).addClass('open').show();
                } else {
                    $(this).removeClass('open').hide();
                }
            });
            if (searchVal.length === 0) $(evt.target).parent().parent().children('li').removeClass('open')
        });

        

        $(".main_menu_section").css({
            'visibility': 'visible',
            'opacity' : '1'
        });

        $('.main_menu_section .sidenav-item[level="3"] > .sidenav-link.url-link').click(function () {
            $(".skeleton").css({display:'block'});
            $(".main_menu_section").css({
                'visibility': 'hidden',
                'opacity' : '0',
            });
        });
    });

    function loadModule(url) {
        if(url != 'javascript:void(0)') {
            $('#loader').show();
            document.querySelectorAll('[data-current-dashboard-link]').forEach(el => {
                el.setAttribute('data-current-dashboard-link', url);
            });
            $.ajax({
                url: url,
                type: 'GET',
                success: function(response) {
                    var newPage = $(response).find('section.home').html();
                    $('section.home').length ? $('section.home').html(newPage) :  window.location.href = url
                },
                complete: function() {
                    $('#loader').hide();
                    rightDropdownScriptLoad();
                }
            });
        }
    }
</script>
