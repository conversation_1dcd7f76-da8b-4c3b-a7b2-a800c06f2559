{{--section right --}}
@php
    $userDetails = ($data['userDetails']!=null)?$data['userDetails']:getUserDetails();
    $addUserStatus = (maxAddUser() <= 0) ? false : true;
    $auth = Auth::user();
    $subusers = getSubUser();
    $data['filterid'] = $data['filterid'] ?? $userDetails->userfilter()->first(['filter_type'])->filter_type;
    $farbklang = $farbklang ?? false;
@endphp
<link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">

<div class="col-lg-4 col-sm-12">
    <div class="tab-container">
        <div class="tab-navigation font-awesome mb-4">
            <div class="navigation-buttons">
                @if((isset($farbklang) && $farbklang && $auth->user_type == 2) || !isset($farbklang) || !$farbklang)
                    <button class="tabs-itemBox-Style custom-select custom-select-box" type="button" id="select-box_change_view"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="ion ion-ios-list">&nbsp;</i><span>{{trans('action.chose_menu')}}</span>
                    </button>
                    <div class="dropdown-menu" aria-labelledby="select-box">
                        <a class="dropdown-item btn-dash @if($farbklang) dashActive_btn @endif" data-url='{{ url('/') }}'
                        href="javascript:void(0)" data-name="{{trans('action.system_setting')}}" id="1">
                            <i class="ion ion-md-settings ">&nbsp;</i><span>{{trans('action.system_setting')}}</span>
                        </a>
                            <a class="dropdown-item btn-dash" href="javascript:void(0)" data-url='{{ url('/') }}'
                            data-name="{{trans('action.last_treatment')}}" id="3">
                                <i class="ion ion-md-cart">&nbsp;</i><span>{{trans('action.last_treatment')}}</span>
                        </a>
                        @if($auth->user_type == 2 || $auth->user_type == 4)
                                <a class="dropdown-item btn-dash" href="javascript:void(0)" data-url='{{ url('/') }}'
                                data-name="{{trans('action.pdf_heading')}}" id="2"><i
                                            class="fas fa-file-pdf">&nbsp;</i><span>{{trans('action.pdf_heading')}}</span></a>
                                <a class="dropdown-item btn-dash" href="javascript:void(0)" data-url='{{ url('/') }}'
                                data-name="{{trans('action.digitalcon&focusPDF')}}" id="5"><i
                                            class="fas fa-file-pdf">&nbsp;</i><span>{{trans('action.digitalcon&focusPDF')}}</span></a>
                        @endif
                        @if ($auth->user_type == 1)
                                <a id="4" class="dropdown-item btn-dash" href="javascript:void(0)" data-url='{{ url('/') }}'
                                data-name="{{trans('action.user')}}"><i class="ion ion-ios-contacts">&nbsp;</i><span>{{trans('action.user')}}</span></a>
                        @endif
                    </div>
                @endif
                @if(is_subclass_of(static::class, \Livewire\Component::class))
                    <select class="custom-select border-none mt-2 mt-sm-0 tabs-itemBox-Style" id="longday" wire:model.live="selectedLongDay">
                        <option value="">{{ __('action.lta_choose_days') }}</option>
                        @if(count($lta_days) > 0)
                            @foreach($lta_days as $days)
                                <option value="{{$days->days}}" @if(isset($selectedLongDay) && $selectedLongDay == $days->days) selected @endif>{{$days->days}} {{__('action.lta_days') }}</option>
                            @endforeach
                        @endif
                    </select>
                @endif
                @if($farbklang && $auth->user_type == 1)
                    <button type="button" class="tabs-itemBox-Style" id="users_button"><i
                            class="fas fa-users">&nbsp;</i><span>{{trans('action.user')}}</span></button>
                @endif
                <button type="button" class="tabs-itemBox-Style" id="frequency_generator_button"><i
                            class="fas fa-wave-square">&nbsp;</i><span>{{trans('action.frequency_generator')}}</span></button>
                <button type="button" class="tabs-itemBox-Style dashActive_btn" id="topic_button"><i
                    class="fas fa-comments">&nbsp;</i><span>{{trans('action.topic_name')}}</span></button>
            </div>
            <div class="tab-contents">
                <div class="card topicTab fade show own-topic-text-div" id="topicTab"
                    style="background: #fff;background-clip: padding-box;border-color:#02a065 !important">
                    <div class="arrow"></div>
                    <div class="card-body">
                        <form action="">
                            <div class="row">
                                <div class="col-6 d-flex align-items-center">
                                    <small class="form-text text-muted d-none" id="frequency-display"></small>
                                    <small class="form-text text-muted d-none" id="frequency-unit" style="margin-left: 2px;">{{trans('action.unit_hertz')}}</small>
                                </div>
                                <div class="col-6 d-flex align-items-center justify-content-end">
                                    @php
                                        $biorythDetails = biorythVisibleDetails();
                                        $randomTime = rand($biorythDetails->gs_min_price, $biorythDetails->gs_max_price);
                                        $topicTimeDisplay = gmdate('i:s', $randomTime);
                                    @endphp
                                    <small class="form-text text-muted text-right d-none" id="topic-time-display" style="color: #28a745;" data-initial-time="{{ $randomTime }}" data-price="{{ $randomTime }}">{{ $topicTimeDisplay }}</small>
                                    <small class="form-text text-muted d-none" id="time-unit" style="margin-left: 2px;">{{trans('action.unit_seconds')}}</small>
                                </div>
                            </div>
                                <div class="form-group topic">
                                    <textarea name="" id="own_topic" rows="2" class="form-control" oninput="debouncedUpdateTopicFrequency(this)"
                                     placeholder="{{trans('action.topic_name_placeholder')}}">{{ $userDetails->thema_speichern }}</textarea>
                             </div>
                            <div class="text-center topic-btns">
                                @if(!$farbklang)
                                <button type="button" class="btn btn-primary icon"
                                    onclick="btnAddCart(this)">{{trans('action.cart')}}</button>
                                @endif
                                <button type="button" class="btn btn-success icon"
                                    onclick="topicSave(this,'Save')">{{trans('action.save')}}</button>
                                <button type="button" class="btn btn-danger icon"
                                    onclick="topicSave(this,'Delete')">{{trans('action.delete')}}</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card frequencyGeneratorTab fade show" id="frequencyGeneratorTab">
                    <div class="arrow"></div>
                    <div class="card-body">
                        <form action="">
                            <div class="form-group topic">
                                <textarea name="" id="frequency_topic" rows="2" class="form-control" oninput="debouncedUpdateTopicFrequency(this)"
                                    placeholder="{{trans('action.topic_name_placeholder')}}" >{{ $userDetails->thema_speichern }}</textarea>
                            </div>
                            <div class="frequency-time-controls">
                                <div class="frequency-input-group">
                                    <div class="input-group">
                                        <input type="text" 
                                            class="form-control" 
                                            id="frequency_hz"
                                            style="border: 0 !important;"
                                            min="250" max="20000" step="50" 
                                            placeholder="{{trans('action.frequency_placeholder')}}" 
                                            value="{{ calculationFrequency($userDetails->thema_speichern) > 0 ? calculationFrequency($userDetails->thema_speichern) : '' }}"
                                            oninput="updateFrequencyDisplay(this)">
                                        <span class="input-group-text">{{trans('action.unit_hertz')}}</span>
                                        <button type="button" class="btn btn-warning harmonics-btn" 
                                                onclick="calculateHarmonics()" 
                                                title="{{__('action.calculate_harmonics')}}" 
                                                data-bs-toggle="tooltip" 
                                                data-bs-placement="top">
                                            <i class="fas fa-wave-square"></i>
                                            <span class="click-indicator d-none"><i class="fas fa-mouse-pointer"></i></span>
                                        </button>
                                    </div>
                                    <small class="form-text d-none">{{trans('action.frequency_placeholder')}}</small>
                                </div>
                                <div class="time-input-group">
                                    @php
                                        $biorythDetails = biorythVisibleDetails();
                                        $randomTime = rand($biorythDetails->gs_min_price, $biorythDetails->gs_max_price);
                                    @endphp
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="frequency_time" 
                                               style="border: 0 !important;"
                                               min="5" max="3600" step="1" placeholder="{{trans('action.seconds_placeholder')}}"
                                               value="{{ $randomTime }}"
                                               oninput="updateTimeDisplay(this)">
                                        <span class="input-group-text">{{trans('action.unit_seconds')}}</span>
                                    </div>
                                    <small class="form-text d-none">{{ trans('action.seconds_placeholder') }}</small>
                                </div>
                            </div>
                        </form>
                        <div class="text-center frequency-btns">
                            @if(!$farbklang)
                            <button type="button" class="btn btn-primary icon"
                                onclick="btnAddCart(this)">{{trans('action.cart')}}</button>
                            @endif
                    </div>
                    </div>
                </div>

                <div id="tab-1" class="tab-content hide">
                    <div class="card">
                        <div class="arrow"></div>
                        <div class="card-body web-kit-scroll">
                            <ul class="sys-setting">
                                <li>
                                    <p>
                                        {{trans('action.calculation_system_dashboard')}}
                                    </p>
                                    <div class="right-side">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-1 mr-2">{{trans('action.year')}}</div>
                                            <label class="switcher switcher-lg switcher-success m-0">
                                                <input type="checkbox" onclick="changeYearMonth()" id="changeYearMonth"
                                                    @if($userDetails->calculation_with == 1) {{'checked'}} @endif
                                                class="switcher-input">
                                                <span class="switcher-indicator">
                                                        <span class="switcher-yes"></span>
                                                        <span class="switcher-no"></span>
                                                    </span>
                                            </label>
                                            <div class="flex-shrink-1 text-success ml-2">{{trans('action.month')}}</div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <p>
                                        {{trans('action.date_system_dashboard')}}
                                    </p>

                                    <div class="right-side">
                                        <div class="right-side ml-3 ml-sm-0">
                                            <input type="text" class="form-control datef"
                                                data-date="{{ $userDetails->datumcore }}" id="datePicker"
                                                onchange="dateChange()" placeholder="mm/dd/yyyy"
                                                value="{{ \Carbon\Carbon::parse($userDetails->datumcore)->toDateString() }}">
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <p>
                                        {{trans('action.sorting_system_dashboard')}}
                                    </p>
                                    <div class="right-side">
                                        <select class="custom-select" id="changeShowFilter" onchange="changeShowFilter()">
                                            <option @if ($data['filterid']==1) {{ 'selected' }} @endif value="1">A-Z</option>
                                            <option @if ($data['filterid']==2) {{ 'selected' }} @endif value="2">Z-A</option>
                                            <option @if ($data['filterid']==3) {{ 'selected' }} @endif value="3">1-100</option>
                                            <option @if ($data['filterid']==4) {{ 'selected' }} @endif value="4">100-1</option>
                                        </select>
                                    </div>
                                </li>
                            </ul>

                        </div>
                    </div>
                </div>

                <div id="tab-2" class="tab-content hide">
                    <div class="card">
                    <div class="arrow"></div>
                        <div class="card-body" id="show_normal_pdfs">

                        </div>
                    </div>
                </div>

                <div id="tab-3" class="tab-content hide">
                    <div class="card">
                        <div class="arrow"></div>
                        <div class="card-body" id="save_treatment_cart_list" style="padding: 15px 10px 15px 15px;">
                        </div>
                    </div>
                </div>

                <div id="tab-4" class="tab-content hide">
                    @if ($auth->user_type == 1)
                        <div class="card">
                            <div class="@if(isset($farbklang) && $farbklang) arrow arrow3 @else arrow @endif"></div>
                            <div class="card-body">
                                <div class="row">
                                        {{-- user --}}
                                <div class="col-md-12">
                                    <ul class="users-rankwise">
                                        <li>
                                        <div class="user-single-box text-center">
                                                <div class="usb-photo">
                                                    @if($userDetails->id !== $auth->id)
                                                    <img data-name="{{$auth->fullName}}" data-email="{{$auth->email}}"
                                                        data-view="true" src="{{ getProfileImage($auth) }}" id="upimg">
                                                    @else
                                                    <img data-name="{{$userDetails->fullName}}"
                                                        data-email="{{$userDetails->email}}" data-view="true"
                                                        data-profileimage="true" id="upimg">
                                                    @endif
                                                </div>
                                                <div class="usb-content">
                                                    <p>{{ $admin->fullName}}</p>
                                                    <span class="text-primary">{{trans('action.no_access2')}}</span>
                                                </div>
                                            </div>
                                        </li>

                                        @if($subusers != null)
                                        @foreach ($subusers as $user)
                                        <li>
                                                <div class="user-single-box text-center">
                                                    <div class="usb-photo" onclick="switch_user({{$user->id}})">
                                                        {{-- @if($user->photo == "")
                                                    <img class="rounded-circle m-1 p-1"
                                                        data-src="{!! ($user->gender == 2)? asset('/images/female.jpg') : asset('/images/avatar.png') !!}"
                                                        alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                                        @else
                                                    <img class="rounded-circle m-1 p-1"
                                                        data-src="{{ getEmbededImage('/profile/users/'.$user->photo) }}"
                                                        alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                                        @endif --}}
                                                    <img class="rounded-circle m-1 p-1" data-src="{{getProfileImage($user)}}"
                                                        alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                                    </div>
                                                <button type="button" class="minus-btn" onclick="deleteSubUser({{$user->id}})"
                                                    data-toggle="tooltip" data-placement="bottom" data-state="secondary"
                                                    title="{{ trans('action.delete')}} {{$user->first_name}}&nbsp;{{ $user->last_name }}"><i
                                                        class="fa text-danger fa-minus-circle"></i></button>

                                                    <div class="usb-content">
                                                        <p>{{$user->fullName }}</p>
                                                </div>
                                            </div>
                                        </li>
                                        @endforeach
                                        @endif

                                        {{-- creat user --}}
                                        @if($addUserStatus)
                                        <li>
                                                <div class="user-single-box text-center">
                                                <a href="{{ route('users.show') }}">
                                                        <div class="usb-icon">
                                                            <i class="fa fa-plus" aria-hidden="true"></i>
                                                        </div>
                                                    </a>
                                                    <div class="usb-content">
                                                    <p>{{trans('action.create_new_user')}}</p>
                                                </div>
                                            </div>
                                        </li>
                                        @endif
                                    </ul>
                                </div>

                                </div>
                            </div>
                        </div>
                    @endif
                </div>
                <div id="tab-5" class="tab-content hide">
                    @if ($auth->user_type == 2 || $auth->user_type == 4)
                        <div class="card ">
                            <div class="arrow"></div>
                            <div class="card-body" id="dc_pdf_view_show">

                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <script>
        // Set up global variables for the shared JavaScript
        window.routes = {
            calculateFrequency: '{{ route("calculation.calculateFrequency") }}'
        };
        
        window.csrfToken = '{{ csrf_token() }}';
        
        window.biorhythDetails = {
            min: {{ biorythVisibleDetails()->gs_min_price }},
            max: {{ biorythVisibleDetails()->gs_max_price }}
        };
        
        window.translations = {
            calculate_harmonics: '{{__('action.calculate_harmonics')}}',
            frequency_too_low: '{{__('action.frequency_too_low')}}',
            please_enter_valid_frequency: '{{__('action.please_enter_a_valid_frequency_first')}}',
            frequency_calculation_in_progress: '{{__('action.frequency_calculation_in_progress')}}'
        };
    </script>
    <script src="{{ asset('js/dashboard/right-panel.js') }}"></script>
</div>
