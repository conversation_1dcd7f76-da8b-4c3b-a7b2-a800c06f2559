@php
    $cartData = !empty($cartData) ? $cartData : getCartData();
    $useroption = $data['user']->useroption;
@endphp
    {{-- cart content --}}
    <div class="right-panel-icon cartContent"  id="">
        <p class="d-inline-block" data-toggle="modal" data-target="#right-panel-slide">
            <i class="fas fa-cart-plus"></i>
            <span class="badge badge-danger indicator" id="cardValue">{{ count($cartData) }}</span>
        </p>
    </div>

    {{-- Right Panel Modal --}}
    <div class="modal modal-slide fade" id="right-panel-slide">
        <div class="modal-dialog">
            <div class="modal-content">
                {{-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button> --}}
                <div class="modal-body">
                    <div class="right-sidepanel padding-10">
                        <div class="right-sidepanel-header">
                            <div>
                            <p class="font-weight-bold mb-3">{{__('action.topictobecoverd')}}</p>
                            </div>
                            <div class="d-block text-right mb-4">
                                <span class="float-left badge badge-pill badge-warning right-cart-total-time mt-1"><i class="fas fa-clock f-18 text-light pr-1"></i>00:00:00</span>
                                <label class="switcher switcher-success switcher-sm mr-1" style="margin-right: 0px; min-height: 1.125rem !important">
                                <input type="checkbox" class="switcher-input reaction-change" id="reaction-change" onclick="changeRAstatus('{{$useroption->ra_status}}', event)" {{$useroption->ra_status == false ? "checked" : ""}}>
                                    <span class="switcher-indicator">
                                        <span class="switcher-yes" data-toggle="tooltip" data-placement="bottom" title="{{__('action.show_reaction')}}">
                                            <span class="ion ion-md-checkmark"></span>
                                        </span>
                                        <span class="switcher-no" data-toggle="tooltip" data-placement="bottom" title="{{__('action.hide_reaction')}}">
                                            <span class="ion ion-md-close"></span>
                                        </span>
                                    </span>
                                    {{--<span class="switcher-label">{{__('action.show_ra')}}</span>--}}
                                </label>

                                <label class="switcher switcher-success switcher-sm ra-pdf-check hide" style="margin-right: 0; min-height: 1.125rem !important">
                                <input type="checkbox" class="switcher-input reaction-pdf"  onclick="changeRApdf('{{$useroption->ra_pdf_status}}', event)" {{$useroption->ra_pdf_status == false ? "checked" : ""}}>
                                    <span class="switcher-indicator">
                                        <span class="switcher-yes" data-toggle="tooltip" data-placement="bottom" title="{{__('action.show_pdf')}}">
                                            <span class="ion ion-md-checkmark"></span>
                                        </span>
                                        <span class="switcher-no" data-toggle="tooltip" data-placement="bottom" title="{{__('action.hide_pdf')}}">
                                            <span class="ion ion-md-close"></span>
                                        </span>
                                    </span>
                                    {{--<span class="switcher-label">{{__('action.show_ra')}}</span>--}}
                                </label>

                                @if(Auth::user()->user_type == 2)
                                <a href="javascript:void(0)" class="d-inline-block ml-2 cursor-pointer" data-toggle="modal" data-target="#pdfGenerateModal">
                                    <span class="d-inline-block" data-toggle="tooltip" data-placement="bottom" title="{{__('action.pdf_title')}}" ><i class="fas fa-file-pdf f-18"></i></span>
                                </a>
                                @endif
                                <a href="javascript:void(0)" class="d-inline-block ml-2 cursor-pointer" data-toggle="modal" data-target="#topicModal">
                                    <span class="d-inline-block">
                                        <i class="fas fa-edit f-18" data-toggle="tooltip" data-placement="bottom" title="{{__('action.manage_topic')}}"></i>
                                    </span>
                                </a>
                                <a href="javascript:void(0)" class="d-inline-block  ml-2 cursor-pointer" onclick="openCartModal()">
                                {{-- <a href="javascript:void(0)" class="d-inline-block  ml-2 cursor-pointer" data-toggle="modal" data-target="#openCartModal"> --}}
                                    <span class="d-inline-block  cursor-pointer" data-toggle="tooltip" data-placement="bottom" title="{{__('action.open_basket')}}">
                                        <i class="ion ion-md-open d-block f-18"></i>
                                    </span>
                                </a>
                                <a href="javascript:void(0)" class="d-inline-block ml-2 cursor-pointer" data-toggle="modal" data-target="#saveCartModal">
                                    <span class="d-inline-block" data-toggle="tooltip" data-placement="bottom" title="{{__('action.list_cart')}}" >
                                        <i class="ion ion-md-download f-18" ></i>
                                    </span>
                                </a>
                                <a href="javascript:void(0)" class="d-inline-block ml-2 cursor-pointer" onclick="clearCart()">
                                    <span class="d-inline-block mr-2" ><i class="far fa-times-circle f-18" data-toggle="tooltip" data-placement="bottom" title="{{__('action.clear')}}"></i></span>
                                </a>

                                {{-- Topic Modal --}}
                                <div class="modal modal-top fade" id="topicModal" tabindex="-1" role="dialog"
                                    aria-labelledby="myModalLabel">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content own-topic-text-div">
                                            <div class="modal-header">
                                                <h4 class="modal-title" id="myModalLabel">{{ __('action.topic_name') }}</h4>
                                                <button type="button" class="close" data-dismiss="modal"
                                                    aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                            </div>
                                            <div class="modal-body">
                                                <textarea name="" class="form-control" id="own_topic" cols="" rows="5"
                                                    placeholder="{{ __('action.cron_note_placeholder') }}">{{ getUserDetails()->thema_speichern }}</textarea>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-primary icon" onclick="btnAddCart(this)" id="add_to_cart" >{{ __('action.cart') }}</button>
                                                <button type="button" class="btn btn-success icon" id="save_topic" onclick="topicSave(this,'Save')">{{ __('action.save') }}</button>
                                                <button type="button" class="btn btn-default icon" id="delete_topic" onclick="topicSave('Delete')">{{ __('action.delete') }}</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="right-sidepanel-body " id="">
                            <ul class="treatment-list">
                                @foreach ($cartData as $key => $cartVelue)
                                <li class="rem_{{$cartVelue->rowId}}" id="__cartList" data-anaid="{{ $cartVelue->options->analysisID }}">
                                    <p data-toggle="tooltip" title="{{ $cartVelue->options->analysisName }}">{{ gmdate("i:s", $cartVelue->options->price) }} &nbsp;&nbsp;&nbsp;{{ $cartVelue->options->analysisName }}</p>

                                    <div class="treatment-list-icon">
                                        @php
                                            $type_id = '';
                                            if($cartVelue->options->type == 'Analysis'){
                                                $type_id = $cartVelue->options->analysisID;
                                            }elseif($cartVelue->options->type == 'Causes'){
                                                $type_id = $cartVelue->options->causes_id;
                                            }elseif($cartVelue->options->type == 'Medium'){
                                                $type_id = $cartVelue->options->medium_id;
                                            }elseif($cartVelue->options->type == 'Tipp'){
                                                $type_id = $cartVelue->options->tipp_id;
                                            }

                                        @endphp
                                        <a href="javascript:void(0)" onclick="removeCid('{{ $cartVelue->rowId }}','{{ $cartVelue->options->type }}',{{ $type_id }})">
                                            <i class="fas fa-times-circle danger-color"></i>
                                        </a>
                                    </div>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        <div class="session-info"> 
                            @livewire('treatment.session-id')
                            {{-- <button id="changeSessionIdBtn">Change Session ID</button> --}}
                        </div>
                        @if (Gate::allows('checkAccess','checkCron'))
                            <div class="model-footer-button-one mb-1">
                                <a class="btn btn-info w-100 checkcarthavedata_rerender_href" id="checkcarthavedata" href="{{ route('cron.remote-analysis',["remote"]) }}">{{ __('action.remote_analysis') }}</a>
                            </div>
                        @endif
                        <div class="right-sidepanel-footer mb-1">
                            <a href="{{ route('treat.treat') }}" id="checkcarthavedata" class="btn btn-outline-success">{{ __('action.behandein') }}</a>
                            <a href="{{ route('treat.dur_power') }}" id="checkcarthavedata" class="btn btn-success">{{ __('action.duration_power') }}</a>
                        </div>
                        

                    </div>
                </div>
            </div>
        </div>
    </div>

    