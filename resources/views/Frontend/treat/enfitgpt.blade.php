@extends('Frontend.partials.layout')
@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/select2/select2.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/treatment.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/blade-css/treat.css') }}">
    <style>
        .ai-button-container { display: flex; justify-content: center; margin: 40px 0; perspective: 1000px; }
        .ai-power-button { position: relative; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 20px 50px; font-size: 18px; font-weight: 700; letter-spacing: 1px; border-radius: 50px; cursor: pointer; overflow: hidden; text-transform: uppercase; transition: all 0.3s ease; box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4); transform-style: preserve-3d; }
        .ai-power-button::after { content: ''; position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); transform: rotate(45deg); transition: all 0.5s; opacity: 0; }
        .ai-power-button:hover::after { opacity: 1; animation: glow 2s linear infinite; }
        @keyframes glow { 0% { transform: rotate(45deg) scale(1); } 100% { transform: rotate(405deg) scale(1); } }
        .ai-power-button:before { content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent); transition: left 0.5s; }
        .ai-power-button:hover:before { left: 100%; }
        .ai-power-button:hover { transform: translateY(-3px) rotateX(-10deg); box-shadow: 0 20px 50px rgba(102, 126, 234, 0.6), 0 0 30px rgba(102, 126, 234, 0.4), inset 0 0 30px rgba(255,255,255,0.1); background: linear-gradient(135deg, #7c94ff 0%, #9e5ed4 100%); }
        .ai-power-button:active { transform: translateY(-1px) rotateX(-5deg); box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4); }
        .ai-power-button:disabled { background: linear-gradient(135deg, #a8a8a8 0%, #7a7a7a 100%); cursor: not-allowed; box-shadow: none; transform: none; }
        .ai-power-button:disabled::after, .ai-power-button:disabled::before { display: none; }
        .ai-icon { display: inline-block; margin-right: 10px; animation: pulse 2s infinite; }
        @keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.1); } 100% { transform: scale(1); } }
        .ai-sparkles { position: absolute; width: 100%; height: 100%; top: 0; left: 0; pointer-events: none; }
        .sparkle { position: absolute; width: 4px; height: 4px; background: white; border-radius: 50%; animation: sparkle 3s linear infinite; opacity: 0; }
        .sparkle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; } .sparkle:nth-child(2) { top: 80%; left: 20%; animation-delay: 0.5s; } .sparkle:nth-child(3) { top: 50%; left: 80%; animation-delay: 1s; } .sparkle:nth-child(4) { top: 10%; left: 90%; animation-delay: 1.5s; } .sparkle:nth-child(5) { top: 90%; left: 70%; animation-delay: 2s; }
        @keyframes sparkle { 0%, 100% { opacity: 0; transform: scale(0); } 50% { opacity: 1; transform: scale(1); } }
        .ai-loader-container { display: none; text-align: center; padding: 60px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); border-radius: 20px; margin: 20px auto; max-width: 500px; }
        .ai-loader { position: relative; width: 100px; height: 100px; margin: 0 auto 20px; }
        .ai-loader-circle { position: absolute; width: 100%; height: 100%; border: 4px solid transparent; border-radius: 50%; border-top-color: #667eea; animation: rotate 1.5s linear infinite; }
        .ai-loader-circle:nth-child(2) { width: 80%; height: 80%; top: 10%; left: 10%; border-top-color: #764ba2; animation-duration: 1s; animation-direction: reverse; }
        .ai-loader-circle:nth-child(3) { width: 60%; height: 60%; top: 20%; left: 20%; border-top-color: #667eea; animation-duration: 0.5s; }
        @keyframes rotate { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .ai-loader-text { font-size: 18px; font-weight: 600; color: #667eea; animation: fade 1.5s ease-in-out infinite; }
        @keyframes fade { 0%, 100% { opacity: 0.3; } 50% { opacity: 1; } }
        #chatContainer { display: none; margin-top: 40px; border: 1px solid #e0e0e0; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.05); }
        .chat-header { display: flex; justify-content: space-between; align-items: center; padding: 10px 20px; background-color: #f7f7f7; border-bottom: 1px solid #e0e0e0; }
        .chat-header h5 { margin: 0; font-weight: 600; }
        #clearChatBtn { padding: 4px 10px; font-size: 12px; }
        #chatBox { height: 400px; overflow-y: auto; padding: 20px; background-color: #f9f9f9; }
        .chat-message { margin-bottom: 15px; display: flex; flex-direction: column; }
        .chat-message p { max-width: 80%; padding: 10px 15px; border-radius: 18px; line-height: 1.5; margin-bottom: 0; word-wrap: break-word; }
        .chat-message p h4 { margin-top: 0; margin-bottom: 10px; font-size: 1.1rem; }
        .chat-message p h5 { margin-top: 15px; margin-bottom: 5px; font-size: 1rem; }
        .chat-message p ul { padding-left: 20px; margin-top: 5px; margin-bottom: 5px; }
        .user-message { align-items: flex-end; }
        .user-message p { background-color: #667eea; color: white; border-bottom-right-radius: 4px; }
        .bot-message { align-items: flex-start; }
        .bot-message p { background-color: #e5e5ea; color: #333; border-bottom-left-radius: 4px; }
        #chatForm { display: flex; padding: 15px; border-top: 1px solid #e0e0e0; background-color: #fff; }
        #chatInput { flex-grow: 1; border: 1px solid #ccc; border-radius: 20px; padding: 10px 15px; font-size: 16px; margin-right: 10px; }
        #chatInput:focus { outline: none; border-color: #667eea; box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2); }
        #chatSubmitBtn { background: #667eea; color: white; border: none; border-radius: 50%; width: 44px; height: 44px; font-size: 20px; cursor: pointer; transition: background-color 0.2s; flex-shrink: 0; }
        #chatSubmitBtn:hover { background: #764ba2; }
        #chatSubmitBtn:disabled { background-color: #ccc; cursor: not-allowed; }
        .typing-indicator { display: flex; align-items: center; padding: 10px 0px !important; }
        .typing-indicator span { height: 8px; width: 8px; background-color: #9E9E9E; border-radius: 50%; display: inline-block; margin: 0 2px; animation: bounce 1.4s infinite ease-in-out both; }
        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }
        @keyframes bounce { 0%, 80%, 100% { transform: scale(0); } 40% { transform: scale(1.0); } }
    </style>
@endsection

@section('content')
    <div class="treatment-container">
        <div class="ai-button-container">
            <button id="generateTreatmentBtn" class="ai-power-button">
                <span class="ai-icon">🤖</span>
                <span>Unleash EnfitGPT Power</span>
                <div class="ai-sparkles"><span class="sparkle"></span><span class="sparkle"></span><span class="sparkle"></span><span class="sparkle"></span><span class="sparkle"></span></div>
            </button>
        </div>

        <div id="loaderContainer" class="ai-loader-container">
            <div class="ai-loader"><div class="ai-loader-circle"></div><div class="ai-loader-circle"></div><div class="ai-loader-circle"></div></div>
            <p class="ai-loader-text">EnfitGPT is analyzing your treatment...</p>
        </div>

        <div id="treatmentContent">{!! $data !!}</div>

        <div id="chatContainer">
            <div class="chat-header">
                <h5>Chat with EnfitGPT</h5>
                <button id="clearChatBtn" class="btn btn-sm btn-outline-danger">Clear History</button>
            </div>
            <div id="chatBox"></div>
            <form id="chatForm">
                <input type="text" id="chatInput" placeholder="Ask EnfitGPT about your program..." autocomplete="off">
                <button type="submit" id="chatSubmitBtn">➤</button>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const generateBtn = document.getElementById('generateTreatmentBtn');
            const loaderContainer = document.getElementById('loaderContainer');
            const treatmentContent = document.getElementById('treatmentContent');
            const chatContainer = document.getElementById('chatContainer');
            const chatForm = document.getElementById('chatForm');
            const chatInput = document.getElementById('chatInput');
            const chatBox = document.getElementById('chatBox');
            const chatSubmitBtn = document.getElementById('chatSubmitBtn');
            const clearChatBtn = document.getElementById('clearChatBtn');

            let conversationHistory = {!! json_encode($chatHistory) !!} || [];

            function renderHistory() {
                chatBox.innerHTML = '';
                conversationHistory.forEach(turn => {
                    appendMessage(turn.content, turn.role === 'user' ? 'user' : 'bot', false);
                });
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            if (treatmentContent.innerHTML.trim().length > 0) {
                chatContainer.style.display = 'block';
                renderHistory();
            }

            generateBtn.addEventListener('click', function() {
                generateBtn.style.transform = 'scale(0.95)';
                setTimeout(() => generateBtn.style.transform = '', 200);
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<span class="ai-icon">⚡</span><span>EnfitGPT Processing...</span>';
                loaderContainer.style.display = 'block';
                treatmentContent.style.display = 'none';
                chatContainer.style.display = 'none';
                conversationHistory = [];
                chatBox.innerHTML = '';

                fetch('{{ route("treat.enfitgpt.create") }}', { method: 'POST', headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': '{{ csrf_token() }}', 'Accept': 'application/json' }, body: JSON.stringify({}) })
                    .then(response => response.ok ? response.json() : Promise.reject('Network response was not ok'))
                    .then(data => {
                        treatmentContent.innerHTML = data.data || '<div class="alert alert-warning">Could not retrieve treatment data.</div>';
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        treatmentContent.innerHTML = '<div class="alert alert-danger">An error occurred while loading treatment data. Please try again.</div>';
                    })
                    .finally(() => {
                        loaderContainer.style.display = 'none';
                        treatmentContent.style.display = 'block';
                        chatContainer.style.display = 'block';
                        generateBtn.disabled = false;
                        generateBtn.innerHTML = '<span class="ai-icon">🤖</span><span>Regenerate with EnfitGPT</span><div class="ai-sparkles"><span class="sparkle"></span><span class="sparkle"></span><span class="sparkle"></span><span class="sparkle"></span><span class="sparkle"></span></div>';
                    });
            });

            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const message = chatInput.value.trim();
                if (!message) return;

                appendMessage(message, 'user');
                const currentHistory = [...conversationHistory];
                conversationHistory.push({ role: 'user', content: message });

                chatInput.value = '';
                chatSubmitBtn.disabled = true;
                showTypingIndicator();

                fetch('{{ route("treat.enfitgpt.chat") }}', { method: 'POST', headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': '{{ csrf_token() }}', 'Accept': 'application/json' }, body: JSON.stringify({ message: message, history: currentHistory }) })
                    .then(response => response.ok ? response.json() : Promise.reject('Network response was not ok'))
                    .then(data => {
                        if (data.reply) {
                            appendMessage(data.reply, 'bot');
                            conversationHistory.push({ role: 'assistant', content: data.reply });
                        }
                    })
                    .catch(error => {
                        console.error('Chat Error:', error);
                        appendMessage('Sorry, I encountered an error. Please try again.', 'bot');
                        conversationHistory.pop(); // Remove the user message that failed
                    })
                    .finally(() => {
                        hideTypingIndicator();
                        chatSubmitBtn.disabled = false;
                        chatInput.focus();
                    });
            });

            clearChatBtn.addEventListener('click', function() {
                if (!confirm('Are you sure you want to clear the chat history?')) return;
                fetch('{{ route("treat.enfitgpt.chat.clear") }}', { method: 'POST', headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': '{{ csrf_token() }}', 'Accept': 'application/json' } })
                    .then(response => response.ok ? response.json() : Promise.reject('Failed to clear'))
                    .then(data => { if (data.success) { chatBox.innerHTML = ''; conversationHistory = []; } })
                    .catch(error => console.error('Clear History Error:', error));
            });

            function appendMessage(text, type, scroll = true) {
                const messageWrapper = document.createElement('div');
                messageWrapper.className = `chat-message ${type}-message`;
                const messageBubble = document.createElement('p');

                if (type === 'bot') {
                    // For bot responses, render the received HTML
                    messageBubble.innerHTML = text;
                } else {
                    // For user messages, set as plain text to prevent self-XSS
                    messageBubble.textContent = text;
                }

                messageWrapper.appendChild(messageBubble);
                chatBox.appendChild(messageWrapper);
                if (scroll) chatBox.scrollTop = chatBox.scrollHeight;
            }

            function showTypingIndicator() {
                const typingIndicatorHTML = `<div id="typingIndicator" class="chat-message bot-message"><p class="typing-indicator"><span></span><span></span><span></span></p></div>`;
                chatBox.insertAdjacentHTML('beforeend', typingIndicatorHTML);
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            function hideTypingIndicator() {
                const indicator = document.getElementById('typingIndicator');
                if (indicator) indicator.remove();
            }
        });
    </script>
@endsection