@extends('Frontend.partials.layout')
@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/select2/select2.css') }}">

    <link rel="stylesheet" href="{{ asset('/css/treatment.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/blade-css/treat.css') }}">
@endsection

@section('content')

@php
    $cartProduct = !empty($cartRecords) ? $cartRecords : getCartdata();
    $cartCnt     = 0;
    $bio = biorythVisibleDetails();
    $udetails  = ($data['userDetails'] != null)?$data['userDetails']:getUserDetails();

    $bgimage = getTreatBGImage($udetails);
    $totaltime = 0;
    $allBGImage = ($allBGImage != null)?$allBGImage:getCustomeImage();
    $auth = Auth::user();
@endphp
{{-- treat section --}}
<section class="treat" >

    {{-- treat page preloader --}}
    <div class="preloader" id="preload_product">
        <img src="{!! asset('images/Fill-4.png') !!}" alt="">
    </div>

    {{-- treat middle section --}}
    <div class="row">
        <div class="col-md-12">
            {{-- treat middle section container --}}
            <div class="treat-main-content treat-main-content-container position-relative" id="treat-main-content">
                <div class="treat-main-content-animation" style="position:relative">
                    <div class="treat-main-content-image">
                        {{-- gif, timer and audio player container  --}}
                        <div class="treat-main-content-image-inner">
                            {{-- gif image --}}
                           <img src="@if(!empty($bgimage)) {!! asset($bgimage) !!} @else{{ asset('images/circle_new.gif') }}@endif" id="bgimgContainer" class="ap-disk-img treat-gif-img" alt="">
                            
                           <div class="audio-player d-flex flex-column align-items-center justify-content-between">

                                <img data-profileimage="true" id="center_img" class="d-inline-block rounded-circle user-avatar" height="100%" width="100%">
                                <img class="d-inline-block rounded-circle treat-body-mental-img" id="center_img_2" src="">

                                {{-- center countdown overlay --}}
                                <div class="analysis_box center-countdown-overlay"></div>

                                {{-- center countdown container--}}
                                <div class="analysis_price center-countdown-container">
                                    {{-- center countdown --}}
                                    <span id="durationCount" class="d-block"></span>
                                    <span class="treat-global-count d-block center-treat-global-count">00:00:00</span>
                                    {{-- audio player --}}
                                    <audio id="audio" autoplay="true" type="audio/mp3"></audio>
                                </div>

                            </div>

                            {{-- treat analysis name --}}
                            <div  class="audio-player-name running-analysis-name">
                                <p id="analysis_name" class="bg-success"></p>
                                <style>
                                    @media(min-width: 768px){
                                        .lessmorebtn {
                                            display: none !important;
                                        }
                                    }
                                    @media(max-width: 767px){
                                        .lessmorebtn {
                                            position: absolute;
                                            bottom: -12px;
                                            background-color: #02BC77 !important;
                                            max-width: 345px;
                                            width: 100%;
                                            padding: 5px;
                                            border-bottom-left-radius: 5px;
                                            border-bottom-right-radius: 5px;
                                        }
                                        
                                        .lessmorebtn span {
                                            font-size: 16px;
                                            text-decoration: underline;
                                            cursor: pointer;
                                        }

                                        .seemore {
                                            display: block;
                                        }

                                        .lessmorebtn.expandable .seeless {
                                            display: none;
                                        }
                                        .lessmorebtn .seemore {
                                            display: none;
                                        }
                                        .lessmorebtn.expandable .seemore {
                                            display: block;
                                        }
                                    }

                                    html, body {
                                        margin: 0;
                                        padding: 0;
                                        height: 100%;
                                        overflow: hidden;
                                        touch-action: manipulation;
                                        -webkit-tap-highlight-color: transparent;
                                    }

                                    .treat {
                                        position: relative;
                                        width: 100vw;
                                        height: 100vh;
                                        background-color: white;
                                        overflow: hidden;
                                        -webkit-overflow-scrolling: touch;
                                    }

                                    #fullscreen-btn {
                                        position: fixed !important;
                                        width: 40px;
                                        height: 40px;
                                        font-size: 24px;
                                        border: none;
                                        border-radius: 50%;
                                        background-color: #28a745;
                                        color: white;
                                        z-index: 9999;
                                        cursor: pointer;
                                        display: block;
                                        touch-action: manipulation;
                                        -webkit-tap-highlight-color: transparent;
                                    }
                                </style>
                                <div class="lessmorebtn" style="display: none;">
                                    <span class="seemore">{{trans('action.more')}}</span>
                                    <span class="seeless">{{trans('action.less')}}</span>
                                </div>
                            </div>
                        </div>

                    </div>

                    @if(count($allBGImage) > 0)
                        <div class="form-group form-inline allBgImgSelectBox">
                            <select data-show-subtext="true" class="selectpicker bs-select form-control m-0 col-md-6" onchange="changeBGImage(this)" data-userid="{{ $udetails->id }}" data-size="8" name="bgImage">
                                <option value="" >{{__('action.defaultimage')}}</option>
                                <option value="Random" @if($data['random_status']) selected @endif>{{__('action.randomimage')}}</option>
                                @foreach ($allBGImage as $img)
                                    <option id="imageid" data-src="{!! asset($img->image) !!}" data-content="<img style='background-color:#02BC77;' src='{!! asset($img->image) !!}' height='25px' width='25px'/>" value="{{ $img->id }}" @if($bgimage == $img->image && $data['random_status'] == false) selected class="selected" @endif></option>
                                @endforeach
                            </select>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- playlist sidebar contents --}}
    <div class="treat-playlist-canvas">
        {{-- playlist sidebar toggle button --}}
        <button class="playlist-toggle-btn">
            <i class="fas fa-angle-left"></i>
        </button>
        <div class="no-padding-md no-padding-lg">
            <div class="right-panel custom-right-panel p-0">
                <div class="card position-relative border-0 rounded-0">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        {{-- playlist heading --}}
                        <h5 class="m-0 text-success" style="line-height: 22px; float: left;">
                            <i class="fas fa-music">&nbsp;</i>{{__('action.currently_treatment')}}
                            {{-- treat playlist total time countdown --}}
                            {{-- <span class="text-secondary" id="trt_count">00:00:00</span> --}}
                            <span class="text-secondary treat-global-count">00:00:00</span>
                        </h5>
                        <i class="fas fa-file-pdf text-success" style="cursor: pointer;"></i>
                    </div>

                    <div class="card-body">
                        <div class="right-panel-list">
                            {{-- treat playlist items --}}
                            <ul class="p-1" id="playlist" >
                                @forelse ($cartProduct as $cart)
                                    @continue($cart->options->optimized == true || $cart->options->price == 0)
                                    @php
                                        $cartCnt++;
                                        $totaltime += $cart->options->price;
                                        $types = [
                                            'Analysis'=>$cart->options->analysisID,
                                            'Causes'=>$cart->options->causes_id,
                                            'Medium'=>$cart->options->medium_id,
                                            'Tipp'=>$cart->options->tipp_id,
                                            'Fokus'=>$cart->options->causes_id,
                                            'Einfluss'=>$cart->options->causes_id,
                                            'Topic'=>$cartCnt."T"
                                        ];
                                        
                                    @endphp
                                    <li class="updateclass divid{{ $types[$cart->options->type].$cart->options->type }}" 
                                        data-name="{{ $cart->options->analysisName }}" 
                                        data-price="{{ $cart->options->price }}" 
                                        data-anaid="{{ $types[$cart->options->type] }}" 
                                        data-subid="{{$cart->options->submenu_id }}" 
                                        data-proid="{{ $cart->options->productID }}" 
                                        data-serialno="{{ $cartCnt }}" 
                                        data-frequncy="{{ $cart->options->type == 'Topic' ? $cart->options->frequency : calculationFrequency($cart->options->analysisName) }}" 
                                        data-body="{{ (($bodyImages) && $bodyImages->firstWhere('analyse_id', $cart->options->analysisID)) ? Storage::disk('images')->url($bodyImages->firstWhere('analyse_id', $cart->options->analysisID)->image) : null }}"
                                        data-mental="{{ (($mentalImages) && $mentalImages->firstWhere('analyse_id', $cart->options->analysisID)) ? Storage::disk('images')->url($mentalImages->firstWhere('analyse_id', $cart->options->analysisID)->image) : null }}"
                                        data-global="{{ (($globalImages) && $globalImages->firstWhere('analyse_id', $cart->options->analysisID)) ? Storage::disk('images')->url($globalImages->firstWhere('analyse_id', $cart->options->analysisID)->desc_image) : null }}"
                                        data-type="{{ $cart->options->type }}">
                                        <p data-toggle="tooltip" data-placement="bottom" title="{{ $cart->options->analysisName }}"> <span class="text-success">{{ gmdate('i:s', $cart->options->price) }}</span> {{ $cart->options->analysisName }} </p>
                                    </li>
                                @empty
                                    <li class="updateclass">
                                        <p class="text-danger" data-toggle="tooltip" data-placement="bottom" title="{{ trans('action.no_data_found') }}"> 00:00 {{ trans('action.no_data_found') }} </p>
                                    </li>
                                @endforelse
                            </ul>
                            <input type="hidden" name="setting" class="_tretment-basic-settings" 
                            data-totalcount={{ $cartCnt }}
                            data-totaltime={{ $totaltime }}
                            data-rangeValue={{ $range ?? 0 }}
                            data-min="{{ $bio->gs_form_frequency }}" 
                            data-max="{{ $bio->gs_to_frequency }}" 
                            data-sound="{{ $udetails->userOption->sound }}" 
                            data-playfrom="treat">
                        </div>
                    </div>
                    
                    {{-- treat sound control buttons container --}}
                    <div class="right-panel-button border-top bg-white">
                        <div class="current-player-session">
                            @livewire('treatment.session-id')
                        </div>
                        <div class="sound_controll_point">

                            {{-- treat sound control button --}}
                            @if($udetails->userOption->sound)
                                <button class="btn btn-default mb-2" id="sound" onclick="soundControll(2)" data-music="1">
                                    <span class="d-inline-block">
                                        <i id="vup" class="fas fa-volume-up"></i> {{ __('action.music') }}
                                    </span>
                                </button>
                            @else 
                                <button class="btn btn-default mb-2" id="sound" onclick="soundControll(2)" data-music="2">
                                    <span class="d-inline-block">
                                        <i id="vup" class="fas fa-volume-down"></i> {{ __('action.music') }}
                                    </span>
                                </button>
                            @endif

                            {{-- treat sound control button --}}
                            <button class="btn btn-default mb-2" id="frequencyset" onclick="soundControll(1)" data-freq="@if(isset(request()->frequency)){{request()->frequency}}@else{{'2'}}@endif">
                                <span class="d-inline-block">
                                    <i id="vdwn" class="fas fa-volume-down"></i> {{ __('action.frequency_due_power') }}
                                </span>
                            </button>
                            <button class="btn bg-success btn-success" id="abbrechen" data-id="relode">{{__('action.abrot')}}</button>
                            <button class="bg-warning btn btn-warning" onclick="playPause(this)" data-play='fas fa-play'><i  class="fas fa-pause">&nbsp;</i><span>Pause</span></button>
                    
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- playlist sidebar backdrop--}}
    <div class="playlist-canvas-backdrop"></div>
    {{-- playlist sidebar end--}}

    {{-- fullscreen button --}}
    <div class="fullscreen-btn-container text-right">
        <button class="text-white" id="fullscreen-btn">
            <i class="fas fa-expand"></i>
        </button>
    </div>

    {{-- Optimized Modal content --}}
    <div class="modal fade custom_modal" id="optimizedModal"  data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="optimizedModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="optimizedModalLabel">{{__('action.reactionValue')}}</h5>
                    {{-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button> --}}
                </div>
                <div class="modal-body">
                    <div class="modal_preloader">
                        <div class="loading">
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </div>
                    <div id="optimizedModalBody" class="web-kit-scroll">

                    </div>
                </div>
                <div class="modal-footer">
                    @if (Auth::user()->user_type)
                        <a href="javascript:void(0)" class="d-inline-block mr-2 btn btn-primary hide" id="reactionPdfButton" data-toggle="modal" data-target="#reactionPdfGenerateModal">
                            <i class="fas fa-file-pdf text-default">&nbsp;</i>{{ __('action.save_pdf') }}
                        </a>
                    @endif
                    <a class="optimizedDsahClone">
                        <button type="button" class="btn btn-success" id="optimizedbtn">{{__('action.optimize')}}</button>
                    </a>
                    <button type="button" class="btn btn-warning" id="optimizedClosed">{{__('action.close')}}</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Pdf Generate Modal --}}
    <div class="modal modal-top fade" id="reactionPdfGenerateModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel">{{ __('action.pdf_title') }}</h4>
                    <button type="button" class="close" data-dismiss="modal"
                        aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <input type="text" id="pdfNameforReaction" class="form-control" placeholder="{{__('action.pdf_title')}}" name="pdf_name">
                </div>
                <div class="modal-footer">
                    <button type="button" id="create-pdf" class="btn btn-info icon"><i class="ion ion-ios-save">&nbsp;</i>{{ __('action.save_pdf') }}</button>
                    <button type="button" class="btn btn-warning icon pdfCreateModal" data-dismiss="modal">{{ __('action.close') }}</button>
                </div>
            </div>
        </div>
    </div>

    {{--START treatment tutorial view modal--}}
    <div class="modal modal-top fade lg" id="treatmentTutorialVideoPlayModal" tabindex="-1" role="dialog"
        aria-labelledby="tutorialModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <video id="treatmentTutorialVideo" controlsList="nodownload" class="vjs-default-skin vjs-big-play-centered" controls
                        preload="auto" width="100%" height="100%" data-setup='{}'>
                        <source src="" type="video/mp4">
                    </video>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning icon pdfCreateModal" data-dismiss="modal">{{
                        __('action.close') }}</button>
                </div>
            </div>
        </div>
    </div>
    {{--END treatment tutorial view modal--}}
    <button id="fullscreen-btn" aria-label="Enter fullscreen">
        ⛶
    </button>
</section>
{{-- treat section /--}}
@endsection

@section('scripts')
    <script src="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('/js/tone/Tone.js') }}"></script>
    <script src="{{ asset('/js/tone/player.js') }}"></script>

    {{-- <script src="{{ asset('/js/commontreatment.js') }}"></script> --}}
    <script src="{{ asset('/js/playtreatment.js') }}"></script>
    <script>
        $(document).ready(function(){
            $('#custom-top-padding').addClass('single-treat-container');
      
           showSwalModal();
            $('#treatmentTutorialVideoPlayModal').on('hidden.bs.modal', function () {
                $('#treatmentTutorialVideo source').closest('video')[0].pause();
                showSwalModal();
            });
            async function showSwalModal() {
                let response = await getTutorialVideoLink();
                Swal.fire({
                   title: getTrans('play_treatment'),
                    icon: 'success',
                    allowOutsideClick: false,
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#fb0404',
                    showConfirmButton: response.video_link ? true : false,
                    confirmButtonText: '{{trans("action.cron_tutorial")}}',
                    cancelButtonText: getTrans('cancel'),
                    showDenyButton: true,
                    denyButtonColor: '#20c997',
                    denyButtonText: getTrans('start_treat')
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Handle Tutorial button action
                        $('#treatmentTutorialVideo source').attr('src', response.video_link).closest('video')[0].load();
                        $('#treatmentTutorialVideoPlayModal').modal('show');
                    } else if (result.isDenied) {
                        // Handle Start Treat button action
                        __startTreatment();
                    } else {
                        // Navigate back to the previous page
                        if (window.history.length > 1) {
                            // There is a history to go back to, so go back one step.
                            window.history.back();
                        } else {
                            // There is no history, so navigate to the home page or another URL.
                            window.location.href = '/';
                        }
                    }
                });
            }
            async function getTutorialVideoLink() {
                try {
                    const response = await fetch(`/get/tutorial/video`);
                    const data = await response.json();
                    return data;
                } catch (error) {
                    return false;
                }
            }
            $('.lessmorebtn').on('click', function(){
                $(this).toggleClass('expandable');
                $('#analysis_name').toggleClass('expandable');
            });
        })


        document.addEventListener('DOMContentLoaded', () => {
            const btn = document.getElementById('fullscreen-btn');
            const target = document.querySelector('.treat');
            const isiOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            let wakeLock = null;

            // iOS Safari requires documentElement for fullscreen
            const getFullscreenTarget = () => isiOS ? document.documentElement : target;

            function requestWakeLock() {
                if ('wakeLock' in navigator) {
                    navigator.wakeLock.request('screen').then(lock => {
                        wakeLock = lock;
                    }).catch(console.error);
                }
            }

            function releaseWakeLock() {
                if (wakeLock) {
                    wakeLock.release().then(() => {
                        wakeLock = null;
                    });
                }
            }

            btn.addEventListener('click', () => {
                const el = getFullscreenTarget();

                if (!document.fullscreenElement) {
                    if (el.requestFullscreen) {
                        el.requestFullscreen();
                    } else if (el.webkitRequestFullscreen) {
                        el.webkitRequestFullscreen();
                    }
                } else {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    }
                }
            });

            document.addEventListener('fullscreenchange', () => {
                const isFS = !!document.fullscreenElement;
                btn.textContent = isFS ? 'X' : '⛶';
                if (isFS) requestWakeLock();
                else releaseWakeLock();
            });
        });
    </script>
    {{-- @include('JSBlade.TreatmentJS') --}}
@endsection
