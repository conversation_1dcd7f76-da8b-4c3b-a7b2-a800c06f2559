@extends('Frontend.partials.layout')

@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/select2/select2.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/blade-css/cron.css') }}">

    <style>

        .crontable-action-btn {
            white-space: nowrap;
            font-size: 0.75rem;
        }

        .analysis-count {
            color: #007bff;
        }
        
        .analysis-info-trigger {
            cursor: pointer;
            font-size: 1.2em;
            transition: all 0.3s ease;
        }
        
        .analysis-info-trigger:hover {
            color: #0056b3;
            transform: scale(1.1);
        }
        
        /* Analysis Modal Styles */
        .analysis-modal-content {
            border: none;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        
        .analysis-modal-header {
            background: linear-gradient(135deg, #28c3d7 0%, #59ab5a 100%);
            color: white;
            border: none;
            padding: 20px 30px;
            position: relative;
        }
        
        .modal-title-container {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            width: 100%;
        }
        
        .modal-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            flex-shrink: 0;
        }
        
        .modal-icon i {
            font-size: 20px;
        }
        
        .modal-user-info {
            flex: 1;
        }
        
        .modal-analyses-count {
            font-size: 0.8rem;
            font-weight: 600;
            color: black;
            padding-top: 15px;
            padding-inline: 30px;
            white-space: nowrap;
        }
        
        .modal-analyses-count .count-number {
            background: linear-gradient(135deg, #28c3d7 0%, #59ab5a 100%);
            color: white;
            border-radius: 12px;
            padding: 4px 12px;
            font-size: 0.9rem;
            font-weight: 600;
            min-width: 24px;
            text-align: center;
        }
        
        .modal-analyses-count .count-text {
            color: #495057;
            font-weight: 500;
        }

        .modal-user-info .namewithdate span.startend span  {
            color: white;
        }
        
        .modal-user-info .namewithdate h4 {
            margin-bottom: 8px;
            font-size: 1.4rem;
            font-weight: 600;
        }
        
        .modal-user-info .startend h5 {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            margin-bottom: 5px;
            font-weight: 400;
        }
        
        .modal-user-info .startend span {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.85rem;
        }
        
        .analysis-modal-close {
            color: white;
            opacity: 0.8;
            font-size: 18px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .analysis-modal-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .analysis-modal-body {
            padding: 0;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .analysis-content-wrapper {
            padding-inline: 30px;
            padding-block: 15px;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .analysis-card {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .analysis-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #28c3d7 0%, #59ab5a 100%);
        }
                
        .analysis-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #59ab5a;
        }
        
        .analysis-text {
            font-size: 0.95rem;
            line-height: 1.5;
            color: #495057;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .analysis-modal-footer {
            background: #f8f9fa;
            border: none;
            padding: 20px 30px;
            text-align: center;
        }
        
        .analysis-modal-footer .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .analysis-modal-footer .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .no-analyses-message {
            text-align: center;
            padding: 60px 30px;
            color: #6c757d;
        }
        
        .no-analyses-message i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 20px;
        }
        
        .no-analyses-message h5 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .no-analyses-message p {
            color: #6c757d;
            margin: 0;
        }
        
        /* Custom scrollbar */
        .analysis-modal-body::-webkit-scrollbar {
            width: 6px;
        }
        
        .analysis-modal-body::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .analysis-modal-body::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #28c3d7 0%, #59ab5a 100%);
            border-radius: 3px;
        }
        
        .analysis-modal-body::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #28c3d7 0%, #59ab5a 100%);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .analysis-grid {
                grid-template-columns: 1fr;
            }
            
            .modal-title-container {
                flex-direction: column;
                text-align: center;
            }
            
            .modal-icon {
                margin: 0 auto 15px auto;
            }
        }
    </style>

@endsection

@section('content')
@if ($message = Session::get('error'))
<div class="alert alert-danger alert-block">
	<button type="button" class="close" data-dismiss="alert">×</button>
        <strong>{{ $message }}</strong>
</div>
@endif
@if ($message = Session::get('warning'))
<div class="alert alert-warning alert-block">
	<button type="button" class="close" data-dismiss="alert">×</button>
	<strong>{{ $message }}</strong>
</div>
@endif

<section class="corndraft">
    <div class="row justify-content-between align-items-center mx-0 px-1">
        <div class="">
            <div class="analysis-content-header d-flex align-items-center">
                <div class="logo">
                    <i class="fas fa-home primary-color"></i>
                </div>
                <div class="heading primary-color">
                    <h2>{{trans('action.page_heading1_cron_view')}}</h2>
                    <p>{{ brandName() }} {{trans('action.remote_analysis')}}</p>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-end button-demo">
            {{-- <a href="{{ route('cron.cronTrash') }}">
                <button class="btn btn-warning mt-3 mr-3 mt-sm-0">
                    <i class="fas fa-trash mr-2"></i>
                    {{trans('action.page_heading1_cron_trash')}}
                </button>
            </a>

            <a href="{{ route('cron.cron_archive', ['archive']) }}">
                <button class="btn btn-primary mt-3 mt-sm-0">
                    <i class="ion ion-md-archive mr-2 ml-2"></i>
                    {{trans('action.cron_archive')}}

                </button>
            </a> --}}
            <a href="{{ route('cron.cronTrash') }}" class="btn btn-warning delete-btn ladda-button mr-2" data-style="slide-down" data-toggle="tooltip" data-placement="bottom" data-state="secondary" title="{{trans('action.page_heading1_cron_trash')}}">
                <span class="ladda-label">
                    <i class="fas fa-trash mr-2"></i>
                    <span class="d-mobile-none">{{trans('action.page_heading1_cron_trash')}}</span>
                </span>
                <span class="ladda-spinner"></span>
                <div class="ladda-progress" style="width: 0px;"></div>
            </a>

            <a href="{{ route('cron.cron_archive', ['archive']) }}" class="btn btn-info archive-btn ladda-button" data-style="expand-right" data-toggle="tooltip" data-placement="bottom" data-state="secondary" title="{{trans('action.cron_archive')}}">
                    <span class="ladda-label">
                    <i class="ion ion-md-archive mr-2 ml-2"></i>
                    <span class="d-mobile-none">{{trans('action.cron_archive')}}</span>
                </span>
                <span class="ladda-spinner"></span>
                <div class="ladda-progress" style="width: 0px;"></div>
            </a>

        </div>
    </div>

    <!-- Filters -->
    <div class="ui-bordered px-4 pt-4 my-4 bg-white card filter-box-cron" id="serach">
        <h4 class="fbc-title">
            {{trans('action.select_filter')}}
        </h4>
        <div class="form-row align-items-center">
            <div class="col-md-3 col-lg-3 mb-4">
                <label class="form-label f-14">{{trans('action.selected_user')}}</label>
                <select class="selectpicker" data-size="10" data-style="btn-light col-md-12" data-live-search="true" data-allow-clear="true" id="user">
                    <option value="">{{trans('action.cronSelectUser')}}</option>
                    @if(!empty($subuser))
                        @foreach($subuser as $sub)
                            <option value="{{ $sub->id }}">{{ $sub->first_name." ".$sub->last_name}}</option>
                        @endforeach
                    @endif
                </select>
            </div>
            <div class="col-md-2 col-lg-2 mb-4">
                <label class="form-label f-14">{{trans('action.cron_type')}}</label>
                <select class="select2-demo form-control selectpicker" style="width: 100%" data-allow-clear="true" id="type">
                    <option value="" selected>{{trans('action.cronTreatmentType')}}</option>
                    <option value="1">{{trans('action.single_cron_view')}}</option>
                    <option value="2">{{trans('action.series_cron_view')}}</option>
                </select>
            </div>
            <div class="col-md-3 col-lg-2 mb-4">
                <label class="form-label f-14">{{trans('action.status')}}</label>
                <select class="select2-demo form-control selectpicker" style="width: 100%" data-allow-clear="true" name="status" id="status">
                    <option value="">{{trans('action.select_status')}}</option>
                    <option value="0">{{trans('action.online')}}</option>
                    <option value="1">{{trans('action.cron_done')}}</option>
                    <option value="2">{{trans('action.stop')}}</option>
                </select>
            </div>

            <div class="col-md-3 col-lg-2 mb-4">
                <label for="">Date Range</label>
                <input class="form-control" type="text" id="datefilter" >
            </div>

            <div class="col-md-3 col-lg-3">
                <div class="button-demo mb-4 filter-btn">
                    <label class="form-label f-14"></label>
                    {{-- <br><button class="btn btn-success" id="filter_button"><i class="fas fa-filter">&nbsp;</i>{{trans('action.select_filter')}}</button> &nbsp;&nbsp;<button class="btn btn-primary" id="refresh_button"><i class="ion ion-md-refresh">&nbsp;</i>{{trans('action.refresh')}}</button> --}}
                    <div>
                        <button class="mx-1 btn btn-success ladda-button" id="filter_button" data-style="zoom-in">
                            <span class="ladda-label"><i class="fas fa-filter">&nbsp;</i>{{trans('action.select_filter')}} </span>
                            <div class="ladda-progress" style="width: 121px;"></div>
                            <span class="ladda-spinner"></span>
                        </button>
        
                        <button class="btn btn-warning ladda-button" id="refresh_button" data-style="zoom-in">
                            <span class="ladda-label"><i class="ion ion-md-refresh">&nbsp;</i>{{trans('action.refresh')}}</span>
                            <div class="ladda-progress" style="width: 121px;"></div>
                            <span class="ladda-spinner"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- / Filters -->
    <div class="card">
        <div class="card-datatable table-responsive" id="card-datatable">
            <table class="table table-striped table-bordered cron-setup-table11 crondatatable" id="cron_table">
                <thead>
                    <tr>
                        <th>{{trans('action.sno')}}</th>
                        <th data-sortable="true" data-order="asc" style="min-width: 160px;">{{trans('action.full_name_cron_view')}}</th>
                        <th>{{trans('action.note_tag')}}</th>
                        <th>{{trans('action.customer_note')}}</th>
                        <th>{{trans('action.manage_topic')}}</th>
                        <th>{{trans('action.cron_type')}}</th>
                        {{-- <th>{{trans('action.run_time_cron_view')}}</th> --}}
                        <th>{{trans('action.status')}}</th>
                        <th>{{trans('action.open_by_user_cron_view')}}</th>
                        <th>{{trans('action.analyses')}}</th>
                        <th>{{trans('action.month')}}/{{trans('action.year')}}</th>
                        <th>{{trans('action.action')}}</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</section>







<div class="modal fade" id="crnDraftModal" data-bs-backdrop="static" data-bs-keyboard="false"></div>



<!-- Analysis Modal -->
<div class="modal fade" id="analysisModal" tabindex="-1" role="dialog" aria-labelledby="analysisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content analysis-modal-content">
            <div class="modal-header analysis-modal-header">
                <div class="modal-title-container">
                    <div class="modal-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="modal-user-info">
                        {{__('action.analyses')}}
                    </div>
                </div>
                <button type="button" class="close analysis-modal-close" data-dismiss="modal" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body analysis-modal-body">
                <div class="modal-analyses-count">
                    {{__('action.modal-analyses-count-message')}} (0)
                </div>
                <div id="analysisContent" class="analysis-content-wrapper">
                    <!-- Analysis list will be populated here -->
                </div>
            </div>
            <div class="modal-footer analysis-modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    <i class="fas fa-times mr-2"></i>{{__('action.close')}}
                </button>
            </div>
        </div>
    </div>
</div>

@endsection



@section('scripts')
    <!-- Dependencies -->
    <script src="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('/js/forms_selects.js') }}"></script>

    <script type="text/javascript">

    var cronProgress = true;
    $(document).ready(function() {

        load_data();
        
        function load_data(user = '',type = '',status ='',order = '', range=''){
            $('#cron_table').DataTable({
                columnDefs: [
                    { targets: [0,2,3,4,5,6,7,8,9], orderable: false },
                    { targets: [0],width: '5%'},
                    // { targets: [13],width: '12%' },
                    {
                        targets: [1], 
                        type: 'user-full-name'
                    }
                ],
                language: {
                    sEmptyTable: `{{trans('action.table_norecord')}}`,
                    sInfo: "_START_ bis _END_ von _TOTAL_ Einträgen",
                    sInfoEmpty: "0 bis 0 von 0 Einträgen",
                    sInfoFiltered: "(gefiltert von _MAX_ Einträgen)",
                    sInfoPostFix: "",
                    sInfoThousands: ".",
                    sLengthMenu: "_MENU_ "+ `{{trans('action.table_showdata')}}`,
                    sLoadingRecords: `{{trans('action.table_loading')}}`,
                    sProcessing: `{{trans('action.table_pleasewait')}}`,
                    sSearch: `{{trans('action.search')}}`,
                    sZeroRecords: `{{trans('action.table_nodata')}}`,
                    oPaginate: {
                        sFirst: `{{trans('action.table_first')}}`,
                        sPrevious: `{{trans('action.table_back')}}`,
                        sNext: `{{trans('action.table_next')}}`,
                        sLast: `{{trans('action.table_latest')}}`,
                    },
                    oAria: {
                        sSortAscending:
                            ": aktivieren, um Spalte aufsteigend zu sortieren",
                        sSortDescending:
                            ": aktivieren, um Spalte absteigend zu sortieren",
                    },
                },
                serverSide: true,
                ajax:{
                    url:'{{ route("cron.cron")}}',
                    type: 'GET',
                    pages: 20,
                    data: { user:user ,type:type,status:status,orderBy:order, range:range},
                },
                order: [[1, 'ASC']],
                button: false,
                searching: true, 
                scrollCollapse: true,
                processing: true,
                autoWidth: true,
                columns:[
                    {
                        data: "id",
                        render: function (data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    {
                        data: 'user_full_name',
                        name: 'user_full_name',
                        searchable: false,
                        render: function (data, type, row, meta) {
                            const hasCronSetup = row.cronsetuptimes && row.cronsetuptimes.length > 0; 
                            if (hasCronSetup) { 
                                return `
                                    <div class="namewithdate">
                                        <h4>${data}</h4>
                                        <span class="startend">
                                            <h5>
                                                <i class="fas fa-clock"></i> ${row.start_date_time}
                                                <i class="fas fa-arrow-right"></i> ${row.end_date_time}
                                                <br>
                                                <span style="margin-left: 1px;">{{trans('action.run_time_cron_view')}} <i class="fas fa-arrow-right"></i> ${row.run_time}</span>
                                            </h5>
                                            
                                            <span><i class="fas fa-map-marker"></i> (${row.cronsetuptimes[0].timezone})</span>
                                        </span>
                                    </div>`;
                            } else {
                                return `
                                    <div class="namewithdate">
                                        <h4>${data}</h4>
                                        <div class="badge badge-danger">No Treatment</div>
                                    </div>`;
                            }
                        }
                    },

                    {data:'note', name:'note', searchable: false},
                    {data:'client_note', name:'client_note', searchable: false},
                    {data:'topic', name:'topic', searchable: false}, 
                    {data:'cron_type', name:'cron_type', searchable: false},
                    // {data:'run_time', name:'run_time', searchable: false},
                    {data:'status', name:'status', searchable: false, className: 'status table-head-width'},
                    {data:'open_status_by_user', name:'open_status_by_user', searchable: false},
                    {
                        data: 'analyses',
                        name: 'analyses',
                        searchable: false,
                        render: function (data, type, row, meta) {
                            if (!Array.isArray(data) || data.length === 0) {
                                return '<span class="text-muted">-</span>';
                            }
                            
                            const count = data.length;
                            const rowIndex = meta.row;
                            
                            return `
                                <div class="d-flex align-items-center">
                                    <span class="mr-2 analysis-count">${count}</span>
                                    <i class="fas fa-info-circle text-info cursor-pointer analysis-info-trigger" 
                                       data-row-index="${rowIndex}"></i>
                                </div>
                            `;
                        }
                    },
                    {
                        data: 'cal_type',
                        name: 'cal_type',
                        searchable: false,
                        render: function (data, type, row, meta) {
                            const check = (data == 1) ? 'checked' : '';
                            return `
                            <div class="col-md-12">
                                <div class="d-flex align-items-center month-year-checkbox">
                                    <div class="flex-shrink-1 mr-1">{{trans('action.year')}}</div>
                                    <label class="switcher switcher-sm switcher-success m-0">
                                        <input type="checkbox" disabled class="switcher-input" ${check} >
                                        <span class="switcher-indicator">
                                            <span class="switcher-yes"></span>
                                            <span class="switcher-no"></span>
                                        </span>
                                    </label>
                                    <div class="flex-shrink-1 text-success ml-2">{{trans('action.month')}}</div>
                                </div>
                            </div>
                            `;
                        }
                    },                    

                    // {data: 'action', name: 'action', searchable: false},
                    {
                        data: 'action',
                        name: 'action',
                        searchable: false,
                        className: 'action-column' // Add a class to the 'action' column
                    },
                    
                ]
            });
            // Define a custom sorting function for the user_full_name column
            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "user-full-name-pre": function(a) {
                    return a.toLowerCase().split(" ").reverse().join(", ");
                },

                "user-full-name-pre": function(a) {
                    return a.toLowerCase().split(" ").reverse().join(", ");
                },
            
                "user-full-name-asc": function(a, b) {
                    return a.localeCompare(b);
                },
            
                "user-full-name-desc": function(a, b) {
                    return b.localeCompare(a);
                }
            });
        }
        

        $(document).on('click','#filter_button',function(){

            var user=$("select#user option").filter(":selected").val();
            var type=$("select#type option").filter(":selected").val();
            var status=$("select#status option").filter(":selected").val();
            var range=$("#datefilter").val();
                $('#cron_table').DataTable().destroy();
                load_data(user,type,status,'',range);
        });

        $('#refresh_button').click(function(){
            $(".selectpicker").val('').change();
            $("#datefilter").val('');
            $('#cron_table').DataTable().destroy();
            load_data();

            $('.filter-box-cron').removeClass('expandCard');
        });

    });

    let checkCornProgress = window.setInterval(function() {
        let cronIds = $('.cron-progress').map(function() {
            if(this.value == 1) return $(this).data('id');
        }).get();

        if(cronIds.length && cronProgress) {
            cronProgress = false;
            let divContent = `<br/><span class='saving text-info'>Processing<span><b>.</b></span><span><b>.</b></span><span><b>.</b></span></span>`;
            $.ajax({
                method: "GET",
                url:`{{ route("cron.progress_status")}}`,
                data: {id : cronIds},
                success: function(res) {
                    $.each(res.status, function(key, value) {
                        let content = $(`#cron_${key} .status`);
                        if(value === 1) {
                            content.find('br, span').remove();
                            content.append(divContent);
                        } else {
                            $(`#cp_${key}`).val(0);
                            $(`#process${key}`).removeClass('show').addClass('hide');
                            content.find('br, span').remove();
                        }
                    })

                }, error: function(){}
            });

        } else {
            clearInterval(checkCornProgress);
        }
        cronProgress = true;
    }, 4000);

    $(document).on('click','#show_cron',function(){

        var id=$(this).data('id');

        $.ajax({
            method: "GET",
            url:'{{ route("cron.cron_show")}}',
            data: {id:id},
            success: function(data) {
                $("#crnDraftModal").html(data).modal('show');
            },
            error: function(){

            }
        });

    });


    function showanadata(timeid){
        $.ajax({
            type: "POST",
            cache: false,
            url: "{{ route('cron.cronShowAnalysis') }}",
            data : {crontimeid: timeid},
            async : false,
            beforeSend: function(){
                Swal.fire({
                    title: `{{__('action.processing')}}`,
                    imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    confirm: true,
                    showLoaderOnConfirm: true,
                    width: '300px',
                    height: '180px'
                })
            },
            datatype: "json",
            success: function(res){
                var data = "";
                data += "<ul class='list-unstyled'>";

                if(res.success == true){
                    $.each(res.allsubmenus, function(keys, values) {
                        if(values.submenu_name != "") data += "<li class='p-2 text-success'><b>"+values.submenu_name+"</b></li>";
                        
                        $.each(values.analyses, function(key, value) {
                            if(value.type == 0){
                                var time = display(value.ana_val);
                                data += "<li class='p-2'><b>"+value.calculation+"% "+time+" - "+value.ana_name+"</b></li>";

                                if(res.causes == 1 && value.cau_title != null) data += "<li class='p-2'>{{ trans('action.causes') }}: "+display(value.cau_price)+" - "+value.cau_title+"</li>";
                                if(res.medium == 1 && value.mid_title != null) data += "<li class='p-2'>{{ trans('action.medium') }}: "+ display(value.mid_price) +" - "+value.mid_title+"</li>";
                                if(res.tipp == 1 && value.tip_title != null)data += "<li class='p-2'>{{ trans('action.tipp') }}: "+ display(value.tip_price)+ " - "+value.tip_title+"</li>";
                            }
                            else if(value.type == 1)data += "<li class='p-2'><b>{{ trans('action.causes') }} : "+ display(value.ana_val) +" - "+value.cau_title+"</b></li>";
                            else if(value.type == 2)data += "<li class='p-2'><b>{{ trans('action.medium') }} : "+ display(value.ana_val) +" - "+value.mid_title+"</b></li>";
                            else if(value.type == 3)data += "<li class='p-2'><b>{{ trans('action.tipp') }} : "+ display(value.ana_val) +" - "+value.tip_title+"</b></li>";
                            else if(value.type == 4)data += "<li class='p-2'><b>{{ trans('action.einfluss') }} : "+ display(value.ein_price) +" - "+value.ein_title+"</b></li>";
                            else if(value.type == 5)data += "<li class='p-2'><b>Fokus : "+ display(value.foc_price) +" - "+value.foc_title+"</b></li>";
                            else if(value.type == 6)data += "<li class='p-2'><b><span class='text-dark'>{{ trans('action.manage_topic') }}</span> : "+ display(value.ana_val) +" - "+value.ana_name+"</b></li>";
                            
                        });
                    });
                } else{
                    $('.add2cartbtn'+timeid).hide()
                    data += "<li class='p-2'><b>{{ trans('action.no_treatment_needed') }}</b></li>";                    
                }
                data += "</ul>";

                $("#analysesTreatment"+timeid).html(data);
                Swal.close();
                $('#showDateModal'+timeid).modal('show')
            }
        });
        
        
    }

    function stopemail(cronid){

        Swal.fire({
            title: `{{trans('action.areyousure')}}`,
            text: `{{trans('action.you_cant_revert')}}`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{trans('action.yes')}}`,
            cancelButtonText: `{{trans('action.cancel')}}`
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    type: "POST",
                    cache: false,
                    url: "{{ route('cron.stopEmail') }}",
                    data : {cronid: cronid},
                    async : false,
                    datatype: "json",
                    success: function(res){
                        Swal.fire(`{{trans('action.update_successfully')}}`,res.msg, 'success');
                        $("#stopemail").html(res.button);
                        $("#stopemail").attr('class', 'mb-2 mr-1 mb-sm-0 btn btn-'+res.color);
                    }

                });
            }
        })
    }


    function updateemail(cronid){

        var email = $("#editcronemail").val();
        var atposition  = email.indexOf("@");
        var dotposition = email.lastIndexOf(".");
        if (atposition<1 || dotposition<atposition+2 || dotposition+2>=email.length){
            toastr.info(`{{trans('action.invalid_email_address')}}`);
            return 0;
        }

        $.ajax({
            type: "POST",
            cache: false,
            url: "{{ route('cron.cronUpdateEmail') }}",
            data : {cronid: cronid, email:email},
            async : false,
            datatype: "json",
            success: function(msg){
                $("#editEmailModal"+cronid).hide();
                if(msg.status == true){
                    Swal.fire({
                        icon: 'success',
                        title: msg.message,
                        showConfirmButton: false,
                        timer: 3500
                    })
                }else{
                    Swal.fire({
                        icon: 'error',
                        title: 'Something Went Wrong. Please Try Again Later',
                        showConfirmButton: false,
                        timer: 3500
                    })
                }
            }

        });
    }


    $(document).ready(function() {
        $('.cron-setup-table11').delegate('.sweet_confirm', 'click', function(e){
            e.preventDefault();
            let url = $(this).data('url');

            Swal.fire({
            title: `{{trans('action.areyousure')}}`,
            text: `{{trans('action.you_cant_revert')}}`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{trans('action.yes_delete')}}`,
            cancelButtonText: `{{trans('action.cancel')}}`
            }).then((result) => {
            if (result.value) {
                $.ajax({
                type: "DELETE",
                url: url,
                success: function(response) {
                    if (response.success) {
                        Swal.fire(`{{trans('action.deleted')}}`, response.message,'success');
                        $('#cron_table').DataTable().ajax.reload();

                    } else {
                        Swal.fire(`{{trans('action.error')}}`, response.message,'error');
                    }
                }
                });
            }
            })
        });

        $('.cron-setup-table11').delegate('.cron-duplicate', 'click', function(e){
            e.preventDefault();
            let url = $(this).data('url');

            $.ajax({
                url: url,
                success: function(response) {
                    window.location.href = url;
                }
            });
        });    
    });


    function endemail(cronid){

        let URL = $("#endmail").data('url');

        Swal.fire({
            title: `{{trans('action.areyousure')}}`,
            text: `{{trans('action.you_cant_revert')}}`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{trans('action.yes')}}`,
            cancelButtonText: `{{trans('action.cancel')}}`
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    type: "POST",
                    cache: false,
                    url: URL,
                    async : false,
                    datatype: "json",
                    success: function(response) {
                        if (response.success) {
                            Swal.fire(response.title,response.msg, 'success');
                            $("#endmail").html(response.button);
                            $("#endmail").attr('class', 'btn btn-'+response.color);
                        } else {
                            Swal.fire(`{{trans('action.error')}}`, `{{trans('action.something_went_wrong')}}`,'error');
                        }
                    }

                });
            }
        })
    }


    function resendtreatmail(tid){

        Swal.fire({
            title: `{{trans('action.areyousure')}}`,
            text: `{{trans('action.resend_treatment')}}`,
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            cancelButtonText:`{{trans('action.cancel')}}`,
            confirmButtonText: `{{trans('action.resend_btn')}}`

        }).then((result) => {

            if (result.value) {
                $.ajax({
                    url: "{{ route('resendtreatmail') }}",
                    type: 'POST',
                    data: {
                        timeid: tid
                    },
                    dataType:"json",
                    success: function(msg) {

                        if(msg.status == true){
                            Swal.fire({
                                icon: 'success',
                                title: `{{trans('action.mail_send_success')}}`,
                                showConfirmButton: false,
                                timer: 3500
                            })
                        }else{
                            Swal.fire({
                                icon: 'error',
                                title: msg.title,
                                text: msg.content,
                                showConfirmButton: false,
                                showCloseButton: true
                            })
                        }
                    }
                });
            }
        })
    }

    function display (seconds) {

        const format = val => `0${Math.floor(val)}`.slice(-2)
        const hours = seconds / 3600
        const minutes = (seconds % 3600) / 60

        return [minutes, seconds % 60].map(format).join(':')
    }


 
    $(document).on('click','.share_cron',function(){  
        // alert('Hello');
        $('.share_cron').parent().removeClass('open');
        $(this).parent().addClass('open');
        $(this).next('.share-tooltip-content').empty();

        // "https://start.energetisch.fit/cron/overview/70576/12880afa60feae8c99b0326756d413bbfb9b"
        let link = window.location.origin+'/cron/overview/'+$(this).data('id')+'/'+$(this).data('uniqueid')
        event.stopPropagation();
        $(this).next('.share-tooltip-content').append(`
            <a href="https://api.whatsapp.com/send?text=${link}" target="_blank" class="what-link"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/></svg></a>
            <a href="https://t.me/share/url?url=${link}" target="_blank" class="tel-link"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 496 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M248,8C111.033,8,0,119.033,0,256S111.033,504,248,504,496,392.967,496,256,384.967,8,248,8ZM362.952,176.66c-3.732,39.215-19.881,134.378-28.1,178.3-3.476,18.584-10.322,24.816-16.948,25.425-14.4,1.326-25.338-9.517-39.287-18.661-21.827-14.308-34.158-23.215-55.346-37.177-24.485-16.135-8.612-25,5.342-39.5,3.652-3.793,67.107-61.51,68.335-66.746.153-.655.3-3.1-1.154-4.384s-3.59-.849-5.135-.5q-3.283.746-104.608,69.142-14.845,10.194-26.894,9.934c-8.855-.191-25.888-5.006-38.551-9.123-15.531-5.048-27.875-7.717-26.8-16.291q.84-6.7,18.45-13.7,108.446-47.248,144.628-62.3c68.872-28.647,83.183-33.623,92.511-33.789,2.052-.034,6.639.474,9.61,2.885a10.452,10.452,0,0,1,3.53,6.716A43.765,43.765,0,0,1,362.952,176.66Z"/></svg></a>
            <a href="#" class="copy-link" data-url="${link}"><i class="fas fa-copy"></i></a>   
        `);
    });



    $(document).on('click', function(event) { 
        if (!$(event.target).closest('.share_cron').length) { 
            $('.share_cron').parent().removeClass('open');
        }
    });


    $(document).on('click','.fbc-title',function(){  
         $(this).parent().addClass('expandCard');
    });

    
 
 
    flatpickr("#datefilter", {
        mode: "range"
    });

    $(document).ready(function() {  
        $(document).on('click', '.analysis-info-trigger', function() {
            const rowIndex = $(this).data('row-index');
            const table = $('#cron_table').DataTable();
            const rowData = table.row(rowIndex).data();
            
            const validAnalyses = Array.isArray(rowData.analyses) ? rowData.analyses : [];
            const userInfo = {
                user_full_name: rowData.user_full_name,
                start_date_time: rowData.start_date_time,
                end_date_time: rowData.end_date_time,
                run_time: rowData.run_time,
                cronsetuptimes: rowData.cronsetuptimes
            };
                        
            let modalUserInfo = '';
            let analysesCount = '';
            
            if (userInfo.cronsetuptimes && userInfo.cronsetuptimes.length > 0) {
                modalUserInfo = `
                    <div class="namewithdate">
                        <h4>${userInfo.user_full_name}</h4>
                        <span class="startend">
                            <h5>
                                <i class="fas fa-clock"></i> ${userInfo.start_date_time}
                                <i class="fas fa-arrow-right"></i> ${userInfo.end_date_time}
                                <br>
                                <span style="margin-left: 1px;">{{trans('action.run_time_cron_view')}} <i class="fas fa-arrow-right"></i> ${userInfo.run_time}</span>
                            </h5>
                            <span><i class="fas fa-map-marker"></i> (${userInfo.cronsetuptimes[0].timezone})</span>
                        </span>
                    </div>
                `;
            } else {
                modalUserInfo = `
                    <div class="namewithdate">
                        <h4>${userInfo.user_full_name || 'Unknown User'}</h4>
                        <div class="badge badge-danger">No Treatment</div>
                    </div>
                `;
            }
            
            analysesCount = `${validAnalyses.length} {{__('action.modal-analyses-count-message')}} `;
            
            if (validAnalyses.length === 0) {
                analysesCount = '{{__("action.no_analyses")}}';
                $('.modal-user-info').html(modalUserInfo);
                $('.modal-analyses-count').hide();
                $('#analysisContent').html(`
                    <div class="no-analyses-message">
                        <i class="fas fa-chart-line"></i>
                        <h5>{{__('action.no_analyses')}}</h5>
                    </div>
                `);
                $('#analysisModal').modal('show');
                return;
            }
            
            $('.modal-user-info').html(modalUserInfo);
            $('.modal-analyses-count').text(analysesCount);
            
            let html = '<div class="analysis-grid">';
            validAnalyses.forEach((analysis, index) => {
                const safeAnalysis = $('<div>').text(analysis).html();
                html += `
                    <div class="analysis-card">
                        <div class="analysis-text">${safeAnalysis}</div>
                    </div>
                `;
            });
            html += '</div>';
            
            $('#analysisContent').html(html);
            
            $('#analysisModal').modal('show');  
        });
    });

</script>
@endsection
{{-- <span class="share-tooltip-content">
    <a href="#" ><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/></svg></a>
    <a href="#" ><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 496 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M248,8C111.033,8,0,119.033,0,256S111.033,504,248,504,496,392.967,496,256,384.967,8,248,8ZM362.952,176.66c-3.732,39.215-19.881,134.378-28.1,178.3-3.476,18.584-10.322,24.816-16.948,25.425-14.4,1.326-25.338-9.517-39.287-18.661-21.827-14.308-34.158-23.215-55.346-37.177-24.485-16.135-8.612-25,5.342-39.5,3.652-3.793,67.107-61.51,68.335-66.746.153-.655.3-3.1-1.154-4.384s-3.59-.849-5.135-.5q-3.283.746-104.608,69.142-14.845,10.194-26.894,9.934c-8.855-.191-25.888-5.006-38.551-9.123-15.531-5.048-27.875-7.717-26.8-16.291q.84-6.7,18.45-13.7,108.446-47.248,144.628-62.3c68.872-28.647,83.183-33.623,92.511-33.789,2.052-.034,6.639.474,9.61,2.885a10.452,10.452,0,0,1,3.53,6.716A43.765,43.765,0,0,1,362.952,176.66Z"/></svg></a>
    <a href="#" ><i class="fas fa-copy"></i></a>  
</span> --}}