@extends('Frontend.partials.layout')

@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/select2/select2.css') }}">
    <style>
        .dropdown-menu .dropdown-header  {
            background-color: #8897AA;
            color: white;
        }

        #bottomFixed.bottom-Fixed {
            left: 50%;
            transform: translateX(-50%);
            background: transparent;
        }
#
        @media only screen and (max-width: 767px) {
            #bottomFixed.some_class {
                bottom: 65px !important;
            }
        }
        .text-muted{
            display: none !important;
        }

    </style>
@endsection
@php
    $udetails = getUserDetails();
@endphp
@section('content')
<section class="create-corn"  style="overflow: visible !important;">
    <div class="row" style="border: none;margin-bottom: 0;padding-bottom: 0;">
        <div class="col-md-12">
            <div class="row-top-border">
                <div class="row">
                    <div class="col-md-6">
                        <div class="analysis-content-header d-flex align-items-center">
                            <div class="logo">
                                <i class="fas fa-home primary-color"></i>
                            </div>
                            <div class="heading primary-color">
                                <h2>{{ trans('action.cron_duplicate')}}</h2>
                                <p>{{ brandName() }} Fernbehandlung</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-right">
                        <div class="corn-button">
                            <a href="{{ route('cron.cron') }}">
                                <button class="btn btn-success mb-2 mb-sm-0"><i class="fas fa-tasks mr-3"></i>{{trans('action.view_cron_draft')}}</button>
                            </a>
                            <a href="{{ route('cron.cronSetting', [md5(getAuthID())]) }}">
                                <button class="btn btn-info"><i class="fas fa-tools mr-3"></i>{{trans('action.cron_setting')}} </button>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="row mt-3 mt-sm-0">

    </div>
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
                    <span class="d-inline-block mb-2 mb-sm-0">{{trans('action.view_cron_draft')}}</span>
                    <!-- <span class="d-inline-block">{{trans('action.preview_cron')}} : {{ $totalcron ?? 0 }} / {{trans('action.cron_total_uses')}} 0</span> -->
                </div>
                <div class="card-body">
                    <form action="{{ route('cron.storeCronSetup') }}" class="create-corn-form" id="crondata" method="POST">
                    @csrf
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{trans('action.cron_select_user')}}</b> </label>
                                    <select class="select2-demo form-control"  id="cronusercheck" name="cronuser" required>
                                        @if (checkTherapist(getUserId()) == 0)
                                            <option value="{{ $udetails->id }}" data-timezoneid="{{ $udetails->timezone_id }}" selected>{{ $udetails->first_name." ".$udetails->last_name}}</option>
                                        @else
                                        <option value="">{{trans('action.cron_select_user')}}</option>
                                        @endif
                                        @if(!empty($subuser))
                                            @foreach($subuser as $sub)
                                                @if(Auth::user()->user_type == 4)
                                                    @continue(!in_array($sub->id,getCollectionArrayForStaffUser(Auth::user()->staffInGorup)))
                                                @endif
                                                <option data-timezoneid="{{ $sub->timezone_id }}" {{ ($user->id == $sub->id && $user->boss_id) ? 'selected':'' }} value="{{ $sub->id }}" id="{{ $sub->id}}" {{ (!empty(old('cronuser')) && old('cronuser')==$sub->id) ? "selected" : ""}}>
                                                    {{ $sub->first_name." ".$sub->last_name . ((!$sub->boss_id)? ' -'.$sub->userRoll:'')}}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                    <span class="invalid-feedback allmsg" role="alert">
                                        <strong id="msg"></strong>
                                    </span>
                                </div>
                                @if(!$cronsetup->cron_type)
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{trans('action.selectet_analysis')}}</b> </label>
                                    <div class="select2-dark">
                                        <select class="selectpicker" id="cronsubcheck" multiple name="cronsubmenu[]" required data-live-search="true" data-count-selected-text= "{0} Submenu Selected" data-selected-text-format="count > 1">
                                            <!-- <option value="">{{trans('action.select_sub_menu')}}</option> -->
                                            @if(!empty($product))
                                                @foreach($product as $pro)
                                                    @if(!empty($pro['submenu'][0]))
                                                    <optgroup class="selectAll{{ $pro['proId'] }}" label="{!! ucfirst($pro['proName']) !!}">
                                                    {{-- <option class="bg-secondary text-white disable-pro" value="{{ $pro['proId'] }}">{{ $pro['proName'] }}</option> --}}
                                                        @foreach($pro['submenu'] as $sub)
                                                            @if($pro['ownmenu'] == "yes")
                                                                <option data-subtext="{!! ucfirst($pro['proName']) !!}" value="{{ $sub->id }}-own" data-pro="{{ $sub->product_id }}" id="subid_{{$sub->id}}" @if(is_array($ownsubs) && in_array($sub->id, $ownsubs)) {{'selected'}} @endif>{{ $sub->name }}</option>
                                                            @elseif($sub->type != 12)
                                                                <option data-subtext="{!! ucfirst($pro['proName']) !!}" value="{{ $sub->id }}-sub" data-pro="{{ $sub->product_id }}" id="subid_{{$sub->id}}" @if(is_array($norsubs) && in_array($sub->id, $norsubs)) {{'selected'}} @endif>{{ $sub->menu_name }}</option>
                                                            @elseif($sub->type == 12)
                                                                @if(is_array($norsubs) && in_array($sub->id, $norsubs))
                                                                    <option data-subtext="{!! ucfirst($pro['proName']) !!}" value="{{ $sub->id }}-sub" data-pro="{{ $sub->product_id }}" id="subid_{{$sub->id}}" selected>{{ $sub->menu_name }}</option>
                                                                @endif
                                                            @endif
                                                        @endforeach
                                                    </optgroup>
                                                    @endif
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>
                                    <span class="invalid-feedback allmsg" role="alert">
                                        <strong id="msg1"></strong>
                                    </span>
                                </div>
                                @elseif($cronsetup->cron_type == 1)

                                <div class="form-group">
                                    <select class="form-control" multiple name="cronanalyses[]">
                                        @if(isset($allData) && $allData->count() > 0)
                                            @foreach ($allData as $car_item)
                                                @php
                                                    if(in_array($car_item->pivot->type_status, [0, 6])) {
                                                        if($car_item->pivot->type_status == 6) {
                                                            $name = $car_item->pivot->topic;
                                                            $value = 1; 
                                                        } else {
                                                            $name = $car_item->name;
                                                            $value = $car_item->id;
                                                        }
                                                    } else {
                                                        $name = $car_item->name;
                                                        if($car_item->pivot->type_status == 1 || $car_item->pivot->type_status == 4 || $car_item->pivot->type_status == 5) {
                                                            $value = $car_item->pivot->causes;
                                                        } elseif($car_item->pivot->type_status == 2) {
                                                            $value = $car_item->pivot->medium;
                                                        } elseif($car_item->pivot->type_status == 3) {
                                                            $value = $car_item->pivot->tipp;
                                                        }
                                                    }
                                                @endphp
                                                <option class="mb-1" value="{{ $value }}" selected>{{ $name }}</option>
                                            @endforeach
                                        @else
                                            @if(!empty($analyses))
                                            @php
                                                $arr[] = 0;
                                                $cauarr[] = 0;
                                                $topic[] = "";
                                            @endphp
                                            @foreach ($analyses as $car_item)
                                                @php
                                                    if($car_item->pivot->type_status == 0){
                                                        if(in_array($car_item->pivot->analyse_id, $arr)) continue;
                                                        $name = $car_item->name;
                                                        $arr[] = $car_item->pivot->analyse_id;
                                                    }
                                                    elseif($car_item->pivot->type_status == 6){
                                                        if(in_array($car_item->pivot->topic, $topic)) continue;
                                                        $name = $car_item->pivot->topic;
                                                        $topic[] = $car_item->pivot->topic;
                                                    }
                                                @endphp
                                                <option class="mb-1" value="{{ $car_item->id }}" selected>{{ $name }}</option>
                                            @endforeach
                                            @endif
                                            @if($causes != null)
                                                @foreach ($causes as $car_item)
                                                @php
                                                $name = null;
                                                    if($car_item->pivot->type_status == 4){
                                                        $name = getCauseNameByCauseid($car_item->pivot->causes);
                                                    }
                                                    elseif($car_item->pivot->type_status == 5){
                                                        $name = getCauseNameByCauseid($car_item->pivot->causes);
                                                    }
                                                @endphp
                                                @continue($name == null)
                                                    <option class="mb-1" value="{{ $car_item->pivot->causes }}" selected>{{ $name }}</option>
                                                @endforeach
                                            @endif
                                        @endif
                                    </select>
                                </div>

                                @endif
                                <div class="d-flex col-md-12 pl-0 pr-0">
                                    <button type="button" class="btn btn-default hover-green w-50 mr-2" id="autoGenerate">{{trans('action.auto_genrate')}}</button>
                                    <button type="button" class="btn btn-default hover-red w-50" id="clearGenerate">{{trans('action.clear')}}</button>
                                </div>
                            </div>

                            <div class="col-md-4" id="duePowerdiv">
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{trans('action.cron_start_date')}}</b> <span class="timeZoneAlert"></span> </label>
                                    <div class="input-group">
                                        <input type="text" id="flatpickr-datetime" name="start_time_duepower" class="form-control start_time_duepower" onchange="checkcron()" placeholder="@if(Config::get('app.locale') == 'de') dd.mm.YYYY @else  YYYY/mm/dd @endif">

                                        <div class="input-group-prepend">
                                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                        </div>
                                    </div>
                                    <span class="invalid-feedback allmsg" role="alert">
                                        <strong id="msg2"></strong>
                                    </span>
                                </div>
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{trans('action.cron_end_date')}}</b> </label>
                                    <select class="select2-demo form-control" name="end_time_duepower" id="end_time_duepower">
                                        <option value="">{{trans('action.select_time')}}</option>
                                        <option value="30" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 30) ? "selected" : ""}}>30 {{trans('action.waiting_minutes')}}</option>
                                        <option value="45" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 45) ? "selected" : ""}}>45 {{trans('action.waiting_minutes')}}</option>
                                        <option value="60" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 60) ? "selected" : ""}}>60 {{trans('action.waiting_minutes')}}</option>
                                        <option value="120" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 120) ? "selected" : ""}}>120 {{trans('action.waiting_minutes')}}</option>
                                        <option value="240" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 240) ? "selected" : ""}}>240 {{trans('action.waiting_minutes')}}</option>
                                    </select>
                                    <span class="invalid-feedback allmsg" role="alert">
                                        <strong id="msg3"></strong>
                                    </span>
                                </div>
                            </div>

                            <div class="col-md-4" id="normalDiv">
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{trans('action.cron_start_date')}}</b> <span class="timeZoneAlert"></span></label>
                                    <div class="input-group">
                                        <input type="text" id="flatpickr-datetime2" name="crondate" class="form-control" onchange="checkcron()" placeholder="@if(Config::get('app.locale') == 'de') dd.mm.YYYY @else  YYYY/mm/dd @endif">

                                        <div class="input-group-prepend">
                                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="form-group" style="width:100%" id="selectFreq">
                                        <label class="form-label mb-2"> <b>{{trans('action.frequency')}}</b> </label>
                                        <select class="custom-select w-100" name="cronfreq" id="frequencyDiv">
                                            <option value="1" @if($default->due_power == 0 && $cronsetup->frequency == 1){{'selected'}}@endif>1</option>
                                            <option value="2" {{!empty(old('cronfreq')) && old('cronfreq') == 2 ? "Selected" : ""}} @if($default->due_power == 0 && $cronsetup->frequency == 2){{'selected'}}@endif>2</option>
                                            <option value="3" {{!empty(old('cronfreq')) && old('cronfreq') == 3 ? "Selected" : ""}} @if($default->due_power == 0 && $cronsetup->frequency == 3){{'selected'}}@endif>3</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="d-flex col-md-12 pl-0 pr-0">
                                    <button type="button" class="btn btn-default hover-green w-50 mr-2" id="autoGenerate_dl">{{trans('action.auto_genrate')}}</button>
                                    <button type="button" class="btn btn-default hover-red w-50" id="clearGenerate_dl">{{trans('action.clear')}}</button>
                                </div>
                            </div>

                            <div class="col-md-4" id="normalDivtime">
                                <div class="row">
                                    <div class="form-group col-md-6" id="lengthdiv">
                                        <label class="form-label mb-2"> <b>{{trans('action.length')}}</b> </label>
                                        <input type="number" class="form-control m-0" id="cronlength" name="cronlength" value="@if(!empty(old('cronlength'))){{old('cronlength')}}@elseif($default->due_power == 0){{$cronsetup->length}}@endif">
                                    </div>
                                    <div class="form-group col-md-6" id="lengthdivday">
                                        <label class="form-label"> <b></b> </label>
                                        <select class="custom-select mt-2" name="cronday" id="cronday">
                                            <option value="">{{trans('action.day')}}</option>
                                            <option value="7"  @if($cronsetup->length == 7) {{"selected"}} @endif>7 {{trans('action.day')}}</option>
                                            <option value="14" @if($cronsetup->length == 14) {{"selected"}} @endif>14 {{trans('action.day')}}</option>
                                            @if(date('t') == 28)
                                                <option value="28" @if($cronsetup->length == 28) {{"selected"}} @endif>28 {{trans('action.day')}}</option>
                                            @elseif(date('t') == 29)
                                                <option value="29" @if($cronsetup->length == 29) {{"selected"}} @endif>29 {{trans('action.day')}}</option>
                                            @elseif(date('t') == 31)
                                                <option value="31" @if($cronsetup->length == 31) {{"selected"}} @endif>31 {{trans('action.day')}}</option>
                                            @else
                                                <option value="30" @if($cronsetup->length == 30) {{"selected"}} @endif>30 {{trans('action.day')}}</option>
                                            @endif
                                        </select>
                                    </div>

                                    <div class="form-group col-md-6" id="freq1" style="display: none">
                                        <label class="form-label mb-2"> <b>{{trans('action.second_start_time')}} </b> </label>
                                        <input type="text" class="form-control" id="flatpickr-time" name="time2">
                                    </div>
                                    <div class="form-group col-md-6" id="freq2" style="display: none">
                                        <label class="form-label mb-2"> <b>{{trans('action.third_start_time')}}</b> </label>
                                        <input type="text" class="form-control" id="flatpickr-time1" name="time3">
                                    </div>
                                    <div class="d-flex align-items-center col-md-12">
                                        <div class="form-group" style="width:100%" id="selectFreq">
                                            <label class="form-label lb-100 mb-2 col-md-12 pl-0" for="">{{ trans('action.timezone') }}</label>
                                            <select class="select2-demo form-control m-0 col-md-12" id="timezone_select">
                                                <option {{ isset($admin) && $admin->timezone_id == '' ? 'selected disabled' : '' }}>{{ trans('action.Select') }}</option>
                                                @foreach($timezones as $zone)
                                                    <option value="{{ $zone->timezone }}" {{ $zone->isSelected ? 'selected' : '' }}>{{ $zone->name }}</option>
                                                @endforeach
                                            </select>
                                            <input type="hidden" name="timezone_id" id="timezone_id" value="{{$admin->timezone_id}}">
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </div>


                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <h5 class="card-header">{{ trans('action.general_setting_header') }}</h5>
                                <div class="card-body">
                                    <h5 class="card-title">{{trans('action.calculation_system_dashboard')}}</h5>
                                    <div class="mb-3 d-flex align-items-center">
                                        <div class="right-side ml-3">
                                          <div class="d-flex align-items-center">
                                             <div class="flex-shrink-1 mr-2">{{trans('action.year')}}</div>
                                             <label class="switcher switcher-sm switcher-success m-0">
                                             <input type="checkbox" id="cal_type" name="cal_type" class="switcher-input" checked>
                                             <span class="switcher-indicator">
                                             <span class="switcher-yes"></span>
                                             <span class="switcher-no"></span>
                                             </span>
                                             </label>
                                             <div class="flex-shrink-1 text-success ml-2">{{trans('action.month')}}</div>
                                          </div>
                                       </div>
                                    </div>
                                    <h5 class="card-title">{{ trans('action.due_power_setting') }}</h5>
                                    <label class="custom-control custom-checkbox mb-3">
                                        <input type="checkbox" name="due_power" class="custom-control-input" id="cronDuePower" @if($default->due_power == 1){{ 'checked' }} @endif>
                                        <span class="custom-control-label">{{trans('action.due_power')}}</span>
                                    </label>
                                </div>
                              </div>
                              <br/>

                              <div class="card">
                                <h5 class="card-header">{{ trans('action.link_email_setting_header') }}</h5>
                                <div class="card-body">
                                  <h5 class="card-title">{{ trans('action.cron_customer_link') }}</h5>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="customer_link" class="custom-control-input"  @if($default->customer_link == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.enable_customer_link')}}</span>
                                </label>
                                <h5 class="card-title">{{ trans('action.link_email_setting_title') }}</h5>
                                <label class="custom-control custom-checkbox mb-3 email1x">
                                    <input type="checkbox" name="start_email" id="sm1x" class="custom-control-input"  @if($default->start_email == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.start_email_1x')}}</span>
                                </label>
                                <label class="custom-control custom-checkbox mb-3 email1x">
                                    <input type="checkbox" name="end_email" id="em1x" class="custom-control-input"  @if($default->end_email == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.end_email_1x')}}</span>
                                </label>
                                <label class="custom-control custom-checkbox mb-3 emaildue">
                                    <input type="checkbox" name="start_email_duepower" id="smd1x" class="custom-control-input"  @if($default->start_email_duepower == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.start_email_due_power')}}</span>
                                </label>
                                <label class="custom-control custom-checkbox mb-3 emaildue">
                                    <input type="checkbox" name="end_email_duepower" id="emd1x" class="custom-control-input"  @if($default->end_email_duepower == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.end_email_due_power')}}</span>
                                </label>
                                </div>
                              </div>
                              <br/>
                              <div class="card">
                                <h5 class="card-header">{{ trans('action.advanced_setting_header') }}</h5>
                                <div class="card-body">
                                  <h5 class="card-title">{{ trans('action.advanced_setting_title') }}</h5>
                                  <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="pdf_export" class="custom-control-input"  @if($default->pdf_export == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.enable_pdf_export')}}</span>
                                  </label>

                                <h5 class="card-title">{{ trans('action.add_cause_remedy_title') }}</h5>

                                <div class="row">
                                    <div class="col-md-2">
                                      <label class="custom-control custom-checkbox mb-3">
                                      <input type="checkbox" name="causes" class="custom-control-input" {{ (!empty(old('causes')) && old('causes') == 'on') ? "checked" : ""}} @if($default->causes == 1){{ 'checked' }} @endif>
                                      <span class="custom-control-label">{{trans('action.causes')}}</span>
                                      </label>
                                   </div>
                                   <div class="col-md-2">
                                      <label class="custom-control custom-checkbox mb-3">
                                      <input type="checkbox" name="medium" class="custom-control-input" {{ (!empty(old('medium')) && old('medium') == 'on') ? "checked" : ""}} @if($default->medium == 1){{ 'checked' }} @endif>
                                      <span class="custom-control-label">{{trans('action.medium')}}</span>
                                      </label>
                                   </div>
                                   <div class="col-md-2">
                                      <label class="custom-control custom-checkbox mb-3">
                                      <input type="checkbox" name="tipp" class="custom-control-input" {{ (!empty(old('tipp')) && old('tipp') == 'on') ? "checked" : ""}} @if($default->tipp == 1){{ 'checked' }} @endif>
                                      <span class="custom-control-label">{{trans('action.tipp')}}</span>
                                      </label>
                                   </div>
                                </div>
                                <h5 class="card-title">{{ trans('action.apply_the_cal_to_all') }}</h5>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="calculation_next_day" class="custom-control-input"  @if($default->calculation_day == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.calculation_next_day')}}</span>
                                </label>

                                <h5 class="card-title">{{ trans('action.no_fixed_cron_time_title') }}</h5>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="fixed_time" class="custom-control-input" {{ (!empty(old('fixed_time')) && old('fixed_time') == 'on') ? "checked" : ""}} @if($default->any_time_cron == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.no_fixed_cron_time')}}</span>
                                </label>
                                </div>
                              </div>
                              <br/>

                              <div class="card">
                                <h5 class="card-header">{{ trans('action.action_value_setting_header') }}</h5>
                                <div class="card-body">
                                  <h5 class="card-title">{{ trans('action.action_value_setting_title') }}</h5>
                                  <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="ra_status" class="custom-control-input" {{ (!empty(old('ra_status')) && old('ra_status') == 'on') ? "checked" : ""}} @if($default->ra_status){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.show_ra')}}</span>
                                  </label>
                                <h5 class="card-title">{{ trans('action.show_cron_pdf_ra_title') }}</h5>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="pdf_status" class="custom-control-input" {{ (!empty(old('pdf_show')) && old('pdf_status') == 'on') ? "checked" : ""}} @if($default->pdf_status){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{trans('action.show_cron_pdf_ra')}}</span>
                                </label>
                                </div>
                              </div>
                            {{-- <p class="mt-2"><strong>{{trans('action.cron_note')}}</strong></p> --}}
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                  {{ trans('action.cron_theme_header') }}
                                </div>
                                <div class="card-body">
                                  <h5 class="card-title">{{ trans('action.cron_theme_title') }}</h5>
                                    <div class="form-group">
                                       <label class="form-label mb-2"> <b>{{trans('action.cron_topic')}}</b> </label>
                                       <textarea id="topic" name="topic" class="form-control" rows="5"
                                          placeholder="{{trans('action.cron_note_placeholder')}}">{{ old('topic') ? old('topic') : $cronsetup->topic }}</textarea>
                                    </div>
                                </div>
                            </div>
                            <br/>
                            <div class="card">
                                <div class="card-header">
                                  {{ trans('action.cron_note_header') }}
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <h5 class="card-title">{{ trans('action.cron_note_title') }}</h5>
                                            <div class="form-group">
                                                <label class="form-label"> <b>{{trans('action.cron_note2')}}</b> </label>
                                                <textarea id="" name="note" class="form-control" rows="5"
                                                placeholder="{{trans('action.note_here')}}">@if(!empty(old('note'))) {{ old('note') }} @else {{$cronsetup->note}} @endif</textarea>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <h5 class="card-title">{{trans('action.cron_note_client_title')}}</h5>
                                            <div class="form-group">
                                                <label class="form-label"> <b>{{trans('action.cron_note2')}}</b> </label>
                                                <textarea id="" name="client_note" class="form-control" rows="5"
                                                placeholder="{{trans('action.note_here')}}">@if(!empty(old('client_note'))) {{ old('client_note') }} @else {{$cronsetup->client_note}} @endif</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if($cronsetup->cron_type == 1)
                            <input type="hidden" name="cron_type" value="{{$cronsetup->cron_type}}">
                            <input type="hidden" name="cron_cart" value="active">
                        @endif
                        <input type="hidden" name="duplicate_cronid" value="{{$cronsetup->id}}">
                        <input type="hidden" name="cron_duplicate" value="active">
                    </div>

                    <div class="d-flex justify-content-center bottom-Fixed" id="bottomFixed">
                        <button type="button" class="btn btn-secondary w-25 mr-1"  onclick="cronpreview()">{{trans('action.cron_preview')}}</button>
                        <button type="submit" class="btn btn-success w-25 fromSubmit" id="fromSubmit"><span>{{trans('action.save')}}</button>
                    </div>

                </form>
                </div>

                {{-- cron preview modal --}}
                <div class="modal modal-top fade" id="preview-modal">
                    <div class="modal-dialog">
                        <form class="modal-content" action="{{ route('cron.storeCronSetup') }}" id="storeCronPreview" method="POST">
                            @csrf
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    {{-- {{trans('action.preview_from')}} --}}
                                    <p>{{trans('action.preview_from')}} <b><span id="pre_name"></span></b> </p>
                                </h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button>
                            </div>

                            <input type="hidden" id="pre_val1" name="cronuser" value="">
                            <input type="hidden" id="pre_val2" name="cronsubmenu" value="">
                            <input type="hidden" id="pre_val3" name="start_time_duepower" value="">
                            <input type="hidden" id="pre_val4" name="end_time_duepower" value="">
                            <input type="hidden" id="pre_val5" name="crondate" value="">
                            <input type="hidden" id="pre_val6" name="time2" value="">
                            <input type="hidden" id="pre_val7" name="time3" value="">
                            <input type="hidden" id="pre_val8" name="cronlength" value="">
                            <input type="hidden" id="pre_val9" name="cronday" value="">
                            <input type="hidden" id="pre_val10" name="cronfreq" value="">
                            <input type="hidden" id="pre_val11" name="topic" value="">
                            <input type="hidden" id="pre_val12" name="note" value="">
                            <input type="hidden" id="pre_val13" name="client_note" value="">
                            <input type="hidden" id="pre_val15" name="timezone_id" value="">

                            <input type="hidden" name="preview" value="active">

                            <div class="modal-body">
                                <div class="modal_preloader">
                                    <div class="loading">
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                    </div>
                                </div>

                                <div class="loaded_data">
                                    {{-- <p>{{trans('action.preview_from')}} <b><span id="pre_name"></span></b> - {{trans('action.start')}}: <span id="pre_time"></span></p> --}}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h5 class="mb-3"> {{trans('action.selectet_subemnus')}}</h5>
                                            <p id="pre_sub" class="web-kit-scroll pAna" style="font-weight: bold"></p>
                                            <div class="mb-3 d-flex align-items-center">
                                                <div class="right-side">
                                                    <div class="d-flex align-items-center">
                                                        <div class="flex-shrink-1 mr-2">{{__('action.year')}}</div>
                                                        <label class="switcher switcher-sm switcher-success m-0">
                                                            <input type="checkbox" id="pre_cal_type" name="cal_type" class="switcher-input" checked>
                                                            <span class="switcher-indicator">
                                                                <span class="switcher-yes"></span>
                                                                <span class="switcher-no"></span>
                                                            </span>
                                                        </label>
                                                        <div class="flex-shrink-1 text-success ml-2">{{__('action.month')}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="due_power" id="pre_dp" value="">
                                                <span class="custom-control-label">{{trans('action.cron_due_power')}} ({{trans('action.cron_due_power1')}} 240 {{trans('action.cron_due_power2')}})</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="customer_link" id="pre_cl" value="">
                                                <span class="custom-control-label">{{trans('action.cron_customer_link')}}</span>
                                            </label>
                                            {{-- <label class="custom-control custom-checkbox mb-3"> --}}
                                                {{-- <input type="checkbox" class="custom-control-input" name="start_email" id="pre_se" value="">
                                                <span class="custom-control-label">{{trans('action.start_email_1x')}}</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="end_email" id="pre_ee" value="">
                                                <span class="custom-control-label">{{trans('action.end_email_1x')}}</span> --}}
                                            {{-- </label> --}}
                                            <label class="custom-control custom-checkbox mb-3 pre_smaildue">
                                                <input type="checkbox" name="start_email_duepower" id="pre_smd1x" class="custom-control-input"  @if($default->start_email_duepower == 1){{ 'checked' }} @endif>
                                                <span class="custom-control-label">{{trans('action.start_email_due_power')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3 pre_emaildue">
                                                <input type="checkbox" name="end_email_duepower" id="pre_emd1x" class="custom-control-input"  @if($default->end_email_duepower == 1){{ 'checked' }} @endif>
                                                <span class="custom-control-label">{{trans('action.end_email_due_power')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3 pre_semail">
                                                <input type="checkbox" class="custom-control-input" name="start_email" id="pre_se" value="">
                                                <span class="custom-control-label">{{trans('action.start_email_1x')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3 pre_email">
                                                <input type="checkbox" class="custom-control-input" name="end_email" id="pre_ee" value="">
                                                <span class="custom-control-label">{{trans('action.end_email_1x')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="pdf_export" id="pre_pdf" value="">
                                                <span class="custom-control-label">{{trans('action.cron_export')}}</span>
                                            </label>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <label class="custom-control custom-checkbox mb-3">
                                                        <input type="checkbox" name="causes" id="pre_cau" class="custom-control-input">
                                                        <span class="custom-control-label">{{trans('action.causes')}}</span>
                                                    </label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <label class="custom-control custom-checkbox mb-3">
                                                        <input type="checkbox" name="medium" id="pre_mid" class="custom-control-input">
                                                        <span class="custom-control-label">{{trans('action.medium')}}</span>
                                                    </label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <label class="custom-control custom-checkbox mb-3">
                                                        <input type="checkbox" name="tipp" id="pre_tip" class="custom-control-input">
                                                        <span class="custom-control-label">{{trans('action.tipp')}}</span>
                                                    </label>
                                                </div>
                                            </div>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="calculation_next_day" id="pre_cnd" value="">
                                                <span class="custom-control-label">{{trans('action.calculation_next_day')}}</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" name="fixed_time" class="custom-control-input" id="pre_atc" value="">
                                                <span class="custom-control-label">{{trans('action.no_fixed_cron_time')}}</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" name="ra_status" id="pre_ra_status" class="custom-control-input">
                                                <span class="custom-control-label">{{trans('action.show_ra')}}</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" name="pdf_status" id="pre_pdf_status" class="custom-control-input">
                                                <span class="custom-control-label">{{trans('action.show_cron_pdf_ra')}}</span>
                                            </label>

                                            <p>{{trans('action.topic')}}: <span id="pre_topic">{{trans('action.no_topic_added')}}</span></p>
                                            <p>{{__('action.external_note')}}: <span id="pre_client_note">{{__('action.no_note_added')}}</span></p>
                                            <p>{{__('action.internal_note')}}: <span id="pre_note">{{__('action.no_note_added')}}</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h5 class="mb-3">{{trans('action.start_time_cron_view')}}: <span id="pre_stime"></span></h5>
                                            <div class="media align-items-center mb-3">
                                                <img id="pre_img" src="" class="d-block ui-w-80">
                                            </div>
                                            <div>
                                                <p> <strong>{{trans('action.cron_user')}}: <span id="pre_user"></span> </strong> </p>
                                                <p> <strong>{{trans('action.cron_birthday')}}:</strong> <span id="pre_dob"></span></p>
                                                <p> <strong>{{trans('action.cron_village')}}:</strong> <span id="pre_pob"></span> </p>
                                                <div class="form-group form-inline">
                                                    <label for="" class="mr-3">{{trans('action.cron_email')}}:</label>
                                                    <input type="email" name="usermail" class="form-control" id="pre_email" value="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="my-2" id="pre_title"><strong>{{trans('action.analysis_data')}}</strong></p>
                                    <p id="pre_analyses"></p>

                                    <input type="hidden" value="" id="causesid" name="causesid">
                                    <input type="hidden" value="" id="mediumid" name="mediumid">
                                    <input type="hidden" value="" id="tippid" name="tippid">
                                    <input type="hidden" value="" id="pre_status" name="preview_status">

                                    @if($cronsetup->cron_type == 1)
                                        <input type="hidden" name="cron_type" value="{{$cronsetup->cron_type}}">
                                        <input type="hidden" name="cron_cart" value="active">
                                    @endif
                                    <input type="hidden" name="duplicate_cronid" value="{{$cronsetup->id}}">
                                    <input type="hidden" name="cron_duplicate" value="active">

                                    <div class="mt-4 d-flex flex-column justify-content-end justify-content-sm-center flex-sm-row align-items-sm-center">
                                        <!-- <button type="button" class="btn btn-danger w-32">{{trans('action.clear')}}</button> -->
                                        {{--<button type="submit" name="draft" value="savedraft" class="btn btn-default w-32">{{trans('action.save_draft')}}</button>--}}
                                        <button type="submit" class="btn btn-success w-32" id="preview_save">{{trans('action.save')}}</button>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">{{trans('action.close')}}</button>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- </div> -->
            </div>
        </div>
    </div>
</section>

@endsection


@section('scripts')


    <!-- Dependencies -->
    <script src="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('/js/cron-validation.js') }}"></script>

    <script>        
        // Initialize validation messages
        CronValidation.setViewType('duplicate');
        CronValidation.setCronType('{{ $cronsetup->cron_type }}');
        CronValidation.setMessages({
            userError: '{{__('action.cron_users_error')}}',
            submenuError: '{{__('action.cron_submenu_error')}}',
            analysesError: '{{__('action.cron_analyses_error')}}',
            startError: '{{__('action.cron_start_error')}}',
            endDateError: '{{__('action.cron_end_date_error')}}',
            frequencyError: '{{__('action.frequency')}}',
            secondStartTimeError: '{{__('action.second_start_time')}}',
            thirdStartTimeError: '{{__('action.third_start_time')}}',
            lengthError: '{{__('action.select_length')}}',
            lengthNotError: '{{__('action.add_length_not')}}'
        });

        // Select2
        $(function() {
            $('.select2-demo').each(function() {
                $(this)
                .wrap('<div class="position-relative"></div>')
                .select2({
                    placeholder: `{{trans('action.select_value')}}`,
                    dropdownParent: $(this).parent()
                });
            })
        });
        var __getReqClose;
        $('#cronsubcheck').on('shown.bs.select', function (e, clickedIndex, isSelected, oldValue) {
            $(document).find(".dropdown-menu li.dropdown-header").attr('onclick','selectBellow(event)')
            $(document).find(".dropdown-menu li.selected").each(function(){
                if($('.'+$(this).find('a').attr('class').split(' ')[1]).hasClass('dropdown-header')) $('.dropdown-header.'+$(this).find('a').attr('class').split(' ')[1]).addClass('selected text-success')
            })
        })

        let addMoreFifteen = true;
        $('#cronsubcheck').on('change', function (e, clickedIndex, isSelected, oldValue) {
            if($(this).find(':selected').length === 15 && addMoreFifteen){
                Swal.fire({
                    title: `{{__('action.alert_max_submenu_hgeading')}}`,
                    text: `{{__('action.alert_max_submenu_details')}}`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#FFB505',
                    cancelButtonColor: '#d33',
                    cancelButtonText:  `{{__('action.cancel')}}`,
                    confirmButtonText:  `{{__('action.confirm')}}`,
                }).then((result) => {
                    if (result.value) {
                        addMoreFifteen = false
                    }
                });
            }
            
        })
        function selectBellow(e){            
            let classes = ''
            if(!$(e.target).hasClass('dropdown-header')) classes = $(e.target).parent().attr('class')
            else classes = $(e.target).attr('class')

            let className = classes.split(' ')[2]
            let selProId = classes.split(' ')[1].split('selectAll')[1]

            if($('.'+classes.split(' ')[2]).hasClass('text-success')){
                $('option[data-pro="'+selProId+'"]').prop('selected',false).trigger('change')
                $('.'+className).removeClass('text-success').removeClass('selected').find('a').removeClass('selected').attr('aria-selected',false)
            }else{
                if(($("#cronsubcheck option:selected").length + ($('.'+classes.split(' ')[2]).length -1)) > 15 && addMoreFifteen){
                    Swal.fire({
                        title: `{{__('action.alert_max_submenu_hgeading')}}`,
                        text: `{{__('action.alert_max_submenu_details')}}`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#FFB505',
                        cancelButtonColor: '#d33',
                        cancelButtonText:  `{{__('action.cancel')}}`,
                        confirmButtonText:  `{{__('action.confirm')}}`,
                    }).then((result) => {
                        if (result.value) {
                            addMoreFifteen = false
                            $('option[data-pro="'+selProId+'"]').prop('selected',true).trigger('change')
                            $('.'+classes.split(' ')[2]).addClass('text-success').addClass('selected').find('a').addClass('selected').attr('aria-selected',true)
                            $('.selectpicker').selectpicker('refresh');
                            toastr.success(`{{__('action.confirm')}}`);
                        }
                    });
                }else{
                    $('option[data-pro="'+selProId+'"]').prop('selected',true).trigger('change')
                    $('.'+classes.split(' ')[2]).addClass('text-success').addClass('selected').find('a').addClass('selected').attr('aria-selected',true)
                }
            }
        }
        
        var userid = 0;
        $(document).ready(function(){
            let initialTimezone = $('#cronusercheck').children("option:selected").data('timezoneid');
            if (initialTimezone) {
                $("#timezone_id").val(initialTimezone);
            }
            getExicutableTime($('#timezone_select option:selected').val());

            var status = "ok";
            var txt = "This field is required";

            $("#cronusercheck").on('change', function(){
                $(".timeZoneAlert").text("");
                var select = $(this).find('option:selected');
                userid = select.val();
                var sTimezone = $(this).children("option:selected").data('timezoneid');
                $("#timezone_select").val(sTimezone).trigger('change');
                $("#timezone_id").val(sTimezone);
                getExicutableTime($('#timezone_select option:selected').val());
            });

            $("#timezone_select").change(function(){
                getExicutableTime($(this).val());
                $("#timezone_id").val($(this).val());
            });

            let checkduePower = $('#cronDuePower').is(":checked");
            updateDuePowerVisibility(checkduePower);

            $("#cronDuePower").on('change', function(){
                checkduePower = $(this).is(":checked");
                updateDuePowerVisibility(checkduePower);
            });

            // Helper function to update UI based on Due Power status
            function updateDuePowerVisibility(checkduePower) {
                if(!checkduePower) {
                    $('.start_time_duepower').prop('disabled', true);
                    $('.crondate').prop('disabled', false);
                    $("#duePowerdiv").addClass("duePowerdivs");
                    $("#normalDiv").attr("style", "display: block");
                    $("#lengthdiv").removeClass("freqtime");
                    $("#lengthdivday").removeClass("freqtime");
                    $(".semaildue").addClass("duePowerdivs");
                    $(".emaildue").addClass("duePowerdivs");
                    $(".semail1x").show();
                    $(".email1x").show();
                    if($('#frequencyDiv').val() == 2){
                        $("#freq1").show();
                    }else if($('#frequencyDiv').val() == 3){
                        $("#freq1").show();
                        $("#freq2").show();
                    }
                    $("#flatpickr-time").val('');
                    $("#flatpickr-time1").val('');
                } else {
                    $('.crondate').prop('disabled', true);
                    $('.start_time_duepower').prop('disabled', false);
                    $("#duePowerdiv").removeClass("duePowerdivs");
                    $("#normalDiv").attr("style", "display: none");
                    $("#lengthdiv").addClass("freqtime");
                    $("#lengthdivday").addClass("freqtime");
                    $("#freq1").hide();
                    $("#freq2").hide();
                    $(".semaildue").removeClass("duePowerdivs");
                    $(".emaildue").removeClass("duePowerdivs");
                    $(".semail1x").hide();
                    $(".email1x").hide();
                    $("#flatpickr-time").val('');
                    $("#flatpickr-time1").val('');
                }
            }

            // Helper function to handle visibility logic
           function updateFrequencyVisibility(freq) {
               if (freq == 2) {
                   $("#freq1").show();
                   $("#freq2").hide();
                   $("#flatpickr-time1").val('');
                   if(!!$("#flatpickr-time").val()){
                       checkcron();
                   }
                } else if (freq == 3) {
                    $("#freq1").show();
                    $("#freq2").show();
                   if(!!$("#flatpickr-time").val() && !!$("#flatpickr-time1").val()){
                       checkcron();
                   }
                } else if (freq == 1) {
                    $('#bottomFixed button').prop('disabled', false);
                    $("#flatpickr-time").val('');
                    $("#flatpickr-time1").val('');
                    $("#freq1").hide();
                    $("#freq2").hide();
                }
            }

            // Initial check on page load
            const initialFreq = $('#frequencyDiv').val();
            updateFrequencyVisibility(initialFreq);

            // Update visibility on change event
            $("#frequencyDiv").on('change', function () {
                const freq = $(this).val();
                updateFrequencyVisibility(freq);
            });


            $("#fromSubmit").click(function(event) {
                const validation = CronValidation.validate();
                if (!validation.isValid) {
                    event.preventDefault();
                    showValidationError(validation.message);
                    return false;
                }
                $(".save_prelodaer").show();
            });

            $("#cronday").on('change', function(){
                var cday = $(this).find('option:selected').val();
                $("#cronlength").val(cday);
            })

            $('#flatpickr-datetime').removeAttr('disabled');
        })

        function cronpreview() {
            const validation = CronValidation.validate();
            if (!validation.isValid) {
                showValidationError(validation.message);
                return;
            }

            $('.modal_preloader').show();
            $('.loaded_data').hide();

            $("#preview-modal").modal("show");
            var postdata = $('#crondata').serialize();

            $.ajax({
                type: "GET",
                cache: false,
                url: "{{ route('cron.cronPreview') }}",
                data: postdata,
                async: false,
                datatype: "json",
                success: function(res) {
                    (res.response.due_power) ? $("#pre_dp").attr("checked", true).val(1) : $("#pre_dp").attr("checked", false);
                    (res.response.customer_link) ? $("#pre_cl").attr("checked", true).val(1) : $("#pre_cl").attr("checked", false);
                    (res.response.cal_type) ? $("#pre_cal_type").attr("checked", true).val(1) : $("#pre_cal_type").attr("checked", false);
                    
                    if(res.response.due_power){
                        $('.pre_smaildue').show();
                        $('.pre_emaildue').show();

                        if (!$(".pre_semail").hasClass("hide")) {
                            $('.pre_semail').hide();
                        }
                        if (!$(".pre_email").hasClass("hide")) {
                            $('.pre_email').hide();
                        }
                        (res.response.start_email_duepower) ? $("#pre_smd1x").prop("checked", true).val(1) : $("#pre_smd1x").prop("checked", false);
                        (res.response.end_email_duepower) ? $("#pre_emd1x").prop("checked", true).val(1) : $("#pre_emd1x").prop("checked", false);
                    }else{
                        $('.pre_semail').show();
                        $('.pre_email').show();
                        if (!$(".pre_smaildue").hasClass("hide")) {
                            $('.pre_smaildue').hide();
                        }
                        if (!$(".pre_emaildue").hasClass("hide")) {
                            $('.pre_emaildue').hide();
                        }
                        (res.response.start_email) ? $("#pre_se").prop("checked", true).val(1) : $("#pre_se").prop("checked", false);
                        (res.response.end_email) ? $("#pre_ee").prop("checked", true).val(1) : $("#pre_ee").prop("checked", false);
                    }

                    (res.response.pdf_export) ? $("#pre_pdf").attr("checked", true).val(1) : $("#pre_pdf").attr("checked", false);
                    (res.response.calculation_next_day) ? $("#pre_cnd").attr("checked", true).val(1) : $("#pre_cnd").attr("checked", false);
                    (res.response.causes) ? $("#pre_cau").attr("checked", true).val(1) : $("#pre_cau").attr("checked", false);
                    (res.response.medium) ? $("#pre_mid").attr("checked", true).val(1) : $("#pre_mid").attr("checked", false);
                    (res.response.tipp) ? $("#pre_tip").attr("checked", true).val(1) : $("#pre_tip").attr("checked", false);
                    (res.response.fixed_time) ? $("#pre_atc").attr("checked", true).val(1) : $("#pre_atc").attr("checked", false);
                    (res.response.ra_status) ? $("#pre_ra_status").attr("checked", true).val(1) : $("#pre_ra_status").attr("checked", false);
                    (res.response.topic == null) ? "" : $("#pre_topic").html(res.response.topic);
                    (res.response.client_note == null) ? "" : $("#pre_client_note").html(res.response.client_note);
                    (res.response.note == null) ? "" : $("#pre_note").html(res.response.note);

                    let name = res.ures.first_name + " " + res.ures.last_name;
                    $("#pre_name").html(name);
                    $("#pre_time").html(res.others.start_time);
                    $("#pre_user").html(name);
                    $("#pre_dob").html(res.others.dob);
                    $("#pre_pob").html(res.ures.gebort);
                    $("#pre_email").val(res.ures.cron_email);
                    $("#pre_stime").html(res.others.start_time);
                    $("#pre_status").val(res.others.preview);
                    $("#pre_img").attr('src', res.predata.img_src);

                    let preCnt = 0;
                    $.each(res.predata, function(key, value) {
                        let index = preCnt + 1;
                        $("#pre_val"+index).val(value);
                        preCnt++;
                    });

                    let analyses = ""; let analysis = "";
                    $.each(res.anaPreview, function(key, value) {
                        analyses += value.sub_name + "<br>";
                        if(res.others.preview != 1){
                            analysis += "<strong>" + value.sub_name + "</strong><br>";
                            $.each(value.analyses, function(keys, values) {
                                result = display(values.price);
                                if(values.type == 6) {
                                    analysis += result +" - "+ values.ana_name+ "<br>";
                                } else {
                                    analysis += result +" - "+values.ana_val+"% "+ values.ana_name+ "<br>";
                                }
                                if(values.type == 0) {
                                    (values.causes != null && res.response.causes) ? analysis += "{{trans('action.causes')}} - " + values.causes + "<br/>" : "";
                                    (values.medium != null && res.response.medium) ? analysis += "{{trans('action.medium')}} - " + values.medium + "<br/>" : "";
                                    (values.tipp != null && res.response.tipp) ? analysis += "{{trans('action.tipp')}} - " + values.tipp + "<br/>" : "";
                                }
                            });
                        }
                    });

                    (res.others.preview == 0) ? $("#pre_analyses").html(analysis) : $("#pre_title").attr("style", "display: none");
                    $("#pre_sub").html(analyses);
                    
                    $('.modal_preloader').hide();
                    $('.loaded_data').show();
                }
            });
        }

        $(document).on('click', "#autoGenerate", function() {
            var userid = "{{ getUserId() }}";
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "{{ route('Aajax.autoGenerate') }}",
                data: {userid: userid},
                success: function(res) {

                    var randomNum = Math.floor(Math.random() * 6) + 3;

                    for(var i=1; i<=randomNum; i++)
                    {
                        var randomSubId = res.subid[Math.floor(Math.random() * res.subid.length)];
                        $("#subid_"+randomSubId).attr('selected', 'selected');
                        $('.selectpicker').selectpicker('refresh');
                    }
                }
            });
        });

        $(document).on('click', "#clearGenerate", function() {

            $('#cronsubcheck option:selected').removeAttr('selected');
            $('.selectpicker').selectpicker('refresh');
        });


        function display (seconds) {

            const format = val => `0${Math.floor(val)}`.slice(-2)
            const hours = seconds / 3600
            const minutes = (seconds % 3600) / 60

            return [minutes, seconds % 60].map(format).join(':')
        }

        function error(mesasge1,mesasge2){
            Swal.fire({
            title: mesasge1,
            text: mesasge2,
            icon: 'warning',
            showCancelButton: false,
            confirmButtonColor: '#FFB505',
            cancelButtonColor: '#d33',
            cancelButtonText:  '',
            confirmButtonText: 'ok',
            });
        }

        $( "#preview_save" ).click(function() {
            $( ".modal_preloader" ).show();
            $( ".loaded_data" ).fadeOut();
        });

        $(function ($) {

            var today = new Date();
            var local_lang = "{{ Config::get('app.locale') }}";
            var datef = (local_lang === 'de') ? "d.m.Y H:i" : "Y/m/d H:i";

            today.setHours(0,0,0,0);

            $("#flatpickr-datetime").flatpickr({
                enableTime: true,
                time_24hr: true,
                dateFormat: datef,
                minDate: new Date()
            });

            $("#flatpickr-datetime1").flatpickr({
                enableTime: true,
                time_24hr: true,
                dateFormat: datef,
                minDate: new Date()
            });
            $("#flatpickr-datetime2").flatpickr({
                enableTime: true,
                time_24hr: true,
                dateFormat: datef,
                minDate: new Date()
            });
            $("#flatpickr-time").flatpickr({
                enableTime: true,
                noCalendar: true,
                time_24hr: true,
                dateFormat: "H:i",
                onClose: function(selectedDates, dateStr, instance) {
                    checkcron();
                }
            });
            $("#flatpickr-time1").flatpickr({
                enableTime: true,
                noCalendar: true,
                time_24hr: true,
                dateFormat: "H:i",
                onClose: function(selectedDates, dateStr, instance) {
                    checkcron();
                }
            });

        
            $('#timezone_select').change(function(){
                getExicutableTime($(this).val())
                $("#timezone_id").val($(this).val());
            })
            
        
        });

        function getExicutableTime(timezone){
            if(__getReqClose) __getReqClose.abort()
            if(!timezone || !timezone.length) return $('.flatpickr-input').not($('[name=time2]')).prop('disabled', true).val('')
            let url = "{{ route('cron.get-date-time') }}"
            __getReqClose = $.get(url,{ timezone: timezone }, function (resp) {                
                $(".timeZoneAlert").text(resp.timeAlert)
                $("#flatpickr-datetime, #flatpickr-datetime1, #flatpickr-datetime2").val(''); // Clear the input field's value
                flatpickr("#flatpickr-datetime, #flatpickr-datetime1, #flatpickr-datetime2", {
                    enableTime: true,
                    dateFormat: resp.format,
                    time_24hr: true,
                    defaultDate: resp.setDefault,
                    minDate: resp.minDate
                });
                
            })
        
        }

        function checkcron(e) {
            var duepow_dt = $("#flatpickr-datetime").val();
            var single_dt = $("#flatpickr-datetime2").val();
            var time2 = $("#flatpickr-time").val();
            var time3 = $("#flatpickr-time1").val();
            var id = $("#cronusercheck").find(":selected").val();
            var user = (userid == 0) ? id : userid;
            var checkduePower = $('#cronDuePower').is(":checked");

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "{{ route('Aajax.check_cron') }}",
                data: {userid: user, duepow: duepow_dt, single: single_dt, time2: time2, time3: time3, cdp: checkduePower},
                success: function(res) {
                    if(res.status == true){
                        toastr.error(res.message);
                        if (e && e.target) {
                            $(e.target).addClass('from-input-error');
                        }
                        $('#bottomFixed button').prop('disabled', true);
                    } else {
                        if (e && e.target) {
                            $(e.target).removeClass('from-input-error');
                        }
                        $('#bottomFixed button').prop('disabled', false);
                    }
                }
            });
        }

        $(document).on('click', "#autoGenerate_dl", function() {

            var length    = Math.floor(Math.random() * 30) + 1;
            var frequency = Math.floor(Math.random() * 3) + 1;

            $("#frequencyDiv").find('option:selected').prop("selected", false);
            $("#selectFreq select").val(frequency);
            $("#cronlength").val(length);

            if(frequency == 2) {
                $("#freq1").attr("style", "display : block");
                $("#freq2").attr("style", "display : none");
            }
            else if(frequency == 3) {
                $("#freq1").attr("style", "display : block");
                $("#freq2").attr("style", "display : block");
            }
            else if(frequency == 1) {
                $("#freq1").attr("style", "display : none");
                $("#freq2").attr("style", "display : none");
            }
        });

        $(document).on('click', "#clearGenerate_dl", function() {

            $('#cronlength').val('');
            $("#flatpickr-time").val("");
            $("#flatpickr-time1").val("");
            $("#freq1").hide();
            $("#freq2").hide();
            $("#frequencyDiv").find('option:selected').prop("selected", false);
            $("#selectFreq select").val(1);
        });

        // Save and restore submenu selections
        $(document).ready(function() {
            // For regular form submission
            $("#crondata").on("submit", function() {
                // Save submenu selections to session via AJAX
                var submenuValues = [];
                
                // Check if we're using cronsubmenu[] (multiple select) or cronanalyses[] (remote analysis)
                if ($("#cronsubcheck").length) {
                    submenuValues = $("#cronsubcheck").val() || [];
                } else if ($("select[name='cronanalyses[]']").length) {
                    submenuValues = $("select[name='cronanalyses[]']").val() || [];
                }
                
                if (submenuValues.length > 0) {
                    $.ajax({
                        url: "{{ route('cron.submenu.save') }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            selections: submenuValues
                        },
                        success: function(response) {
                            console.log("Submenu selections saved to session");
                        },
                        error: function(xhr, status, error) {
                            console.error("Error saving submenu selections:", error);
                        }
                    });
                }
            });
            
            // For preview form submission
            $("#storeCronPreview").on("submit", function() {
                var submenuValues = [];
                
                if ($("#cronsubcheck").length) {
                    submenuValues = $("#cronsubcheck").val() || [];
                } else if ($("select[name='cronanalyses[]']").length) {
                    submenuValues = $("select[name='cronanalyses[]']").val() || [];
                }
                
                if (submenuValues.length > 0) {
                    $.ajax({
                        url: "{{ route('cron.submenu.save') }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            selections: submenuValues
                        },
                        success: function(response) {
                            console.log("Submenu selections saved to session");
                        },
                        error: function(xhr, status, error) {
                            console.error("Error saving submenu selections:", error);
                        }
                    });
                }
            });
            
            // Restore selections from session
            $.ajax({
                url: "{{ route('cron.submenu.get') }}",
                type: "GET",
                success: function(response) {
                    if (response.success && response.selections && response.selections.length > 0) {
                        var selectionArray = response.selections;
                        
                        // Different handling based on which select we're using
                        if ($("#cronsubcheck").length) {
                            // For multiple select dropdown
                            $("#cronsubcheck").val(selectionArray).selectpicker('refresh');
                        } else if ($("select[name='cronanalyses[]']").length) {
                            // For cronanalyses select (remote analysis)
                            $("select[name='cronanalyses[]']").val(selectionArray);
                        }
                        
                        // Clear session after restoration to avoid unwanted restorations
                        $.ajax({
                            url: "{{ route('cron.submenu.clear') }}",
                            type: "POST",
                            data: {
                                _token: "{{ csrf_token() }}"
                            }
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error retrieving submenu selections:", error);
                }
            });
        });
    </script>

@endsection
