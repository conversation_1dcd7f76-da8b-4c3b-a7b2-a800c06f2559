@extends('Frontend.partials.layout')

@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/select2/select2.css') }}">
    <style>
        .corn-setting-tab-contents{
            gap: 30px;
        }

        div#v-pills-tab.corn-setting-tab-sidebar{
            width: 378px;
            /* max-height: 267px; */
            overflow: hidden;
        }

        div#v-pills-tabContent.corn-setting-tab-content {
            width: calc(100% - 408px);
            padding: 15px;
        }

        div#v-pills-tab.corn-setting-tab-sidebar,
        div#v-pills-tabContent.corn-setting-tab-content {
            background-color: #fff;
            box-shadow: 0 1px 4px rgb(24 28 33 / 1%);
            border: 1px solid rgba(24, 28, 33, 0.06);
            border-radius: 6px;
        }

        div#v-pills-tab.corn-setting-tab-sidebar a.nav-link {
            transition: .4s ease;
            margin-right: 0px;
            color: dimgray;
            padding: 15px 25px;
            border-radius: 0px;
        }

        div#v-pills-tab.corn-setting-tab-sidebar a.nav-link:not(:last-child) {
            border-bottom: 1px solid #cdcdcda8;
        }

        div#v-pills-tab.corn-setting-tab-sidebar a.nav-link.active {
            font-weight: 600;
            background-color: #ebebeb;
            color: dimgray;
        }

        div#v-pills-tab.corn-setting-tab-sidebar a.nav-link:hover {
            background-color: #ebebeb;
            font-weight: 600;
            color: dimgray;
        }

        /* horizontal tabs */
        .lang-contents-container ul#lang-items-wrapper {
            box-shadow: 0 1px 4px rgb(24 28 33 / 9%);
            max-width: fit-content;
            border: 1px solid #cdcdcda8;
            border-radius: 6px;
            margin: 0px auto;
            padding: 0px;
        }

        .lang-contents-container ul#lang-items-wrapper li.nav-item a.nav-link{
            transition: .4s ease;
            margin: 0px;
            padding: 10px 20px;
            border-radius: 0px;
            border: none;
            min-width: 90px;
            text-align: center;
            cursor: pointer;
        }

        .lang-contents-container ul#lang-items-wrapper li.nav-item:first-child a.nav-link {
            border-radius: 6px 0px 0px 6px;
        }
        
        .lang-contents-container ul#lang-items-wrapper li.nav-item:last-child a.nav-link {
            border-radius: 0px 6px 6px 0px;
        }

        .lang-contents-container ul#lang-items-wrapper li.nav-item:not(:last-child) a.nav-link {
            border-right: 1px solid #cdcdcda8;
        }
        
        .lang-contents-container ul#lang-items-wrapper li.nav-item a.nav-link.active {
            background-color: #ebebeb;
            font-weight: 600;
        }
        
        .lang-contents-container ul#lang-items-wrapper li.nav-item .nav-link {
            color: dimgray;
        }
        #v-pills-tabContent.corn-setting-tab-content .card-footer button {
            width: auto !important;
        }

        @media only screen and (max-width: 767px) {
            .from-to-container{
                gap: 1rem;
            }
            
            .special-setting-from{
                padding-right: 0;
            }

            .special-setting-to{
                padding-left: 0;
            }
        }
    </style>
@endsection
@section('content')
@php

@endphp
<section class="corn-setting">
    <div class="row">
        <div class="col-md-6">
            <div class="analysis-content-header d-flex align-items-center">
                <div class="logo">
                    <i class="fas fa-home primary-color"></i>
                </div>
                <div class="heading primary-color">
                    <h2>{{ trans('action.cron_setting')}}</h2>
                    <p>{{  trans('action.page_heading2_cron_setting') }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <input type="hidden" name="userid" value="{{ getUserId() }}">
        <div class="row mx-0 corn-setting-tab-contents col-md-12">
            <div class="nav flex-column nav-pills corn-setting-tab-sidebar" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                <a class="nav-link active" id="v-pills-cron-default-setting-tab" data-toggle="pill" href="#v-pills-cron-default-setting" role="tab" aria-controls="v-pills-cron-default-setting" aria-selected="true">
                    <i class="fas fa-users-cog ">&nbsp;</i>{{ trans('action.default_setting_header') }}
                </a>
                <a class="nav-link" id="v-pills-smtp-setting-tab" data-toggle="pill" href="#v-pills-smtp-setting" role="tab" aria-controls="v-pills-smtp-setting" aria-selected="true">
                    <i class="fas fa-marker fa-fw"></i>{{ trans('action.smtp_setting_title') }}
                </a>
                <a class="nav-link" id="v-pills-start-email-tab" data-toggle="pill" href="#v-pills-start-email" role="tab" aria-controls="v-pills-start-email" aria-selected="true">
                    <i class="fa fa-envelope fa-fw"></i>{{ trans('action.start_email_template_remote_handle_header') }}
                </a>
                <a class="nav-link" id="v-pills-end-email-tab" data-toggle="pill" href="#v-pills-end-email" role="tab" aria-controls="v-pills-end-email" aria-selected="false">
                    <i class="fa fa-envelope fa-fw"></i>{{ trans('action.end_email_template_remote_handle_header') }}
                </a>
                <a class="nav-link" id="v-pills-red-day-email-tab" data-toggle="pill" href="#v-pills-red-day-email" role="tab" aria-controls="v-pills-red-day-email" aria-selected="false">
                    <i class="fa fa-envelope fa-fw"></i>{{ trans('action.red_day_email_template_remote_handle_header') }}
                </a>
                <a class="nav-link" id="v-pills-green-day-email-tab" data-toggle="pill" href="#v-pills-green-day-email" role="tab"
                    aria-controls="v-pills-green-day-email" aria-selected="false">
                    <i class="fa fa-envelope fa-fw"></i>{{ trans('action.green_day_email_template_remote_handle_header') }}
                </a>
                <a class="nav-link" id="v-pills-resend-email-tab" data-toggle="pill" href="#v-pills-resend-email" role="tab" aria-controls="v-pills-resend-email" aria-selected="false">
                    <i class="fa fa-envelope fa-fw"></i>{{ trans('action.resend_email_template') }}
                </a>
                <a class="nav-link" id="v-pills-footer-tab" data-toggle="pill" href="#v-pills-footer" role="tab" aria-controls="v-pills-footer" aria-selected="false">
                    <i class="fa fa-envelope fa-fw"></i>{{ trans('action.global_footer_message') }}
                </a>
            </div>
            
            <div class="tab-content corn-setting-tab-content" id="v-pills-tabContent">
                <div class="tab-pane fade show active" id="v-pills-cron-default-setting" role="tabpanel" aria-labelledby="v-pills-start-email">
                    <div class="card">
                        <form action="{{ route('cron.storeCronSetting') }}" autocomplete="off" method="POST">
                            @csrf
                            <h5 class="card-header font-weight-bold m-0"><i class="fas fa-users-cog fa-fw"></i>&nbsp;{{ trans('action.default_setting_header') }}</h5>
                            <div class="card-body">
                                <input type="hidden" name="userid" value="{{ getUserId() }}">
                                <h5 class="card-title">{{  trans('action.default_setting_title') }}</h5>
                                <div class="card">
                                    <h5 class="card-header">{{  trans('action.link_email_setting_header') }}</h5>
                                    <div class="card-body">
                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="customerlink" class="custom-control-input" @if($default->customer_link == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.enable_customer_link')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3" @if(!$default->due_power) style="display:none;" @endif>
                                            <input type="checkbox" name="start_email_due_power"  class="custom-control-input due-email" @if($default->start_email_due_power == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.start_email_due_power')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3" @if(!$default->due_power) style="display:none;" @endif>
                                            <input type="checkbox" name="end_email_due_power"  class="custom-control-input due-email" @if($default->end_email_due_power == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.end_email_due_power')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3" @if($default->due_power) style="display:none;" @endif>
                                            <input type="checkbox" name="start_email_1x"  class="custom-control-input x-email" @if($default->start_email == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.start_email_1x')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3" @if($default->due_power) style="display:none;" @endif>
                                            <input type="checkbox" name="end_email_1x"  class="custom-control-input x-email" @if($default->end_email == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.end_email_1x')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="end_email_therapist" class="custom-control-input" @if($default->end_email_therapist == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.end_email_the_therapist')}}</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="card mt-2">
                                    <h5 class="card-header">{{  trans('action.advanced_setting_header') }}</h5>
                                    <div class="card-body">
                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="due_power" id="duePowerid" class="custom-control-input" @if($default->due_power == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.due_power_setting')}}</span>
                                        </label>

                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="pdfexport" class="custom-control-input" @if($default->pdf_export == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.enable_pdf_export')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="calculation_next_day" class="custom-control-input" @if($default->calculation_next_day == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.calculation_next_day')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="fixed_time" @if($default->fixed_time == 1){{ 'checked' }} @endif class="custom-control-input">
                                            <span class="custom-control-label">{{ trans('action.no_fixed_cron_time')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="list_status" class="custom-control-input" @if($default->preview_status == 1){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.calculation_preview_list')}}</span>
                                        </label>
                                        <div class="row">
                                            <div class="col-md-2">
                                            <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="causes" class="custom-control-input" {{ (!empty(old('causes')) && old('causes') == 'on') ? "checked" : ""}} @if($default->causes ){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.causes')}}</span>
                                            </label>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="medium" class="custom-control-input" {{ (!empty(old('medium')) && old('medium') == 'on') ? "checked" : ""}} @if($default->medium ){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.medium')}}</span>
                                            </label>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="tipp" class="custom-control-input" {{ (!empty(old('tipp')) && old('tipp') == 'on') ? "checked" : ""}} @if($default->tipp ){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.tipp')}}</span>
                                            </label>
                                        </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card mt-2">
                                    <h5 class="card-header">{{  trans('action.action_value_setting_header') }}</h5>
                                    <div class="card-body">
                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="ra_status" class="custom-control-input" @if($default->ra_status ){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.show_ra')}}</span>
                                        </label>
                                        <label class="custom-control custom-checkbox mb-3">
                                            <input type="checkbox" name="pdf_status" class="custom-control-input" @if($default->pdf_status ){{ 'checked' }} @endif>
                                            <span class="custom-control-label">{{ trans('action.show_cron_pdf_ra')}}</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="card mt-2">
                                    <h5 class="card-header">{{  trans('action.special_setting_header') }}</h5>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" id="redtick" name="redvalue" class="custom-control-input" @if($setting->red_value) {{ "checked" }} @endif>
                                                <span class="custom-control-label font-weight-bold">{{ trans('action.red_values')}}</span>
                                            </label>

                                            <div id="reddiv" style="display: none" class="mb-3">
                                                <label class="form-label"><b>Enter values</b></label>
                                                <div class="row mx-0 mt-2 align-items-center from-to-container">
                                                    <div class="col-md-6 col-sm-12 pl-0 special-setting-from">
                                                        <span class="d-inline-block">{{ trans('action.cron_setting_from')}}</span>
                                                        <input type="text" name="from" class="form-control" value="{{ $setting->from }}">
                                                    </div>
                                                    <div class="col-md-6 col-sm-12 pr-0 special-setting-to">
                                                        <span class="d-inline-block">{{ trans('action.cron_setting_to')}}</span>
                                                        <input type="text" name="to" class="form-control"  value="{{ $setting->to }}">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label">{{ trans('action.cron_send_email_time')}}{{ trans('action.cron_in_minutes')}}</label>
                                                <input type="text" name="send_email_time" class="form-control"  value="{{ $setting->send_email_time }}">
                                            </div>

                                            <div class="form-group">
                                                <label class="form-label">{{ trans('action.cron_topic')}}</label>
                                                <textarea rows="5" name="topic" class="form-control">{{ $setting->topic }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-2">
                                    <h5 class="card-header">{{  trans('action.pre_select_setting_header') }}</h5>
                                    <div class="card-body row">
                                        <div class="col-md-12">
                                            <input type="hidden" name="cronid" value="@if(!empty($setting)){{ $setting->id }} @endif">
                                            <div class="select2-dark">
                                                <label class="form-label d-block lb-150 mb-2"><i class="ion ion-md-list">&nbsp;</i>{{  trans('action.pre_select_submenu_title') }}</label>
                                                <select class="selectpicker" id="preSubmenus" multiple name="preSubmenus[]" data-live-search="true" title="{{ trans('action.nothingSelected')}}" data-count-selected-text= "{0} {{ trans('action.selectedsubmenus')}}" data-selected-text-format="count > 1">
                                                {{-- <select name="preSubmenus[]" id="preSubmenus" multiple class="select2 form-control select-boxs web-kit-scroll"> --}}
                                                    {{-- <option value="">{{ trans('action.select_sub_menu')}}</option> --}}
                                                    @if($products && !$products->isEmpty())
                                                        @foreach($products as $pro)
                                                            @continue($pro['submenus'] == null)
                                                            <optgroup class="selectAll{{ $pro['proId'] }}" label="{!! ucfirst($pro['product_name']) !!}">
                                                                @foreach ($pro['submenus'] as $submenu)
                                                                    @continue($submenu->type == 12)
                                                                    @php
                                                                        $submenu_id = ($pro['ownmenu'] == "yes") ? $submenu->id.'-own' : $submenu->id.'-sub';
                                                                        $isSelected = ($preselected && $preselected->where('type',(($pro['ownmenu'] == "yes")?true:false))->contains('submenu_id',$submenu->id))?true:false;
                                                                    @endphp
                                                                    <option value="{{ $submenu_id }}" isSelect= "{{ $isSelected }}" data-pro="{{ $submenu->product_id }}" id="subid_{{$submenu->id}}" @if(is_array(old('cronsubmenu'))) {{ (in_array($submenu, old('cronsubmenu'))) ? "selected" : "" }} @endif {{ ($isSelected) ? "selected=true" :"" }}> {{ $submenu->name ?? $submenu->menu_name }} </option>
                                                                @endforeach
                                                            </optgroup>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group form-inline col-md-6">
                                            <label class="form-label mb-2">{{  trans('action.per_select_days') }}</label>
                                            <select name="treat_days" class="custom-select custom-select-box select-boxs">
                                                <option value="">{{  trans('action.lta_choose_days') }}</option>
                                                <option value="7"  @if($default->days == 7) {{"selected"}} @endif>7 {{  trans('action.lta_days') }}</option>
                                                <option value="14" @if($default->days == 14) {{ 'selected' }} @endif>14 {{  trans('action.lta_days') }}</option>
                                                @if(date('t') != $nextMonth )
                                                    <option value="{{$nextMonth}}" @if($default->days == $nextMonth) {{"selected"}} @endif>{{$nextMonth}} {{ trans('action.day')}}</option>
                                                @endif
                                                <option value="{{date('t')}}" @if($default->days == date('t')) {{"selected"}} @endif>{{date('t')}} {{ trans('action.day')}}</option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 ">
                                            <label class="form-label d-block mb-2"><i class="ion ion-md-timer">&nbsp;</i>{{  trans('action.pre_select_time') }}</label>
                                            <div class="input-group">
                                                <input type="text" id="flatpickr-time" name="pre_time"  class="form-control input-group" placeholder="{{  trans('action.pre_time_select') }}">

                                                <div class="input-group-prepend ">
                                                    <div class="input-group-text custom-date-label"><i  class="fa fa-calendar "></i></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer text-right">
                                <button type="submit" class="btn btn-success m-0 text-white">{{ trans('action.update') }}</button>
                            </div>
                        </form>
                    </div>
                </div>
                {{-- SMTP Setting section  --}}
                <div class="tab-pane fade" id="v-pills-smtp-setting" role="tabpanel" aria-labelledby="v-pills-smtp-setting">
                    
                    @if(Auth::user()->user_type == 2)
                    @php
                        $smtpCheck = ($smtp->smtp_email == env('MAIL_FROM_ADDRESS') || $smtp->smtp_user_name == env('MAIL_USERNAME') || $smtp->smtp_password == env('MAIL_PASSWORD') || $smtp->smtp_email == null || $smtp->smtp_user_name == null || $smtp->smtp_password == null);
                        $smtpFiledCheck = ($smtp->smtp_email == env('MAIL_FROM_ADDRESS') || $smtp->smtp_user_name == env('MAIL_USERNAME') || $smtp->smtp_password == env('MAIL_PASSWORD'));
                    @endphp
                    <div class="card">
                        <h5 class="card-header font-weight-bold m-0"><i class="fas fa-marker fa-fw"></i>{{  trans('action.smtp_setting_title') }}</h5>

                        <div class="card-body">
                            @if($smtpCheck)
                                <div class="form-group form-inline">
                                    <label class="form-label mb-2">{{trans('action.smtp_not_set_msg')}}: &NonBreakingSpace;</label>
                                    <a class="btn btn-warning btn-sm" data-toggle="tooltip" title="Customize your own SMTP setting" onclick="$('.smtp-filed').removeClass('hide');$(this).parent().remove()"><i class="fa fa-edit fa-fw"></i>{{trans('action.smtp_customize')}}</a>
                                </div>
                            @endif
                            <div class="smtp-filed @if($smtpCheck){{'hide'}} @endif" >
                                <form action="{{ route('cron.store-smtp') }}" autocomplete="off" method="POST">
                                    @csrf
                                    <div class="form-group form-inline">
                                        <label class="form-label d-block lb-150 mb-2">{{ trans('action.sender_name')}} :</label>
                                        <input type="text" name="sender_name" id="smtpSN" class="form-control" value="{{  $smtp->sender_name }}">
                                    </div>
                                    <div class="form-group form-inline">
                                        <label class="form-label d-block lb-150 mb-2">{{ trans('action.sender_mail_address')}} :</label>
                                        <input type="email" name="smtp_email" id="smtpE" placeholder="{{env('MAIL_FROM_ADDRESS')}}" class="form-control" value="{{ (!$smtpFiledCheck) ? $smtp->smtp_email : '' }}">
                                    </div>
                                    <div class="form-group form-inline">
                                        <label class="form-label d-block lb-150 mb-2">{{ trans('action.smtp_host')}} : <i class="fas fa-info-circle ml-2" data-toggle="tooltip" data-placement="top" title="Ex.ssl://smtp.gmail.com or tls://smtp.gmail.com"></i></label>
                                        <input type="text" name="smtp_host" id="smtpH" class="form-control" value="{{ $smtp->smtp_host }}">
                                    </div>
                                    <div class="form-group form-inline">
                                        <label class="form-label d-block lb-150 mb-2">{{ trans('action.smtp_port')}} :</label>
                                        <input type="text" name="smtp_port" id="smtpP" class="form-control" value="{{ $smtp->smtp_port }}">
                                    </div>
                                    <div class="form-group form-inline">
                                        <label class="form-label d-block lb-150 mb-2">{{ trans('action.smtp_user_name')}} : <i class="fas fa-info-circle ml-2" data-toggle="tooltip" data-placement="top" title="Your SMTP Gmail"></i></label>
                                        <input type="text" name="smtp_user_name" id="smtpU" class="form-control" value="{{ (!$smtpFiledCheck) ? $smtp->smtp_user_name : '' }}">
                                    </div>
                                    <div class="form-group form-inline">
                                        <label class="form-label d-block lb-150 mb-2">{{ trans('action.smtp_host_password')}} :</label>
                                        <input type="password" name="smtp_password" onclick="$(this).removeAttr('readonly').attr('type','text');" onblur="$(this).attr('readonly', true).attr('type','password');" id="smtpPWD" class="form-control" autocomplete="false" value="{{ (!$smtpFiledCheck)?$smtp->smtp_password : '' }}">
                                    </div>
                                    <div class="form-group form-inline">
                                        <label class="form-label d-block lb-150 mb-2">{{ trans('action.smtp_crypto')}}:</label>
                                        <select name="smtp_ssl" id="smtpSSL" class="custom-select custom-select-box">
                                            <option value="null">Select Cyptro</option>
                                            <option value="ssl" @if($smtp->smtp_ssl == "ssl"){{ 'selected' }} @endif>ssl</option>
                                            <option value="tls" @if($smtp->smtp_ssl == "tls"){{ 'selected' }} @endif>tls</option>
                                        </select>

                                        <span class="invalid-feedback allmsg" role="alert">
                                            <strong id="msgCyptro"></strong>
                                        </span>
                                    </div>
                                    <input type="hidden" name="status" value="1" class="form-control">
                                    <div id="msg" style="padding:10px 2px"></div>
                                    <div class="text-right smtp-buttons">
                                        <button type="button" id="smtp_test" class="btn btn-success"><i class="fas fa-user-check ml-2"></i> {{ trans('action.smtp_test_connection')}}</button>
                                        <button type="submit" class="btn btn-success m-0 text-white">{{trans('action.update')}}</button>
                                        <a class="btn btn-xs btn-danger text-white" title="Delete" onclick="clearSmptSetting()"
                                            target="_self">
                                            <i class="fa fa-eraser"></i> Clear
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    @else
                        <div class="card">
                            <h5 class="card-header"><i class="fa fa-envelope fa-fw"></i>{{  trans('action.email_connection_setting_header') }}</h5>

                            <div class="card-body">
                                {{-- <label class="custom-control mb-3"> --}}
                                    <span class="d-inline-block font-weight-bolder text-black-50 ">{{trans('action.smtp_alert_for_not_therapist')}}</span>
                                {{-- </label> --}}
                            </div>
                        </div>
                    @endif
                </div>
                {{-- Start Mail setting section  --}}
                <div class="tab-pane fade" id="v-pills-start-email" role="tabpanel" aria-labelledby="v-pills-start-email">
                    <div class="card">
                        
                        <div class="card-header d-flex justify-content-between">
                            <h5 class="font-weight-bold m-0"><i class="fa fa-envelope fa-fw"></i>{{  trans('action.start_email_template_remote_handle_header') }}</h5>
                        </div>
                        
                        <div class="lang-contents-container card-body">
                            <ul class="nav selected_lang mb-4 start" id="lang-items-wrapper">
                                @foreach ($langs as $lang)
                                    <li class="nav-item">
                                        <a class="nav-link {{ (Lang::locale() == $lang->short_code) ? 'active':'' }}" data-langid="{{$lang->id}}">{{ $lang->name }}</a>
                                    </li>
                                @endforeach
                            </ul>
                            <div class="form-group">
                                <h5 class="d-inline-block font-weight-normal">{{  trans('action.subjct_line_title') }}</h5>
                                <input type="text" name="email_template_title_start" id="text_start" class="form-control" value="">
                                
                            </div>
                            <div class="form-group">
                                <div class="d-flex justify-content-between mb-3 text-weight-bold">
                                    <h5 class="d-inline-block">{{ trans('action.cron_template_start')}}</h5>
                                    <span class="d-none d-sm-inline-block cursor-pointer mr-1" data-toggle="modal" data-target="#placeholderModal">
                                        <i class="fa fa-hand-point-right fa-fw"></i>{{trans('action.cron_available_placeholder')}}                
                                    </span>                                                                
                                </div>
                                <textarea name="email_template_start" id="text_start" class="editor"></textarea>
                                <span class="d-inline-block d-sm-none cursor-pointer mb-2" data-toggle="modal"
                                data-target="#placeholderModal">{{ trans('action.cron_available_placeholder')}}</span>
                            </div>
                        </div>

                        <div class="card-footer text-right">
                            <a onclick="store_record('start')" class="btn btn-success m-0 text-white">{{trans('action.update')}}</a>
                        </div>
                    </div>
                </div>
                {{-- End Mail Setting Section  --}}
                <div class="tab-pane fade" id="v-pills-end-email" role="tabpanel" aria-labelledby="v-pills-end-email-tab">
                    <div class="card">
                        <h5 class="card-header font-weight-bold"><i class="fa fa-envelope fa-fw"></i>{{  trans('action.end_email_template_remote_handle_header') }}</h5>

                        <div class="lang-contents-container card-body">
                            <ul class="nav selected_lang mb-4 end" id="lang-items-wrapper">
                                @foreach ($langs as $lang)
                                    <li class="nav-item">
                                        <a class="nav-link {{ (Lang::locale() == $lang->short_code) ? 'active':'' }}" data-langid="{{$lang->id}}">{{ $lang->name }}</a>
                                    </li>
                                @endforeach
                            </ul>

                            <div class="lang-contents">
                                <div class="form-group">
                                    <h5 class="d-inline-block">{{  trans('action.subjct_line_title') }}</h5>
                                    <input type="text" name="email_template_title_stop" id="text_end" class="form-control" value="">
                                </div>
                                <div class="form-group">
                                    <div class="d-flex justify-content-between mb-3">
                                    <h5 class="d-inline-block">{{ trans('action.cron_template_stop')}}</h5>                                                            
                                        <span class="d-none d-sm-inline-block cursor-pointer mr-1" data-toggle="modal" data-target="#placeholderModal">
                                            <i class="fa fa-hand-point-right fa-fw"></i>{{trans('action.cron_available_placeholder')}}                
                                        </span> 
                                    </div>
                                    <textarea name="email_template_stop" id="text_end" class="editor"></textarea>
                                    <span class="d-inline-block d-sm-none cursor-pointer mb-2" data-toggle="modal"
                                    data-target="#placeholderModal">{{ trans('action.cron_available_placeholder')}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-right">
                            <a onclick="store_record('end')" class="btn btn-success m-0 text-white">{{trans('action.update')}}</a>
                        </div>
                    </div>
                </div>
                {{-- Red Day email setting  --}}
                <div class="tab-pane fade" id="v-pills-red-day-email" role="tabpanel" aria-labelledby="v-pills-red-day-email-tab">
                    <div class="card">
                        <h5 class="card-header font-weight-bold"><i class="fa fa-envelope fa-fw"></i>{{  trans('action.red_day_email_template_remote_handle_header') }}</h5>
                        <div class="lang-contents-container card-body">
                            <ul class="nav selected_lang mb-4 red" id="lang-items-wrapper">
                                @foreach ($langs as $lang)
                                    <li class="nav-item">
                                        <a class="nav-link {{ (Lang::locale() == $lang->short_code) ? 'active':'' }}"  data-langid="{{$lang->id}}">{{ $lang->name }}</a>
                                    </li>
                                @endforeach
                            </ul>
                            <div class="form-group">
                                <h5 class="d-inline-block">{{  trans('action.subjct_line_title') }}</h5>
                                <input type="text" name="email_template_title_blank" class="form-control" id="text_red" value="">
                            </div>
                            <div class="form-group">
                                <div class="d-flex justify-content-between mb-3">
                                    <h5 class="d-inline-block">{{ trans('action.cron_blank_placeholder')}}</h5>
                                    <span class="d-none d-sm-inline-block cursor-pointer mr-1" data-toggle="modal" data-target="#placeholderModal">
                                        <i class="fa fa-hand-point-right fa-fw"></i>{{trans('action.cron_available_placeholder')}}                
                                    </span>                                   
                                </div>
                                <textarea name="email_template_blank" id="text_red" class="editor"></textarea>
                                <span class="d-inline-block d-sm-none cursor-pointer mb-2" data-toggle="modal"
                                data-target="#placeholderModal">{{ trans('action.cron_available_placeholder')}}</span>
                            </div>
                        </div>
                        <div class="card-footer text-right">
                            <a onclick="store_record('red')" class="btn btn-success m-0 text-white">{{trans('action.update')}}</a>
                        </div>
                    </div>
                </div>

                {{-- Green Day email setting --}}
                <div class="tab-pane fade" id="v-pills-green-day-email" role="tabpanel" aria-labelledby="v-pills-green-day-email-tab">
                    <div class="card">
                        <h5 class="card-header font-weight-bold"><i class="fa fa-envelope fa-fw"></i>{{
                            trans('action.green_day_email_template_remote_handle_header') }}</h5>
                        <div class="lang-contents-container card-body">
                            <ul class="nav selected_lang mb-4 green" id="lang-items-wrapper">
                                @foreach ($langs as $lang)
                                <li class="nav-item">
                                    <a class="nav-link {{ (Lang::locale() == $lang->short_code) ? 'active':'' }}"
                                        data-langid="{{$lang->id}}">{{ $lang->name }}</a>
                                </li>
                                @endforeach
                            </ul>
                            <div class="form-group">
                                <h5 class="d-inline-block">{{ trans('action.subjct_line_title') }}</h5>
                                <input type="text" name="email_template_title_green" class="form-control" id="text_green" value="">
                            </div>
                            <div class="form-group">
                                <div class="d-flex justify-content-between mb-3">
                                    <h5 class="d-inline-block">{{ trans('action.cron_green_placeholder')}}</h5>
                                    <span class="d-none d-sm-inline-block cursor-pointer mr-1" data-toggle="modal"
                                        data-target="#placeholderModal">
                                        <i class="fa fa-hand-point-right fa-fw"></i>{{trans('action.cron_available_placeholder')}}
                                    </span>
                                </div>
                                <textarea name="email_template_green" id="text_green" class="editor"></textarea>
                                <span class="d-inline-block d-sm-none cursor-pointer mb-2" data-toggle="modal"
                                    data-target="#placeholderModal">{{ trans('action.cron_available_placeholder')}}</span>
                            </div>
                        </div>
                        <div class="card-footer text-right">
                            <a onclick="store_record('green')" class="btn btn-success m-0 text-white">{{trans('action.update')}}</a>
                        </div>
                    </div>
                </div>
                {{-- Green Day email setting  --}}
                {{-- Resend Email Section  --}}
                <div class="tab-pane fade" id="v-pills-resend-email" role="tabpanel" aria-labelledby="v-pills-resend-email-tab">
                    <div class="card">
                        <h5 class="card-header font-weight-bold"><i class="fa fa-envelope fa-fw"></i>{{ trans('action.resend_email_template') }}</h5>
                        <div class="lang-contents-container card-body">
                            <ul class="nav selected_lang mb-4 resend" id="lang-items-wrapper">
                                @foreach ($langs as $lang)
                                    <li class="nav-item">
                                        <a class="nav-link {{ (Lang::locale() == $lang->short_code) ? 'active':'' }}" data-langid="{{$lang->id}}">{{ $lang->name }}</a>
                                    </li>
                                @endforeach
                            </ul>
                            <div class="form-group">
                                <h5 class="d-inline-block">{{  trans('action.subjct_line_title') }}</h5>
                                <input type="text" name="email_template_title_resend" id="text_resend" class="form-control" value="">
                            </div>
                            <div class="form-group">
                                <div class="d-flex justify-content-between mb-3">
                                    <h5 class="d-inline-block">{{ trans('action.resend_email_template_content_title')}}</h5>
                                    <span class="d-none d-sm-inline-block cursor-pointer mr-1" data-toggle="modal" data-target="#placeholderModal">
                                        <i class="fa fa-hand-point-right fa-fw"></i>{{trans('action.cron_available_placeholder')}}                
                                    </span> 
                                </div>
                                <textarea name="email_template_resend" id="text_resend" class="editor"></textarea>
                                <span class="d-inline-block d-sm-none cursor-pointer mb-2" data-toggle="modal" data-target="#placeholderModal">{{ trans('action.cron_available_placeholder')}}</span>
                            </div>
                        </div>
                        <div class="card-footer text-right">
                            <a onclick="store_record('resend')" class="btn btn-success m-0 text-white">{{trans('action.update')}}</a>
                        </div>
                    </div>
                </div>
                {{-- Footer Section  --}}
                <div class="tab-pane fade" id="v-pills-footer" role="tabpanel" aria-labelledby="v-pills-footer-tab">
                    <div class="card">
                        <h5 class="card-header font-weight-bold"><i class="fa fa-envelope fa-fw"></i>{{ trans('action.global_footer_message') }}</h5>
                        <div class="lang-contents-container card-body">
                            <ul class="nav selected_lang mb-4 footer" id="lang-items-wrapper">
                                @foreach ($langs as $lang)
                                    <li class="nav-item">
                                        <a class="nav-link {{ (Lang::locale() == $lang->short_code) ? 'active':'' }}" data-langid="{{$lang->id}}">{{ $lang->name }}</a>
                                    </li>
                                @endforeach
                            </ul>
                            <div class="form-group">
                                <div class="d-flex justify-content-between mb-3">
                                    <h5 class="d-inline-block">{{ trans('action.cron_main_global_footer_title')}}</h5>
                                    <span class="d-none d-sm-inline-block cursor-pointer mr-1" data-toggle="modal" data-target="#placeholderModal">
                                        <i class="fa fa-hand-point-right fa-fw"></i>{{trans('action.cron_available_placeholder')}}                
                                    </span>                                                             
                                </div>
                                <textarea name="email_template_footer" id="text_footer" class="editor"></textarea>
                                <span class="d-inline-block d-sm-none cursor-pointer mb-2" data-toggle="modal"
                                data-target="#placeholderModal">{{ trans('action.cron_available_placeholder')}}</span>
                            </div>

                        </div>
                        <div class="card-footer text-right">
                            <a class="btn btn-success text-white m-a" onclick="store_record('footer')">{{ trans('action.update') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</section>

<div class="modal modal-top fade" id="placeholderModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">{{ trans('action.available_placeholders')}}</h4>
                <button type="button" class="close" data-dismiss="modal"
                    aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">
                <p><i class="fas fa-check-circle text-success"></i> {{ "{first_name}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{last_name}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{start_date_time}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{end_date_time}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{link}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{time_before}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{client_note}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{pdf_link}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{overview_link}" }}</p>
                <p><i class="fas fa-check-circle text-success"></i> {{ "{remote_id}" }}</p>
            </div>

            <div class="modal-footer">
               <button type="button" class="btn btn-default icon" data-dismiss="modal">{{  trans('action.close') }}</button>
            </div>
        </div>
    </div>
</div>
@endsection


@section('scripts')
    <script src="{!! asset('/vendor/js/tinymce/tinymce.min.js') !!}"></script>
    <script>tinymce.init({ selector:'.editor' });</script>
    <script src="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('/vendor/libs/select2/select2.js') }}"></script>

    <script>
        $('.select-boxs').select2();
        
        $(document).ready(function(){

            var redvals = $('#redtick').is(":checked");
            if(redvals == false) $("#reddiv").attr("style", 'display: block');

            $('.selectpicker').selectpicker('refresh');
            
            var ids = ['#smtpSN','#smtpE', '#smtpH', '#smtpP', '#smtpU', '#smtpSSL', '#smtpPWD'];
            let allFieldsFilled = true;
            validate_smtp();
            $(ids.join(', ')).on('input', validate_smtp);

            $("#smtp_test").on('click',function(e){
                var form = $(e.target).closest('form');
                var smtp_email = form.find("#smtpE").val();
                var smtp_host  = form.find("#smtpH").val();
                var smtp_port  = form.find("#smtpP").val();
                var smtp_user_name = form.find("#smtpU").val();
                var smtp_ssl = form.find("#smtpSSL").val();
                var smtp_password  = form.find("#smtpPWD").val();
                if(smtp_email.length == 0 || smtp_host.length == 0 || smtp_port.length == 0 || smtp_user_name.length == 0 || smtp_password.length == 0){
                    return  toastr.warning(`{{trans("action.all_fileds_required")}}`);
                }
                if(smtp_ssl == 'null'){
                    $('.allmsg').attr('style','display:block;padding-left: 160px');
                    $('#msgCyptro').html('Select SMTP Crypto').fadeIn().delay('slow').fadeOut(500);
                    return;
                }

                var action = "{{url('cron/smpt')}}";
                $.ajax({
                    url: action,
                    type: "POST",
                    data: {smtp_host,smtp_port,smtp_user_name,smtp_password,smtp_ssl,smtp_email},
                    dataType: "json",
                    beforeSend: function(){
                        Swal.fire({
                            title: `{{ trans('action.processing')}}`,
                            imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            confirm: true,
                            showLoaderOnConfirm: true,
                            width: '300px',
                            height: '180px'
                        })
                    },
                    success:function(data){
                        Swal.close()
                        if(data.status){
                            $("#msg").html(`<p class='alert alert-success'>${data.msg}</p>`).fadeIn().delay('slow').fadeOut(5000);
                        }else{
                            $("#msg").html(`<p class='alert alert-danger alert-dismissible fade show text-justify' role='alert'>${data.msg} <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button></p>`).fadeIn().delay('slow').fadeOut(5000);
                        }
                    },
                    error:function(data){}
                });
            });
            function validate_smtp(){
                allFieldsFilled = ids.every(function(id) {
                    return $(id).val() !== '';
                });

                ids.forEach(function(id) {
                    var $input = $(id);
                    if ($input.val() === '' || $input.val() === 'null') {
                        $input.addClass('from-input-error');
                    } else {
                        $input.removeClass('from-input-error');
                    }
                });

                $('.smtp-buttons button').prop('disabled', !allFieldsFilled);
            }

            $("#duePowerid").on('change', function(){
                var checkduePower = $('#duePowerid').is(":checked");
                if(!checkduePower) {
                    $(".due-email").attr("checked", false);
                    $(".x-email").attr("checked", true);
                    $(".x-email").parent().show()
                    $(".due-email").parent().hide()
                } else {
                    $(".due-email").attr("checked", true);
                    $(".x-email").attr("checked", false);
                    $(".x-email").parent().hide()
                    $(".due-email").parent().show()
                }
            });

            $("#redtick").on('click',function(e){
                var redval = $('#redtick').is(":checked");

                if(redval == true)
                    $("#reddiv").attr("style", 'display: none');
                else
                    $("#reddiv").attr("style", 'display: block');
            })

            $("#flatpickr-time").flatpickr({
                enableTime: true,
                noCalendar: true,
                time_24hr: true,
                dateFormat: "H:i",
                defaultDate: "{{ $default->pre_time }}"
            });
        })
        
        
        $('#v-pills-tab a').on('click', function(event) {
            event.preventDefault();
            
            var id = $(this).attr('href')
            if(id == '#v-pills-smtp-setting' || id == '#v-pills-cron-default-setting') return
            setTemplateData($(id+' .selected_lang').attr('class').split(" ")[3],$(id).find('.selected_lang li .active').data('langid'))

        });
        

        $('.selected_lang li a').on('click',function(){
            if($(this).hasClass('active')) return;

            let from = $(this).parents('.selected_lang').attr('class').split(" ")[3];

            let currentLangId = $(this).data('langid')
            // Remove 'active' class from all sibling anchor elements
            $(this).parents('.selected_lang').find('li a').removeClass('active');

            // Add 'active' class to the clicked anchor element
            $(this).addClass('active');

            setTemplateData(from,currentLangId)
            
        })
        function setTemplateData(from,currentLangId){
            $.ajax({
                url: '{{ route("cron.get-template") }}',
                type: "GET",
                dataType: "json",
                data: {
                    from:from,
                    langid:currentLangId
                },
                beforeSend: function(){
                    Swal.fire({
                        title: `{{ trans('action.processing')}}`,
                        imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        confirm: true,
                        showLoaderOnConfirm: true,
                        width: '300px',
                        height: '180px'
                    })
                },
                success:function(data){
                    Swal.close()
                    if(from != 'footer') $('input#text_'+from).val(data.subject);
                    tinymce.get('text_'+from).setContent(data.content);
                },
                error:function(data){Swal.close();console.log(data);}
            });
        }
        function store_record(from){
            let langid = $('.'+from).find('.active').data('langid');
            let subject ='';
            if(from != 'footer') subject = $('input#text_'+from).val();
            let content = tinymce.get('text_'+from).getContent();
            $.ajax({
                url: '{{ route("cron.store-mail-template") }}',
                type: "POST",
                data: {
                    langid: langid,
                    from: from,
                    subject: subject,
                    content: content
                },
                dataType: "json",
                beforeSend: function(){
                    Swal.fire({
                        title: `{{ trans('action.processing')}}`,
                        imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        confirm: true,
                        showLoaderOnConfirm: true,
                        width: '300px',
                        height: '180px'
                    })
                },
                success:function(data){
                    Swal.close()
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: data.message,
                        showConfirmButton: false,
                        timer: 1500
                    })
                },
                error:function(data){}
            });
        }

        function clearSmptSetting()
        {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Clear it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '{{ route("cron.clear_smpt_setting") }}',
                        type: 'GET',
                        success: function (data) {
                            if (data.success) {
                                Swal.fire({
                                    position: 'top-end',
                                    title:'Success...',
                                    icon: 'success',
                                    title: data.msg,
                                    showConfirmButton: false,
                                    timer: 1500
                                });
                                setTimeout(function () {
                                    location.reload();
                                }, 1500);
                            } else {
                                Swal.fire({
                                    position: 'top-end',
                                    title:'Oops...',
                                    icon: 'error',
                                    title: data.message,
                                    showConfirmButton: false,
                                    timer: 1500
                                });
                            }
                        },
                        error: function (data) {}
                    });
                }
            });
        }
    </script>
@endsection
