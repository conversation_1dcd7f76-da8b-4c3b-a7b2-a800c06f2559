@extends('Frontend.partials.layout')

@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/select2/select2.css') }}">
    <style>
        .dropdown-menu .dropdown-header  {
            background-color: #8897AA;
            color: white !important;
        }

        #bottomFixed.bottom-Fixed {
            left: 50%;
            transform: translateX(-50%);
            background: transparent;
        }

        @media only screen and (max-width: 767px) {
            #lengthdivday label{
                display: none;
            }

            .theme-card{
                margin-top: 20px;
            }
            
            #bottomFixed.some_class {
                bottom: 65px !important;
            }
        }

        @media only screen and (max-width: 576px) {
            .corn-setup-page-button .btn{
                padding: 8px;
            }

            .corn-setup-page-button i {
                margin-right: 0px !important;
            }
        }
        .text-muted{
            display: none !important;
        }

    </style>
@endsection
@php
    $user = getUserDetails();
    $auth = Auth::user();
@endphp

@section('content')

<section class="create-corn" style="overflow: visible !important;">
    <div class="row analysis_header_flex" style="border: none;margin-bottom: 0;padding-bottom: 0;">
        <div class="col-md-12">
            <div class="row-top-border">
               <div class="row justify-content-between align-items-center mx-0">
                    <div class="">
                        <div class="analysis-content-header d-flex align-items-center">
                            <div class="logo">
                                <i class="fas fa-home primary-color"></i>
                            </div>
                            <div class="heading primary-color">
                                <h2>{{ __('action.cron_setup')}}</h2>
                                <p>{{ brandName() }} {{trans('action.remote_analysis')}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="">
                        <div class="corn-button corn-setup-page-button text-right">
                            <a class="btn btn-success" href="{{ route('cron.cron') }}" data-toggle="tooltip" data-placement="bottom" data-state="secondary" title="{{trans('action.view_cron_draft')}}">
                                <i class="fas fa-tasks mr-3"></i><span class="d-mobile-none">{{trans('action.view_cron_draft')}}</span>
                            </a>
                            <a class="btn btn-info" href="{{ route('cron.cronSetting', [md5(getAuthID())]) }}" data-toggle="tooltip" data-placement="bottom" data-state="secondary" title="{{trans('action.cron_setting')}}">
                                <i class="fas fa-tools mr-3"></i><span class="d-mobile-none">{{trans('action.cron_setting')}}</span>
                            </a>
                        </div>
                    </div>
               </div>
            </div>
        </div>

    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header font-weight-bold d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
                    <span class="d-inline-block mb-2 mb-sm-0">{{__('action.view_cron_draft')}}</span>
                </div>
                <div class="card-body">
                        <div class="save_prelodaer" style="display:none;">
                            <div class="loading">
                                <div></div>
                                <div></div>
                                <div></div>
                            </div>
                        </div>

                    <form action="{{ route('cron.storeCronSetup') }}" class="create-corn-form" id="crondata" method="POST">
                    @csrf
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{__('action.cron_select_user')}}</b> </label>
                                    <select class="select2-demo form-control required"  id="cronusercheck" name="cronuser" required>
                                        {{-- @if ($user->user_type == 0)
                                            <option value="{{$user->id}}" data-timezoneid="{{ $user->timezone_id }}" selected>{{ $user->first_name." ".$user->last_name}}</option>
                                        @endif --}}

                                        @if(!empty($subuser))
                                            <option value="">{{__('action.cron_users_error')}}</option>                                        
                                            @foreach($subuser as $sub)
                                                @if ($auth->user_type == 4)
                                                    @continue(!in_array($sub->id,getCollectionArrayForStaffUser($auth->staffInGorup)))
                                                @endif

                                                <option data-timezoneid="{{ $sub->timezone_id }}" {{ ($user->id == $sub->id && $user->boss_id) ? 'selected':'' }} value="{{ $sub->id }}" id="{{ $sub->id}}" {{ (!empty(old('cronuser')) && old('cronuser')==$sub->id) ? "selected" : ""}}>
                                                    {{ $sub->first_name." ".$sub->last_name . ((!$sub->boss_id)? ' -'.$sub->userRoll:'')}}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                    <span class="invalid-feedback allmsg" role="alert">
                                        <strong id="msg"></strong>
                                    </span>
                                </div>
                                {{-- @dd($cronsetting->preassignsubmenus , $cronsetting->preassignsubmenus->where('type',(($product[10]['ownmenu'] == "yes")?true:false))->contains('submenu_id',23)?true:false) --}}
                               {{-- @dd($preselected,$product->isEmpty(),$product && !$product->isEmpty(),$product[80],$product[73],$product[10])  --}}
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{__('action.selectet_analysis')}}</b> </label>
                                    <div class="select2-dark">
                                        <select class="selectpicker" id="cronsubcheck" multiple name="cronsubmenu[]" required data-live-search="true" data-size="8" title="{{__('action.nothingSelected')}}" data-count-selected-text= "{0} {{__('action.selectedsubmenus')}}" data-selected-text-format="count > 1">
                                            @if($product && !$product->isEmpty())
                                                @foreach($product as $pro)
                                                    @continue($pro['submenus'] == null)
                                                    <optgroup class="selectAll{{ $pro['proId'] }}" label="{!! ucfirst($pro['proName']) !!}" onclick="selectBellow(this)">
                                                        @foreach ($pro['submenus'] as $submenu)
                                                            @continue($submenu->type == 12)
                                                            @php
                                                                $submenu_id = ($pro['ownmenu'] == "yes") ? $submenu->id.'-own' : $submenu->id.'-sub';
                                                                $isSelected = ($cronsetting->preassignsubmenus && $cronsetting->preassignsubmenus->where('type',(($pro['ownmenu'] == "yes")?true:false))->contains('submenu_id',$submenu->id))?true:false;
                                                            @endphp
                                                            <option data-subtext="{!! ucfirst($pro['proName']) !!}" value="{{ $submenu_id }}" isSelect= "{{ $isSelected }}" data-pro="{{ $submenu->product_id }}" id="subid_{{$submenu->id}}" @if(is_array(old('cronsubmenu'))) {{ (in_array($submenu, old('cronsubmenu'))) ? "selected" : "" }} @endif {{ ($isSelected) ? "selected=true" :"" }}> {{ $submenu->name ?? $submenu->menu_name }} </option>
                                                        @endforeach
                                                    </optgroup>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>
                                    <span class="invalid-feedback allmsg" role="alert">
                                        <strong id="msg1"></strong>
                                    </span>
                                </div>
                                <div class="d-flex col-md-12 pl-0 pr-0">
                                    <button type="button" class="btn btn-default hover-green w-50 mr-2" id="autoGenerate">{{__('action.auto_genrate')}}</button>
                                    <button type="button" class="btn btn-default hover-red w-50" id="clearGenerate">{{__('action.clear')}}</button>
                                </div>
                            </div>

                            <div class="col-md-4" id="duePowerdiv" style="display: none">
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{__('action.cron_start_date')}}</b> <span class="timeZoneAlert"></span></label>
                                    <div class="input-group">
                                        <input type="text" id="flatpickr-datetime" name="start_time_duepower" onchange="checkcron(this)" class="form-control start_time_duepower" placeholder="@if(Config::get('app.locale') == 'de') dd.mm.YYYY @else  YYYY/mm/dd @endif">

                                        <div class="input-group-prepend">
                                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                        </div>
                                    </div>
                                    <span class="invalid-feedback allmsg" role="alert">
                                        <strong id="msg2"></strong>
                                    </span>
                                </div>
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{__('action.cron_end_date')}}</b> </label>
                                    <select class="select2-demo form-control" name="end_time_duepower" id="end_time_duepower">
                                        <option value="">{{__('action.select_time')}}</option>
                                        <option value="30" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 30) ? "Selected" : ""}}>30 {{__('action.waiting_minutes')}}</option>
                                        <option value="45" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 45) ? "Selected" : ""}}>45 {{__('action.waiting_minutes')}}</option>
                                        <option value="60" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 60) ? "Selected" : ""}}>60 {{__('action.waiting_minutes')}}</option>
                                        <option value="120" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 120) ? "Selected" : ""}}>120 {{__('action.waiting_minutes')}}</option>
                                        <option value="240" {{ (!empty(old('end_time_duepower')) && old('end_time_duepower') == 240) ? "Selected" : ""}}>240 {{__('action.waiting_minutes')}}</option>
                                    </select>
                                    <span class="invalid-feedback allmsg" role="alert">
                                        <strong id="msg3"></strong>
                                    </span>
                                </div>
                            </div>

                            <div class="col-md-4" id="normalDiv">
                                <div class="form-group">
                                    <label class="form-label mb-2"> <b>{{__('action.cron_start_date')}}</b> <span class="timeZoneAlert"></span> </label>
                                    <div class="input-group">
                                        <input type="text" id="flatpickr-datetime2" name="crondate" onchange="checkcron(this)" class="form-control crondate" placeholder="@if(Config::get('app.locale') == 'de') dd.mm.YYYY @else  YYYY/mm/dd @endif">

                                        <div class="input-group-prepend">
                                            <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="form-group" style="width:100%" id="selectFreq">
                                        <label class="form-label mb-2"> <b>{{__('action.frequency')}}</b> </label>
                                        <select class="custom-select w-100" name="cronfreq" id="frequencyDiv">
                                            <option value="1" id="fq_1">1</option>
                                            <option value="2" id="fq_2" {{!empty(old('cronfreq')) && old('cronfreq')==2 ? "Selected" : ""}}>2</option>
                                            <option value="3" id="fq_3" {{!empty(old('cronfreq')) && old('cronfreq')==3 ? "Selected" : ""}}>3</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="d-flex col-md-12 pl-0 pr-0 form-group">
                                    <button type="button" class="btn btn-default hover-green w-50 mr-2" id="autoGenerate_dl">{{__('action.auto_genrate')}}</button>
                                    <button type="button" class="btn btn-default hover-red w-50" id="clearGenerate_dl">{{__('action.clear')}}</button>
                                </div>
                            </div>

                            <div class="col-md-4" id="normalDivtime" >
                                <div class="row">
                                    <div class="form-group col-md-6" id="lengthdiv">
                                        <label class="form-label mb-2"> <b>{{__('action.length')}}</b> </label>
                                        <input type="number" class="form-control m-0" placeholder="1" id="cronlength" min="1" max="31" name="cronlength" value="@if(!empty($default->days)){{$default->days}}@else{{old('cronlength')}}@endif">
                                    </div>
                                    <div class="form-group col-md-6" id="lengthdivday">
                                        <label class="form-label"></label>
                                        <select class="custom-select mt-2" name="cronday" id="cronday">
                                            <option value="">{{__('action.day')}}</option>
                                            <option value="7"  @if($default->days == 7) {{"selected"}} @endif>7 {{__('action.day')}}</option>
                                            <option value="14" @if($default->days == 14) {{"selected"}} @endif>14 {{__('action.day')}}</option>
                                            @if(date('t') != $nextMonth )
                                                <option value="{{$nextMonth}}">{{$nextMonth}} {{__('action.day')}}</option>
                                            @endif
                                            <option value="{{date('t')}}" @if($default->days == date('t')) {{"selected"}} @endif>{{date('t')}} {{__('action.day')}}</option>
                                        </select>
                                    </div>
                                    <div class="form-group col-md-6" id="freq1" style="display: none">
                                        <label class="form-label mb-2"> <b>{{__('action.second_start_time')}} </b> </label>
                                        <input type="text" class="form-control" id="flatpickr-time" name="time2">
                                    </div>
                                    <div class="form-group col-md-6" id="freq2" style="display: none">
                                        <label class="form-label mb-2"> <b>{{__('action.third_start_time')}}</b> </label>
                                        <input type="text" class="form-control" id="flatpickr-time1" name="time3">
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="form-group mb-0" style="width:100%" id="selectFreq">
                                        <label class="form-label lb-100 mb-2 col-md-12 pl-0" for="">{{ trans('action.timezone') }}</label>
                                        <select class="select2-demo form-control m-0 col-md-12" id="timezone_select">
                                            <option selected>{{trans('action.Select')}}</option>
                                            <?php foreach($timezones as $zone){?>
                                                <option value="{{$zone->timezone}}" >{{ $zone->name }} </option>
                                            <?php }?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Hidden input for timezone -->
                                <input type="hidden" name="timezone_id" id="timezone_id" value="">
                            </div>
                        </div>

                        <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <h5 class="card-header font-weight-bold">{{ __('action.general_setting_header') }}</h5>
                                <div class="card-body">
                                    <h5 class="card-title">{{__('action.calculation_system_dashboard')}}</h5>
                                    <div class="mb-3 d-flex align-items-center">
                                        <div class="right-side ml-3">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-1 mr-2">{{__('action.year')}}</div>
                                                <label class="switcher switcher-sm switcher-success m-0">
                                                <input type="checkbox" id="cal_type" name="cal_type" class="switcher-input" checked>
                                                    <span class="switcher-indicator">
                                                        <span class="switcher-yes"></span>
                                                        <span class="switcher-no"></span>
                                                    </span>
                                                </label>
                                                <div class="flex-shrink-1 text-success ml-2">{{__('action.month')}}</div>
                                            </div>
                                       </div>
                                    </div>
                                    <h5 class="card-title">{{ __('action.due_power_setting') }}</h5>
                                    <label class="custom-control custom-checkbox mb-3">
                                        <input type="checkbox" name="due_power" class="custom-control-input" id="cronDuePower" @if($default->due_power == 1){{ 'checked' }} @endif>
                                        <span class="custom-control-label">{{__('action.due_power')}}</span>
                                    </label>
                                </div>
                            </div>
                              <br/>

                            <div class="card">
                                <h5 class="card-header font-weight-bold">{{ __('action.link_email_setting_header') }}</h5>
                                <div class="card-body">
                                  <h5 class="card-title">{{ __('action.cron_customer_link') }}</h5>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="customer_link" class="custom-control-input"  @if($default->customer_link == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.enable_customer_link')}}</span>
                                </label>
                                <h5 class="card-title">{{ __('action.link_email_setting_title') }}</h5>
                                <label class="custom-control custom-checkbox mb-3 email1x">
                                    <input type="checkbox" name="start_email" id="sm1x" class="custom-control-input"  @if($default->start_email == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.start_email_1x')}}</span>
                                </label>
                                <label class="custom-control custom-checkbox mb-3 email1x">
                                    <input type="checkbox" name="end_email" id="em1x" class="custom-control-input"  @if($default->end_email == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.end_email_1x')}}</span>
                                </label>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="end_email_therapist" id="end_email_therapist" class="custom-control-input"  @if($default->end_email_therapist == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.end_email_the_therapist')}}</span>
                                </label>
                                <label class="custom-control custom-checkbox mb-3 emaildue">
                                    <input type="checkbox" name="start_email_duepower" id="smd1x" class="custom-control-input"  @if($default->start_email_duepower == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.start_email_due_power')}}</span>
                                </label>
                                <label class="custom-control custom-checkbox mb-3 emaildue">
                                    <input type="checkbox" name="end_email_duepower" id="emd1x" class="custom-control-input"  @if($default->end_email_duepower == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.end_email_due_power')}}</span>
                                </label>
                                </div>
                            </div>
                            <br/>
                            <div class="card">
                                <h5 class="card-header font-weight-bold">{{ __('action.advanced_setting_header') }}</h5>
                                <div class="card-body">
                                  <h5 class="card-title">{{ __('action.advanced_setting_title') }}</h5>
                                  <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="pdf_export" class="custom-control-input"  @if($default->pdf_export == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.enable_pdf_export')}}</span>
                                  </label>

                                <h5 class="card-title">{{ __('action.add_cause_remedy_title') }}</h5>

                                <div class="row">
                                    <div class="col-md-2">
                                      <label class="custom-control custom-checkbox mb-3">
                                      <input type="checkbox" name="causes" class="custom-control-input" {{ (!empty(old('causes')) && old('causes') == 'on') ? "checked" : ""}} @if($default->causes == 1){{ 'checked' }} @endif>
                                      <span class="custom-control-label">{{__('action.causes')}}</span>
                                      </label>
                                   </div>
                                   <div class="col-md-2">
                                      <label class="custom-control custom-checkbox mb-3">
                                      <input type="checkbox" name="medium" class="custom-control-input" {{ (!empty(old('medium')) && old('medium') == 'on') ? "checked" : ""}} @if($default->medium == 1){{ 'checked' }} @endif>
                                      <span class="custom-control-label">{{__('action.medium')}}</span>
                                      </label>
                                   </div>
                                   <div class="col-md-2">
                                      <label class="custom-control custom-checkbox mb-3">
                                      <input type="checkbox" name="tipp" class="custom-control-input" {{ (!empty(old('tipp')) && old('tipp') == 'on') ? "checked" : ""}} @if($default->tipp == 1){{ 'checked' }} @endif>
                                      <span class="custom-control-label">{{__('action.tipp')}}</span>
                                      </label>
                                   </div>
                                </div>
                                <h5 class="card-title">{{ __('action.apply_the_cal_to_all') }}</h5>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="calculation_next_day" class="custom-control-input"  @if($default->calculation_next_day == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.calculation_next_day')}}</span>
                                </label>

                                <h5 class="card-title">{{ __('action.no_fixed_cron_time_title') }}</h5>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="fixed_time" class="custom-control-input" {{ (!empty(old('fixed_time')) && old('fixed_time') == 'on') ? "checked" : ""}} @if($default->fixed_time == 1){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.no_fixed_cron_time')}}</span>
                                </label>
                                </div>
                            </div>
                              <br/>

                            <div class="card">
                                <h5 class="card-header font-weight-bold">{{ __('action.action_value_setting_header') }}</h5>
                                <div class="card-body">
                                  <h5 class="card-title">{{ __('action.action_value_setting_title') }}</h5>
                                  <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="ra_status" class="custom-control-input" {{ (!empty(old('ra_status')) && old('ra_status') == 'on') ? "checked" : ""}} @if($default->ra_status){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.show_ra')}}</span>
                                  </label>
                                <h5 class="card-title">{{ __('action.show_cron_pdf_ra_title') }}</h5>
                                <label class="custom-control custom-checkbox mb-3">
                                    <input type="checkbox" name="pdf_status" class="custom-control-input" {{ (!empty(old('pdf_show')) && old('pdf_status') == 'on') ? "checked" : ""}} @if($default->pdf_status){{ 'checked' }} @endif>
                                    <span class="custom-control-label">{{__('action.show_cron_pdf_ra')}}</span>
                                </label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card theme-card">
                                <div class="card-header font-weight-bold">
                                  {{ __('action.cron_theme_header') }}
                                </div>
                                <div class="card-body">
                                  <h5 class="card-title">{{ __('action.cron_theme_title') }}</h5>
                                    <div class="form-group">
                                       <label class="form-label mb-2"> <b>{{__('action.cron_topic')}}</b> </label>
                                       <textarea id="topic" name="topic" class="form-control" rows="5"
                                          placeholder="{{__('action.cron_note_placeholder')}}">{{ old('topic') ? old('topic') : $cronsetting->topic }}</textarea>
                                    </div>
                                </div>
                            </div>
                            <br/>
                            <div class="card">
                                <div class="card-header font-weight-bold">
                                  {{ __('action.cron_note_header') }}
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <h5 class="card-title">{{ __('action.cron_note_title') }}</h5>
                                            <div class="form-group">
                                                <label class="form-label"> <b>{{__('action.cron_note2')}}</b> </label>
                                                <textarea id="" name="note" class="form-control" rows="5"
                                                placeholder="{{__('action.note_here')}}">{{ old('note')  }}</textarea>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <h5 class="card-title">{{trans('action.cron_note_client_title')}}</h5>
                                            <div class="form-group">
                                                <label class="form-label"> <b>{{__('action.cron_note2')}}</b> </label>
                                                <textarea id="" name="client_note" class="form-control" rows="5"
                                                placeholder="{{__('action.note_here')}}">{{ old('client_note')  }}</textarea>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        </div>

                        <div class="d-flex justify-content-center bottom-Fixed" id="bottomFixed">
                            <button type="button" class="btn btn-secondary w-25 mr-1" id="cronPreview"  onclick="cronpreview()">{{__('action.cron_preview')}}</button>
                            <button type="submit" class="btn btn-success w-25 fromSubmit" id="fromSubmit">{{__('action.save')}}</button>
                        </div>
                    </form>
                </div>

                <div class="modal modal-top fade" id="preview-modal">
                    <div class="modal-dialog">
                        <form class="modal-content" action="{{ route('cron.storeCronSetup') }}" id="storeCronPreview" method="POST">
                            @csrf
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <p>{{__('action.preview_from')}} <b><span id="pre_name"></span></b> </p>
                                </h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button>
                            </div>

                            <input type="hidden" id="pre_val1" name="cronuser" value="">
                            <input type="hidden" id="pre_val2" name="cronsubmenu" value="">
                            <input type="hidden" id="pre_val3" name="start_time_duepower" value="">
                            <input type="hidden" id="pre_val4" name="end_time_duepower" value="">
                            <input type="hidden" id="pre_val5" name="crondate" value="">
                            <input type="hidden" id="pre_val6" name="time2" value="">
                            <input type="hidden" id="pre_val7" name="time3" value="">
                            <input type="hidden" id="pre_val8" name="cronlength" value="">
                            <input type="hidden" id="pre_val9" name="cronday" value="">
                            <input type="hidden" id="pre_val10" name="cronfreq" value="">
                            <input type="hidden" id="pre_val11" name="topic" value="">
                            <input type="hidden" id="pre_val12" name="note" value="">
                            <input type="hidden" id="pre_val13" name="client_note" value="">
                            <input type="hidden" id="pre_val15" name="timezone_id" value="">
                            

                            <input type="hidden" name="preview" value="active">

                            <div class="modal-body web-kit-scroll">
                                <div class="modal_preloader">
                                    <div class="loading">
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                    </div>
                                </div>

                                <div class="loaded_data">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h5 class="mb-3"> {{__('action.selectet_analysis')}}</h5>
                                            <p id="pre_sub" class="web-kit-scroll pAna" style="font-weight: bold"></p>
                                            <div class="mb-3 d-flex align-items-center">
                                                <div class="right-side">
                                                    <div class="d-flex align-items-center">
                                                        <div class="flex-shrink-1 mr-2">{{__('action.year')}}</div>
                                                        <label class="switcher switcher-sm switcher-success m-0">
                                                            <input type="checkbox" id="pre_cal_type" name="cal_type" class="switcher-input" checked>
                                                            <span class="switcher-indicator">
                                                                <span class="switcher-yes"></span>
                                                                <span class="switcher-no"></span>
                                                            </span>
                                                        </label>
                                                        <div class="flex-shrink-1 text-success ml-2">{{__('action.month')}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="due_power" id="pre_dp" value="">
                                                <span class="custom-control-label">{{__('action.cron_due_power')}} ({{__('action.cron_due_power1')}} 240 {{__('action.cron_due_power2')}})</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="customer_link" id="pre_cl" value="">
                                                <span class="custom-control-label">{{__('action.cron_customer_link')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3 pre_smaildue">
                                                <input type="checkbox" name="start_email_duepower" id="pre_smd1x" class="custom-control-input"  @if($default->start_email_duepower == 1){{ 'checked' }} @endif>
                                                <span class="custom-control-label">{{__('action.start_email_due_power')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3 pre_emaildue">
                                                <input type="checkbox" name="end_email_duepower" id="pre_emd1x" class="custom-control-input"  @if($default->end_email_duepower == 1){{ 'checked' }} @endif>
                                                <span class="custom-control-label">{{__('action.end_email_due_power')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3 pre_semail">
                                                <input type="checkbox" class="custom-control-input" name="start_email" id="pre_se" value="">
                                                <span class="custom-control-label">{{__('action.start_email_1x')}}</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3 pre_email">
                                                <input type="checkbox" class="custom-control-input" name="end_email" id="pre_ee" value="">
                                                <span class="custom-control-label">{{__('action.end_email_1x')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="end_email_therapist" id="pre_eet" value="">
                                                <span class="custom-control-label">{{__('action.end_email_the_therapist')}}</span>
                                            </label>

                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="pdf_export" id="pre_pdf" value="">
                                                <span class="custom-control-label">{{__('action.cron_export')}}</span>
                                            </label>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <label class="custom-control custom-checkbox mb-3">
                                                        <input type="checkbox" name="causes" id="pre_cau" class="custom-control-input">
                                                        <span class="custom-control-label">{{__('action.causes')}}</span>
                                                    </label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <label class="custom-control custom-checkbox mb-3">
                                                        <input type="checkbox" name="medium" id="pre_mid" class="custom-control-input">
                                                        <span class="custom-control-label">{{__('action.medium')}}</span>
                                                    </label>
                                                </div>
                                                <div class="col-sm-4">
                                                    <label class="custom-control custom-checkbox mb-3">
                                                        <input type="checkbox" name="tipp" id="pre_tip" class="custom-control-input">
                                                        <span class="custom-control-label">{{__('action.tipp')}}</span>
                                                    </label>
                                                </div>
                                            </div>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" class="custom-control-input" name="calculation_next_day" id="pre_cnd" value="">
                                                <span class="custom-control-label">{{__('action.calculation_next_day')}}</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" name="fixed_time" class="custom-control-input" id="pre_atc" value="">
                                                <span class="custom-control-label">{{__('action.no_fixed_cron_time')}}</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" name="ra_status" id="pre_ra_status" class="custom-control-input">
                                                <span class="custom-control-label">{{__('action.show_ra')}}</span>
                                            </label>
                                            <label class="custom-control custom-checkbox mb-3">
                                                <input type="checkbox" name="pdf_status" id="pre_pdf_status" class="custom-control-input">
                                                <span class="custom-control-label">{{__('action.show_cron_pdf_ra')}}</span>
                                            </label>

                                            <p>{{__('action.topic')}}: <span id="pre_topic">{{__('action.no_topic_added')}}</span></p>
                                            <p>{{__('action.external_note')}}: <span id="pre_client_note">{{__('action.no_note_added')}}</span></p>
                                            <p>{{__('action.internal_note')}}: <span id="pre_note">{{__('action.no_note_added')}}</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h5 class="mb-3">{{__('action.start_time_cron_view')}}: <span id="pre_stime"></span></h5>
                                            <div class="media align-items-center mb-3">
                                                <img id="pre_img" src="" class="d-block ui-w-80">

                                            </div>
                                            <div>
                                                <p> <strong>{{__('action.cron_user')}}:</strong> <span id="pre_user"></span></p>
                                                <p> <strong>{{__('action.cron_birthday')}}:</strong> <span id="pre_dob"></span></p>
                                                <p> <strong>{{__('action.cron_village')}}:</strong> <span id="pre_pob"></span> </p>
                                                <div class="form-group form-inline">
                                                    <label for="" class="mr-3">{{__('action.cron_email')}}:</label>
                                                    <input type="email" name="usermail" class="form-control" id="pre_email" value="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="my-2" id="pre_title"><strong>{{__('action.analysis_data')}}</strong></p>
                                    <p id="pre_analyses"></p>
                                    <div>
                                        <input type="hidden" value="" id="causesid" name="causesid">
                                        <input type="hidden" value="" id="mediumid" name="mediumid">
                                        <input type="hidden" value="" id="tippid" name="tippid">
                                        <input type="hidden" value="" id="preStatus" name="preview_status">
                                    </div>
                                    <div class="mt-4 d-flex flex-column justify-content-end justify-content-sm-center flex-sm-row align-items-sm-center">
                                        <!-- <button type="button" class="btn btn-danger w-32">{{__('action.clear')}}</button> -->
                                        {{--<button type="submit" name="draft" value="savedraft" class="btn btn-default w-32">{{__('action.save_draft')}}</button>--}}
                                        <button type="submit" class="btn btn-success w-32" id="preview_save">{{__('action.save')}}</button>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">{{__('action.close')}}</button>
                            </div>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    </div>
</section>

@endsection


@section('scripts')
    <!-- Dependencies -->
    <script src="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('/js/cron-validation.js') }}"></script>

    <script>
        // Initialize validation messages
        CronValidation.setViewType('default');
        CronValidation.setMessages({
            userError: '{{__('action.cron_users_error')}}',
            submenuError: '{{__('action.cron_submenu_error')}}',
            startError: '{{__('action.cron_start_error')}}',
            endDateError: '{{__('action.cron_end_date_error')}}',
            frequencyError: '{{__('action.frequency')}}',
            secondStartTimeError: '{{__('action.second_start_time')}}',
            thirdStartTimeError: '{{__('action.third_start_time')}}',
            lengthError: '{{__('action.select_length')}}',
            lengthNotError: '{{__('action.add_length_not')}}'
        });

        // Select2
        var __getReqClose;
        
        $(function() {
            $('.select2-demo').each(function() {
                $(this)
                .wrap('<div class="position-relative"></div>')
                .select2({
                    dropdownParent: $(this).parent()
                });
            })
        });
        
        let addMoreFifteen = true;
        
        $('#cronsubcheck').on('shown.bs.select', function (e, clickedIndex, isSelected, oldValue) {
            $(document).find(".dropdown-menu li.dropdown-header").attr('onclick','selectBellow(event)')
            // Extract unique classes from selected dropdown menu items
            const uniqueClasses = Array.from(new Set($(".dropdown-menu li.selected a[class]").map(function() {
                return $(this).attr('class').split(' ')[1];
            }).get()));
            
            // Add classes 'selected' and 'font-weight-bold' to matching li.dropdown-header elements
            $(".dropdown-header[class*=' ']").filter(function() {
                return uniqueClasses.includes($(this).attr('class').split(' ')[1]);
            }).addClass('selected font-weight-bold');

            // Use the mousewheel event to handle scrolling within the Bootstrap SelectPicker dropdown
            $('.bootstrap-select .dropdown-menu').on('mousewheel', function () {
                const newUniqueClasses = Array.from(new Set($(".dropdown-menu li.selected a[class]").map(function() {
                    return $(this).attr('class').split(' ')[1];
                }).get()));
                
                // Add classes 'selected' and 'font-weight-bold' to matching li.dropdown-header elements
                $(".dropdown-header[class*=' ']").filter(function() {
                    return newUniqueClasses.includes($(this).attr('class').split(' ')[1]);
                }).addClass('selected font-weight-bold');
                $('.dropdown-menu li.dropdown-header:not([onclick])').attr('onclick','selectBellow(event)');
            });
        })
        
        $('#cronsubcheck').on('change', function (e, clickedIndex, isSelected, oldValue) {
            if($(this).find(':selected').length === 15 && addMoreFifteen){
                Swal.fire({
                    title: `{{__('action.alert_max_submenu_hgeading')}}`,
                    text: `{{__('action.alert_max_submenu_details')}}`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#FFB505',
                    cancelButtonColor: '#d33',
                    cancelButtonText:  `{{__('action.cancel')}}`,
                    confirmButtonText:  `{{__('action.confirm')}}`,
                }).then((result) => {
                    if (result.value) {
                        addMoreFifteen = false
                    }
                });
            }
            
        })

        function selectBellow(e){        
            let classes = ''
            if(!$(e.target).hasClass('dropdown-header')) classes = $(e.target).parent().attr('class')
            else classes = $(e.target).attr('class')

            let className = classes.split(' ')[2]
            let selProId = classes.split(' ')[1].split('selectAll')[1]

            if($('.'+classes.split(' ')[2]).hasClass('font-weight-bold')){
                $('option[data-pro="'+selProId+'"]').prop('selected',false).trigger('change')
                $('.'+className).removeClass('font-weight-bold').removeClass('selected').find('a').removeClass('selected').attr('aria-selected',false)
            }else{
                if(($("#cronsubcheck option:selected").length + ($('.'+classes.split(' ')[2]).length -1)) > 15 && addMoreFifteen){
                    Swal.fire({
                        title: `{{__('action.alert_max_submenu_hgeading')}}`,
                        text: `{{__('action.alert_max_submenu_details')}}`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#FFB505',
                        cancelButtonColor: '#d33',
                        cancelButtonText:  `{{__('action.cancel')}}`,
                        confirmButtonText:  `{{__('action.confirm')}}`,
                    }).then((result) => {
                        if (result.value) {
                            addMoreFifteen = false
                            $('option[data-pro="'+selProId+'"]').prop('selected',true).trigger('change')
                            $('.'+classes.split(' ')[2]).addClass('font-weight-bold').addClass('selected').find('a').addClass('selected').attr('aria-selected',true)
                            $('.selectpicker').selectpicker('refresh');
                            toastr.success(`{{__('action.confirm')}}`);
                        }
                    });
                }else{
                    $('option[data-pro="'+selProId+'"]').prop('selected',true).trigger('change')
                    $('.'+classes.split(' ')[2]).addClass('font-weight-bold').addClass('selected').find('a').addClass('selected').attr('aria-selected',true)
                }
            }
        }

        var userid = 0;
        var today = new Date();
        var local_lang = "{{ Config::get('app.locale') }}";
        var datef = (local_lang === 'de') ? "d.m.Y H:i" : "Y/m/d H:i";

        $(document).ready(function(){
            
            let seTimezone = $('#cronusercheck').children("option:selected").data('timezoneid');
            $("#timezone_select").val(seTimezone).trigger('change');
            
            getExicutableTime($('#timezone_select option:selected').val())
            
            let status = "ok";
            let txt = "This field is required";
            
            $("#duePowerdiv").removeAttr("style")
            $("#freq1").removeAttr("style")
            $("#freq2").removeAttr("style")


            let checkduePower = $('#cronDuePower').is(":checked");
            updateDuePowerVisibility(checkduePower);

            $("#cronDuePower").on('change', function(){
                checkduePower = $(this).is(":checked");
                updateDuePowerVisibility(checkduePower);
            });

            function updateDuePowerVisibility(checkduePower) {
                if(!checkduePower) {
                    $('.start_time_duepower').prop('disabled', true);
                    $('.crondate').prop('disabled', false);
                    $("#duePowerdiv").addClass("duePowerdivs");
                    $("#normalDiv").attr("style", "display: block");
                    $("#lengthdiv").removeClass("freqtime");
                    $("#lengthdivday").removeClass("freqtime");
                    $("#freq1").removeClass("freqtime");
                    $("#freq2").removeClass("freqtime");
                    $(".emaildue").addClass("duePowerdivs");
                    $(".email1x").attr("style", "display: block");
                    $("#smd1x").attr("checked", false);
                    $("#emd1x").attr("checked", false);
                    $("#sm1x").attr("checked", true);
                    $("#em1x").attr("checked", true);
                    // const $dt1 = $("#flatpickr-datetime2").flatpickr();
                    // $dt1.clear();
                } else {
                    $('.crondate').prop('disabled', true);
                    $('.start_time_duepower').prop('disabled', false);
                    $("#duePowerdiv").removeClass("duePowerdivs");
                    $("#normalDiv").attr("style", "display: none");
                    $("#lengthdiv").addClass("freqtime");
                    $("#lengthdivday").addClass("freqtime");
                    $("#freq1").addClass("freqtime");
                    $("#freq2").addClass("freqtime");
                    $(".emaildue").removeClass("duePowerdivs");
                    $(".email1x").attr("style", "display: none");
                    $("#sm1x").attr("checked", false);
                    $("#em1x").attr("checked", false);
                    $("#smd1x").attr("checked", true);
                    $("#emd1x").attr("checked", true);
                    $("#flatpickr-time").val('');
                    $("#flatpickr-time1").val('');
                    // const $dt2 = $("#flatpickr-datetime").flatpickr();
                    // $dt2.clear();
                }
            }
            
            
            
            $("#cronusercheck").on('change', function(){
                var sTimezone = $(this).children("option:selected").data('timezoneid');
                $("#timezone_select").val(sTimezone).trigger('change');
                getExicutableTime(sTimezone)
                $('.start_time_duepower').removeAttr('disabled')

            });

            // Helper function to handle visibility logic
            function updateFrequencyVisibility(freq) {
                if (freq == 2) {
                    $("#freq1").show();
                    $("#freq2").hide();
                    $("#flatpickr-time1").val('');
                    if(!!$("#flatpickr-time").val()){
                        checkcron();
                    }
                } else if (freq == 3) {
                    $("#freq1").show();
                    $("#freq2").show();
                    if(!!$("#flatpickr-time").val() && !!$("#flatpickr-time1").val()){
                        checkcron();
                    }
                } else if (freq == 1) {
                    $('#bottomFixed button').prop('disabled', false);
                    $("#flatpickr-time").val('');
                    $("#flatpickr-time1").val('');
                    $("#freq1").hide();
                    $("#freq2").hide();
                }
            }

            // Initial check on page load
            const initialFreq = $('#frequencyDiv').val();
            updateFrequencyVisibility(initialFreq);

            // Update visibility on change event
            $("#frequencyDiv").on('change', function () {
                const freq = $(this).val();
                updateFrequencyVisibility(freq);
            });

            $("#fromSubmit").click(function(event) {
                const validation = CronValidation.validate();
                if (!validation.isValid) {
                    event.preventDefault();
                    showValidationError(validation.message);
                    return false;
                }
                $(".save_prelodaer").show();
            });

            $("#cronday").on('change', function(){
                var cday = $(this).find('option:selected').val();
                $("#cronlength").val(cday);
            })

        })
        
        $(document).on('click', "#autoGenerate", function() {
            var userid = "{{ getUserId() }}";
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "{{ route('Aajax.autoGenerate') }}",
                data: {userid: userid},
                success: function(res) {

                    var randomNum = Math.floor(Math.random() * 6) + 3;

                    for(var i=1; i<=randomNum; i++)
                    {
                        var randomSubId = res.subid[Math.floor(Math.random() * res.subid.length)];
                        $("#subid_"+randomSubId).attr('selected', 'selected');
                        $('.selectpicker').selectpicker('refresh');
                    }
                }
            });
        });

        $(document).on('click', "#clearGenerate", function() {

            $('#cronsubcheck option:selected').removeAttr('selected');
            $('.selectpicker').selectpicker('refresh');
        });

        $(document).on('click', "#autoGenerate_dl", function() {

            var length    = Math.floor(Math.random() * 30) + 1;
            var frequency = Math.floor(Math.random() * 3) + 1;

            $("#frequencyDiv").find('option:selected').prop("selected", false);
            $("#selectFreq select").val(frequency);
            $("#cronlength").val(length);

            if(frequency == 2) {
                $("#freq1").attr("style", "display : block");
                $("#freq2").attr("style", "display : none");
            }
            else if(frequency == 3) {
                $("#freq1").attr("style", "display : block");
                $("#freq2").attr("style", "display : block");
            }
            else if(frequency == 1) {
                $("#freq1").attr("style", "display : none");
                $("#freq2").attr("style", "display : none");
            }
        });

        $(document).on('click', "#clearGenerate_dl", function() {

            $('#cronlength').val('');
            $("#flatpickr-time").val("");
            $("#flatpickr-time1").val("");
            // $time2.clear();
            // $time3.clear();
            $("#freq1").hide();
            $("#freq2").hide();
            $("#frequencyDiv").find('option:selected').prop("selected", false);
            $("#selectFreq select").val(1);
        });

        $(window).scroll(function() {
            $("#bottomFixed").removeClass("some_class");
            if(Math.ceil($(window).scrollTop() + $(window).height()) == $(document).height()) {
                //you are at bottom
                $("#bottomFixed").addClass("some_class");
            }
        });

        $( "#preview_save" ).click(function() {

            var email = $('input#pre_email').val();

            // Check if the input is blank
            if (email.trim() === '') {
                $( ".modal_preloader" ).show();
                $( ".loaded_data" ).fadeOut();
            }

            // Check if '@' and '.' exist in the email
            if (email.includes('@') && email.includes('.')) {
                $( ".modal_preloader" ).show();
                $( ".loaded_data" ).fadeOut();
            }



        });
        
        $(function ($) {
            today.setHours(0,0,0,0);

            $("#flatpickr-time").flatpickr({
                enableTime: true,
                noCalendar: true,
                time_24hr: true,
                dateFormat: "H:i",
                onClose: function(selectedDates, dateStr, instance) {
                    checkcron();
                }
            });
            $("#flatpickr-time1").flatpickr({
                enableTime: true,
                noCalendar: true,
                time_24hr: true,
                dateFormat: "H:i",
                onClose: function(selectedDates, dateStr, instance) {
                    checkcron();
                }
            });

            let initialTimezone = $('#cronusercheck').children("option:selected").data('timezoneid');
            if (initialTimezone) {
                $("#timezone_id").val(initialTimezone);
            }

            $('#cronusercheck').change(function(){
                $(".timeZoneAlert").text("")
                var sTimezone = $(this).children("option:selected").data('timezoneid');
                $("#timezone_select").val(sTimezone).trigger('change');
                $("#timezone_id").val(sTimezone);
            })
            $('#timezone_select').change(function(){
                getExicutableTime($(this).val())
                $("#timezone_id").val($(this).val());
            })
        });

        function cronpreview() {
            const validation = CronValidation.validate();
            if (!validation.isValid) {
                showValidationError(validation.message);
                return;
            }

            $('.modal_preloader').show();
            $('.loaded_data').hide();

            $("#preview-modal").modal("show");
            var postdata = $('#crondata').serialize();

            $.ajax({
                type: "GET",
                cache: false,
                url: "{{ route('cron.cronPreview') }}",
                data: postdata,
                async: false,
                datatype: "json",
                success: function(res) {
                    (res.response.due_power) ? $("#pre_dp").attr("checked", true).val(1) : $("#pre_dp").attr("checked", false);
                    (res.response.customer_link) ? $("#pre_cl").attr("checked", true).val(1) : $("#pre_cl").attr("checked", false);
                    (res.response.cal_type) ? $("#pre_cal_type").attr("checked", true).val(1) : $("#pre_cal_type").attr("checked", false);

                    (res.response.end_email_therapist) ? $("#pre_eet").prop("checked", true).val(1) : $("#pre_eet").prop("checked", false);
                    if(res.response.due_power){
                        $('.pre_smaildue').show();
                        $('.pre_emaildue').show();

                        if (!$(".pre_semail").hasClass("hide")) {
                            $('.pre_semail').hide();
                        }
                        if (!$(".pre_email").hasClass("hide")) {
                            $('.pre_email').hide();
                        }
                        (res.response.start_email_duepower) ? $("#pre_smd1x").prop("checked", true).val(1) : $("#pre_smd1x").prop("checked", false);
                        (res.response.end_email_duepower) ? $("#pre_emd1x").prop("checked", true).val(1) : $("#pre_emd1x").prop("checked", false);
                    }else{
                        $('.pre_semail').show();
                        $('.pre_email').show();
                        if (!$(".pre_smaildue").hasClass("hide")) {
                            $('.pre_smaildue').hide();
                        }
                        if (!$(".pre_emaildue").hasClass("hide")) {
                            $('.pre_emaildue').hide();
                        }
                        (res.response.start_email) ? $("#pre_se").prop("checked", true).val(1) : $("#pre_se").prop("checked", false);
                        (res.response.end_email) ? $("#pre_ee").prop("checked", true).val(1) : $("#pre_ee").prop("checked", false);
                    }

                    (res.response.pdf_export) ? $("#pre_pdf").attr("checked", true).val(1) : $("#pre_pdf").attr("checked", false);
                    (res.response.calculation_next_day) ? $("#pre_cnd").attr("checked", true).val(1) : $("#pre_cnd").attr("checked", false);
                    (res.response.causes) ? $("#pre_cau").attr("checked", true).val(1) : $("#pre_cau").attr("checked", false);
                    (res.response.medium) ? $("#pre_mid").attr("checked", true).val(1) : $("#pre_mid").attr("checked", false);
                    (res.response.tipp) ? $("#pre_tip").attr("checked", true).val(1) : $("#pre_tip").attr("checked", false);
                    (res.response.fixed_time) ? $("#pre_atc").attr("checked", true).val(1) : $("#pre_atc").attr("checked", false);
                    (res.response.ra_status) ? $("#pre_ra_status").attr("checked", true).val(1) : $("#pre_ra_status").attr("checked", false);
                    (res.response.pdf_status) ? $("#pre_pdf_status").attr("checked", true).val(1) : $("#pre_pdf_status").attr("checked", false);
                    (res.response.topic == null) ? "" : $("#pre_topic").html(res.response.topic);
                    (res.response.client_note == null) ? "" : $("#pre_client_note").html(res.response.client_note);
                    (res.response.note == null) ? "" : $("#pre_note").html(res.response.note);

                    let name = res.ures.first_name + " " + res.ures.last_name;
                    $("#pre_name").html(name);
                    $("#pre_time").html(res.others.start_time);
                    $("#pre_user").html(name);
                    $("#pre_dob").html(res.others.dob);
                    $("#pre_pob").html(res.ures.gebort);
                    $("#pre_email").val(res.ures.cron_email);
                    $("#pre_stime").html(res.others.start_time);
                    $("#pre_status").val(res.others.preview);
                    $("#pre_img").attr('src', res.predata.img_src);

                    if(res.response.causes == "on") $("#causesid").val(res.causesid);
                    if(res.response.medium == "on") $("#mediumid").val(res.mediumid);
                    if(res.response.tipp == "on") $("#tippid").val(res.tippid);

                    let preCnt = 0;
                    $.each(res.predata, function(key, value) {
                        let index = preCnt + 1;
                        $("#pre_val"+index).val(value);
                        preCnt++;
                    });

                    let analyses = ""; let analysis = "";
                    $.each(res.anaPreview, function(key, value) {
                        analyses += value.sub_name + "<br>";
                        if(res.others.preview != 1){
                            analysis += "<strong>" + value.sub_name + "</strong><br>";
                            $.each(value.analyses, function(keys, values) {
                                result = display(values.price);
                                if(values.type == 6) {
                                    analysis += result +" - "+ values.ana_name+ "<br>";
                                } else {
                                    analysis += result +" - "+values.ana_val+"% "+ values.ana_name+ "<br>";
                                }
                                if(values.type == 0) {
                                    (values.causes != null && res.response.causes) ? analysis += "{{trans('action.causes')}} - " + values.causes + "<br/>" : "";
                                    (values.medium != null && res.response.medium) ? analysis += "{{trans('action.medium')}} - " + values.medium + "<br/>" : "";
                                    (values.tipp != null && res.response.tipp) ? analysis += "{{trans('action.tipp')}} - " + values.tipp + "<br/>" : "";
                                }
                            });
                        }
                    });

                    (res.others.preview == 0) ? $("#pre_analyses").html(analysis) : $("#pre_title").attr("style", "display: none");
                    $("#pre_sub").html(analyses);
                    $('.modal_preloader').hide();
                    $('.loaded_data').show();
                }
            });
        }
        
        function display (seconds) {

            const format = val => `0${Math.floor(val)}`.slice(-2)
            const hours = seconds / 3600
            const minutes = (seconds % 3600) / 60

            return [minutes, seconds % 60].map(format).join(':')
        }

        function error(mesasge1,mesasge2){
            Swal.fire({
            title: mesasge1,
            text: mesasge2,
            icon: 'warning',
            showCancelButton: false,
            confirmButtonColor: '#FFB505',
            cancelButtonColor: '#d33',
            cancelButtonText:  '',
            confirmButtonText: 'ok',
            });
        }
        
        function getExicutableTime(timezone){
            if(__getReqClose) __getReqClose.abort()
            
            if(!timezone || !timezone.length) return $('.flatpickr-input').not($('[name=time2]')).prop('disabled', true).val('')
            let url = "{{ route('cron.get-date-time') }}"
            __getReqClose = $.get(url,{ timezone: timezone }, function (resp) {
                if(resp.success){
                    $(".timeZoneAlert").text(resp.timeAlert)
                    $(".start_time_duepower, #flatpickr-datetime1, #flatpickr-datetime2").val(''); // Clear the input field's value
                    
                    flatpickr("#flatpickr-datetime, #flatpickr-datetime1, #flatpickr-datetime2", {
                        enableTime: true,
                        dateFormat: resp.format,
                        time_24hr: true,
                        defaultDate: resp.setDefault,
                        minDate: resp.minDate
                    });
                }
            })
        }
        
        function checkcron(e) {
            var duepow_dt = $("#flatpickr-datetime").val();
            var single_dt = $("#flatpickr-datetime2").val();
            var time2 = $("#flatpickr-time").val();
            var time3 = $("#flatpickr-time1").val();
            var id = $("#cronusercheck").find(":selected").val();
            var user = (userid == 0) ? id : userid;
            var checkduePower = $('#cronDuePower').is(":checked");

            $.ajax({
                type: "POST",
                dataType: "json",
                url: "{{ route('Aajax.check_cron') }}",
                data: {userid: user, duepow: duepow_dt, single: single_dt, time2: time2, time3: time3, cdp: checkduePower},
                success: function(res) {
                    if(res.status == true){
                        toastr.error(res.message);
                        if (e && e.target) {
                            $(e.target).addClass('from-input-error');
                        }
                        $('#bottomFixed button').prop('disabled', true);
                    } else {
                        if (e && e.target) {
                            $(e.target).removeClass('from-input-error');
                        }
                        $('#bottomFixed button').prop('disabled', false);
                    }
                }
            });
        }

    </script>

@endsection
