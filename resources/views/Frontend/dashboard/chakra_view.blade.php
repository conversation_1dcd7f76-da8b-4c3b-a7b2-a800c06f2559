@extends('Frontend.partials.layout')
@php
    $cartData = getCartDataInstance(1);
    $user = $data['user'];
@endphp

@section('content')
{{-- main section --}}
<section class="chakra">
    {{-- analysis header --}}
    <div class="row">
        <div class="col-md-4">
            <div class="analysis-content-header d-flex align-items-center">
                <div class="logo">
                    <i class="fas fa-home primary-color"></i>
                </div>
                <div class="heading primary-color">
                    <h2>{{ ucfirst($data['subName']) }}</h2>
                    <p>{{ ucfirst($data['proName']) }}</p>
                </div>
            </div>
        </div>
        {{-- ********* Top Slide Panel ************ --}}
        @include('Frontend.partials.includes.layout-top')
    </div>
    {{-- /analysis header --}}

    {{-- analhsis body --}}
    <div class="mt-2">
        <div class="col-md-12 chakra-contents-container">
            {{-- pagination --}}
            @include('Frontend.partials.includes.product-paginate-number')
            <div class="card">
                {{-- card charka header --}}
                @if($user->useroption->ran_ana == 1)
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <span class="d-inline-block mr-3">Musteranalyse / Momentanalyse</span>
                            <div class="d-flex align-items-center">
                                <label class="switcher switcher-lg switcher-success m-0">
                                    <input type="checkbox" id="mysomeSwitchOptionDefault07" class="switcher-input" @if($analyses[0]['ranValStatus'] == 1){{ "checked" }} @endif onclick="chakra_calculation({{ json_encode($anaids) }}, {{ $analyses[0]['poolid'] }}, {{ $data['subid']}}, {{ $data['proid'] }}, 'sub')">
                                    <span class="switcher-indicator">
                                        <span class="switcher-yes"></span>
                                        <span class="switcher-no"></span>
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                @endif

                {{-- *********** Chakra Body ************ --}}
                <div class="card-body chakra-body">
                    <div class="chakra-img text-center">
                        <img src="{!! asset('images/cakra-new.png') !!}" style="height: 100%;" alt="energetisch">
                    </div>
                    <div class="chakra-content">
                        {{-- *********** Chakra Content List-item ************ --}}
                        <div class="chakra-content-list">
                            <ul class="d-flex flex-column">
                                @foreach($analyses as $key => $print)
                                @php
                                    if($bodyimages) $bodyimg = array_search($print['anaid'],$bodyimages,true);
                                    else $bodyimg = "";
                                    if($mentalimages) $mentalimg = array_search($print['anaid'],$mentalimages,true);
                                    else $mentalimg = "";
                                @endphp
                                @if($key%2 == 0)
                                <li class="list-one">
                                    <div class="d-flex align-items-center">
                                        <div class="d-flex align-items-center mr-2 position-relative">
                                            <p class="mb-0 mr-2 chakra-list-heading" data-toggle="tooltip" data-placement="bottom" title="{{ e($print['anaName']) }}">{{ $print['anaName'] }}</p>
                                            @if(!empty($print['desc']) || !empty($print['bodyDesc']) || !empty($print['mentalDesc']) || !empty($bodyimg) || !empty($mentalimg) || !empty($print['desc_img']))
                                            <span class="d-block info" id="ch-info{{ $print['anaid'] }}" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                <i class="fas fa-info-circle"></i>
                                            </span>
                                            @endif

                                            <div id="popover-content{{ $print['anaid'] }}" class="hide">
                                                @if(!empty($print['title']))
                                                    <h3>{!! $print['title'] !!}</h3>
                                                    <hr>
                                                @endif

                                                @if(!empty($print['desc']))
                                                    <h6>{{__('action.main_desc')}}</h6>
                                                    <p>{!! $print['desc'] !!}</p>

                                                @endif
                                                @if(isset($print['desc_img']))
                                                    <img src="{{ asset('/storage/analyse/images/description') }}/{{ $print['desc_img'] }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-desc" data-type="desc" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                @endif
                                                <hr>

                                                @if(!empty($print['bodyDesc']) || !empty($bodyimg))
                                                    <h6>{{__('action.body')}}</h6>
                                                @endif
                                                @if(!empty($print['bodyDesc']))
                                                    <p>{!! $print['bodyDesc'] !!}</p>
                                                @endif
                                                @if($bodyimg)
                                                    <img src="{{ asset('/storage/analyse/images/body') }}/{{ $bodyimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-body" data-type="body" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                @endif
                                                <hr>

                                                @if(!empty($print['mentalDesc']) || !empty($mentalimg))
                                                    <h6>{{__('action.mental')}}</h6>
                                                @endif
                                                @if(!empty($print['mentalDesc']))
                                                    <p>{!! $print['mentalDesc'] !!}</p>
                                                @endif
                                                @if($mentalimg)
                                                    <img src="{{ asset('/storage/analyse/images/mental') }}/{{ $mentalimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-mental" data-type="mental" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                @endif
                                            </div>

                                        </div>
                                        <div
                                            class="nav-item d-block text-big font-weight-light line-height-1 mr-1 primary-color">
                                            |
                                        </div>
                                        <div class="left-icon mx-2 d-none text-center d-lg-flex align-items-center">
                                            <span class="mr-3"><i class="fas fa-male"
                                                    style="{{ $print['maleColor'] }}"></i></span>
                                            <span class=""><i class="fas fa-heart"
                                                    style="{{ $print['heartColor'] }}"></i></span>
                                        </div>
                                        <div
                                            class="nav-item d-none d-lg-block text-big font-weight-light line-height-1 mr-1 ml-1 primary-color">
                                            |
                                        </div>
                                        <div
                                            class="right-icon d-none align-items-center d-lg-flex flex-column justify-content-center mx-1">
                                            @if($print['anaVal'] < $print['beergod']) <span class="f-18 m-minus"><i
                                                    class="fas fa-sort-up"></i></span>
                                                <span class="f-18"><i class="fas fa-sort-down"></i></span>
                                                @endif
                                                @if($print['anaVal'] == $print['beergod'])
                                                <span class="f-18 m-minus"><i class="fas fa-sort-up"
                                                        style="color:#D3D3D3"></i></span>
                                                <span class="f-18"><i class="fas fa-sort-down"
                                                        style="color:#D3D3D3"></i></span>
                                                @endif
                                                @if($print['anaVal'] > $print['beergod'])
                                                <span class="f-18 m-minus"><i class="fas fa-sort-up"
                                                        style="color:#D3D3D3"></i></span>
                                                <span class="f-18"><i class="fas fa-sort-down"
                                                        style="color:#888"></i></span>
                                                @endif
                                        </div>
                                        <div
                                            class="nav-item d-none d-lg-block text-big font-weight-light line-height-1 mr-1 ml-1 primary-color">
                                            |
                                        </div>

                                        <div class="dropright mx-1">
                                            <button type="button" class="btn btn-default px-2" data-display="static" data-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div class="dropdown-menu p-0 dropmenu-left">
                                                <div class="dropdown-submenu d-block d-lg-none">
                                                    <div class="d-flex align-items-center">
                                                        <p
                                                            class="dropdown-item d-flex flex-column justify-content-center m-0">
                                                            @if($print['anaVal'] < $print['beergod']) <span
                                                                class="f-18 m-minus"><i
                                                                    class="fas fa-sort-up"></i></span>
                                                                <span class="f-18"><i
                                                                        class="fas fa-sort-down"></i></span>
                                                                @endif
                                                                @if($print['anaVal'] == $print['beergod'])
                                                                <span class="f-18 m-minus"><i class="fas fa-sort-up"
                                                                        style="color:#D3D3D3"></i></span>
                                                                <span class="f-18"><i class="fas fa-sort-down"
                                                                        style="color:#D3D3D3"></i></span>
                                                                @endif
                                                                @if($print['anaVal'] > $print['beergod'])
                                                                <span class="f-18 m-minus"><i class="fas fa-sort-up"
                                                                        style="color:#D3D3D3"></i></span>
                                                                <span class="f-18"><i class="fas fa-sort-down"
                                                                        style="color:#888"></i></span>
                                                                @endif
                                                        </p>
                                                        <div
                                                            class="nav-item d-block text-big font-weight-light line-height-1 mr-1 ml-1 primary-color">
                                                            |
                                                        </div>
                                                        <p class="dropdown-item d-flex m-0">
                                                            <span class="mr-3"><i class="fas fa-male"
                                                                    style="{{ $print['maleColor'] }}"></i></span>
                                                            <span class=""><i class="fas fa-heart"
                                                                    style="{{ $print['heartColor'] }}"></i></span>
                                                        </p>
                                                    </div>
                                                </div>

                                                <div class="dropdown-divider d-md-block d-lg-none"></div>
                                                <div class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" data-display="static" data-toggle="dropdown"
                                                        href="#">{{__('action.causes')}}</a>
                                                    <ul class="dropdown-menu p-0 dropmenu-left">
                                                        <div class="ch-cntn chakra-urs">
                                                            @php
                                                                $result = 0;
                                                                foreach ($cartData as $_cart) if($_cart->options->causes_id == $print['causes']?->id && $_cart->options->type == 'Causes') $result = true;
                                                                // dd();
                                                            @endphp
                                                            <div class="pt-2">

                                                                <p class="d-flex justify-content-between align-items-center m-0 cartPluse{{$print['causes']?->id}}">
                                                                    <span class="analysis-sample-list-content-heading">{{ $print['causes']->title ?? '' }}</span>
                                                                        {{-- <a href="javascript:void(0)" onclick="addtocartCMT({{ $print['anaid'] }},'{{$print['causes']->title}}',{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},{{ $print['causes']?->id }},'Causes')">
                                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                        </a> --}}
                                                                        @if ($result == true)
                                                                            <div class="temp_{{ $print['causes']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)">
                                                                                    <i class="fas fa-check-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['causes']?->id  ?? ''}}"></div>
                                                                        @else
                                                                            <div class="temp_{{ $print['causes']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}',{{ $print['medium']?->id ?? ''}},{{ $print['tipp']?->id ?? ''}},'{{ $print['anaColor'] }}','Causes')">
                                                                                    <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['causes']?->id  ?? ''}}"></div>
                                                                        @endif


                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-2">
                                                                <p>{{ $print['causes']->description ?? ''}}</p>
                                                            </div>
                                                        </div>

                                                    </ul>
                                                </div>
                                                <div class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" data-display="static" data-toggle="dropdown" href="#">{{__('action.medium')}}</a>
                                                    <ul class="dropdown-menu p-0 dropmenu-left">
                                                        <div class="ch-cntn chakra-mitt">
                                                            @php
                                                                $result = 0;
                                                                foreach ($cartData as $_cart) if($_cart->options->medium_id == $print['medium']?->id && $_cart->options->type == 'Medium') $result = true;
                                                            @endphp
                                                            <div class="pt-2">
                                                                <p class="d-flex justify-content-between align-items-center m-0 cartPluse{{$print['medium']?->id}}">
                                                                    <span class="analysis-sample-list-content-heading">{{ $print['medium']->title ?? '' }}</span>
                                                                    @if ($result == true)
                                                                        <div class="temp_{{ $print['medium']?->id ?? ''}} temp_dis" id="">
                                                                            <a href="javascript:void(0)">
                                                                                <i class="fas fa-check-circle primary-color f-18"></i>
                                                                            </a>
                                                                        </div>
                                                                        <div class="showTemp_{{ $print['medium']?->id ?? ''}} temp_dis"></div>
                                                                    @else
                                                                        <div class="temp_{{ $print['medium']?->id ?? ''}} temp_dis" id="">
                                                                            <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}',{{ $print['medium']?->id ?? ''}},{{ $print['tipp']?->id ?? ''}},'{{ $print['anaColor'] }}','Medium')">
                                                                                <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                            </a>
                                                                        </div>
                                                                        <div class="showTemp_{{ $print['medium']?->id ?? ''}}"></div>
                                                                    @endif

                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-2">
                                                                <p> {{ $print['medium']->description ?? ''}} </p>
                                                            </div>
                                                        </div>
                                                    </ul>
                                                </div>
                                                <div class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" data-display="static" data-toggle="dropdown"
                                                        href="#">{{__('action.tipp')}}</a>
                                                    <ul class="dropdown-menu p-0 dropmenu-left">
                                                        <div class="ch-cntn chakra-tipp">
                                                            @php
                                                                $result = 0;
                                                                foreach ($cartData as $_cart) if($_cart->options->tipp_id == $print['tipp']?->id && $_cart->name == 'Tipp') $result = true;
                                                            @endphp
                                                            <div class="pt-2">
                                                                <p class="d-flex justify-content-between align-items-center m-0 cartPluse{{$print['tipp']?->id}}">
                                                                    <span class="analysis-sample-list-content-heading">{{ $print['tipp']->title ?? '' }}</span>
                                                                        @if ($result == true)
                                                                            <div class="temp_{{ $print['tipp']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)">
                                                                                    <i class="fas fa-check-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['tipp']?->id ?? ''}}"></div>
                                                                        @else
                                                                            <div class="temp_{{ $print['tipp']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}',{{ $print['medium']?->id ?? ''}},{{ $print['tipp']?->id ?? ''}},'{{ $print['anaColor'] }}','Tipp')">
                                                                                    <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['tipp']?->id ?? ''}}"></div>
                                                                        @endif
                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-2">
                                                                <p>{!! $print['tipp']->description ?? '' !!}</p>
                                                            </div>
                                                        </div>

                                                    </ul>
                                                </div>

                                            </div>
                                        </div>
                                        <div
                                            class="nav-item d-block text-big font-weight-light line-height-1 mr-1 ml-1 primary-color">
                                            |
                                        </div>
                                        <div>
                                            @php
                                                $result = 0;
                                                foreach ($cartData as $_cart) if($_cart->options->analysisID == $print['anaid'] && $_cart->options->type == 'Analysis') $result = true;
                                            @endphp

                                            <span class="d-inline-block cartPluse{{ $print['anaid'] ?? ''}} mt-1 mr-1" id="">

                                                @if ($result == true)
                                                    <div class="temp_{{ $print['anaid'] ?? ''}}" id="">
                                                        <a href="javascript:void(0)">
                                                            <i class="fas fa-check-circle primary-color f-18"></i>
                                                        </a>
                                                    </div>
                                                    <div class="showTemp_{{ $print['anaid'] ?? ''}}"></div>
                                                @else
                                                    <div class="temp_{{ $print['anaid'] ?? ''}}" id="">
                                                        <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,'{{ 'sub-'.$data['subid'] }}',{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}',{{ $print['medium']?->id ?? ''}},{{ $print['tipp']?->id ?? ''}},'{{ $print['anaColor'] }}','Analysis')">
                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                        </a>
                                                    </div>
                                                    <div class="showTemp_{{ $print['anaid'] ?? ''}}"></div>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                    <div class="progress mt-1 relative-mt">
                                        <div class="progress-bar" id="chakVal_{{ $print['anaid'] }}" role="progressbar" aria-valuenow="{{ $print['anaVal'] }}" aria-valuemin="0" aria-valuemax="100" style="max-width: {{ $print['anaVal'] }}%; background-color: {{ $print['anaColor'] }}">
                                            <span class="title" id="chaktxtVal_{{ $print['anaid'] }}" >{{ $print['anaVal'] }}%</span>
                                        </div>
                                    </div>
                                </li>
                                @elseif($key%2 == 1)
                                <li class="align-self-end list-two">
                                    <div class="d-flex align-items-center">
                                        <div class="d-flex align-items-center mr-2 position-relative">
                                            <p class="mb-0 mr-2 chakra-list-heading" data-toggle="tooltip" data-placement="bottom"
                                                title="{{ e($print['anaName']) }}">{{ $print['anaName'] }}</p>
                                            @if(!empty($print['desc']) || !empty($print['bodyDesc']) || !empty($print['mentalDesc']) || !empty($bodyimg) || !empty($mentalimg) || !empty($print['desc_img']))
                                            <span class="d-block info" id="ch-info{{ $print['anaid'] }}" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content=""><i
                                                    class="fas fa-info-circle"></i></span>
                                            @endif

                                            <div id="popover-content{{ $print['anaid'] }}" class="hide">
                                                @if(!empty($print['title']))
                                                    <h3>{!! $print['title'] !!}</h3>
                                                    <hr>
                                                @endif

                                                @if(!empty($print['desc']))
                                                    <h6>{{__('action.main_desc')}}</h6>
                                                    <p>{!! $print['desc'] !!}</p>

                                                @endif
                                                @if(isset($print['desc_img']))
                                                    <img src="{{ asset('/storage/analyse/images/description') }}/{{ $print['desc_img'] }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-desc" data-type="desc" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                @endif
                                                <hr>

                                                @if(!empty($print['bodyDesc']) || !empty($bodyimg))
                                                    <h6>{{__('action.body')}}</h6>
                                                @endif
                                                @if(!empty($print['bodyDesc']))
                                                    <p>{!! $print['bodyDesc'] !!}</p>
                                                @endif
                                                @if($bodyimg)
                                                    <img src="{{ asset('/storage/analyse/images/body') }}/{{ $bodyimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-body" data-type="body" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                @endif
                                                <hr>

                                                @if(!empty($print['mentalDesc']) || !empty($mentalimg))
                                                    <h6>{{__('action.mental')}}</h6>
                                                @endif
                                                @if(!empty($print['mentalDesc']))
                                                    <p>{!! $print['mentalDesc'] !!}</p>
                                                @endif
                                                @if($mentalimg)
                                                    <img src="{{ asset('/storage/analyse/images/mental') }}/{{ $mentalimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-mental" data-type="mental" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                @endif
                                            </div>
                                        </div>
                                        <div
                                            class="nav-item d-block text-big font-weight-light line-height-1 mr-1 primary-color">
                                            |
                                        </div>
                                        <div class="left-icon mx-2 d-none text-center d-lg-flex align-items-center">
                                            <span class="mr-3 f-18"><i class="fas fa-male"
                                                    style="{{ $print['maleColor'] }}"></i></span>
                                            <span class=""><i class="fas fa-heart"
                                                    style="{{ $print['heartColor'] }}"></i></span>
                                        </div>
                                        <div
                                            class="nav-item d-none d-lg-block text-big font-weight-light line-height-1 mr-1 ml-1 primary-color">
                                            |
                                        </div>
                                        <div
                                            class="right-icon d-none align-items-center d-lg-flex flex-column justify-content-center mx-1">
                                            @if($print['anaVal'] < $print['beergod']) <span class="f-18 m-minus"><i
                                                    class="fas fa-sort-up"></i></span>
                                                <span class="f-18"><i class="fas fa-sort-down"></i></span>
                                                @endif
                                                @if($print['anaVal'] == $print['beergod'])
                                                <span class="f-18 m-minus"><i class="fas fa-sort-up"
                                                        style="color:#D3D3D3"></i></span>
                                                <span class="f-18"><i class="fas fa-sort-down"
                                                        style="color:#D3D3D3"></i></span>
                                                @endif
                                                @if($print['anaVal'] > $print['beergod'])
                                                <span class="f-18 m-minus"><i class="fas fa-sort-up"
                                                        style="color:#D3D3D3"></i></span>
                                                <span class="f-18"><i class="fas fa-sort-down"
                                                        style="color:#888"></i></span>
                                                @endif
                                        </div>
                                        <div
                                            class="nav-item d-none d-lg-block text-big font-weight-light line-height-1 mr-1 ml-1 primary-color">
                                            |
                                        </div>

                                        <div class="dropleft mx-1">
                                            <button type="button" class="btn btn-default px-2" data-display="static" data-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div class="dropdown-menu dropmenu-left p-0">
                                                <div class="dropdown-submenu d-block d-lg-none">
                                                    <div class="d-flex align-items-center">
                                                        <p
                                                            class="dropdown-item d-flex flex-column justify-content-center m-0">
                                                            @if($print['anaVal'] < $print['beergod']) <span
                                                                class="f-18 m-minus"><i
                                                                    class="fas fa-sort-up"></i></span>
                                                                <span class="f-18"><i
                                                                        class="fas fa-sort-down"></i></span>
                                                                @endif
                                                                @if($print['anaVal'] == $print['beergod'])
                                                                <span class="f-18 m-minus"><i class="fas fa-sort-up"
                                                                        style="color:#D3D3D3"></i></span>
                                                                <span class="f-18"><i class="fas fa-sort-down"
                                                                        style="color:#D3D3D3"></i></span>
                                                                @endif
                                                                @if($print['anaVal'] > $print['beergod'])
                                                                <span class="f-18 m-minus"><i class="fas fa-sort-up"
                                                                        style="color:#D3D3D3"></i></span>
                                                                <span class="f-18"><i class="fas fa-sort-down"
                                                                        style="color:#888"></i></span>
                                                                @endif
                                                        </p>
                                                        <div
                                                            class="nav-item d-block text-big font-weight-light line-height-1 mr-1 ml-1 primary-color">
                                                            |
                                                        </div>
                                                        <p class="dropdown-item d-flex m-0">
                                                            <span class="mr-3"><i class="fas fa-male"
                                                                    style="{{ $print['maleColor'] }}"></i></span>
                                                            <span class=""><i class="fas fa-heart"
                                                                    style="{{ $print['heartColor'] }}"></i></span>
                                                        </p>
                                                    </div>
                                                </div>

                                                <div class="dropdown-divider d-block d-lg-none"></div>
                                                <div class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" data-display="static" data-toggle="dropdown"
                                                        href="#">{{__('action.causes')}}</a>
                                                    <ul class="dropdown-menu dropmenu-left">
                                                        <div class="ch-cntn chakra-urs">
                                                            @php
                                                                $result = 0;
                                                                foreach ($cartData as $_cart) if($_cart->options->causes_id ==  $print['causes']?->id && $_cart->name == 'Causes') $result = true;
                                                            @endphp
                                                            <div class="pt-2">
                                                                <p class="d-flex justify-content-between align-items-center m-0 cartPluse{{$print['causes']?->id}}">
                                                                    <span class="analysis-sample-list-content-heading">{{ $print['causes']->title ?? ''}}</span>
                                                                        {{-- <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},'{{$print['causes']->title}}',{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},{{ $print['causes']?->id }},'Causes')">
                                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                        </a> --}}
                                                                        @if ($result == true)
                                                                            <div class="temp_{{ $print['causes']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)">
                                                                                    <i class="fas fa-check-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['causes']?->id ?? ''}} temp_dis"></div>
                                                                        @else
                                                                            <div class="temp_{{ $print['causes']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}',{{ $print['medium']?->id ?? ''}},{{ $print['tipp']?->id ?? ''}},'{{ $print['anaColor'] }}','Causes')">
                                                                                    <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['causes']?->id ?? ''}}"></div>
                                                                        @endif
                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-2">
                                                                <p>{!! $print['causes']->description ?? '' !!}</p>
                                                            </div>
                                                        </div>
                                                    </ul>
                                                </div>
                                                <div class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" data-display="static" data-toggle="dropdown"
                                                        href="#">{{__('action.medium')}}</a>
                                                    <ul class="dropdown-menu dropmenu-left">
                                                        <div class="ch-cntn chakra-mitt">
                                                            @php
                                                                $result = 0;
                                                                foreach ($cartData as $_cart) if($_cart->options->medium_id == $print['medium']?->id && $_cart->name == 'Medium') $result = true;
                                                            @endphp
                                                            <div class="pt-2">
                                                                <p
                                                                    class="d-flex justify-content-between align-items-center m-0 cartPluse{{$print['medium']?->id}}">
                                                                    <span
                                                                        class="analysis-sample-list-content-heading">{{ $print['medium']->title ?? ''}}</span>

                                                                        @if ($result == true)
                                                                            <div class="temp_{{ $print['medium']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)">
                                                                                    <i class="fas fa-check-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>                                                                         </div>
                                                                            <div class="showTemp_{{ $print['medium']?->id ?? ''}}"></div>
                                                                        @else
                                                                            <div class="temp_{{ $print['medium']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}',{{ $print['medium']?->id ?? ''}},{{ $print['tipp']?->id ?? ''}},'{{ $print['anaColor'] }}','Medium')">
                                                                                    <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['medium']?->id ?? ''}}"></div>
                                                                        @endif
                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-2">
                                                                <p> {!! $print['medium']->description ?? '' !!} </p>
                                                            </div>
                                                        </div>
                                                    </ul>
                                                </div>
                                                <div class="dropdown-submenu">
                                                    <a class="dropdown-item dropdown-toggle" data-display="static" data-toggle="dropdown"
                                                        href="#">{{__('action.tipp')}}</a>
                                                    <ul class="dropdown-menu dropmenu-left">
                                                        <div class="ch-cntn chakra-tipp">
                                                            @php
                                                                $result = 0;
                                                                foreach ($cartData as $_cart) if($_cart->options->tipp_id== $print['tipp']?->id && $_cart->name == 'Tipp') $result = true;
                                                            @endphp
                                                            <div class="pt-2">
                                                                <p
                                                                    class="d-flex justify-content-between align-items-center m-0 cartPluse{{$print['tipp']?->id}}">
                                                                    <span class="analysis-sample-list-content-heading">{{ $print['tipp']->title ?? ''}}</span>
                                                                        {{-- <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},'{{$print['tipp']->title}}',{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},{{ $print['tipp']?->id }},'Tipp')">
                                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                        </a> --}}
                                                                        @if ($result == true)
                                                                            <div class="temp_{{ $print['tipp']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)">
                                                                                    <i class="fas fa-check-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['tipp']?->id ?? ''}}"></div>
                                                                        @else
                                                                            <div class="temp_{{ $print['tipp']?->id ?? ''}} temp_dis" id="">
                                                                                <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}',{{ $print['medium']?->id ?? ''}},{{ $print['tipp']?->id ?? ''}},'{{ $print['anaColor'] }}','Tipp')">
                                                                                    <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                                </a>
                                                                            </div>
                                                                            <div class="showTemp_{{ $print['tipp']?->id ?? ''}}"></div>
                                                                        @endif
                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-2">
                                                                <p>{!! $print['tipp']->description ?? '' !!}</p>
                                                            </div>
                                                        </div>

                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="nav-item d-block text-big font-weight-light line-height-1 mr-1 ml-1 primary-color">
                                            |
                                        </div>
                                        <div>
                                        <span class="d-inline-block cartPluse{{ $print['anaid'] }} mt-1 mr-1" id="">
                                            @php
                                                $result = 0;
                                                foreach ($cartData as $_cart) if($_cart->options->analysisID == $print['anaid'] && $_cart->name == 'Analysis') $result = true;
                                            @endphp
                                            @if ($result == true)
                                            <div class="temp_{{ $print['anaid'] ?? '' }}" id="">
                                                <a href="javascript:void(0)">
                                                    <i class="fas fa-check-circle primary-color f-18"></i>
                                                </a>
                                            </div>
                                            <div class="showTemp_{{ $print['anaid'] ?? ''}}"></div>
                                            @else
                                            <div class="temp_{{ $print['anaid'] ?? ''}}" id="">
                                                <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,'{{ 'sub-'.$data['subid'] }}',{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}',{{ $print['medium']?->id ?? ''}},{{ $print['tipp']?->id ?? ''}},'{{ $print['anaColor'] }}','Analysis')">
                                                    <i class="fas fa-plus-circle primary-color f-18"></i>
                                                </a>
                                            </div>
                                            <div class="showTemp_{{ $print['anaid'] ?? ''}}"></div>
                                            @endif
                                            </span>
                                        </div>
                                    </div>
                                    <div class="progress mt-3">
                                        <div class="progress-bar" id="chakVal_{{ $print['anaid'] }}" role="progressbar" aria-valuenow="{{ $print['anaVal'] }}" aria-valuemin="0" aria-valuemax="100" style="max-width: {{ $print['anaVal'] }}%; background-color: {{ $print['anaColor'] }}">
                                            <span class="title" id="chaktxtVal_{{ $print['anaid'] }}">{{ $print['anaVal'] }}%</span>
                                        </div>
                                    </div>
                                </li>
                                @endif
                                    <script>
                                        $(function ($) {
                                            $('#ch-info{{ $print['anaid'] }}').popover({
                                                html:true,
                                                content: function(){
                                                    return $ ('#popover-content{{ $print["anaid"] }}').html();
                                                }
                                            });
                                        });
                                        $(function(){
                                            $(document).on('click',".popover-showimg", function () {
                                                let type = $(this).data('type')
                                                let id = $(this).data('id')
                                                // Get the modal
                                                var modal = document.getElementById("analysisPopup_Modal");

                                                // Get the image and insert it inside the modal - use its "alt" text as a caption
                                                var img = document.getElementById(`popover-img${id}-${type}`);
                                                var modalImg = document.getElementById("imgShow");

                                                modal.style.display = "block";
                                                modalImg.src = this.src;
                                            });

                                            $(document).on('click',"#modal-close", function () {
                                                var modal = document.getElementById("analysisPopup_Modal");
                                                modal.style.display = "none";
                                            })
                                        });
                                    </script>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {{-- /card --}}
        </div>
    </div>
    {{-- /analhsis body --}}
    {{-- ********* Right Slide Panel ************ --}}
    @livewire('dashboard.right-cart-modal', ['key' => 'right-cart-modal'])

    <div id="analysisPopup_Modal" class="modal analysisPopup_Modal">
        <span class="close" id="modal-close">&times;</span>
        <img class="modal-content" id="imgShow">
    </div>
</section>
{{-- /main section --}}
@endsection

@section('scripts')
    <script>

        var longday = 0;

        function chakra_calculation(anaid, poolid, subid, proid, type) {
            if (poolid == '') poolid = 0;
            var check = ($('#mysomeSwitchOptionDefault07').is(':checked'));
            if (check == true || check == false) {
                $.each(anaid, function(key, value) {
                    var dataString = 'subid=' + subid + '&proid=' + proid + '&anaid=' + value + '&poolid=' + poolid + '&type=' + type + '&is_ck=' + check;
                    if (check == true){
                        $.ajax({
                            type: "POST",
                            url: "{{ route('Aajax.saveCalculation') }}",
                            data: dataString,
                            success: function(data) {
                                $("#chakVal_"+value).attr("aria-valuenow", data.record.ma);
                                $("#chakVal_"+value).attr("style", "max-width: "+data.record.ma+"%; background-color: "+data.record.color);
                                $("#chaktxtVal_"+value).html(data.record.ma+"%");
                            }
                        });
                    } else {
                        $.ajax({
                            type: "POST",
                            url: "{{ route('Aajax.saveCalculation') }}",
                            data: dataString,
                            success: function(data) {
                                $("#chakVal_"+value).attr("aria-valuenow", data.record.ma);
                                $("#chakVal_"+value).attr("style", "max-width: "+data.record.ma+"%; background-color: "+data.record.color);
                                $("#chaktxtVal_"+value).html(data.record.ma+"%");
                            }

                        });
                    }
                });
            }
        }

        $("#longday").on('change', function(){

            var select = $(this).find('option:selected');
            longday = select.val();
            var proid = "{{ request()->proid }}";
            var subid = "{{ request()->subid }}";
            var comid = "{{ request()->comid }}";
            if(comid == "")
                var url = "{{ url('/dashboard/p') }}/"+proid+"/"+subid+"/"+longday;
            else
                var url = "{{ url('/dashboard/c/p') }}/"+proid+"/"+subid+"/"+longday+"/"+comid;
            // $("#longday_link").attr("href", url);
            $("#cb-rl2").attr("data-href", url);
            window.location.href = url;
        })

        function checkLongDay(){
            if(longday < 30 ) Swal.fire({ type: 'warning', title: "{{ __('action.choose_days') }}", showConfirmButton: false, timer: 3500 });
        }

    </script>
@endsection
