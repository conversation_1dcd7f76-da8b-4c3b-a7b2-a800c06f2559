@extends('Frontend.partials.layout')

@section('styles')
    <script src="{{ asset('/vendor/js/chartjs.js') }}"></script>
@endsection

@php
    // if(auth()->user()->user_type == 0 && auth()->user()->id == getID()){
    //     $submenusList = session()->get('menuList'.getAuthId())['submenu'];
    //     $subids = [];
    //         foreach ($submenusList as $key => $sub) {
    //             if($sub['product_id'] == $data['proid'] && $sub['from'] == 2)
    //                 $subids[] = $sub['submenu_id'];
    //         }
    //         $submenus = App\Model\Menu::with(['usersubmenus' => function ($query) use($subids) {
    //             $query->whereIn('id',$subids);
    //         }])->find(request()->ownid);
    // }else
    //     $submenus = getOwnSubmenusByMenuid(request()->ownid);

    // if(!empty($submenus->usersubmenus)){
    //     $first = $submenus->usersubmenus[0]?->id;
    //     $last  = $submenus->usersubmenus[count($submenus->usersubmenus)-1]?->id;
    //     $_submenu = $submenus->usersubmenus;
    //     if(count($_submenu) > 0){
    //         $nextId = $_submenu->where('id', '>', $data['subid'])->take(1)->first()->id ?? $_submenu->first()->id;
    //         $previousId = $_submenu->where('id', '<', $data['subid'])->sortByDesc('id')->take(1)->first()->id ?? $_submenu->last()->id;
    //     }
    // }
    $cartData = getCartDataInstance(1);
@endphp

@section('content')
{{-- analysis content dashboard view --}}
<section class="analysis-content dsh-view">
    <div class="row">
        {{-- analysis content header --}}
        <div class="col-md-5 col-sm-5 col-12">
            <div class="analysis-content-header d-flex align-items-center">
                <div class="logo">
                    <i class="fas fa-home primary-color"></i>
                </div>
                <div class="heading primary-color">
                    <h2>{{ ucfirst($data['subName']) }}</h2>
                    <p>{{ ucfirst($data['proName']) }}</p>
                </div>
            </div>
        </div>
        {{-- layout-top --}}
        @include('Frontend.partials.includes.layout-top')
    </div>

    {{-- pagenation div --}}
    <div class="row mt-2">
        <div class="col-md-12">
            
            @include('Frontend.partials.includes.ownproduct-paginate-number')
            {{-- <div class="pagination-div">
                <nav class="">
                    <ul class="pagination flex-wrap justify-content-start justify-content-sm-end">
                        <li class="page-item back {{($previousId == null)? 'disabled':''}}" id="{{($previousId == null)? 'disabled':''}}">
                        <a class="page-link currentt{{$previousId}}" href="{{ (request()->comid == null)?route('dashboard.longownproduct', [$data['proid'], $previousId, request()->days]):route('dashboard.combo.longownproduct', [$data['proid'], $previousId, request()->days,request()->comid]) }}">«</a>
                        </li>
                        @if(!empty($submenus->usersubmenus))
                        @foreach($submenus->usersubmenus as $key => $sub)
                        <li class="page-item @if($data['subid'] == $sub->id){{ 'active' }} @endif">
                            <a class="page-link currentt" href="{{ request()->comid == null ? route('dashboard.longownproduct', [$data['proid'], $sub->id, request()->days]) : route('dashboard.combo.longownproduct', [$data['proid'], $sub->id, request()->days,request()->comid]) }}">{{ $key+1 }}</a>
                        </li>
                        @endforeach
                        @endif
                        <li class="page-item front {{($nextId == null)?'disabled':''}}" id="{{($nextId == null)?'disabled':''}}">
                            <a  class="page-link currentt{{$nextId}}" href="{{ request()->comid == null ? route('dashboard.longownproduct', [$data['proid'], $nextId, request()->days]) : route('dashboard.combo.longownproduct', [$data['proid'], $nextId, request()->days,request()->comid]) }}" id="@if($data['subid'] == $first){{ 'disabled' }} @endif">»</a>
                        </li>
                    </ul>
                </nav>
            </div> --}}

            <div class="analysis-content-body posts endless-pagination" data-lastpage="{{($analyses && $analyses->lastPage()==true)? $analyses->lastPage() : 1 }}" id="ok">

                <div class="row position-relative">
                    <div class="preloader" id="preload_product">
                        <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                    </div>

                    {{-- breadcum --}}
                    @if($end_date != null)
                    <div class="col-md-12 col-xl-12 col-sm-12">
                        <nav class="custom-breadcrumb mar-bot-20" aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item active text-success" aria-current="page">{{ date_change($start_date) }} - {{ date_change($end_date)}}</li>
                            </ol>
                        </nav>
                    </div>
                    @endif

                    @if(!empty($analyses))
                    @foreach($analyses as $key => $print)

                    @php
                        if($bodyimages) $bodyimg = array_search($print['anaid'],$bodyimages,true);
                        else $bodyimg = "";
                        if($mentalimages) $mentalimg = array_search($print['anaid'],$mentalimages,true);
                        else $mentalimg = "";
                    @endphp

                    <div class="col-md-4 col-xl-3">
                        <div class="card color-border-one mb-3 analysis-sample">
                            <div class="card-header analysis-sample-header">
                                <div class="analysis-sample-header-left">
                                    <p class="d-flex align-items-center m-0">
                                        <span data-toggle="tooltip" data-placement="bottom" title="{{e($print['anaName'])}}" class="analysis-title d-inline-block mr-2">{!! $print['anaName'] !!}</span>
                                        @if(!empty($print['desc']) || !empty($print['url_link']) || !empty($print['bodyDesc']) || !empty($print['mentalDesc']) || !empty($bodyimg) || !empty($mentalimg) || !empty($print['desc_img']))
                                        <span class="d-block info" id="info_{{ $print['anaid'] }}" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content=""><i class="fas fa-info-circle"></i></span>
                                        @endif
                                    </p>

                                    <div id="popover-content{{ $print['anaid'] }}" style="display:none;">
                                        @if(!empty($print['title']))
                                            <h3>{!! $print['title'] !!}</h3>
                                            <hr>
                                        @endif

                                        @if(!empty($print['desc']))
                                            <h6>{{__('action.main_desc')}}</h6>
                                            <p>{!! $print['desc'] !!}</p>

                                        @endif
                                        @if(isset($print['desc_img']))
                                            <img src="{{ asset('/storage/analyse/images/description') }}/{{ $print['desc_img'] }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-desc" data-type="desc" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                        <hr>
                                        @endif
                                        @if(!empty($print['bodyDesc']) || !empty($bodyimg))
                                            <h6>{{__('action.body')}}</h6>
                                        @endif
                                        @if(!empty($print['bodyDesc']))
                                            <p>{!! $print['bodyDesc'] !!}</p>
                                        @endif
                                        @if($bodyimg)
                                            <img src="{{ asset('storage/analyse/images/body') }}/{{ $bodyimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-body" data-type="body" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                        <hr>
                                        @endif

                                        @if(!empty($print['mentalDesc']) || !empty($mentalimg))
                                            <h6>{{__('action.mental')}}</h6>
                                        @endif
                                        @if(!empty($print['mentalDesc']))
                                            <p>{!! $print['mentalDesc'] !!}</p>
                                        @endif
                                        @if($mentalimg)
                                            <img src="{{ asset('storage/analyse/images/mental') }}/{{ $mentalimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-mental" data-type="mental" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                        <hr>
                                        @endif
                                        @if($print['url_link'] != null)
                                            <a class="btn btn-success" href="{{ $print['url_link'] ?? 'javascript:void(0)' }}" target="_blank">{{ $print['url_name'] ?? trans('action.link') }}</a>
                                        @endif
                                    </div>
                                </div>

                                <script>
                                    $(function($) {
                                        $('#info_{{ $print["anaid"] }}').popover({
                                            html: true,
                                            content: function() {
                                                $('.popover').popover('hide')
                                                return $('#popover-content{{ $print["anaid"] }}').html();
                                            }
                                        });
                                    });

                                    $(function(){
                                        $(document).on('click',".popover-showimg", function () {
                                            let type = $(this).data('type')
                                            let id = $(this).data('id')
                                            // Get the modal
                                            var modal = document.getElementById("analysisPopup_Modal");

                                            // Get the image and insert it inside the modal - use its "alt" text as a caption
                                            var img = document.getElementById(`popover-img${id}-${type}`);
                                            var modalImg = document.getElementById("imgShow");

                                            modal.style.display = "block";
                                            modalImg.src = this.src;
                                        });

                                        $(document).on('click',"#modal-close", function () {
                                            var modal = document.getElementById("analysisPopup_Modal");
                                            modal.style.display = "none";
                                        })
                                    });
                                </script>

                                <div class="analysis-sample-header-right">
                                    @if($data['user']->useroption->pattern_switch == 1)
                                    {{--<div class="mr-2">
                                        <label class="switcher switcher-sm switcher-success m-0">
                                            <input id="someSwitchOptionDefault{{ $print['anaid'] }}" type="checkbox" @if($print['ranValStatus']==1) {{ "checked" }} @endif class="switcher-input" onclick="change_calculation({{ $print['anaid'] }}, 101 , {{ $data['subid']}}, {{ $data['proid'] }}, 'own')">
                                            <span class="switcher-indicator">
                                                <span class="switcher-yes"></span>
                                                <span class="switcher-no"></span>
                                            </span>
                                        </label>
                                    </div>--}}
                                    @endif
                                        @php
                                            $result = 0;
                                            foreach ($cartData as $_cart) if($_cart->options->analysisID == $print['anaid'] && $_cart->options->type == 'Analysis') $result = true;
                                         @endphp

                                        <span class="d-inline-block cartPluse{{ $print['anaid'] }}" id="">

                                                @if ($result == true)
                                                <div class="temp_{{ $print['anaid'] }}" id="">
                                                    <a href="javascript:void(0)">
                                                        <i class="fas fa-check-circle primary-color f-18"></i>
                                                    </a>
                                                </div>
                                                <div class="showTemp_{{ $print['anaid'] }}"></div>
                                                @else
                                                <div class="temp_{{ $print['anaid'] }}" id="">
                                                    <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,'{{ 'own-'.$data['subid'] }}',{{ $data['proid'] }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Analysis',{'allred':{{ $print['anaVal_red'] }},'allgreen':{{ $print['anaVal_green'] }}})">
                                                        <i class="fas fa-plus-circle primary-color f-18"></i>
                                                    </a>
                                                </div>
                                                <div class="showTemp_{{ $print['anaid'] }}"></div>
                                                @endif
                                        </span>
                                </div>
                            </div>

                            <div class="card-body analysis-sample-body">
                                <div class="lta-leftIcon left-icon text-center d-flex flex-column justify-content-center">
                                    <span class="mb-3 male-icon">
                                        <i class="fas fa-male" style="{{ $print['maleColor'] }}"></i>
                                        <div class="male-counts"><span style="color: grey" data-toggle="tooltip" data-placement="bottom" title="{{__('action.lta_body_gray')}}">{{ $print['maleGray'] }}</span><span>&frasl;</span><span style="color: #E84E1B" data-toggle="tooltip" data-placement="bottom" title="{{__('action.lta_body_red')}}">{{ $print['maleRed'] }}</span> </div>
                                    </span>
                                    <span class="">
                                        <i class="fas fa-heart" style="{{ $print['heartColor'] }}"></i>
                                        <div class="heart-counts"><span style="color: grey" data-toggle="tooltip" data-placement="bottom" title="{{__('action.lta_heart_gray')}}">{{ $print['heartGray'] }}</span><span>&frasl;</span><span style="color: #E84E1B" data-toggle="tooltip" data-placement="bottom" title="{{__('action.lta_heart_red')}}">{{ $print['heartRed'] }}</span> </div>
                                    </span>
                                </div>

                                {{-- <div class="mid">
                                    <div class="progres" id="progress_{{ $print['anaid'] }}"></div>
                                </div> --}}
                                {{--<div class="lta-progress progress-circle-mini" style="padding: 0;width:100%;text-align: center;">
                                    <div id="morrisjs-donut{{ $print['anaid'] }}" class="morris-progressbar-mini" style="height: 150px;"></div>
                                </div>--}}

                                <div class="lta-progress-normal" style="padding: 0;text-align: center;">
                                    <canvas id="efitLTAChart{{$print['anaid']}}" width="200" height="200"></canvas><p class="percent"></p>
                                </div>

                                <script>
                                    var ro = parseInt("{{ $print['anaVal_red'] }}");
                                    var gr = parseInt("{{ $print['anaVal_green'] }}");
                                    var options = {
                                        type: 'doughnut',
                                        data: {
                                            labels: [ro, gr],
                                            datasets: [{
                                                label: '',
                                                data: [ro, gr],
                                                backgroundColor: [
                                                    'rgba(232, 78, 27 ,1)',
                                                    'rgba(47, 171, 102 ,1)',
                                                ],
                                                borderColor: [
                                                    'rgba(255, 255, 255 ,1)',
                                                    'rgba(255, 255, 255 ,1)'
                                                ],
                                                borderWidth: 5
                                            }]
                                        },
                                        options: {
                                            rotation: 1 * Math.PI,
                                            circumference: 1 * Math.PI,
                                            legend: {
                                                display: true,
                                                position: 'bottom'
                                            },
                                            tooltip: {
                                                enabled: false,
                                                position: 'average'
                                            },
                                            cutoutPercentage: 75
                                        }
                                    }

                                    var ctx1 = document.getElementById('efitLTAChart{{$print["anaid"]}}').getContext('2d');
                                    new Chart(ctx1, options);
                                </script>

                                <div class="lta-rightIcon right-icon d-flex flex-column justify-content-center">
                                    @if($print['anaVal'] < $print['beergod']) <span class=""><i class="fas fa-sort-up"></i></span>
                                        <span class=""><i class="fas fa-sort-down"></i></span>
                                        @endif
                                        @if($print['anaVal'] == $print['beergod'])
                                        <span class=""><i class="fas fa-sort-up" style="color:#D3D3D3"></i></span>
                                        <span class=""><i class="fas fa-sort-down" style="color:#D3D3D3"></i></span>
                                        @endif
                                        @if($print['anaVal'] > $print['beergod'])
                                        <span class=""><i class="fas fa-sort-up" style="color:#D3D3D3"></i></span>
                                        <span class=""><i class="fas fa-sort-down" style="color:#888"></i></span>
                                        @endif
                                </div>
                            </div>
                            @if(!empty($print['causes']) or !empty($print['medium']) or !empty($print['tipp']))
                            <div class="card-footer analysis-sample-footer">
                                <ul class="analysis-sample-list">
                                    <li>
                                        <a href="javascript:void(0)" @if(!empty($print['causes'])) onclick="saveToday({{ $data['subid'] }},{{ $print['anaid'] }},{{ $print['causes']?->id }},'Causes')" @endif><span class="urs" onclick="ursCheck('{{ $print['anaid'] }}')">{{__('action.causes')}}</span></a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0)" @if(!empty($print['medium'])) onclick="saveToday({{ $data['subid'] }},{{$print['anaid']}},{{ $print['medium']?->id }},'Medium')" @endif><span class="mit" onclick="mitCheck('{{ $print['anaid'] }}')">{{__('action.medium')}}</span></a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0)" @if(!empty($print['tipp'])) onclick="saveToday({{ $data['subid'] }},{{$print['anaid']}},{{ $print['tipp']?->id }},'Tipp')" @endif><span class="tip" onclick="tipCheck('{{ $print['anaid'] }}')">{{__('action.tipp')}}</span></a>
                                    </li>
                                </ul>
                            </div>
                            @endif
                            <div class="analysis-sample-list-des">
                                <div class="analysis-sample-list-content" id="urs-content{{$print['anaid']}}">
                                    <div class="card" id="">
                                        <div class="card-header pt-3">
                                            <p class="d-flex justify-content-between align-items-center m-0">
                                                <span class="analysis-sample-list-content-heading">{{$print['causes']->title ?? ''}}</span>
                                                {{-- <i class="fas fa-plus-circle primary-color f-18"></i> --}}
                                                {{-- <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},'{{$print['causes']->title}}',{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},{{ $print['causes']?->id }},'Causes')">
                                                <i class="fas fa-plus-circle primary-color f-18"></i>
                                                </a> --}}
                                                @php
                                                $result = 0;
                                                foreach ($cartData as $_cart) if($_cart->options->causes_id == $print['causes']?->id && $_cart->options->type == 'Causes') $result = true;
                                                // dd($result)
                                                @endphp

                                                <span class="d-inline-block cartPluse{{ $print['causes']?->id ?? ''}}" id="">

                                                    @if ($result == true)
                                                    <span class="temp_{{ $print['causes']?->id ?? ''}}" id="">
                                                        <a href="javascript:void(0)">
                                                            <i class="fas fa-check-circle primary-color f-18"></i>
                                                        </a>
                                                    </span>
                                                    <span class="showTemp_{{ $print['causes']?->id ?? ''}}"></span>
                                                    @else
                                                    <span class="temp_{{ $print['causes']?->id ?? ''}}" id="">
                                                        <a href="javascript:void(0)" @if(!empty($print['causes'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Causes',{'allred':{{ $print['anaVal_red'] }},'allgreen':{{ $print['anaVal_green'] }}})" @endif>
                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                        </a>
                                                    </span>
                                                    <span class="showTemp_{{ $print['causes']?->id ?? ''}}"></span>
                                                    @endif
                                                </span>
                                            </p>

                                        </div>
                                        <div class="card-body">
                                            <p>{{ $print['causes']->description ?? ''}}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="analysis-sample-list-content" id="mit-content{{$print['anaid']}}">
                                    <div class="card">
                                        <div class="card-header pt-3">
                                            <p class="d-flex justify-content-between align-items-center m-0">
                                                <span class="analysis-sample-list-content-heading">{{ $print['medium']->title ?? ''}}</span>
                                                @php
                                                $result = 0;
                                                foreach ($cartData as $_cart) if($_cart->options->medium_id == $print['medium']?->id && $_cart->options->type == 'Medium') $result = true;

                                                @endphp

                                                <span class="d-inline-block cartPluse{{ $print['medium']?->id ?? ''}}" id="">

                                                    @if ($result == true)
                                                    <span class="temp_{{ $print['medium']?->id ?? ''}}" id="">
                                                        <a href="javascript:void(0)">
                                                            <i class="fas fa-check-circle primary-color f-18"></i>
                                                        </a>
                                                    </span>
                                                    <span class="showTemp_{{ $print['medium']?->id ?? ''}}"></span>
                                                    @else
                                                    <span class="temp_{{ $print['medium']?->id ?? ''}}" id="">
                                                       <a href="javascript:void(0)" @if(!empty($print['medium'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Medium',{'allred':{{ $print['anaVal_red'] }},'allgreen':{{ $print['anaVal_green'] }}})" @endif>
                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                        </a>
                                                    </span>
                                                    <span class="showTemp_{{ $print['medium']?->id ?? ''}}"></span>
                                                    @endif
                                            </p>
                                        </div>
                                        <div class="card-body">
                                            <p>{{ $print['medium']->description ?? ''}}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="analysis-sample-list-content" id="tip-content{{$print['anaid']}}">
                                    <div class="card">
                                        <div class="card-header pt-3">
                                            <p class="d-flex justify-content-between align-items-center m-0">
                                                <span class="analysis-sample-list-content-heading">{{ $print['tipp']->title ?? ''}}</span>
                                                @php
                                                $result = 0;
                                                foreach ($cartData as $_cart) if($_cart->options->tipp_id == $print['tipp']?->id && $_cart->options->type == 'Tipp') $result = true;
                                                @endphp

                                                <span class="d-inline-block cartPluse{{ $print['tipp']?->id ?? ''}}" id="">

                                                    @if ($result == true)
                                                    <span class="temp_{{ $print['tipp']?->id ?? ''}}" id="">
                                                        <a href="javascript:void(0)">
                                                            <i class="fas fa-check-circle primary-color f-18"></i>
                                                        </a>
                                                    </span>
                                                    <span class="showTemp_{{ $print['tipp']?->id ?? ''}}"></span>
                                                    @else
                                                    <span class="temp_{{ $print['tipp']?->id ?? ''}}" id="">
                                                        <a href="javascript:void(0)" @if(!empty($print['tipp'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Tipp',{'allred':{{ $print['anaVal_red'] }},'allgreen':{{ $print['anaVal_green'] }}})" @endif>
                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                        </a>
                                                    </span>
                                                    <span class="showTemp_{{ $print['tipp']?->id ?? ''}}"></span>
                                                    @endif
                                            </p>
                                        </div>
                                        <div class="card-body">
                                            <p>{{ $print['tipp']->description ?? ''}}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>
    {{-- pagenation div --}}

    {{-- for cart show  --}}
    @livewire('dashboard.right-cart-modal', ['key' => 'right-cart-modal-' . $userDetails->id])

    <div id="analysisPopup_Modal" class="modal analysisPopup_Modal">
        <span class="close" id="modal-close">&times;</span>
        <img class="modal-content" id="imgShow">
    </div>
</section>
{{-- analysis content dashboard view /--}}
@endsection


@section('scripts')

<script>
    // ursCheck Fuction
    var tgl = [];
    var tgl1 = [];
    var tgl2 = [];
    function ursCheck(id)
    {
        tgl2 = [];
        tgl1 = [];
        $('.analysis-sample-list-content').removeClass('showw');
        $('#tip'+id).removeClass('active');
        $('#tip-content'+id).removeClass('showw');
        $('#mit'+id).removeClass('active');
        $('#mit-content'+id).removeClass('showw');
        if($.inArray(id,tgl)==-1){
            tgl = [];
            tgl.push(id);
            $('#urs'+id).toggleClass('active');
            $('#urs-content'+id).toggleClass('showw');
        }
        else tgl = [];
    }
    // mitCheck Function
    function mitCheck(id)
    {
        tgl2 = [];
        tgl = [];
        $('.analysis-sample-list-content').removeClass('showw');
        $('#tip'+id).removeClass('active');
        $('#tip-content'+id).removeClass('showw');
        $('#urs'+id).removeClass('active');
        $('#urs-content'+id).removeClass('showw');
        if($.inArray(id,tgl1)==-1){
            tgl1 = [];
            tgl1.push(id);
            $('#mit'+id).toggleClass('active');
            $('#mit-content'+id).toggleClass('showw');
        }
        else tgl1 = [];
    }
    // tipCheck Function
    function tipCheck(id)
    {
        tgl1 = [];
        tgl = [];
        $('.analysis-sample-list-content').removeClass('showw');
        $('#mit'+id).removeClass('active');
        $('#mit-content'+id).removeClass('showw');
        $('#urs'+id).removeClass('active');
        $('#urs-content'+id).removeClass('showw');
        if($.inArray(id,tgl2)==-1){
            tgl2 = [];
            tgl2.push(id);
            $('#tip'+id).toggleClass('active');
            $('#tip-content'+id).toggleClass('showw');
        }
        else tgl2 = [];
    }

    $('body').on('click', function (e) {
        $('.analysis-sample-list-content').each(function () {
            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.analysis-sample').has(e.target).length === 0) {
                $(this).removeClass('showw');
            }
        });
    });


    $(document).ready(function() {

        var page = "{{ isset($_REQUEST['page'])  ? (int)$_REQUEST['page'] : 1 }}";

        var isLoading = false;
        var $window = $(window);
        var $document = $(document);

        $('#ok').bind('DOMMouseScroll mousewheel touchmove', function(e) {
            var last = parseInt($('#ok').data('lastpage')) || 1;
            if (last <= page || isLoading) return;

            if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
                // Scrolling up
            } else {
                // Scrolling down
                if ($window.scrollTop() + $window.height() + 150 >= $document.height()) {
                    page++;
                    loadMoreData(page);
                }
            }
        });

        function loadMoreData(page) {
            isLoading = true;
            $.ajax({
                url: '?page=' + page,
                type: "get",
                beforeSend: function() {
                    $(document.body).css({
                        'cursor': 'wait'
                    });
                    $(".preloader").show();
                },
                complete: function() {
                    $(document.body).css({
                        'cursor': 'default'
                    });
                    $(".preloader").hide();
                    isLoading = false;
                }
            }).done(function(data) {
                if (data.posts) {
                    $('.posts').append(data.posts);
                    // If there are more pages, update the data attribute
                    if (data.last_page) {
                        $('#ok').data('lastpage', data.last_page);
                    }
                }
            }).fail(function(jqXHR, ajaxOptions, thrownError) { });
        }

    })

    $("#longday").on('change', function(){

        var select = $(this).find('option:selected');
        longday = select.val();
        var proid = "{{ request()->ownid }}";
        var subid = "{{ request()->ownsubid }}";
        var comid = "{{ request()->comid }}";

        if(comid == "")
            var url = "{{ url('/dashboard/op') }}/"+proid+"/"+subid+"/"+longday;
        else
            var url = "{{ url('/dashboard/c/op') }}/"+proid+"/"+subid+"/"+longday+"/"+comid;

        $("#cb-rl2").attr("data-href", url);
        window.location.href = url;
        // $("#longday_link").attr("href", url);
    })

</script>


@endsection
