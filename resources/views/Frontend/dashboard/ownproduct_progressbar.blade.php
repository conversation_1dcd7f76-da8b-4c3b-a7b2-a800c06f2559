@extends('Frontend.partials.layout')

@php
    // if(auth()->user()->user_type == 0 && auth()->user()->id == getID()){
    //     $submenusList = session()->get('menuList'.getAuthId())['submenu'];
    //     $subids = [];
    //         foreach ($submenusList as $key => $sub) {
    //             if($sub['product_id'] == $data['proid'] && $sub['from'] == 2)
    //                 $subids[] = $sub['submenu_id'];
    //         }
    //         $submenus = App\Model\Menu::with(['usersubmenus' => function ($query) use($subids) {
    //             $query->whereIn('id',$subids);
    //         }])->find(request()->ownid);
    // } else {
    //     $submenus = getOwnSubmenusByMenuid(request()->ownid);
    // }

    // if(!empty($submenus->usersubmenus)){
    //     $first = $submenus->usersubmenus[0]?->id;
    //     $last  = $submenus->usersubmenus[count($submenus->usersubmenus)-1]?->id;
    //     $_submenu = $submenus->usersubmenus;
    //     if(count($_submenu) > 0){
    //         $nextId = $_submenu->where('id', '>', $data['subid'])->take(1)->first()->id ?? $_submenu->first()->id;
    //         $previousId = $_submenu->where('id', '<', $data['subid'])->sortByDesc('id')->take(1)->first()->id ?? $_submenu->last()->id;
    //     }
    // }
    $cartData = getCartDataInstance(1);
@endphp

@section('content')
{{-- analysis content section --}}
<section class="analysis-content">
    <div class="row">
        {{-- analysis content header --}}
        <div class="col-md-4">
            <div class="analysis-content-header d-flex align-items-center">
                <div class="logo">
                    <i class="fas fa-home primary-color"></i>
                </div>
                <div class="heading primary-color">
                    <h2>{{ ucfirst($data['subName']) }}</h2>
                    <p>{{ ucfirst($data['proName']) }}</p>
                </div>
            </div>
        </div>
        {{-- includes layout top --}}
        @include('Frontend.partials.includes.layout-top')
    </div>

    <div class="row mt-2">
        <div class="col-md-12">
            
            @include('Frontend.partials.includes.ownproduct-paginate-number')
            {{-- pagination
            <div class="pagination-div">
                <nav class="">
                    <ul class="pagination flex-wrap justify-content-start justify-content-sm-end">
                        <li class="page-item back {{$previousId == null ? 'disabled' : ''}}" id="{{$previousId == null ? 'disabled' : ''}}">
                        <a class="page-link currentt{{$previousId}}" href="{{ (request()->comid == null)?route('dashboard.ownProduct', [$data['proid'], $previousId]):route('dashboard.combo.ownProduct', [$data['proid'], $previousId,request()->comid]) }}">«</a>
                        </li>
                        @if(!empty($submenus->usersubmenus))
                        @foreach($submenus->usersubmenus as $key => $sub)
                        <li class="page-item @if($data['subid'] == $sub->id){{ 'active' }} @endif">
                            <a class="page-link currentt" href="{{ (request()->comid == null)?route('dashboard.ownProduct', [$data['proid'], $sub->id]) :route('dashboard.combo.ownProduct', [$data['proid'], $sub->id,request()->comid]) }}">{{ $key+1 }}</a>
                        </li>
                        @endforeach
                        @endif
                        <li class="page-item front {{$nextId == null ? 'disabled' : ''}}" id="{{$nextId == null ? 'disabled' : ''}}">
                            <a  class="page-link currentt{{$nextId}}" href="{{ request()->comid == null ? route('dashboard.ownProduct', [$data['proid'], $nextId]):route('dashboard.combo.ownProduct', [$data['proid'], $nextId,request()->comid]) }}" id="@if($data['subid'] == $first){{ 'disabled' }} @endif">»</a>
                        </li>
                    </ul>
                </nav>
            </div> --}}

            {{-- *************** Progressbar Analysis Section *************** --}}
            <div class="analysis-progressbar position-relative" data-lastpage="{{($analyses && $analyses->lastPage()==true)? $analyses->lastPage() : 1 }}" id="ok">
                <div class="preloader">
                    <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                </div>
                <div class="table">
                    <table class="table table-bordered mb-0">
                        <thead>
                            <tr class="bg-whight">
                                <th style="width: 30%">{{__('action.Name')}}</th>
                                <th style="width: 30%">{{__('action.Value')}}</th>
                                <th colspan="5"></th>
                            </tr>
                        </thead>
                        <tbody class="posts">
                            @if(!empty($analyses))
                                @foreach($analyses as $key => $print)
                                    @php
                                        if($bodyimages)
                                            $bodyimg = array_search($print['anaid'],$bodyimages,true);
                                        if($mentalimages)
                                            $mentalimg = array_search($print['anaid'],$mentalimages,true);
                                    @endphp
                                    <tr>
                                        <td scope="row">
                                            <!-- *************** Progressbar-Analysis name *************** -->

                                            <div class="analysis-sample-header">

                                                <div class="analysis-sample-header-left">
                                                    <p class="d-flex align-items-center m-0">
                                                        <span class="analysis-title d-inline-block mr-2" data-toggle="tooltip" data-placement="bottom" title="{{ e($print['anaName']) }}">{{ $print['anaName'] }}</span>
                                                    </p>
                                                </div>

                                                <div class="analysis-sample-header-right">

                                                    @if(!empty($print['desc']) || !empty($print['url_link']) || !empty($print['bodyDesc']) || !empty($print['mentalDesc']) || !empty($bodyimg) || !empty($mentalimg) || !empty($print['desc_img']))
                                                        <span class="d-block info mr-2" id="info_{{ $print['anaid'] }}" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content=""><i class="fas fa-info-circle"></i></span>
                                                    @endif

                                                    <div id="popover-content{{ $print['anaid'] }}" class="hide">
                                                        @if(!empty($print['title']))
                                                            <h3>{!! $print['title'] !!}</h3>
                                                            <hr>
                                                        @endif

                                                        @if(!empty($print['desc']))
                                                            <h6>{{__('action.main_desc')}}</h6>
                                                            <p>{!! $print['desc'] !!}</p>

                                                        @endif
                                                        @if(isset($print['desc_img']))
                                                            <img src="{{ asset('/storage/analyse/images/description') }}/{{ $print['desc_img'] }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-desc" data-type="desc" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                        <hr>
                                                        @endif

                                                        @if(!empty($print['bodyDesc']) || !empty($bodyimg))
                                                            <h6>{{__('action.body')}}</h6>
                                                        @endif
                                                        @if(!empty($print['bodyDesc']))
                                                            <p>{!! $print['bodyDesc'] !!}</p>
                                                        @endif
                                                        @if($bodyimg)
                                                            <img src="{{ asset('storage/analyse/images/body') }}/{{ $bodyimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-body" data-type="body" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                        <hr>
                                                        @endif

                                                        @if(!empty($print['mentalDesc']) || !empty($mentalimg))
                                                            <h6>{{__('action.mental')}}</h6>
                                                        @endif
                                                        @if(!empty($print['mentalDesc']))
                                                            <p>{!! $print['mentalDesc'] !!}</p>
                                                        @endif
                                                        @if($mentalimg)
                                                            <img src="{{ asset('storage/analyse/images/mental') }}/{{ $mentalimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-mental" data-type="mental" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                                        <hr>
                                                        @endif
                                                        @if($print['url_link'] != null)
                                                            <a class="btn btn-success" href="{{ $print['url_link'] ?? 'javascript:void(0)' }}" target="_blank">{{ $print['url_name'] ?? trans('action.link') }}</a>
                                                        @endif
                                                    </div>

                                                    <script>
                                                        $(function ($) {
                                                            $('#info_{{ $print["anaid"] }}').popover({
                                                                html:true,
                                                                content: function(){
                                                                    return $('#popover-content{{ $print["anaid"] }}').html();
                                                                }
                                                            });
                                                        });
                                                        $(function(){
                                                            $(document).on('click',".popover-showimg", function () {
                                                                let type = $(this).data('type')
                                                                let id = $(this).data('id')
                                                                // Get the modal
                                                                var modal = document.getElementById("analysisPopup_Modal");

                                                                // Get the image and insert it inside the modal - use its "alt" text as a caption
                                                                var img = document.getElementById(`popover-img${id}-${type}`);
                                                                var modalImg = document.getElementById("imgShow");

                                                                modal.style.display = "block";
                                                                modalImg.src = this.src;
                                                            });

                                                            $(document).on('click',"#modal-close", function () {
                                                                var modal = document.getElementById("analysisPopup_Modal");
                                                                modal.style.display = "none";
                                                            })
                                                        });
                                                    </script>

                                                    @if($data['user']->useroption->pattern_switch == 1)
                                                    <div class="mr-2">
                                                        <label class="switcher switcher-sm switcher-success m-0">
                                                            <input type="checkbox" class="switcher-input" id="someSwitchOptionDefault{{ $print['anaid']}}" @if($print['ranValStatus']==1) {{ "checked" }} @endif onclick="change_calculation({{ $print['anaid'] }}, 101, {{ $data['subid']}}, {{ $data['proid'] }})">
                                                            <span class="switcher-indicator">
                                                                <span class="switcher-yes"></span>
                                                                <span class="switcher-no"></span>
                                                            </span>
                                                        </label>
                                                    </div>
                                                    @endif
                                                    @php
                                                        $result = 0;
                                                        foreach ($cartData as $_cart) if($_cart->options->analysisID == $print['anaid'] && $_cart->options->type == 'Analysis') $result = true;
                                                    @endphp

                                                    <span class="d-inline-block cartPluse{{ $print['anaid'] ?? ''}}" id="">

                                                        @if ($result == true)
                                                        <div class="temp_{{ $print['anaid'] ?? ''}}" id="">
                                                            <a href="javascript:void(0)">
                                                                <i class="fas fa-check-circle primary-color f-18"></i>
                                                            </a>
                                                        </div>
                                                        <div class="showTemp_{{ $print['anaid'] ?? ''}}"></div>
                                                        @else
                                                        <div class="temp_{{ $print['anaid'] ?? ''}}" id="">
                                                            <a href="javascript:void(0)" onclick="cartbutton({{ $print['anaid'] }},null,'{{ 'own-'.$data['subid'] }}',{{ $data['proid'] }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Analysis')">
                                                                <i class="fas fa-plus-circle primary-color f-18"></i>
                                                            </a>
                                                        </div>
                                                        <div class="showTemp_{{ $print['anaid'] ?? ''}}"></div>
                                                        @endif


                                                    </span>
                                                </div>
                                            </div>
                                        </td>

                                        <td>
                                            <!-- *************** Progressbar-Analysis progressbar *************** -->

                                            <div class="progress">
                                                <div class="progress-bar" id="progrssRandomVal_{{ $print['anaid'] }}" role="progressbar" aria-valuenow="{{ $print['anaVal'] }}" aria-valuemin="0" aria-valuemax="100" style="max-width: {{ $print['anaVal'] }}%; background-color: {{ $print['anaColor'] }}">
                                                    <span class="title" id="progrssRandomtxt_{{ $print['anaid'] }}">{{ $print['anaVal'] }}%</span>
                                                </div>
                                            </div>

                                        </td>

                                        <td>
                                            <!-- *************** Progressbar-Analysis crat up/down icon *************** -->

                                            <div class="right-icon align-items-center d-flex flex-column justify-content-center">
                                                @if($print['anaVal'] < $print['beergod']) <span class="m-minus"><i class="fas fa-sort-up"></i></span>
                                                    <span class=""><i class="fas fa-sort-down"></i></span>
                                                    @endif
                                                    @if($print['anaVal'] == $print['beergod'])
                                                    <span class="m-minus"><i class="fas fa-sort-up" style="color:#D3D3D3"></i></span>
                                                    <span class=""><i class="fas fa-sort-down" style="color:#D3D3D3"></i></span>
                                                    @endif
                                                    @if($print['anaVal'] > $print['beergod'])
                                                    <span class="m-minus"><i class="fas fa-sort-up" style="color:#D3D3D3"></i></span>
                                                    <span class=""><i class="fas fa-sort-down" style="color:#888"></i></span>
                                                    @endif
                                            </div>

                                        </td>

                                        <td>
                                            <!-- *************** Progressbar-Analysis heart/male icon *************** -->

                                            <div class="left-icon text-center d-flex justify-content-center">
                                                <span class="mr-3"><i class="fas fa-male" style="{{ $print['maleColor'] }}"></i></span>
                                                <span class=""><i class="fas fa-heart" style=" {{ $print['heartColor'] }}"></i></span>
                                            </div>
                                        </td>

                                        @if(!empty($print['causes']) or !empty($print['medium']) or !empty($print['tipp']))
                                            <td>

                                                <!-- *************** Progressbar-Analysis icon *************** -->

                                                <ul class="analysis-progressbar-sample-list">
                                                    <li>
                                                        <a href="javascript:void(0)" @if(!empty($print['causes'])) onclick="saveToday({{ $data['subid'] }},{{ $print['anaid'] }},{{ $print['causes']?->id }},'Causes')" @endif><span class="urs-prg{{ $print['anaid'] }}" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">{{__('action.causes')}}</span></a>
                                                        <script>
                                                            $(function($) {
                                                                $('.urs-prg{{ $print["anaid"] }}').popover({
                                                                    html: true,
                                                                    content: function() {
                                                                        $('.popover').popover('hide')
                                                                        return $('#urs-content-prg{{ $print["anaid"] }}').html();
                                                                    }
                                                                });
                                                            });
                                                        </script>
                                                        <div class="analysis-sample-list-content" id="urs-content-prg{{ $print['anaid'] }}">
                                                            <div class="pt-3">
                                                                <p class="d-flex justify-content-between align-items-center m-0">
                                                                    <span class="analysis-sample-list-content-heading">{{ $print['causes']->title ?? ''}}</span>
                                                                    @php
                                                                    $result = 0;
                                                                    foreach ($cartData as $_cart) if($_cart->options->causes_id == $print['causes']?->id && $_cart->options->type == 'Causes') $result = true;
                                                                    @endphp

                                                                    <span class="d-inline-block cartPluse{{ $print['causes']?->id ?? ''}}" id="">

                                                                        @if ($result == true)
                                                                        <span class="temp_{{ $print['causes']?->id ?? ''}}" id="">
                                                                            <a href="javascript:void(0)">
                                                                                <i class="fas fa-check-circle primary-color f-18"></i>
                                                                            </a>
                                                                        </span>
                                                                        <span class="showTemp_{{ $print['causes']?->id ?? ''}}"></span>
                                                                        @else
                                                                        <span class="temp_{{ $print['causes']?->id ?? ''}}" id="">
                                                                            <a href="javascript:void(0)" @if(!empty($print['causes'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Causes')" @endif>
                                                                                <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                            </a>
                                                                        </span>
                                                                        <span class="showTemp_{{ $print['causes']?->id ?? ''}}"></span>
                                                                        @endif
                                                                    </span>
                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-3">
                                                                <p class="text-justify">{{ $print['causes']->description ?? ''}}</p>
                                                            </div>
                                                        </div>
                                                    </li>

                                                    <li>
                                                        <a href="javascript:void(0)" @if(!empty($print['medium'])) onclick="saveToday({{ $data['subid'] }},{{$print['anaid']}},{{ $print['medium']?->id }},'Medium')" @endif><span class="mit-prg{{ $print['anaid'] }}" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">{{__('action.medium')}}</span></a>
                                                        <script>
                                                            $(function($) {
                                                                $('.mit-prg{{ $print["anaid"] }}').popover({
                                                                    html: true,
                                                                    content: function() {
                                                                        $('.popover').popover('hide')
                                                                        return $('#mit-content-prg{{ $print["anaid"] }}').html();
                                                                    }
                                                                });
                                                            });
                                                        </script>
                                                        <!-- *************** Medium content *************** -->
                                                        <div class="analysis-sample-list-content" id="mit-content-prg{{ $print['anaid'] }}">
                                                            <div class="pt-3">
                                                                <p class="d-flex justify-content-between align-items-center m-0">
                                                                    <span class="analysis-sample-list-content-heading">{{ $print['medium']->title ?? ''}}</span>
                                                                    @php
                                                                    $result = 0;
                                                                    foreach ($cartData as $_cart) if($_cart->options->medium_id == $print['medium']?->id && $_cart->options->type == 'Medium') $result = true;

                                                                    @endphp

                                                                    <span class="d-inline-block cartPluse{{ $print['medium']?->id ?? ''}}" id="">

                                                                        @if ($result == true)
                                                                        <span class="temp_{{ $print['medium']?->id ?? ''}}" id="">
                                                                            <a href="javascript:void(0)">
                                                                                <i class="fas fa-check-circle primary-color f-18"></i>
                                                                            </a>
                                                                        </span>
                                                                        <span class="showTemp_{{ $print['medium']?->id ?? ''}}"></span>
                                                                        @else
                                                                        <span class="temp_{{ $print['medium']?->id ?? ''}}" id="">
                                                                            <a href="javascript:void(0)" @if(!empty($print['medium'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Medium')" @endif>
                                                                                <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                            </a>
                                                                        </span>
                                                                        <span class="showTemp_{{ $print['medium']?->id ?? ''}}"></span>
                                                                        @endif
                                                                    </span>
                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-3">
                                                                <p class="text-justify">{{ $print['medium']->description ?? ''}}</p>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <a href="javascript:void(0)" @if(!empty($print['tipp'])) onclick="saveToday({{ $data['subid'] }},{{$print['anaid']}},{{ $print['tipp']?->id }},'Tipp')" @endif><span class="tip-prg{{ $print['anaid'] }}" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">{{__('action.tipp')}}</span></a>
                                                        <script>
                                                            $(function($) {
                                                                $('.tip-prg{{ $print["anaid"] }}').popover({
                                                                    html: true,
                                                                    content: function() {
                                                                        $('.popover').popover('hide')
                                                                        return $('#tip-content-prg{{ $print["anaid"] }}').html();
                                                                    }
                                                                });
                                                            });
                                                        </script>
                                                        <!-- *************** Tip content *************** -->

                                                        <div class="analysis-sample-list-content" id="tip-content-prg{{ $print['anaid'] }}">
                                                            <div class="pt-3">
                                                                <p class="d-flex justify-content-between align-items-center m-0">
                                                                    <span class="analysis-sample-list-content-heading">{{ $print['tipp']->title ?? ''}}</span>
                                                                    @php
                                                                    $result = 0;
                                                                    foreach ($cartData as $_cart) if($_cart->options->tipp_id == $print['tipp']?->id && $_cart->options->type == 'Tipp') $result = true;
                                                                    @endphp

                                                                    <span class="d-inline-block cartPluse{{ $print['tipp']?->id ?? ''}}" id="">

                                                                        @if ($result == true)
                                                                        <span class="temp_{{ $print['tipp']?->id ?? ''}}" id="">
                                                                            <a href="javascript:void(0)">
                                                                                <i class="fas fa-check-circle primary-color f-18"></i>
                                                                            </a>
                                                                        </span>
                                                                        <span class="showTemp_{{ $print['tipp']?->id ?? ''}}"></span>
                                                                        @else
                                                                        <span class="temp_{{ $print['tipp']?->id ?? ''}}" id="">
                                                                            <a href="javascript:void(0)" href="javascript:void(0)" @if(!empty($print['tipp'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Tipp')" @endif>
                                                                                <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                            </a>
                                                                        </span>
                                                                        <span class="showTemp_{{ $print['tipp']?->id ?? ''}}"></span>
                                                                        @endif
                                                                    </span>
                                                                </p>
                                                            </div>
                                                            <hr>
                                                            <div class="pt-3">
                                                                <p class="text-justify">{{ $print['tipp']->description ?? ''}} </p>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </td>
                                        @endif
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {{-- ********* Right Slide Panel ************ --}}
    @livewire('dashboard.right-cart-modal', ['key' => 'right-cart-modal'])

    <div id="analysisPopup_Modal" class="modal analysisPopup_Modal">
        <span class="close" id="modal-close">&times;</span>
        <img class="modal-content" id="imgShow">
    </div>
</section>
@endsection

@section('scripts')

<script>
    var longday = 0;
    function change_calculation(anaid, poolid, subid, proid) {
        if (poolid == "") {
            poolid = 0;
        }
        var check = $('#someSwitchOptionDefault' + anaid).is(':checked');
        var dataString = 'subid=' + subid + '&proid=' + proid + '&anaid=' + anaid + '&poolid=' + poolid + '&is_ck=' + check;

        if (check == true) {
            $.ajax({
                type: "POST",
                url: "{{ route('Aajax.saveCalculation') }}",
                data: dataString,
                success: function(data) {
                    $("#progrssRandomVal_" + anaid).attr("aria-valuenow", data.record.ma);
                    $("#progrssRandomVal_" + anaid).attr("style", "max-width: " + data.record.ma + "%; background-color: " + data.record.color);
                    $("#progrssRandomtxt_" + anaid).html(data.record.ma + "%");
                }
            });
        }else{
            $.ajax({
                type: "POST",
                url: "{{ route('Aajax.saveCalculation') }}",
                data: dataString,
                success: function(data) {
                    $("#progrssRandomVal_" + anaid).attr("aria-valuenow", data.record.ma);
                    $("#progrssRandomVal_" + anaid).attr("style", "max-width: " + data.record.ma + "%; background-color: " + data.record.color);
                    $("#progrssRandomtxt_" + anaid).html(data.record.ma + "%");
                }
            });
        }
    }

    $('body').on('click', function(e) {
        $('[data-toggle=popover]').each(function() {
            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                $(this).popover('hide');
            }
        });
    });

    $("#longday").on('change', function(){

        var select = $(this).find('option:selected');
        longday = select.val();
        var proid = "{{ request()->ownid }}";
        var subid = "{{ request()->ownsubid }}";
        var comid = "{{ request()->comid }}";

        if(comid == "")
            var url = "{{ url('/dashboard/op') }}/"+proid+"/"+subid+"/"+longday;
        else
            var url = "{{ url('/dashboard/c/op') }}/"+proid+"/"+subid+"/"+longday+"/"+comid;
            
        $("#cb-rl2").attr("data-href", url);
        window.location.href = url;
        // $("#longday_link").attr("href", url);
    })

    function checkLongDay(){
        if(longday < 30 ) Swal.fire({ type: 'warning', title: "Please Choose days", showConfirmButton: false, timer: 3500 });
    }

    $(document).ready(function() {

        var page = "{{ isset($_REQUEST['page']) ? (int)$_REQUEST['page'] : 1 }}";

        var isLoading = false;
        var $window = $(window);
        var $document = $(document);

        $('#ok').bind('DOMMouseScroll mousewheel touchmove', function(e) {
            var last = parseInt($('#ok').data('lastpage')) || 1;
            if (last <= page || isLoading) return;

            if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
                // Scrolling up
            } else {
                // Scrolling down
                if ($window.scrollTop() + $window.height() + 150 >= $document.height()) {
                    page++;
                    loadMoreData(page);
                }
            }
        });

        function loadMoreData(page) {
            isLoading = true;
            $.ajax({
                url: '?page=' + page,
                type: "get",
                beforeSend: function() {
                    $(document.body).css({
                        'cursor': 'wait'
                    });
                    $(".preloader").show();
                },
                complete: function() {
                    $(document.body).css({
                        'cursor': 'default'
                    });
                    $(".preloader").hide();
                    isLoading = false;
                }
            }).done(function(data) {
                if (data.posts) {
                    $('.posts').append(data.posts);
                    // If there are more pages, update the data attribute
                    if (data.last_page) {
                        $('#ok').data('lastpage', data.last_page);
                    }
                }
            }).fail(function(jqXHR, ajaxOptions, thrownError) { });
        }

    })

</script>
@endsection
