@extends('Frontend.partials.layout')

@section('content')
{{-- products view section --}}
<section class="products">
    <div class="row">
        <div class="col-lg-8">
            @if(checkDashAccess(getAuthId()) && !isSetupDiagram())
                @include('Frontend.partials.includes.layout-biorhythmus')
            @elseif(isSetupDiagram())
                @include('Frontend.dashboard.View.Content.dashboard_widget')
            @endif
            @if($submenu != null)
                <div class="cui-example">
                    <div class="preloader">
                            <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                        </div>
                    <div class="row">
                        <div class="col-md">
                            <div class="card mb-3 dashboard-card">
                                <div class="card-header">
                                    <a class="dashboardSubmenu mr-5" href="{{ (request()->comid == null) ? route('dashboard.products', [$product['proid']]) : route('dashboard.combo.products', [$product['proid'], request()->comid]) }}">{{ $product['proName'] }} </a>
                                    @if($data['randomAnalysis'] == 1)
                                    <div class="card-tools pull-right">
                                        <a href="{{ route('dashboard.changerandomstatus') }}" class="btn border border-warning text-center text-warning random_blinking">{{__('action.rancheckedmsg')}}</a>
                                    </div>
                                    @endif
                                </div>

                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="dashboard-progress-content text-center">

                                                <!-- progress bar 1 -->
                                                @if($data['randomAnalysis'] == 1)
                                                    <div class="progress progressBar1 mx-auto mb-3" data-value="100">
                                                        <span class="progress-left">
                                                            <span class="progress-bar border-secondary"></span>
                                                        </span>
                                                        <span class="progress-right">
                                                            <span class="progress-bar border-secondary"></span>
                                                        </span>
                                                        <div class="progress-value w-100 h-100 rounded-circle d-flex align-items-center justify-content-center">
                                                            <div class="h2 font-weight-bold">0<sup class="small">%</sup></div>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="progress progressBar1 mx-auto mb-3" data-value="{{ $product['totalAvg'] }}">
                                                        <span class="progress-left">
                                                            <span class="progress-bar" style="border-color: {{$product['color']}}"></span>
                                                        </span>
                                                        <span class="progress-right">
                                                            <span class="progress-bar" style="border-color: {{$product['color']}}"></span>
                                                        </span>
                                                        <div class="progress-value w-100 h-100 rounded-circle d-flex align-items-center justify-content-center">
                                                            <div class="h2 font-weight-bold">{{ $product['totalAvg'] }}<sup class="small">%</sup></div>
                                                        </div>
                                                    </div>
                                                @endif
                                                <p>{{ $product['proName'] }}</p>

                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="row">
                                                @if(!empty($submenu) && $submenu !=  null)
                                                @foreach($submenu as $key => $sub)
                                                    <div class="col-md-6">
                                                        <div class="card color-border-one text-center mb-3">
                                                            <h6 class="card-header f-16">
                                                                <a class="dashboardSubmenu" href="{{ (request()->comid == null) ? route('dashboard.product', [$product['proid'], $sub['submenuId']]) : route('dashboard.combo.product', [$product['proid'], $sub['submenuId'], request()->comid]) }}"> {{ $sub['submenuName']}} </a>
                                                            </h6>
                                                            <div class="progress-circle-mini">
                                                                <!-- progress bar 2 -->
                                                                @if($data['randomAnalysis'] == 1)
                                                                    <div class="progress progressBar2 mx-auto" data-value="100">
                                                                        <span class="progress-left">
                                                                            <span class="progress-bar border-secondary"></span>
                                                                        </span>
                                                                        <span class="progress-right">
                                                                            <span class="progress-bar border-secondary"></span>
                                                                        </span>
                                                                        <div class="progress-value w-100 h-100 rounded-circle d-flex align-items-center justify-content-center">
                                                                            <div class="h2 font-weight-bold">0<sup class="small">%</sup></div>
                                                                        </div>
                                                                    </div>
                                                                @else
                                                                    <div class="progress progressBar2 mx-auto" data-value="{{ $sub['subAvg'] }}">
                                                                        <span class="progress-left">
                                                                            <span class="progress-bar" style="border-color: {{$sub['subcolor']}}"></span>
                                                                        </span>
                                                                        <span class="progress-right">
                                                                            <span class="progress-bar" style="border-color: {{$sub['subcolor']}}"></span>
                                                                        </span>
                                                                        <div class="progress-value w-100 h-100 rounded-circle d-flex align-items-center justify-content-center">
                                                                            <div class="h2 font-weight-bold">{{ $sub['subAvg'] }}<sup class="small">%</sup></div>
                                                                        </div>
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>

                                                <script>

                                                </script>
                                                @endforeach
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
        {{-- include layout dashboard Right Section --}}
        @include('Frontend.partials.includes.layout-dashright', ['farbklang' => $farbklang])
    </div>
</section>
{{-- products view section /--}}

@endsection

@section('scripts')
    <!-- Dependencies -->
    <script src="{{ asset('/vendor/libs/chartjs/chartjs.js') }}"></script>
    <script src="{{ asset('/vendor/libs/swiper/swiper.js') }}"></script>
    <script src="{{ asset('/js/ui_carousel.js') }}"></script>

    <!-- Dependencies -->
    <script src="{{ asset('/vendor/libs/eve/eve.js') }}"></script>
    <script src="{{ asset('/vendor/libs/raphael/raphael.js') }}"></script>
    <script src="{{ asset('/vendor/libs/morris/morris.js') }}"></script>
<script>
    function deletePDF(id,pdf){

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {

        if (result.value) {

            $.ajax({

            type: "POST",

            url: "{{ route('Aajax.deletePDF') }}",

            data: { id:id, pdfid:pdf },

            success: function(data) {

                Swal.fire(
                    'Deleted!', 'Your file has been deleted.', 'success'
                );

                $("#pdf"+id).remove();
            }

            });
        }
        })
    }

</script>
<script>
    $(function() {
        $(".progress").each(function() {

        var value = $(this).attr('data-value');
        var left = $(this).find('.progress-left .progress-bar');
        var right = $(this).find('.progress-right .progress-bar');

        if (value > 0) {
            if (value <= 50) {
            right.css('transform', 'rotate(' + percentageToDegrees(value) + 'deg)')
            } else {
            right.css('transform', 'rotate(180deg)')
            left.css('transform', 'rotate(' + percentageToDegrees(value - 50) + 'deg)')
            }
        }

        })

        function percentageToDegrees(percentage) {

        return percentage / 100 * 360

        }
    });

</script>

@endsection
