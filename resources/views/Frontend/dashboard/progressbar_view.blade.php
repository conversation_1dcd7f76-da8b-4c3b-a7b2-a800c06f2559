@extends('Frontend.partials.layout')

@php
    // if(auth()->user()->user_type == 0 && auth()->user()->id == getID()){
    //     $submenusList = session()->get('menuList'.getAuthId())['submenu'];
    //     $subids = [];
    //         foreach ($submenusList as $key => $sub) {
    //             if($sub['product_id'] == $data['proid'] && $sub['from'] == 1)
    //                 $subids[] = $sub['submenu_id'];
    //         }
    //         $submenus = App\Model\Product::with(['submenu' => function ($query) use($subids) {
    //             $query->whereIn('id',$subids);
    //         }])->find(request()->proid);
    // }else
    //     $submenus = getSubmenusByProductId(request()->proid);

    // if(!empty($submenus->submenu)){
    //     $first = $submenus->submenu[0]?->id;
    //     $last  = $submenus->submenu[count($submenus->submenu)-1]?->id;
    //     $_submenu = $submenus->submenu;
    //     if(count($_submenu) > 0){
    //         $nextId = $_submenu->where('id', '>', $data['subid'])->take(1)->first()->id ?? $_submenu->first()->id;
    //         $previousId = $_submenu->where('id', '<', $data['subid'])->sortByDesc('id')->take(1)->first()->id ?? $_submenu->last()->id;
    //     }
    // }

    $cartData = getCartDataInstance(1);
@endphp

@section('styles')
<style>
    .map-location {
        position: absolute;
    }
    .map-location-details {
        position: relative;
        display: none;
        background: #fff;
        padding: 1em;
        border: 1px solid #ccc;
        border-radius: 6px;
        box-shadow: 0 0px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid #02a065;
    }
    .map-location-details:before {
        pointer-events: none;
        position: absolute;
        z-index: -1;
        content: '';
        border-style: solid;
        -webkit-transition-duration: 0.3s;
        transition-duration: 0.3s;
        -webkit-transition-property: transform;
        transition-property: transform;
        left: calc(50% - 10px);
        top: -10px;
        border-width: 0 10px 10px 10px;
        border-bottom-color: #02a065 !important;
        border-color: transparent transparent #fff transparent;
    }
    .map-point {
        display: inline-block;
        position: absolute;
        top: -10px;
        left: -10px;
        width: 8px;
        height: 8px;
        background: red;
        border-radius: 100%;
        cursor: pointer;
    }
    .nyc1 {
        top: 30%;
        left: 27%;
    }
    .analysis-progressbar .analysis-sample-list-content{
        overflow: unset;
        top: 43px;
        left: 50%;
        width: 306px !important;
        z-index: 9;
        transform: translate(-50%, 1px);
    }
    .analysis-sample-list-content .arrow::before,
    .analysis-sample-list-content .arrow::after {
        position: absolute;
        display: block;
        content: "";
        border-color: transparent;
        border-style: solid;
    }
    .analysis-sample-list-content>.arrow::after{
        top: 1px;
        border-width: 0 0.5rem 0.5rem 0.5rem;
        border-bottom-color: #fff;
    }
    .analysis-sample-list-content>.arrow::before{
        top: 0;
        border-width: 0 0.5rem 0.5rem 0.5rem;
        border-bottom-color: rgba(24, 28, 33, 0.11);
    }
    .analysis-sample-list-content>.arrow::before{
        border-bottom-color: #02a065 !important;
    }
    .analysis-sample-list-content .arrow {
        position: absolute;
        content: '';
        top: -9px;
        left: calc(50% - 10px);
    }

    @media (max-width: 1024px){
        .analysis-progressbar-sample-list li span {
            width: 100%;
        }
        .analysis-progressbar .analysis-sample-list-content {
            overflow: unset;
            top: -3px;
            left: -160px !important;
            width: 306px !important;
            z-index: 9;
            transform: translate(-50%, 1px);
        }
        .analysis-sample-list-content .arrow {
            position: absolute;
            content: '';
            top: 7px;
            left: calc(100% - -8px) !important;
            transform: rotate(90deg);
        }
    }
</style>
@endsection

@section('content')

{{-- progressbar view analysis content --}}
<section class="analysis-content" style="padding-bottom: 100px;">
    <div class="row">
        <div class="col-md-4">
            <div class="analysis-content-header d-flex align-items-center">
                <div class="logo">
                    <i class="fas fa-home primary-color"></i>
                </div>
                <div class="heading primary-color">
                    <h2>{{ ucfirst($data['subName']) }}</h2>
                    <p>{{ ucfirst($data['proName']) }}</p>
                </div>
            </div>
        </div>

        @include('Frontend.partials.includes.layout-top')
    </div>

    <div class="row mt-2">
        <div class="col-md-12">
            {{-- pagination --}}

            @include('Frontend.partials.includes.product-paginate-number')
            {{-- <div class="pagination-div">
                <nav class="">
                    <ul class="pagination flex-wrap justify-content-start justify-content-sm-end">
                        <li class="page-item back {{$previousId == null ? 'disabled':''}}" id="{{$previousId == null ? 'disabled':''}}">
                        <a class="page-link currentt{{$previousId}}" href="{{ (request()->comid == null) ? route('dashboard.product', [$data['proid'], $previousId]) : route('dashboard.combo.product', [$data['proid'], $previousId, request()->comid]) }}">«</a>
                        </li>
                        @if(!empty($submenus->submenu))
                        @foreach($submenus->submenu as $key => $sub)
                        <li class="page-item @if(request()->subid == $sub->id){{ 'active' }} @endif">
                            <a class="page-link currentt" href="{{ (request()->comid == null) ? route('dashboard.product', [$data['proid'], $sub->id]) : route('dashboard.combo.product', [$data['proid'], $sub->id, request()->comid]) }}">{{ $key+1 }}</a>
                        </li>
                        @endforeach
                        @endif
                        <li class="page-item front {{$nextId == null ? 'disabled':''}}" id="{{$nextId == null ? 'disabled':''}}">
                            <a  class="page-link currentt{{$nextId}}" href="{{ (request()->comid == null) ? route('dashboard.product', [$data['proid'], $nextId]) : route('dashboard.combo.product', [$data['proid'], $nextId, request()->comid]) }}" id="@if(request()->subid == $first){{ 'disabled' }} @endif">»</a>
                        </li>
                    </ul>
                </nav>
            </div> --}}

            {{-- *************** Progressbar Analysis Section *************** --}}
            <div class="analysis-content-body endless-pagination" data-lastpage="{{($analyses && $analyses != null && $analyses->lastPage()==true)? $analyses->lastPage() : 1 }}" id="ok">
            <div class="analysis-progressbar position-relative">
                <div class="preloader">
                    <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                </div>
                <div class="table progress-view-table table-responsive">
                    <table class="table table-bordered table-striped mb-0">
                        <thead>
                            <tr class="bg-whight">
                                <th style="width: 30%">{{__('action.name')}}</th>
                                <th style="width: 30%">{{__('action.value')}}</th>
                                <th colspan="5"></th>
                            </tr>
                        </thead>
                        <tbody class="posts">

                        @foreach($analyses as $key => $print)
                            @php
                                if($bodyimages) $bodyimg = array_search($print['anaid'],$bodyimages,true);
                                else $bodyimg = "";
                                if($mentalimages) $mentalimg = array_search($print['anaid'],$mentalimages,true);
                                else $mentalimg = "";
                            @endphp
                            <tr>
                                <td scope="row">
                                    {{-- *************** Progressbar-Analysis name *************** --}}
                                    <div class="analysis-sample-header" style="justify-content: space-between;">

                                        <div class="analysis-sample-header-left">
                                            <p class="d-flex align-items-center m-0">
                                                <span class="analysis-title d-inline-block mr-2" data-toggle="tooltip" data-placement="bottom" title="{{ e($print['anaName']) }}">{{ $print['anaName'] }}</span>
                                            </p>
                                        </div>

                                        <div class="analysis-sample-header-right">

                                        @if(!empty($print['desc']) || !empty($print['url_link']) || !empty($print['bodyDesc']) || !empty($print['mentalDesc']) || !empty($bodyimg) || !empty($mentalimg) || !empty($print['desc_img']))
                                            <span class="d-block info mr-2" id="info_{{ $print['anaid'] }}" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content=""><i class="fas fa-info-circle"></i></span>
                                        @endif


                                        <div id="popover-content{{ $print['anaid'] }}" style="display:none;">
                                            @if(!empty($print['title']))
                                                <h3>{!! $print['title'] !!}</h3>
                                                <hr>
                                            @endif

                                            @if(!empty($print['desc']))
                                                <h6>{{__('action.main_desc')}}</h6>
                                                <p>{!! $print['desc'] !!}</p>

                                            @endif
                                            @if(isset($print['desc_img']))
                                                <img src="{{ asset('/storage/analyse/images/description') }}/{{ $print['desc_img'] }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-desc" data-type="desc" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                            <hr>
                                            @endif

                                            @if(!empty($print['bodyDesc']) || !empty($bodyimg))
                                                <h6>{{__('action.body')}}</h6>
                                            @endif
                                            @if(!empty($print['bodyDesc']))
                                                <p>{!! $print['bodyDesc'] !!}</p>
                                            @endif
                                            @if($bodyimg)
                                                <img src="{{ asset('/storage/analyse/images/body') }}/{{ $bodyimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-body" data-type="body" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                            <hr>
                                            @endif

                                            @if(!empty($print['mentalDesc']) || !empty($mentalimg))
                                                <h6>{{__('action.mental')}}</h6>
                                            @endif
                                            @if(!empty($print['mentalDesc']))
                                                <p>{!! $print['mentalDesc'] !!}</p>
                                            @endif
                                            @if($mentalimg)
                                                <img src="{{ asset('/storage/analyse/images/mental') }}/{{ $mentalimg }}" class="popover-showimg" id="popover-img{{ $print['anaid'] }}-mental" data-type="mental" data-id="{{ $print['anaid'] }}" alt="" height="250px" width="auto">
                                            <hr>
                                            @endif
                                            @if($print['url_link'] != null)
                                                <a class="btn btn-success" href="{{ $print['url_link'] ?? 'javascript:void(0)' }}" target="_blank">{{ $print['url_name'] ?? trans('action.link') }}</a>
                                            @endif
                                        </div>

                                        <script>
                                            $(function ($) {
                                                $('#info_{{ $print["anaid"] }}').popover({
                                                    html:true,
                                                    content: function(){
                                                        return $('#popover-content{{ $print["anaid"] }}').html();
                                                    }
                                                });
                                            });

                                            $(function(){
                                                $(document).on('click',".popover-showimg", function () {
                                                    let type = $(this).data('type')
                                                    let id = $(this).data('id')
                                                    // Get the modal
                                                    var modal = document.getElementById("analysisPopup_Modal");

                                                    // Get the image and insert it inside the modal - use its "alt" text as a caption
                                                    var img = document.getElementById(`popover-img${id}-${type}`);
                                                    var modalImg = document.getElementById("imgShow");

                                                    modal.style.display = "block";
                                                    modalImg.src = this.src;
                                                });

                                                $(document).on('click',"#modal-close", function () {
                                                    var modal = document.getElementById("analysisPopup_Modal");
                                                    modal.style.display = "none";
                                                })
                                            });
                                        </script>

                                        @if($data['user']->useroption->pattern_switch == 1)
                                            <div class="mr-2">
                                                <label class="switcher switcher-sm switcher-success m-0">
                                                    <input type="checkbox" class="switcher-input" id="someSwitchOptionDefault{{ $print['anaid']}}" @if($print['ranValStatus'] == 1) {{ "checked" }} @endif onclick="change_calculation({{ $print['anaid'] }}, {{ $print['poolid'] }}, {{ $data['subid']}}, {{ $data['proid'] }}, 'sub')">
                                                    <span class="switcher-indicator">
                                                        <span class="switcher-yes"></span>
                                                        <span class="switcher-no"></span>
                                                    </span>
                                                </label>
                                            </div>
                                        @endif
                                            @php
                                                $result = 0;
                                                foreach ($cartData as $_cart) if($_cart->options->analysisID == $print['anaid'] && $_cart->options->type == 'Analysis') $result = true;
                                            @endphp

                                            <span class="d-inline-block cartPluse{{ $print['anaid'] }}" id="">

                                                @if ($result == true)
                                                <div class="temp_{{ $print['anaid'] }}" id="">
                                                    <a href="javascript:void(0)">
                                                        <i class="fas fa-check-circle primary-color f-18"></i>
                                                    </a>
                                                </div>
                                                <div class="showTemp_{{ $print['anaid'] }}"></div>
                                                @else
                                                <div class="temp_{{ $print['anaid'] }}" id="">
                                                    <a href="javascript:void(0)" 
                                                    onclick="cartbutton(
                                                        {{ $print['anaid'] }}, 
                                                        null, 
                                                        '{{ 'sub-'.$data['subid'] }}', 
                                                        {{ request()->proid }}, 
                                                        {{ $print['anaVal'] }}, 
                                                        {{ $print['maleVal'] }}, 
                                                        {{ $print['heartVal'] }}, 
                                                        {{ $print['randPrice'] }}, 
                                                        '{{ is_object($print['causes']) ? $print['causes']?->id : '' }}',
                                                        '{{ is_object($print['medium']) ? $print['medium']?->id : '' }}',
                                                        '{{ is_object($print['tipp']) ? $print['tipp']?->id : '' }}',
                                                        '{{ $print['anaColor'] }}', 
                                                        'Analysis')">
                                                    <i class="fas fa-plus-circle primary-color f-18"></i>
                                                </a>                                                
                                                </div>
                                                <div class="showTemp_{{ $print['anaid'] }}"></div>
                                                @endif


                                            </span>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    {{-- *************** Progressbar-Analysis progressbar *************** --}}
                                    <div class="progress">
                                        <div class="progress-bar" id="progrssRandomVal_{{ $print['anaid'] }}" role="progressbar" aria-valuenow="{{ $print['anaVal'] }}" aria-valuemin="0" aria-valuemax="100" style="max-width: {{ $print['anaVal'] }}%; background-color: {{ $print['anaColor'] }}" >
                                            <span class="title" id="progrssRandomtxt_{{ $print['anaid'] }}">{{ $print['anaVal'] }}%</span>
                                        </div>
                                    </div>

                                </td>

                                <td>
                                    {{-- *************** Progressbar-Analysis crat up/down icon *************** --}}
                                    <div class="right-icon align-items-center d-flex flex-column justify-content-center">
                                        @if($print['anaVal'] < $print['beergod'])
                                        <span class="m-minus"><i class="fas fa-sort-up"></i></span>
                                        <span class=""><i class="fas fa-sort-down"></i></span>
                                        @endif
                                        @if($print['anaVal'] == $print['beergod'])
                                        <span class="m-minus"><i class="fas fa-sort-up"   style="color:#D3D3D3"></i></span>
                                        <span class=""><i class="fas fa-sort-down" style="color:#D3D3D3"></i></span>
                                        @endif
                                        @if($print['anaVal'] > $print['beergod'])
                                        <span class="m-minus"><i class="fas fa-sort-up"   style="color:#D3D3D3"></i></span>
                                        <span class=""><i class="fas fa-sort-down" style="color:#888"></i></span>
                                        @endif
                                    </div>
                                </td>

                                <td>
                                    {{-- *************** Progressbar-Analysis heart/male icon *************** --}}
                                    <div class="left-icon text-center d-flex justify-content-center">
                                        <span class="mr-3"><i class="fas fa-male" style="{{ $print['maleColor'] }}"></i></span>
                                        <span class=""><i class="fas fa-heart" style=" {{ $print['heartColor'] }}"></i></span>
                                    </div>
                                </td>

                                @if(!empty($print['causes']) or !empty($print['medium']) or !empty($print['tipp']))
                                <td>
                                    {{-- *************** Progressbar-Analysis icon *************** --}}
                                   <style>
                                       .hide{
                                           display: none !important;
                                       }
                                   </style>
                                    <ul class="analysis-progressbar-sample-list">
                                        <li>
                                        <a href="javascript:void(0)" @if(!empty($print['causes'])) onclick="saveToday({{ $data['subid'] }},{{ $print['anaid'] }},{{ $print['causes']?->id }},'Causes')" @endif>
                                            <span class="urs-prg{{ $print['anaid'] }} btn btn-outline-success" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">{{__('action.causes')}}</span>
                                        </a>
                                            <script>

                                                $( ".urs-prg{{ $print['anaid'] }}" ).click(function() {
                                                    $(".urs-content-prg{{ $print['anaid'] }}").fadeToggle();

                                                });
                                                $(document).click(function (e) {
                                                    if (!$(e.target).hasClass("urs-prg{{ $print['anaid'] }}")
                                                        && $(e.target).parents(".urs-content-prg{{ $print['anaid'] }}").length === 0)
                                                    {
                                                        $(".urs-content-prg{{ $print['anaid'] }}").hide();
                                                    }
                                                });



                                            </script>

                                            <div class="analysis-sample-list-content urs-content-prg{{ $print['anaid'] }}" id="urs-content-prg{{ $print['anaid'] }}" style="left: 50%;display: none;">
                                                <div class="arrow"></div>
                                                <div class="pt-3">
                                                    <p class="d-flex justify-content-between align-items-center m-0">
                                                        <span class="analysis-sample-list-content-heading">{{ $print['causes']->title ?? '' }}</span>
                                                            @php
                                                            $result = 0;
                                                            foreach ($cartData as $_cart) if($_cart->options->causes_id == $print['causes']?->id && $_cart->options->type == 'Causes') $result = true;
                                                            @endphp

                                                        <span class="d-inline-block cartPluse{{ $print['causes']?->id ?? '' }}" id="">

                                                                @if ($result == true)
                                                                <span class="temp_{{ $print['causes']?->id ?? '' }}" id="">
                                                                    <a href="javascript:void(0)">
                                                                        <i class="fas fa-check-circle primary-color f-18"></i>
                                                                    </a>
                                                                </span>
                                                                <span class="showTemp_{{ $print['causes']?->id ?? '' }}"></span>
                                                                @else
                                                                <span class="temp_{{ $print['causes']?->id ?? ''}}" id="">
                                                                    <a href="javascript:void(0)" @if(!empty($print['causes'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Causes')" @endif>
                                                                        <i class="fas fa-plus-circle primary-color f-18"></i>
                                                                    </a>
                                                                </span>
                                                                <span class="showTemp_{{ $print['causes']?->id ?? '' }}"></span>
                                                                @endif
                                                        </span>
                                                    </p>
                                                </div>
                                                <hr>
                                                <div class="pt-3">
                                                    <p class="text-justify">{{ $print['causes']->description ?? ''}}</p>
                                                </div>
                                            </div>
                                        </li>

                                        <li>
                                        <a href="javascript:void(0)" @if(!empty($print['medium'])) onclick="saveToday({{ $data['subid'] }},{{$print['anaid']}},{{ $print['medium']?->id }},'Medium')" @endif ><span class="mit-prg{{ $print['anaid'] }} btn btn-outline-success" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">{{__('action.medium')}}</span></a>
                                            <script>

                                                $( ".mit-prg{{ $print['anaid'] }}" ).click(function() {
                                                    $(".mit-content-prg{{ $print['anaid'] }}").fadeToggle();
                                                })
                                                $(document).click(function (e) {
                                                    if (!$(e.target).hasClass("mit-prg{{ $print['anaid'] }}")
                                                        && $(e.target).parents(".mit-content-prg{{ $print['anaid'] }}").length === 0)
                                                    {
                                                        $(".mit-content-prg{{ $print['anaid'] }}").hide();
                                                    }
                                                });
                                            </script>
                                            {{-- *************** Medium content *************** --}}
                                            <div class="analysis-sample-list-content mit-content-prg{{ $print['anaid'] }}" id="mit-content-prg{{ $print['anaid'] }}" style="left: 50%;">
                                                <div class="arrow"></div>
                                                <div class="pt-3">
                                                    <p class="d-flex justify-content-between align-items-center m-0">
                                                        <span class="analysis-sample-list-content-heading">{{ $print['medium']->title ?? '' }}</span>
                                                        @php
                                                        $result = 0;
                                                        foreach ($cartData as $_cart) if($_cart->options->medium_id == $print['medium']?->id && $_cart->options->type == 'Medium') $result = true;

                                                        @endphp

                                                        <span class="d-inline-block cartPluse{{ $print['medium']?->id ?? ''}}" id="">

                                                                @if ($result == true)
                                                                <span class="temp_{{ $print['medium']?->id ?? ''}}" id="">
                                                                    <a href="javascript:void(0)">
                                                                        <i class="fas fa-check-circle primary-color f-18"></i>
                                                                    </a>
                                                                </span>
                                                                <span class="showTemp_{{ $print['medium']?->id ?? ''}}"></span>
                                                                @else
                                                                <span class="temp_{{ $print['medium']?->id ?? ''}}" id="">
                                                                    <a href="javascript:void(0)" @if(!empty($print['medium'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id ?? ''}}','{{ $print['medium']?->id ?? ''}}','{{ $print['tipp']?->id ?? ''}}','{{ $print['anaColor'] }}','Medium')" @endif>
                                                                <i class="fas fa-plus-circle primary-color f-18"></i>
                                                            </a>
                                                            </span>
                                                        <span class="showTemp_{{ $print['medium']?->id ?? ''}}"></span>
                                                        @endif
                                                        </span>
                                                    </p>
                                                </div>
                                                <hr>
                                                <div class="pt-3">
                                                    <p class="text-justify">{{ $print['medium']->description ?? ''}}</p>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                        <a href="javascript:void(0)" @if(!empty($print['tipp'])) onclick="saveToday({{ $data['subid'] }},{{$print['anaid']}},{{ $print['tipp']?->id }},'Tipp')" @endif><span class="tip-prg{{ $print['anaid'] }} btn btn-outline-success" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">{{__('action.tipp')}}</span></a>
                                            <script>

                                                $( ".tip-prg{{ $print['anaid'] }}" ).click(function() {
                                                    $(".tip-content-prg{{ $print['anaid'] }}").fadeToggle();
                                                })
                                                $(document).click(function (e) {
                                                    if (!$(e.target).hasClass("tip-prg{{ $print['anaid'] }}")
                                                        && $(e.target).parents(".tip-content-prg{{ $print['anaid'] }}").length === 0)
                                                    {
                                                        $(".tip-content-prg{{ $print['anaid'] }}").hide();
                                                    }
                                                });
                                            </script>
                                            {{-- *************** Tip content *************** --}}
                                            <div class="analysis-sample-list-content tip-content-prg{{ $print['anaid'] }} tip-content" id="tip-content-prg{{ $print['anaid'] }}" style="left: 50%;">
                                                <div class="arrow"></div>
                                                <div class="pt-3">
                                                    <p class="d-flex justify-content-between align-items-center m-0">
                                                        <span class="analysis-sample-list-content-heading">{{ $print['tipp']->title ?? ''}}</span>
                                                       @php
                                                            $result = 0;
                                                            foreach ($cartData as $_cart) if($_cart->options->tipp_id == $print['tipp']?->id && $_cart->options->type == 'Tipp') $result = true;
                                                        @endphp

                                                    <span class="d-inline-block cartPluse{{ $print['tipp']?->id ?? ''}}" id="">

                                                        @if ($result == true)
                                                        <span class="temp_{{ $print['tipp']?->id ?? ''}}" id="">
                                                            <a href="javascript:void(0)">
                                                                <i class="fas fa-check-circle primary-color f-18"></i>
                                                            </a>
                                                        </span>
                                                        <span class="showTemp_{{ $print['tipp']?->id ?? ''}}"></span>
                                                        @else
                                                        <span class="temp_{{ $print['tipp']?->id ?? ''}}" id="">
                                                        {{-- <a href="javascript:void(0)" @if(!empty($print['tipp'])) onclick="cartbutton({{ $print['anaid'] }},null,{{ $data['subid'] }},{{ request()->proid }},{{ $print['anaVal'] }},{{ $print['maleVal'] }},{{ $print['heartVal'] }},{{ $print['randPrice'] }},'{{ $print['causes']?->id }}','{{ $print['medium']?->id }}','{{ $print['tipp']?->id }}','{{ $print['anaColor'] }}','Tipp')" @endif>
                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                        </a> --}}
                                                        <a href="javascript:void(0)" 
                                                            onclick="cartbutton(
                                                                {{ $print['anaid'] }}, 
                                                                null, 
                                                                '{{ 'sub-'.$data['subid'] }}', 
                                                                {{ request()->proid }}, 
                                                                {{ $print['anaVal'] }}, 
                                                                {{ $print['maleVal'] }}, 
                                                                {{ $print['heartVal'] }}, 
                                                                {{ $print['randPrice'] }}, 
                                                                '{{ is_object($print['causes']) ? $print['causes']?->id : '' }}',
                                                                '{{ is_object($print['medium']) ? $print['medium']?->id : '' }}',
                                                                '{{ is_object($print['tipp']) ? $print['tipp']?->id : '' }}',
                                                                '{{ $print['anaColor'] }}', 
                                                                'Tipp')">
                                                            <i class="fas fa-plus-circle primary-color f-18"></i>
                                                        </a>

                                                        </span>
                                                        <span class="showTemp_{{ $print['tipp']?->id ?? ''}}"></span>
                                                        @endif
                                                    </span>
                                                    </p>
                                                </div>
                                                <hr>
                                                <div class="pt-3">
                                                    <p class="text-justify">{{ $print['tipp']->description ?? ''}} </p>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </td>
                                @endif
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div>


        </div>
    </div>

    {{-- ********* Right Slide Panel ************ --}}
    @livewire('dashboard.right-cart-modal', ['key' => 'right-cart-modal'])

    <div id="analysisPopup_Modal" class="modal analysisPopup_Modal">
        <span class="close" id="modal-close">&times;</span>
        <img class="modal-content" id="imgShow">
    </div>
</section>
@endsection

@section('scripts')
<script>
    var longday = 0;

    $(document).ready(function() {

        var page = "{{ isset($_REQUEST['page']) ? (int)$_REQUEST['page'] : 1 }}";

        var isLoading = false;
        var $window = $(window);
        var $document = $(document);

        $('#ok').bind('DOMMouseScroll mousewheel touchmove', function(e) {
            var last = parseInt($('#ok').data('lastpage')) || 1;
            if (last <= page || isLoading) return;

            if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
                // Scrolling up
            } else {
                // Scrolling down
                if ($window.scrollTop() + $window.height() + 150 >= $document.height()) {
                    page++;
                    loadMoreData(page);
                }
            }
        });

        function loadMoreData(page) {
            isLoading = true;
            $.ajax({
                url: '?page=' + page,
                type: "get",
                beforeSend: function() {
                    $(document.body).css({
                        'cursor': 'wait'
                    });
                    $(".preloader").show();
                },
                complete: function() {
                    $(document.body).css({
                        'cursor': 'default'
                    });
                    $(".preloader").hide();
                    isLoading = false;
                }
            }).done(function(data) {
                if (data.posts) {
                    $('.posts').append(data.posts);
                    // If there are more pages, update the data attribute
                    if (data.last_page) {
                        $('#ok').data('lastpage', data.last_page);
                    }
                }
            }).fail(function(jqXHR, ajaxOptions, thrownError) { });
        }

    })

    function change_calculation(anaid, poolid, subid, proid, type){
        if (poolid == "") { poolid = 0; }
        var check = $('#someSwitchOptionDefault' + anaid).is(':checked');
        var dataString = 'subid=' + subid + '&proid=' + proid + '&anaid=' + anaid + '&poolid=' + poolid + '&type='+ type +'&is_ck=' + check;
        if (check == true) {
            $.ajax({
                type: "POST",
                url: "{{ route('Aajax.saveCalculation') }}",
                data: dataString,
                success: function(data) {
                    $("#progrssRandomVal_"+anaid).attr("aria-valuenow", data.record.ma);
                    $("#progrssRandomVal_"+anaid).attr("style", "max-width: "+data.record.ma+"%; background-color: "+data.record.color);
                    $("#progrssRandomtxt_"+anaid).html(data.record.ma+"%");
                }
            });
        }else{
            $.ajax({
                type: "POST",
                url: "{{ route('Aajax.saveCalculation') }}",
                data: dataString,
                success: function(data) {
                    $("#progrssRandomVal_"+anaid).attr("aria-valuenow", data.record.ma);
                    $("#progrssRandomVal_"+anaid).attr("style", "max-width: "+data.record.ma+"%; background-color: "+data.record.color);
                    $("#progrssRandomtxt_"+anaid).html(data.record.ma+"%");
                }
            });
        }
    }

    $('body').on('click', function (e) {
        $('[data-toggle=popover]').each(function () {

            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                $(this).popover('hide');
            }
        });
    });

    $("#longday").on('change', function(){

        var select = $(this).find('option:selected');
        longday = select.val();
        var proid = "{{ request()->proid }}";
        var subid = "{{ request()->subid }}";
        var comid = "{{ request()->comid }}";
        if(comid == "")
            var url = "{{ url('/dashboard/p') }}/"+proid+"/"+subid+"/"+longday;
        else
            var url = "{{ url('/dashboard/c/p') }}/"+proid+"/"+subid+"/"+longday+"/"+comid;

        $("#cb-rl2").attr("data-href", url);
        window.location.href = url;
        // $("#longday_link").attr("href", url);
    })

    function checkLongDay(){

        if(longday < 30 ) Swal.fire({ type: 'warning', title: "{{__('action.choose_days')}}", showConfirmButton: false, timer: 3500 });
    }

</script>
<script>$.ajaxPrefilter(function( options, originalOptions, jqXHR ) {    options.async = true; });</script>
@endsection
