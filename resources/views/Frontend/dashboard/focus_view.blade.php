@extends('Frontend.partials.layout')

@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/swiper/swiper.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/js/huebee/huebee.min.css') }}">
    <style>
        .page_dot_preloader {
            position: absolute;
            content: '';
            width: 100%;
            height: 100%;
            background: #9696961f;
            z-index: 11;
            top: 0;
            left: 0;
            padding-top: 9%;
        }

        #focusImage{
            max-width: 100%;
        }

        .card-header {
            display: none;
        }

        @media only screen and (max-width: 1280px) {
            .widget-three-button button{
                height: 50px;
            }
        }

        @media only screen and (min-width: 1025px) {
            .alert.alert-info.alert-dismissible.mt-3 {
                display: none;
            }

            .widget-two-box-content {
                height: 90px !important;
                margin-bottom: 3px !important;
            }

            .widget-two-box-content p {
                font-size: .75rem !important;
                line-height: .9rem !important;
            }

            .widget-two-box-content.pdf-ready p {
                font-size: 1rem !important;
                line-height: 1.1rem !important;
            }

            .widget-two-box {
                min-height: 250px !important;
                padding-left: 9px;
            }

            .systematic-view .widget-three {
                margin-top: 0px !important;
                padding-left: 30px;
                margin-right: -11px;
            }

            .widget-two.influence-section {
                margin-bottom: 0px !important;
            }

            .main-sys-view {
                padding: 0px;
                min-height: calc(100vh - 250px);
                height: calc(100vh - 250px);
                overflow: hidden;
            }

            .main-sys-view-image {
                display: block;
                height: 100%;
            }

            .dc-title-preview {
                margin-top: 0px !important;
            }

            #custom-top-padding {
                padding-bottom: 0px !important;
            }

            .card-body {
                padding-bottom: 10px !important;
                padding-right: 20px !important;
            }

            .widget-two-box .col-md-4 {
                padding-left: 3px !important;
            }

            .systematic-view {
            }

            .layout-container {
            }

            .layout-inner {min-height: calc(100vh - 43px);}


            .layout-content  .systematic-view .widget-three {
                padding-bottom: 65px !important;
                min-height: 361px !important;
            }

            .layout-content .widget-three-button-default {
                bottom: -55px !important;
            }

            .layout-content .systematic-view .widget-three {
                margin-top:  0px !important;
            }


            .card-body {
                padding: 10px !important;
            }

            img#focusImage {
                position: relative;
                object-fit: contain !important;
            }

            .main-sys-view-image {
                height: calc(100vh - 215px) !important;
                margin-bottom: 0px !important;
                margin-top: -131px !important;
                overflow: hidden !important;
            }

            .layout-container .main-sys-view {
                height: calc(100vh - 205px) !important;
                min-height: calc(100vh - 205px) !important;
            }

            .form-group.custom-fullHight-SelectBox {
                margin-bottom: 4px !important;
            }

            .form-group.d-flex {
                margin-bottom: 0px !important;
            }

            .layout-container .widget-two-box-content {
                height: 80px !important;
            }

            .layout-container .widget-two-box {
                height: 250px !important;
            }

            .layout-container .widget-input-box-one {
                height: 40px !important;
            }

            .layout-container .widget-input-box-three {
                height: 40px !important;
            }

            .layout-container .widget-input-box-two-input {
                height: 40px !important;
            }

            .layout-container .widget-input-box.d-flex.justify-content-between.align-items-center.px-2 {
                height: 65px !important;
            }

            .layout-container .widget-three-image.position-relative.mt-3.px-1 {}

            .layout-container .widget-three-image .form-control {
                height: 30px !important;
            }

            .layout-container .layout-content .systematic-view .widget-three {
                min-height: 298px !important;
            }

            .layout-container #custom-top-padding {
                padding-top: 5px !important;
            }

            .layout-container .widget-three-image img {
                max-height: 100px !important;
            }
            div#dcIcon-category {
                margin-bottom: 5px !important;
            }

            .dc-name {
                margin-top: -15px !important;
                margin-bottom: -9px !important;
            }

            .dc-title-preview {
                margin-bottom: 0px !important;
                padding-left: 0px !important;
            }

            .swiper-container .swiper-slide {}

            .swiper-container .swiper-slide img {
                height: 30px !important;
            }
            .append-crcl {
                bottom: 0px;
            }

            .main-sys-view-image {
                height: calc(100vh - 341px) !important;
                margin-bottom: 0px !important;
                margin-top: 0px !important;
            }

            .main-sys-view-image img {
                /* height: 100%; */
                width: 100%;
                object-fit: contain !important;
                object-position: center center !important;
            }


            /* Space Issue */
            .all-widgets-for-dc-view {
                position: relative;
                height: calc(100vh - 160px);
            }

            .all-widgets-for-dc-view .widget-two {
                height: 50% !important;
            }

            .layout-container  .all-widgets-for-dc-view .widget-two-box {
                height: calc(100% - 55px) !important;
            }

            .layout-container .all-widgets-for-dc-view .widget-two-box .widget-two-box-content {height: 100% !important;}

            .layout-container .all-widgets-for-dc-view .widget-two-box .col-md-4 {
                height: 33% !important;
            }

            .layout-container .all-widgets-for-dc-view .widget-two-box .row {
                position: relative;
                height: 100% !important;
            }

            .layout-container .layout-content .systematic-view .widget-three {
                max-height: 50% !important;
            }
            /* Space Issue End */

             /* DC - Background image / Border not fine - it is too small. */
             .layout-container .main-sys-view {
                padding-top: 0px !important;
            }

            .main-sys-view-image {
                height: 100% !important;
            }
            /* DC - Background image / Border not fine - it is too small. End */


            .d-flex.flex-column.flex-sm-row.only-fig-img {
                margin-top: 50px;
            }

        }


        @media screen and (max-height: 800px) {
            .layout-container .widget-three-image img {
                max-height: 70px !important;
            }
        }

        @media screen and (max-height: 700px) {
            .layout-container .widget-three-image img {
                max-height: 60px !important;
            }
            .widget-content.widc-n {
                transform: scale(0.75);
                margin-top: -15px;
            }
        }


        @media only screen and (max-width:1199px){
            .focus-main-view,
            .influence-section{
                border-bottom: 1px solid rgba(24, 28, 33, 0.06);
            }

            .focus-main-view {
                padding-left: 0px;
                padding-right: 0px;
                margin: 0px 1rem;
                padding-bottom: 1rem !important;
            }

            .influence-section {
                margin-top: 0px !important;
                padding-bottom: 1rem !important;
            }

            .influence-section + #default-figure-view.default-figure-view{
                margin-top: 0px !important;
            }

            .influence-section .widget-two-box {
                min-height: 200px;
            }
        }

        @media only screen and (max-width:576px){
            .influence-section .widget-two-box {
                min-height: 150px;
            }

            #btnScrShot {
                padding: 5px 8px;
                font-size: 12px;
            }
        }


        @media (max-width: 576px) and (min-width: 320px) {
            .systematic-view .widget-three {
                height: 660px !important;
            }
        }
        .star-six {
             top: -6px;
        }
        .infotest-content-default.mt-3.own-topic-text-div.showw {
            top: -36px;
            height: 320px;
        }

        .infotest-content {
            height: 303px !important;
            top: unset !important;
            bottom: -75px !important;
            z-index: 99999 !important;
        }

        @media screen and (max-height: 895px) and (min-width: 768px) {
            .layout-content .widget-three-button-default {
                top: 230px !important;
            }
        }
        @media screen and (max-height: 760px) and (min-width: 768px){
            .layout-container .all-widgets-for-dc-view .widget-two-box {
                min-height: 80% !important;
            }
            .layout-content .widget-three-button-default {
                top: 230px !important;
            }
        }
    </style>
@endsection

@section('content')
{{-- focus blade content --}}
<section class="systematic-view" id="refreshFukos">
    <div class="row">
        <div class="col-md-12">
            {{-- card --}}
            <div class="card">
                {{-- card body --}}
                <div class="card-body">
                    <div class="page_dot_preloader hide">
                        <div class="loading">
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </div>

                    <div class="row">
                        {{-- col-7 --}}
                        <div class="col-xl-7 dis-w-100 dis-h-100 mb-3 focus-main-view">
                            <div class="row">
                                <div class="@if($submenu->show_button == 1){{'col-md-3'}}@else{{'col-md-3'}}@endif col-7">
                                    <div class="form-group custom-fullHight-SelectBox">
                                        <select class="custom-select selectpicker" id="selectFocusimg" data-live-search="true">
                                            <option value="0" disabled>{{__('action.Select_Image')}}</option>
                                            @if (!empty($images))
                                                @foreach($images as $image)
                                                    <option id="{{ 'focus_'.$image->id }}" value="{{ $image->id }}" data-src='{{ asset($image->image) }}' data-content="<img src='{{ asset($image->thumbnil) }}' height='25px' width='25px'/> &nbsp;{{$image->name}}"></option>
                                                @endforeach
                                            @endif
                                        </select>
                                        <input type="hidden" id="direction" value="{{$submenu->direction_status}}">
                                        <input type="hidden" id="view_option" value="{{$submenu->view_option}}">
                                    </div>
                                </div>

                                <div style="padding-right: 0.75rem;">
                                    @if($subStatus == true)
                                    <div class="dropdown focus-dropdown">
                                        <button class="btn btn-secondary " type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fa fa-bars" aria-hidden="true"></i>
                                        </button>
                                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                            <button type="button" class="dropdown-item" onclick="modal_render('1')"><i class="ion ion-ios-save">&nbsp;</i>{{__('action.save_session')}}</button>
                                            <button type="button" class="dropdown-item" onclick="modal_render('2')"><i class="ion ion-ios-folder-open">&nbsp;</i>{{__('action.open_session')}}</button>

                                            <button type="button" class="dropdown-item" onclick="modal_render('3')"><i class="fas fa-file-pdf">&nbsp;</i>{{__('action.pdf_title')}}</button>
                                            <button type="button" class="dropdown-item" onclick="modal_render('4')" ><i class="fas fa-file-pdf">&nbsp;</i>{{__('action.create_pdf_send_mail')}}</button>

                                            <div class="divider"></div>
                                            @if($submenu->show_button == 1)
                                            <div class="dropdown-item fileUpload blue-btn btn width100 text-left" href="javascript:void()">
                                                <span><i class="fa fa-paperclip" aria-hidden="true"></i>{{__('action.upload_own_img')}}</span>
                                                <input type="file" class="uploadlogo" />
                                            </div>
                                            <button type="button" class="dropdown-item" onclick="showOWnImages()"><i class="fas fa-images">&nbsp;</i>{{__('action.open_own_images')}}</button>
                                            @endif
                                            @if($submenu->direction_status)
                                            <div class="dropdown-item btn width100 text-left" href="javascript:void()">
                                                <label class="custom-control custom-checkbox">
                                                    <input type="checkbox" name="direction_status" id="direction_status" class="custom-control-input" @if($data["user"]->useroption->dc_direction_view == 1) {{"checked"}} @endif>
                                                    <span class="custom-control-label">{{__('action.auto_direction_dc')}}</span>
                                                </label>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                    @endif
                                    <input type="hidden" id="point" value="1">
                                </div>

                                <div class="">
                                    <div class="form-group d-flex" style="gap: 0.75rem;">
                                        @if($subStatus == true)
                                            <button class="btn btn-default saving" id="btnScrShot" onclick="ScreenShot()">
                                                <img src="{!! asset('images/screenshot.png') !!}" alt="" height="auto" width="20px">
                                                <b>{{__('action.screenshot')}}</b><span class="shotdot hidedot"><b>.</b></span><span class="shotdot hidedot"><b>.</b></span><span class="shotdot hidedot"><b>.</b></span>
                                            </button>

                                            <button class="btn btn-default saving" id="ssModal" onclick="ssModal()" style="@if(count(getFocusScreenShot()) <= 0){{'display: none'}}@endif">
                                                <i class="fas fa-images"></i>
                                            </button>
                                        @endif
                                        @if($submenu->show_button == 1)
                                            <button class="btn btn-default saving" id="saveUp" onclick="saveUpload()" style="display: none">
                                                <i class="fas fa-upload"></i>
                                                <b>{{trans('action.save_upload')}}</b><span class="shotdot hidedot"><b>.</b></span><span class="shotdot hidedot"><b>.</b></span><span class="shotdot hidedot"><b>.</b></span>
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            {{-- *************** Systematic View Image and Shapes *************** --}}

                            <div class="main-sys-view pdf-focus-view" id="focusMainView">
                                @if($submenu->manual_direction == 1)
                                    <div id="default-direction-btns" class="hide">
                                        <div class="direction_btn_default">
                                            <button id="default_dc_front" class="btn btn-success b-block" data-toggle="tooltip" data-placement="right" title="{{ __('action.dc_front') }}"><i class="fa fa-caret-left top_arrow" ></i></button>
                                            <button id="default_dc_left" class="btn btn-success b-block" data-toggle="tooltip" data-placement="right" title="{{ __('action.dc_left') }}"><i class="fa fa-caret-left" ></i></button>
                                            <button id="default_dc_normal" class="btn btn-success b-block" style="padding-left: 14px; padding-right: 14px;" data-toggle="tooltip" data-placement="right" title="{{ __('action.dc_default') }}"><i class="fa fa-arrows-alt-h"></i></button>
                                            <button id="default_dc_right" class="btn btn-success b-block" data-toggle="tooltip" data-placement="right" title="{{ __('action.dc_right') }}"><i class="fa fa-caret-right" ></i></button>
                                            <button id="default_dc_back" class="btn btn-success  b-block" data-toggle="tooltip" data-placement="right" title="{{ __('action.dc_back') }}"><i class="fa fa-caret-right bottom_arrow" ></i></button>
                                        </div>
                                    </div>
                                    <div id="normal-direction-btns" class="hide">
                                        <div class="direction_btn1 direction-btns">
                                            <button id="dc-top-left" class="btn btn-success b-block dc-normal-buttons" data-toggle="tooltip" data-placement="left" data-original-title=""><i class="fas fa-long-arrow-alt-left icon-transform1"></i></button>
                                            <button id="dc-top-right" class="btn btn-success b-block dc-normal-buttons" data-toggle="tooltip" data-placement="right" data-original-title="" style="margin-left: 57px;"><i class="fas fa-long-arrow-alt-right icon-transform2"></i></button>
                                        </div>
                                        <div class="direction_btn direction-btns">
                                            <button id="dc-front" class="btn btn-success b-block dc-normal-buttons" data-toggle="tooltip" data-placement="right" data-original-title=""><i class="fas fa-long-arrow-alt-left top_arrow" ></i></button>
                                            <button id="dc-normal" class="btn btn-success b-block hide" data-toggle="tooltip" data-placement="top" data-original-title=""><i class="fa fa-arrows-alt-h"></i></button>
                                            <button id="dc-back" class="btn btn-success  b-block dc-normal-buttons" data-toggle="tooltip" data-placement="bottom" data-original-title=""><i class="fas fa-long-arrow-alt-right bottom_arrow" ></i></button>
                                        </div>
                                        <div class="direction_btns direction-btns">
                                            <button id="dc-left" class="btn btn-success b-block dc-normal-buttons" data-toggle="tooltip" data-placement="left" data-original-title=""><i class="fas fa-long-arrow-alt-left"></i></button>
                                            <button id="dc-right" class="btn btn-success b-block dc-normal-buttons" data-toggle="tooltip" data-placement="right" data-original-title="" style="margin-left: 57px;"><i class="fas fa-long-arrow-alt-right"></i></button>
                                        </div>
                                        <div class="direction_btn4 direction-btns">
                                            <button id="dc-bottom-left" class="btn btn-success b-block dc-normal-buttons" data-toggle="tooltip" data-placement="left" data-original-title=""><i class="fas fa-long-arrow-alt-left icon-transform2"></i></button>
                                            <button id="dc-bottom-right" class="btn btn-success b-block dc-normal-buttons" data-toggle="tooltip" data-placement="right" data-original-title="" style="margin-left: 57px;"><i class="fas fa-long-arrow-alt-right icon-transform1"></i></button>
                                        </div>
                                    </div>
                                @endif
                                <div class="main-sys-view-image" id="appendiconBox">
                                    <img src="{{ asset('/images/blank.png') }}" alt="" id="focusImage" data-own="0">
                                </div>

                                <div id="trash">
                                    <i class="fas fa-door-open d-block" id="trashicon" style="font-size: 48px" data-toggle="tooltip" data-placement="top" title="{{ __('action.delete_widget') }}"></i>
                                </div>
                            </div>
                        </div>
                        {{-- col-5 --}}
                        <div class="col-xl-5 dis-w-100">
                            <div class="p-all-sm-0 all-widgets-for-dc-view">

                                {{-- *************** Systematic View Right Panel *************** --}}
                                {{-- <div class="d-flex flex-column justify-content-center h-100"></div>  --}}

                                <div class="widget-two mb-3 d-xl-block dis-none influence-section">
                                        <div class="widget-two-button">
                                            <div class="row">
                                                <div class="col-md-7 col-6 pr-1 elfuless_select_box_parent">
                                                    <div class="form-group elfuless_select_box custom-fullHight-SelectBox">
                                                        <select class="selectpicker" id="selectGroup" multiple required data-live-search="true" title="{{__('action.enfluselect')}}" data-count-selected-text= "{0} {{__('action.groupselected')}}" data-selected-text-format="count > 1">
                                                            @if(!$groups->isEmpty())
                                                                @foreach($groups as $group)
                                                                    <option id="FocusGroupid_{{ $group->id}}" value="{{ ($group->status) ? 'g_'.$group->id : 'p_'.$group->id }}">
                                                                        {{ $group->group_name }}
                                                                    </option>
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-5 col-6 pl-0 elfuless_btn_box">
                                                    <div class="text-left">
                                                        <button class="btn btn-success" id="btnEnfluss">{{__('action.einfluss')}}</button>
                                                        <button class="btn btn-success" id="btnTopNine">{{__('action.top9')}}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="widget-two-box" id="efBoxes" data-photo="false">
                                            <div class="row">
                                                {{-- 1st --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_1">

                                                        <div class="widget-two-box-content-inner" id="boxColor_1">
                                                            <p id="boxText_1" data-toggle="tooltip" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_1" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 1 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_1" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_1">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head1"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body1"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_1').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_1').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 1 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox1" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                {{-- 2nd --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_2">
                                                        <div class="widget-two-box-content-inner" id="boxColor_2">
                                                            <p id="boxText_2"  data-toggle="tooltip" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_2" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 2 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_2" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_2">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head2"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body2"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_2').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_2').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 2 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox2" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                {{-- 3rd --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_3">
                                                        <div class="widget-two-box-content-inner" id="boxColor_3">
                                                            <p id="boxText_3"  data-toggle="tooltip" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_3" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 3 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_3" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_3">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head3"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body3"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_3').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_3').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 3 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox3" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                {{-- 4th --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_4">
                                                        <div class="widget-two-box-content-inner" id="boxColor_4">
                                                            <p id="boxText_4"  data-toggle="tooltip" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_4" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 4 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_4" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_4">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head4"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body4"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_4').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_4').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 4 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox4" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                {{-- 5th --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_5">
                                                        <div class="widget-two-box-content-inner" id="boxColor_5">
                                                            <p id="boxText_5"  data-toggle="tooltip" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_5" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 5 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_5" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_5">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head5"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body5"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_5').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_5').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 5 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox5" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                {{-- 6th --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_6">
                                                        <div class="widget-two-box-content-inner" id="boxColor_6">
                                                            <p id="boxText_6"  data-toggle="tooltip" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_6" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 6 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_6" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_6">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head6"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body6"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_6').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_6').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 6 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox6" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                {{-- 7th --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_7">
                                                        <div class="widget-two-box-content-inner" id="boxColor_7">
                                                            <p id="boxText_7"  data-toggle="tooltip" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_7" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 7 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_7" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_7">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head7"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body7"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_7').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_7').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 7 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox7" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                {{-- 8th --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_8">
                                                        <div class="widget-two-box-content-inner" id="boxColor_8">
                                                            <p id="boxText_8"  data-toggle="tooltip" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_8" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 8 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_8" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_8">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head8"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body8"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_8').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_8').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 8 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox8" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                {{-- 9th --}}
                                                <div class="col-md-4 col-6 pr-0">
                                                    <div class="widget-two-box-content" id="boxid_9">
                                                        <div class="widget-two-box-content-inner" id="boxColor_9">
                                                            <p id="boxText_9"  data-toggle="tooltip" data-placement="auto" data-placement="bottom" data-state="secondary"></p>
                                                        </div>
                                                        <span id="boxCart_9" class="widget-two-box-content-plusIcon">
                                                            {{-- box Info 9 --}}
                                                            <a href="javascript:void(0)" class="boxInfo_9" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" data-content="">
                                                                <i class="fas fa-info-circle"></i>
                                                                {{-- popover content --}}
                                                                <div class="boxInfo-content" id="boxInfoContent_9">
                                                                    <div class="pt-0">
                                                                        <p class="d-flex justify-content-between align-items-center m-0">
                                                                            <span class="boxInfo-content-heading font-weight-bold" id="boxinfo_head9"></span>
                                                                        </p>
                                                                    </div>
                                                                    <hr class="boxHr">
                                                                    <div class="pt-1">
                                                                        <p class="text-justify mb-0" id="boxinfo_body9"></p>
                                                                    </div>
                                                                </div>
                                                                {{-- /popover content --}}
                                                            </a>
                                                            <script>
                                                                $(function($) {
                                                                    $('.boxInfo_9').popover({
                                                                        html: true,
                                                                        content: function() {
                                                                            $('.popover').popover('hide')
                                                                            return $('#boxInfoContent_9').html();
                                                                        }
                                                                    });
                                                                });
                                                            </script>
                                                            {{-- /box Info 9 --}}
                                                            <a href="javascript:void(0)" id="cartaddbox9" class="">
                                                                <i class="fas fa-plus-circle"></i>
                                                            </a>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-12" style="z-index: 9999;">
                                                    <div class="circle-number-label text-center">
                                                        <span class="cnl-label" id="efCrclNum"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                </div>

                                @if($submenu->view_option == 1 || $submenu->view_option == 3)
                                    <div class="widget-three text-center mt-xl-0 default-figure-view show" id="default-figure-view">
                                        <div class="row">
                                            <div class="col-md-12 p-all-sm-0">
                                                <div class="widget-content widc-n">
                                                    {{--<div class="text-right mb-3">
                                                        <button type="button" id="default-figure-btn" class="btn btn-success">Switch View</button>
                                                    </div>--}}
                                                    @if($submenu->show_icon == 1)
                                                    <div class="widget-input-box d-flex justify-content-between align-items-center px-2">
                                                        <div class="">
                                                            <input type="text" class="widget-input-box-one form-control" id="iconTxt1" value="" onchange="ChangeTextDefault('iconTxt1')">
                                                        </div>
                                                        <div class="star-six">
                                                            <input type="text" class="widget-input-box-three form-control" id="iconTxt3" value="" onchange="ChangeTextDefault('iconTxt3')">
                                                        </div>
                                                        <div class="">
                                                            <div class="widget-input-box-two">
                                                                <input type="text" class="widget-input-box-two-input form-control" id="iconTxt2" value="" onchange="ChangeTextDefault('iconTxt2')">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @endif

                                                    <div class="widget-three-image position-relative mt-3 px-1">

                                                        @if($submenu->show_icon == 1)
                                                        <div class="d-flex flex-column flex-sm-row only-fig-img">
                                                            <div class="d-flex">
                                                                <div class="mr-2">
                                                                    <img class="oldImg-default" src="{!! asset('images/analysis_icon/opa_default.png') !!}" alt="">
                                                                    <input type="text" value="" class="form-control"
                                                                        onchange="ChangeTextDefault('opa_default')" id="opa_default">
                                                                </div>
                                                                <div class="mr-2">
                                                                    <img class="oldImg-default" src="{!! asset('images/analysis_icon/oma_default.png') !!}" alt="">
                                                                    <input type="text" value="" class="form-control"
                                                                        onchange="ChangeTextDefault('oma_default')" id="oma_default">
                                                                </div>

                                                                <div class="mr-2">
                                                                    <img class="normalImg-default" src="{!! asset('images/analysis_icon/man_default.png') !!}" alt="">
                                                                    <input type="text" value="" class="form-control"
                                                                        onchange="ChangeTextDefault('man_default')" id="man_default">
                                                                </div>
                                                                <div class="mr-2">
                                                                    <img class="normalImg-default" src="{!! asset('images/analysis_icon/woman_default.png') !!}"
                                                                        alt="">
                                                                    <input type="text" value="" class="form-control"
                                                                        onchange="ChangeTextDefault('woman_default')" id="woman_default">
                                                                </div>
                                                                <div class="mr-2">
                                                                    <img class="childImg-default kindMH-default" src="{!! asset('images/analysis_icon/kindM_default.png') !!}"
                                                                        class="sm" alt="">
                                                                    <input type="text" value="" class="form-control"
                                                                        onchange="ChangeTextDefault('kindM_default')" id="kindM_default">
                                                                </div>
                                                                <div class="mr-2">
                                                                    <img class="childImg-default kindMH-default" src="{!! asset('images/analysis_icon/kindw_default.png') !!}"
                                                                        class="sm" alt="">
                                                                    <input type="text" value="" class="form-control"
                                                                        onchange="ChangeTextDefault('kindw_default')" id="kindw_default">
                                                                </div>
                                                                <div>
                                                                    <img src="{!! asset('images/analysis_icon/baby_default.png') !!}"
                                                                        class="baby-default babyImg-default babyH-default" alt="">
                                                                    <input type="text" value="" class="form-control"
                                                                        onchange="ChangeTextDefault('baby_default')" id="baby_default">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @endif
                                                    </div>

                                                    {{-- *************** Infotest Content *************** --}}
                                                    <div class="infotest-content-default mt-3 own-topic-text-div">
                                                        <div class="form-group position-relative info-test-content-box">
                                                            <button type="button" style="float: right; margin-bottom: 10px; cursor: pointer;"
                                                                onClick="(function(){
                                                                $('.infotest-content-default').removeClass('showw');
                                                                })();"class="btn btn-danger" data-dismiss="modal">
                                                                {{ trans('action.close_dc_infotest') }}
                                                            </button>
                                                            <textarea id="own_topic" name="" class="form-control text-center" rows="4">{{ $data["user"]->thema_speichern }}</textarea>
                                                            <div class="info-test-content-box-two" id="infoPopUp2">
                                                                <div class="info-test-content-box-two-inner">
                                                                    <p class="f-18"> <b>{{ trans('action.information_tested') }}</b> </p>
                                                                    <div class="d-block info-pr">
                                                                        <div class="progress mt-2">
                                                                            <div class="progress-bar info-pr-bar bg-primary"
                                                                                data-size="100" role="progressbar">
                                                                                <span class="title"></span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                            </div>
                                                        </div>

                                                        <div class="progress mb-3">
                                                            <div class="progress-bar" id="infoProgressValue332" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="max-width: 100%">
                                                                <span class="title" id="infovalue2"></span>
                                                            </div>
                                                        </div>


                                                        <div class="info-button cus-info-button">
                                                            <div class="d-flex justify-content-start flex-wrap" style="gap:4px;">
                                                                <button type="button" class="btn btn-primary icon w-auto" onclick="btnAddCart(this)">{{__('action.add_to_cart')}}</button>

                                                                <button type="button" class="btn btn-success icon w-auto" onclick="topicSave(this,'Save')">{{__('action.save_topic')}}</button>

                                                                <button class="btn btn-primary mb-2 mb-sm-0 w-auto" onclick="infoTestDefault()">{{trans('action.info_test')}}</button>

                                                                <button class="btn btn-success w-auto" id="korBtn" onclick="KorrigierenDefault()">{{ trans('action.correct') }}</button>
                                                            </div>

                                                        </div>
                                                    </div>
                                                    <div class="widget-three-button widget-three-button-default @if($submenu->show_icon != 1) widget-three-button-x @endif d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
                                                        <div class="ml-2 d-flex" id="crcl2" ondblclick="mainCrclCopy()" ontouchstart="mainCrclCopys()">
                                                            <span class="crcl"></span>
                                                        </div>
                                                        <div class="ml-1 mt-2 mt-sm-0 d-flex focusAction_btn">
                                                            <button class="btn btn-info" onclick="checkEnergifeld()">{{__('action.checkenergiefeld')}}</button>
                                                            <button class="btn btn-danger ml-1" onclick="refreshDiv()">{{__('action.deleteView')}}</button>
                                                        </div>
                                                        <div class="mr-1 mt-2 mt-sm-0 d-flex ml-1 ml-sm-0 focusAction_btn" style="position: relative;right: -22px;">
                                                            <button class="btn btn-info" id="infotestBtnDefault">{{__('action.infotest')}}</button>
                                                            @if($submenu->view_option == 3 && $data["user"]->user_type != 0)
                                                                <button class="btn btn-success ml-1" id="default-figure-btn" data-toggle="tooltip" data-placement="top" data-state="secondary" title="{{ trans('action.dc_switch_view') }}"><i class="fas fa-sync-alt fa-fw"></i></button>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    @if($submenu->view_option == 1)
                                                        <input class="color-input form-control set-huebee-bg-color" style="display:none"/>
                                                    @endif

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                @if($submenu->view_option == 2 || $submenu->view_option == 3)
                                    <div class="widget-three text-center mt-xl-0 @if($submenu->view_option == 3) hide @endif normal-figure-view" id="widget-setup">
                                        {{--<div class="text-right mb-3">
                                            <button type="button" id="normal-figure-btn" class="btn btn-success">Switch View</button>
                                        </div>--}}
                                        <div class="row">
                                            <div class="col-md-12 col-sm-12 mb-3">
                                                <div class="col-md-6 col-sm-12 dc-name">
                                                    <input type="text" name="icon_title" id="icon-title" class="form-control" placeholder="{{ trans('action.dc_enter_name') }}">
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="row">
                                                    <div class="col-md-8" style="margin-left: 0px;">
                                                        <div class="row">
                                                            <div class="col-md-12 col-sm-12">
                                                                <div class="swiper-container mb-4" id="dcIcon-category">
                                                                    <div class="swiper-wrapper">
                                                                        @if (!$dcicon_category->isEmpty())
                                                                            @foreach($dcicon_category as $icon)
                                                                                <div class="swiper-slide dcIconCategory" id="category_{{ $icon['id'] }}" data-id="{{ $icon['id'] }}" data-toggle="tooltip" data-placement="top" title="{{ $icon['name'] }}" data-name="{{ $icon['name'] }}" data-src="{{ asset('/storage/dc-icons/category/'.$icon['default_image']) }}"><img src="{{ asset('/storage/dc-icons/category/'.$icon['default_image']) }}" height="40px" width="auto"></div>
                                                                            @endforeach
                                                                        @endif
                                                                    </div>
                                                                    <div class="swiper-button-prev"><i class="ion ion-ios-arrow-back d-block"></i></div>
                                                                    <div class="swiper-button-next"><i class="ion ion-ios-arrow-forward d-block"></i></i></div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12 col-sm-12">
                                                                <div class="swiper-container" id="dcIcon-size">
                                                                    <div class="swiper-wrapper" id="dcIconSize-body"></div>
                                                                    <div class="swiper-button-prev"><i class="ion ion-ios-arrow-back d-block"></i></div>
                                                                    <div class="swiper-button-next"><i class="ion ion-ios-arrow-forward d-block"></i></i></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4 col-sm-12 dc-bottom-content">
                                                        <div class="col-md-12" id="preview-icon" data-update="off"></div>
                                                        <input class="color-input form-control set-huebee-bg-color rounded-circle ui-w-30" data-toggle="tooltip" data-placement="top" title="{{ trans('action.dc_color') }}" data-name="{{ trans('action.dc_color') }}"/>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12 mt-5 mb-3 dc-title-preview">
                                                <div class="col-md-4 col-sm-12 dc-bottom-content">
                                                    <input type="text" placeholder="{{ trans('action.dc_anchor_box') }}" class="form-control" id="dc-anchor-box" onchange="appendAnchorBox()">
                                                </div>
                                                <div class="col-md-3 btn-append dc-bottom-content">
                                                    <button class="btn btn-warning mr-2 hide" id="cancle-btn"><i class="fas fa-times"></i></button>
                                                    <button class="btn btn-success hide" id="append-btn"><i class="fas fa-plus"></i></button>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="infotest-content mt-3 own-topic-text-div">
                                                    <div class="form-group position-relative info-test-content-box">
                                                        <button type="button" style="float: right; margin-bottom: 10px; cursor: pointer;" onClick="(function(){
                                                            $('.infotest-content').removeClass('showw');
                                                            })();" class="btn btn-danger" data-dismiss="modal">
                                                            {{ trans('action.close_dc_infotest') }}
                                                        </button>

                                                        <textarea id="own_topic" id="txt" name="" class="form-control text-center txt-infoPopUp1" rows="4">{{ $data["user"]->thema_speichern }}</textarea>

                                                        <div class="info-test-content-box-two" id="infoPopUp1">
                                                            <div class="info-test-content-box-two-inner">
                                                                <p class="f-18"> <b>{{ trans('action.information_tested') }}</b> </p>
                                                                <div class="d-block info-pr">
                                                                    <div class="progress mt-2">
                                                                        <div class="progress-bar info-pr-bar bg-primary"
                                                                            data-size="100" role="progressbar">
                                                                            <span class="title"></span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="progress mb-3">
                                                        <div class="progress-bar" id="infoProgressValue33" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="max-width: 100%">
                                                            <span class="title" id="infovalue"></span>
                                                        </div>
                                                    </div>

                                                    <div class="info-button cus-info-button">
                                                        <div class="d-flex justify-content-start flex-wrap" style="gap:4px;">
                                                            <button type="button" class="btn btn-primary icon w-auto" onclick="btnAddCart(this)">{{__('action.add_to_cart')}}</button>

                                                            <button type="button" class="btn btn-success icon w-auto" onclick="topicSave(this,'Save')">{{__('action.save_topic')}}</button>

                                                            <button class="btn btn-primary mb-2 mb-sm-0 w-auto" onclick="infoTest()">{{trans('action.info_test')}}</button>

                                                            <button class="btn btn-success w-auto" id="korBtn" onclick="Korrigieren()">{{ trans('action.correct') }}</button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="widget-three-button @if($submenu->show_icon != 1) widget-three-button-x @endif d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
                                                    <div class="ml-2 d-flex" id="crcl2" ondblclick="mainCrclCopy()" ontouchstart="mainCrclCopys()">
                                                        <span class="crcl"></span>
                                                    </div>
                                                    <div class="ml-1 mt-2 mt-sm-0 d-flex focusAction_btn">
                                                        <button class="btn btn-info" onclick="checkEnergifeld()">{{__('action.checkenergiefeld')}}</button>
                                                        <button class="btn btn-danger ml-1" onclick="refreshDiv()">{{__('action.deleteView')}}</button>
                                                    </div>
                                                    <div class="mr-1 mt-2 mt-sm-0 d-flex ml-1 ml-sm-0 focusAction_btn" style="position: relative; right: -12px;">
                                                        <button class="btn btn-info" id="infotestBtn">{{__('action.infotest')}}</button>
                                                        @if($submenu->view_option == 3 && $data["user"]->user_type != 0)
                                                            <button class="btn btn-success ml-1" id="normal-figure-btn" data-toggle="tooltip" data-placement="top" data-state="secondary" title="{{ trans('action.dc_switch_view') }}"><i class="fas fa-sync-alt fa-fw"></i></button>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="alert alert-info alert-dismissible mt-3" style="padding: .5rem .5rem">
                                <button type="button" class="close" data-dismiss="alert" style="padding: .5rem .5rem">&times;</button>
                                <strong>{{ trans('action.info') }}!</i></strong> {!! __('action.focus_cicrle_info') !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{-- card --}}
        </div>
    </div>

    {{-- ********* Right Slide Panel ************ --}}
    @livewire('dashboard.right-cart-modal', ['key' => 'right-cart-modal'])
    @include('Frontend.dashboard.focus_modal')
    <div class="modal-show-area"></div>
</section>
{{-- focus blade content / --}}
@endsection

@section('scripts')
    <script src="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('/vendor/js/dom-to-img.js') }}"></script>
    <script src="{{ asset('/vendor/libs/swiper/swiper.js') }}"> </script>
    <script src="{{ asset('/vendor/js/fileserver.js') }}"></script>
    <script src="{{ asset('/vendor/js/huebee/huebee.min.js') }}"></script>

    <script type="text/javascript">
        var direction_view = ($("#direction_status").is(":checked")) ? 1 : 0;
        $(document).ready( function () {
            $('#openSession_table').DataTable();
        });
        $(document).on('click', '.dropdown', function (e) {
            e.stopPropagation();
        });
        $(document).on('change', '#direction_status', function (e) {
            direction_view = ($(this).is(":checked")) ? 1 : 0;
        });

    </script>

    <script>
        let fuid = $("#sendpdfuser").find('option:selected').val();
        $(document).ready(function () {
            //init();
            $(".draggable").draggable({
                containment: $("#focusMainView")
            });
            if(fuid != 0) fetchdata();

            if($('#view_option').val() == 2){
                initSwiper('dcIcon-category');
                initSwiper('dcIcon-size');
            }
        });

        function touchHandler(event) {
            var touch = event.changedTouches[0];

            var simulatedEvent = document.createEvent("MouseEvent");
            simulatedEvent.initMouseEvent({
                    touchstart: "mousedown",
                    touchmove: "mousemove",
                    touchend: "mouseup"
                } [event.type], true, true, window, 1,
                touch.screenX, touch.screenY,
                touch.clientX, touch.clientY, false,
                false, false, false, 0, null);

            touch.target.dispatchEvent(simulatedEvent);
        }

        function init() {
            document.addEventListener("touchstart", touchHandler, true);
            document.addEventListener("touchmove", touchHandler, true);
            document.addEventListener("touchend", touchHandler, true);
            document.addEventListener("touchcancel", touchHandler, true);
        }
    </script>

    <script>
        $('body').on('click', function(e) {
            $('[data-toggle=popover]').each(function() {
                if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                    $(this).popover('hide');
                }
            });
        });
        $('#appendiconBox').on('click', function(e) {
            $('.single-dragable-img').each(function() {
                if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.select_direction').has(e.target).length === 0) {
                    $("#normal-direction-btns").removeClass("show").addClass("hide")
                    $(this).removeClass('select_direction');
                    $('.active_img').remove();
                    let id = $(this).data('id')
                    iconColourId = null
                    currentSelectedIcon = 0
                    iconCategoryId = null
                    iconSizeId = null
                    iconDetails = null
                    resetSetup()
                }
            });

            $('.default-single-dragable-img').each(function() {
                if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.select_direction').has(e.target).length === 0) {
                    $(this).removeClass('select_direction');
                    $('.active_img').remove();
                    let id = $(this).data('id')
                    $("#default-direction-btns").removeClass("show").addClass("hide")
                    currentSelectedIcon = 0
                }
            });
        })

        $('#cancle-btn').on('click', function(e) {
            $('.single-dragable-img').each(function() {
                if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.select_direction').has(e.target).length === 0) {
                    $(this).removeClass('select_direction');
                    $('.active_img').remove();
                    $("#normal-direction-btns").removeClass("show").addClass("hide")
                    let id = $(this).data('id');
                    iconColourId = null
                    iconCategoryId = null
                    iconSizeId = null
                    currentSelectedIcon = 0;
                    iconDetails = null
                    resetSetup();
                }
            });
        })


        $('.js-example-basic-multiple').select2({
            placeholder: "Select multypol catogory",
            allowClear: true
        });

        $("#sendpdfuser").on('change', function(){
            fuid = $(this).find('option:selected').val();
            let email = $this.find('option:selected').data('email')
            if(email.length != 0) return $("#sendusermail").val(email);
            if(fuid != 0 ) fetchdata();
        })

        function fetchdata(){
            $.ajax({
                type: "POST",
                cache: false,
                url: "{{ route('Aajax.fetchdata') }}",
                data : {id: fuid},
                datatype: "json",
                success: function(res){
                    // $(id).html(res.post)
                    // $modalname.show()
                    $("#sendusermail").val(res.email);
                }
            })
        }

        function removeDirectionBtnAttr() {
            $("#dc_left").removeAttr("onclick")
            $("#dc_top_left").removeAttr("onclick")
            $("#dc_top_right").removeAttr("onclick")
            $("#dc_left").removeAttr("onclick")
            $("#dc_front").removeAttr("onclick")
            $("#dc_normal").removeAttr("onclick")
            $("#dc_back").removeAttr("onclick")
            $("#dc_right").removeAttr("onclick")
            $("#dc_bottom_left").removeAttr("onclick")
            $("#dc_bottom_right").removeAttr("onclick")
        }

        // for modal rendering
        function modal_render(id) {
            // switch(parseInt(id)) {
            //     case 1:
            //         if($('.modal-show-area div').is('#saveSession_modal')) return $('#saveSession_modal').modal('show');
            //         break;
            //     case 2:
            //         if($('.modal-show-area div').is('#openSession_modal')) return $('#openSession_modal').modal('show');
            //         break;
            //     case 3:
            //         if($('.modal-show-area div').is('#creatPDF_modal')) return $('#creatPDF_modal').modal('show');
            //         break;
            //     case 4:
            //         if($('.modal-show-area div').is('#sendMail_modal')) return $('#sendMail_modal').modal('show');
            //         break;
            //     default:
            //         break;
            // }
            $.ajax({
                type: "POST",
                url: "{{ route('products.modal-render') }}",
                data : {
                    id: id,
                    productName: "{{$product ?? ''}}"
                },
                datatype: "json",
                success: function(res){
                    $('.modal-show-area').empty().append(res.modal);
                    switch(parseInt(id)) {
                        case 1:
                            $('#saveSession_modal').modal('show');
                            break;
                        case 2:
                            $('#openSession_modal').modal('show');
                            break;
                        case 3:
                            $('#creatPDF_modal').modal('show');
                            break;
                        case 4:
                            $('#sendMail_modal').modal('show');
                            break;
                        default:
                            break;
                    }
                }
            });
        }
    </script>

    @include('JSBlade.AJsblade')
    @include('JSBlade.dcjs')
@endsection
