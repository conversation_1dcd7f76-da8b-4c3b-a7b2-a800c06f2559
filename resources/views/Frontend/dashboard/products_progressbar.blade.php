@extends('Frontend.partials.layout')

@section('content')
{{-- product progressbar view --}}
<section class="products-progressbar">
    <div class="row">
        <div class="col-md-8">
            @if(checkDashAccess(getAuthId()) && !isSetupDiagram())
                @include('Frontend.partials.includes.layout-biorhythmus')
            @elseif(isSetupDiagram())
                @include('Frontend.dashboard.View.Content.dashboard_widget')
            @endif
            {{-- cui example --}}
            <div class="cui-example">
                <div class="preloader">
                    <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                </div>
                <div class="row">
                    <div class="col-md">
                        {{-- card --}}
                        <div class="card mb-3 dashboard-card">
                            {{-- card header --}}
                            <div class="card-header p-name">
                                {{ $product['proName'] }}
                            </div>
                            {{-- card body --}}
                            <div class="card-body">
                                <div class="row">
                                    @if(!empty($submenu))

                                    <div class="col-md-12 col-xl-12">
                                        <div class="card mb-4">
                                            <div class="card-header text-center">
                                                <h5 class="m-0 d-inline-block">
                                                <a href="{{ (request()->comid == null) ? route('dashboard.products', [$product['proid']]) : route('dashboard.combo.products', [$product['proid'], request()->comid]) }}" style="color:#000;">{{ $product['proName'] }}</a>
                                                </h5>
                                            </div>
                                            <div class="card-body customProgressView">
                                                {{--<div class="row">
                                                    <div class="col-4 col-md-4 px-0">
                                                        <div class="percentage text-right text-muted small">60</div>
                                                        <div class="progress custom_progressBar" >
                                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 60%" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-4 col-md-4 px-0">
                                                        <div class="percentage text-right text-muted small">80</div>
                                                        <div class="progress custom_progressBar" >
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: 80%" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-4 col-md-4 px-0">
                                                        <div class="percentage text-right text-muted small">35</div>
                                                        <div class="progress custom_progressBar">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: 35%" aria-valuenow="35" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                </div>--}}
                                                <div class="progress">
                                                    @if($data['randomAnalysis'] == 1)
                                                        <div class="progress-bar" role="progressbar" style="width: 100%; background-color:gray" aria-valuenow="{{ $sub['anaRed'] ?? 0}}" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="title pro-progress-title">{{ $product['anaRed']+$product['anaGreen']+$product['anaOrange'] }}</span>
                                                        </div>
                                                    @else

                                                        <div class="progress-bar" role="progressbar" style="width: 100%; background-color:#E84E1B" aria-valuenow="{{ $sub['anaRed'] ?? 0}}" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="title pro-progress-title">{{ $product['anaRed'] ?? 0 }}</span>
                                                        </div>
                                                        <div class="progress-bar " role="progressbar" style="width: 100%; background-color: #F8B133" aria-valuenow="{{ $sub['anaOrange'] ?? 0}}" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="title pro-progress-title">{{ $product['anaOrange'] ?? 0 }}</span>
                                                        </div>
                                                        <div class="progress-bar" role="progressbar" style="width: 100%; background-color: #2FAB66" aria-valuenow="{{ $sub['anaGreen'] ?? 0}}" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="title pro-progress-title">{{ $product['anaGreen'] ?? 0 }}</span>
                                                        </div>
                                                    @endif

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                        @foreach($submenu as $key => $sub)
                                    <div class="col-md-12 col-xl-6">
                                        <div class="card mb-4">
                                            <div class="card-header text-center">
                                                <h5 class="m-0 d-inline-block">
                                                <a href="{{ (request()->comid == null) ? route('dashboard.product', [$product['proid'], $sub['submenuId']]) : route('dashboard.combo.product', [$product['proid'], $sub['submenuId'], request()->comid]) }}" style="color:#000;">{{ $sub['submenuName']}}</a>
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                {{-- progressbar view --}}
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar" style="width: 100%; background-color:#E84E1B" aria-valuenow="{{ $sub['anaRed'] ?? 0}}" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="title pro-progress-title">{{ $sub['anaRed'] ?? 0}}</span>
                                                    </div>
                                                    <div class="progress-bar " role="progressbar" style="width: 100%; background-color: #F8B133" aria-valuenow="{{ $sub['anaOrange'] ?? 0}}" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="title pro-progress-title">{{ $sub['anaOrange'] ?? 0}}</span>
                                                    </div>
                                                    <div class="progress-bar" role="progressbar" style="width: 100%; background-color: #2FAB66" aria-valuenow="{{ $sub['anaGreen'] ?? 0}}" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="title pro-progress-title">{{ $sub['anaGreen'] ?? 0}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                    @endif

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('Frontend.partials.includes.layout-dashright', ['farbklang' => $farbklang])
    </div>
</section>
{{-- product progressbar view /--}}
@endsection


@section('scripts')

<script>
    function deletePDF(id,pdf){

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    type: "POST",
                    url: "{{ route('Aajax.deletePDF') }}",
                    data: { id:id, pdfid:pdf },
                    success: function(data) {
                        Swal.fire(
                            'Deleted!', 'Your file has been deleted.', 'success'
                        );
                        $("#pdf"+id).remove();
                    }
                });
            }
        })
    }


</script>

@endsection
