@extends('Frontend.partials.layout')

@section('content')
{{-- own product progressbar --}}
<section class="products-progressbar">
    <div class="row">
        {{-- col-8 --}}
        <div class="col-md-8">
            @if(checkDashAccess(getAuthId()) && !isSetupDiagram())
                @include('Frontend.partials.includes.layout-biorhythmus')
            @elseif(isSetupDiagram())
                @include('Frontend.dashboard.View.Content.dashboard_widget')
            @endif
            <div class="cui-example">
                {{-- preloader --}}
                {{-- <div class="preloader">
                    <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                </div> --}}
                {{-- content --}}
                <div class="row">
                    <div class="col-md">
                        <div class="card mb-3 dashboard-card">
                            <div class="card-header p-name">
                                <h3 class="card-title">{{ $product['proName'] }}</h3>
                                @if($data['randomAnalysis'] == 1)
                                <div class="card-tools pull-right">
                                    <a href="{{ route('dashboard.changerandomstatus') }}" class="btn border border-warning text-center text-warning random_blinking">{{__('action.rancheckedmsg')}}</a>
                                </div>
                                @endif
                            </div>
                            <div class="card-body">
                                <div class="row">
                                @if(!empty($submenu))

                                    <div class="col-md-12 col-xl-12">
                                        <div class="card mb-4">
                                            <div class="card-header text-center">
                                                <h5 class="m-0 d-inline-block">
                                                <a href="{{ (request()->comid == null)?route('dashboard.ownProducts', [$product['proid']]):route('dashboard.combo.ownProducts', [$product['proid'],request()->comid]) }}" style="color:#000;">{{ $product['proName'] }}</a>
                                                </h5>
                                            </div>
                                            <div class="card-body customProgressView">

                                                <div class="progress">
                                                    @if($data['randomAnalysis'] == 1)
                                                        <div class="progress-bar" role="progressbar" style="width: 100%; background-color:gray" aria-valuenow="{{ $sub['anaRed'] }}" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="title pro-progress-title">{{ $product['anaRed']+$product['anaGreen']+$product['anaOrange'] }}</span>
                                                        </div>
                                                    @else

                                                        <div class="progress-bar" role="progressbar" style="width: 100%; background-color:#E84E1B" aria-valuenow="{{ $sub['anaRed'] }}" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="title pro-progress-title">{{ $product['anaRed'] }}</span>
                                                        </div>
                                                        <div class="progress-bar " role="progressbar" style="width: 100%; background-color: #F8B133" aria-valuenow="{{ $sub['anaOrange'] }}" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="title pro-progress-title">{{ $product['anaOrange'] }}</span>
                                                        </div>
                                                        <div class="progress-bar" role="progressbar" style="width: 100%; background-color: #2FAB66" aria-valuenow="{{ $sub['anaGreen'] }}" aria-valuemin="0" aria-valuemax="100">
                                                            <span class="title pro-progress-title">{{ $product['anaGreen'] }}</span>
                                                        </div>
                                                    @endif

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                        @foreach($submenu as $key => $sub)
                                    <div class="col-md-12 col-xl-6">
                                        <div class="card mb-4">
                                            <div class="card-header text-center">
                                                <h5 class="m-0 d-inline-block">
                                                <a href="{{ (request()->comid == null)?route('dashboard.ownProduct', [$product['proid'], $sub['submenuId']]):route('dashboard.combo.ownProduct', [$product['proid'], $sub['submenuId'],request()->comid]) }}" style="color:#000;">{{ $sub['submenuName']}}</a>
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                @if($data['randomAnalysis'] == 1)
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar" style="width: 100%; background-color:gray" aria-valuenow="{{ $sub['anaRed']+$sub['anaOrange']+$sub['anaGreen'] }}" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="title pro-progress-title">{{ $sub['anaRed']+$sub['anaOrange']+$sub['anaGreen'] }}</span>
                                                    </div>
                                                </div>
                                                @else
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar" style="width: 100%; background-color:#E84E1B" aria-valuenow="{{ $sub['anaRed'] }}" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="title pro-progress-title">{{ $sub['anaRed'] }}</span>
                                                    </div>
                                                    <div class="progress-bar " role="progressbar" style="width: 100%; background-color: #F8B133" aria-valuenow="{{ $sub['anaOrange'] }}" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="title pro-progress-title">{{ $sub['anaOrange'] }}</span>
                                                    </div>
                                                    <div class="progress-bar" role="progressbar" style="width: 100%; background-color: #2FAB66" aria-valuenow="{{ $sub['anaGreen'] }}" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="title pro-progress-title">{{ $sub['anaGreen'] }}</span>
                                                    </div>
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                    @endif

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- content /--}}
            </div>
        </div>
        {{-- layout dashboard Right --}}
        @include('Frontend.partials.includes.layout-dashright', ['farbklang' => $farbklang])
    </div>
</section>
{{-- own product progressbar --}}
@endsection

@section('scripts')

<script>

  

</script>

@endsection
