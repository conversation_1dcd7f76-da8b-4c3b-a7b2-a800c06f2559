@extends('Frontend.partials.layout')

@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/morris/morris.css') }}">
@endsection

@section('content')
{{-- products view section --}}
<section class="products">
    <div class="row">
        <div class="col-lg-8">
            @if(checkDashAccess(getAuthId()) && !isSetupDiagram())
                @include('Frontend.partials.includes.layout-biorhythmus')
            @elseif(isSetupDiagram())
                @include('Frontend.dashboard.View.Content.dashboard_widget')
            @endif
            <div class="cui-example">
                <div class="preloader">
                        <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                    </div>
                <div class="row">
                    <div class="col-md">
                        <div class="card mb-3 dashboard-card">
                            <div class="card-header">
                                <a class="dashboardSubmenu mr-5" href="{{ (request()->comid == null) ? route('dashboard.products', [$product['proid']]) : route('dashboard.combo.products', [$product['proid'], request()->comid]) }}">{{ $product['proName'] }}</a>
                                @if($data['randomAnalysis'] == 1)
                                <div class="card-tools pull-right">
                                    <a href="{{ route('dashboard.changerandomstatus') }}" class="btn border border-warning text-center text-warning random_blinking">{{__('action.rancheckedmsg')}}</a>
                                </div>
                                @endif
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="dashboard-progress-content text-center">
                                            <div id="morrisjs-donut-pro{{ $product['proid'] }}" style="height: 250px"></div>
                                            <p>{{ $product['proName'] }}

                                            </p>

                                        </div>
                                    </div>

                                    <script>

                                        $(function () {
                                            if("{{$data['randomAnalysis'] }}" == 1){
                                                var morrisDonut = new Morris.Donut({
                                                    element: 'morrisjs-donut-pro{{ $product["proid"] }}',
                                                    data: [{
                                                            label: '',
                                                            value: "{{ $product['anaGreen']+$product['anaOrange']+$product['anaRed'] }}"
                                                        }

                                                    ],
                                                    colors: ['gray'],
                                                    resize: true,
                                                    labelColor: '#888',

                                                    formatter: function (value) {
                                                        return value
                                                    }
                                                });
                                            }else{
                                                var morrisDonut = new Morris.Donut({
                                                    element: 'morrisjs-donut-pro{{ $product["proid"] }}',
                                                    data: [{
                                                            label: '',
                                                            value: "{{ $product['anaGreen'] }}"
                                                        },
                                                        {
                                                            label: '',
                                                            value: "{{ $product['anaOrange'] }}"
                                                        },{
                                                            label: '',
                                                            value: "{{ $product['anaRed'] }}"
                                                        }

                                                    ],
                                                    colors: ['#2FAB66', '#F8B133', '#E84E1B'],
                                                    resize: true,
                                                    labelColor: '#888',

                                                    formatter: function (value) {
                                                        return value
                                                    }
                                                });
                                                morrisDonut.select(2);
                                            }


                                        });

                                    </script>

                                    <div class="col-md-8">
                                        <div class="row">
                                            @if(!empty($submenu))
                                            @foreach($submenu as $key => $sub)
                                            {{-- @dd($product['proid'], $sub['submenuId']) --}}
                                            @php $random_number = str_random(15); @endphp
                                                <div class="col-md-6">
                                                    <div class="card color-border-one text-center mb-3">
                                                        <h6 class="card-header f-16">
                                                            <a class="dashboardSubmenu" href="{{ (request()->comid == null) ? route('dashboard.product', [$product['proid'], $sub['submenuId']]) : route('dashboard.combo.product', [$product['proid'], $sub['submenuId'], request()->comid]) }}"> {{ $sub['submenuName']}} </a>
                                                        </h6>
                                                        <div class="progress-circle-mini">
                                                            <div id="morrisjs-donut{{ $sub['submenuId'] . $random_number }}" style="height: 150px"></div>
                                                        </div>
                                                    </div>
                                                </div>

                                            <script>
                                                $(function () {
                                                    if("{{$data['randomAnalysis'] }}" == 1) {
                                                        var morrisDonut = new Morris.Donut({
                                                            element: 'morrisjs-donut{{ $sub["submenuId"] . $random_number }}',
                                                            data: [{
                                                                    label: '',
                                                                    value: "{{ $sub['anaGreen']+$sub['anaOrange']+$sub['anaRed'] }}"
                                                                }

                                                            ],
                                                            colors: ['gray'],
                                                            resize: true,
                                                            labelColor: '#888',

                                                            formatter: function (value) {
                                                                return value
                                                            }
                                                        });
                                                    }else{
                                                        var morrisDonut = new Morris.Donut({
                                                            element: 'morrisjs-donut{{ $sub["submenuId"] . $random_number }}',
                                                            data: [{
                                                                    label: '',
                                                                    value: "{{ $sub['anaGreen'] }}"
                                                                },
                                                                {
                                                                    label: '',
                                                                    value: "{{ $sub['anaOrange'] }}"
                                                                },
                                                                {
                                                                    label: '',
                                                                    value: "{{ $sub['anaRed'] }}"
                                                                }

                                                            ],
                                                            colors: ['#2FAB66', '#F8B133', '#E84E1B'],
                                                            resize: true,
                                                            labelColor: '#888',

                                                            formatter: function (value) {
                                                                return value
                                                            }
                                                        });
                                                        morrisDonut.select(2);
                                                    }

                                                });

                                            </script>
                                            @endforeach
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{-- include layout dashboard Right Section --}}
        @include('Frontend.partials.includes.layout-dashright', ['farbklang' => $farbklang])
    </div>
</section>
{{-- products view section /--}}

@endsection

@section('scripts')
    <!-- Dependencies -->
    <script src="{{ asset('/vendor/libs/chartjs/chartjs.js') }}"></script>
    <script src="{{ asset('/vendor/libs/swiper/swiper.js') }}"></script>
    <script src="{{ asset('/js/ui_carousel.js') }}"></script>

    <!-- Dependencies -->
    <script src="{{ asset('/vendor/libs/eve/eve.js') }}"></script>
    <script src="{{ asset('/vendor/libs/raphael/raphael.js') }}"></script>
    <script src="{{ asset('/vendor/libs/morris/morris.js') }}"></script>
<script>
    function deletePDF(id,pdf){

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {

        if (result.value) {

            $.ajax({

            type: "POST",

            url: "{{ route('Aajax.deletePDF') }}",

            data: { id:id, pdfid:pdf },

            success: function(data) {

                Swal.fire(
                    'Deleted!', 'Your file has been deleted.', 'success'
                );

                $("#pdf"+id).remove();
            }

            });
        }
        })
    }


</script>


@endsection
