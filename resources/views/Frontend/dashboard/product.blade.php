@extends('Frontend.partials.layout')

@php
    $cartData = !empty($cartData) ? $cartData : getCartDataInstance();
@endphp

@section('content')
{{-- main analysis content section --}}
<section class="analysis-content dsh-view">
    {{--analysis header  --}}
    <div class="row">
        <div class="col-md-5 col-sm-5 col-12">
            <div class="analysis-content-header d-flex align-items-center">
                <div class="logo">
                    <i class="fas fa-home primary-color"></i>
                </div>
                <div class="heading primary-color">
                <h2>{{ ucfirst($data['subName']) }}</h2>
                <p>{{ ucfirst($data['proName']) }}</p>
                </div>
            </div>
        </div>
        <!-- top layout -->
        @include('Frontend.partials.includes.layout-top')
    </div>
    {{--/analysis header  --}}

    {{-- analysis content --}}
    <div class="row mt-4">
        <div class="col-md-12">
            
            @include('Frontend.partials.includes.product-paginate-number')

            {{-- analysis full content --}}
            <div class="analysis-content-body posts endless-pagination" data-lastpage="{{($analyses && $analyses->lastPage()==true)? $analyses->lastPage() : 1 }}" id="ok">
                <div class="row position-relative">
                    {{-- preloader --}}
                    <div class="preloader" id="preload_product">
                        <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                    </div>
                    {{-- analyss sample content --}}
                    @if(!empty($analyses))
                        @foreach($analyses as $key => $print)
                            @php
                                if($bodyimages) $bodyimg = array_search($print['anaid'],$bodyimages,true);
                                else $bodyimg = "";
                                if($mentalimages) $mentalimg = array_search($print['anaid'],$mentalimages,true);
                                else $mentalimg = "";
                            @endphp
                            @include('Frontend.Products.Product.system_product_normal')
                            

                            <script>
                                $(document).ready(function(){

                                    var val   = Number("{{ $print['anaVal'] }}");
                                    var color = "{{ $analysesColor ?? '' }}";

                                    new CircleProgress("#progress_{{ $print['anaid'] }}", {
                                        max: 100,
                                        value: val,
                                        textFormat: 'percent',
                                        animation: 'easeInOutCubic',
                                        animationDuration: 3000
                                    });

                                    $("#progress_{{ $print['anaid'] }} .circle-progress-value").attr("stroke", "{{ $print['anaColor'] }}");

                                });
                            </script>
                        @endforeach
                    @endif
                    {{-- /analysis sample header --}}
                </div>
            </div>
            {{-- /analysis  full content --}}
        </div>
    </div>
    {{-- /analysis content --}}

    {{-- for cart show  --}}
    @livewire('dashboard.right-cart-modal', ['key' => 'right-cart-modal-' . $auth->id])
    <div id="analysisPopup_Modal" class="modal analysisPopup_Modal">
        <span class="close" id="modal-close">&times;</span>
        <img class="modal-content" id="imgShow">
    </div>

</section>
{{-- /main analysis content section --}}
@endsection

@section('scripts')
<script>

    var tgl  = [];
    var tgl1 = [];
    var tgl2 = [];
    var longday = 0;

    $('.info').each(function(){
        $(this).popover({
            html: true,
            content: function(){
                return $('#popover-content'+$(this).data('anaid')).html();
            }
        })
    })

    function ursCheck(id) {
        tgl2 = [];
        tgl1 = [];
        $('.analysis-sample-list-content').removeClass('showw');
        $('#tip'+id).removeClass('active');
        $('#tip-content'+id).removeClass('showw');
        $('#mit'+id).removeClass('active');
        $('#mit-content'+id).removeClass('showw');
        if($.inArray(id,tgl)==-1){
            tgl = [];
            tgl.push(id);
            $('#urs'+id).toggleClass('active');
            $('#urs-content'+id).toggleClass('showw');
        }
        else tgl = [];
    }

    function mitCheck(id) {
        tgl2 = [];
        tgl = [];
        $('.analysis-sample-list-content').removeClass('showw');
        $('#tip'+id).removeClass('active');
        $('#tip-content'+id).removeClass('showw');
        $('#urs'+id).removeClass('active');
        $('#urs-content'+id).removeClass('showw');
        if($.inArray(id,tgl1)==-1){
            tgl1 = [];
            tgl1.push(id);
            $('#mit'+id).toggleClass('active');
            $('#mit-content'+id).toggleClass('showw');
        }
        else tgl1 = [];
    }

    function tipCheck(id) {
        tgl1 = [];
        tgl = [];
        $('.analysis-sample-list-content').removeClass('showw');
        $('#mit'+id).removeClass('active');
        $('#mit-content'+id).removeClass('showw');
        $('#urs'+id).removeClass('active');
        $('#urs-content'+id).removeClass('showw');
        if($.inArray(id,tgl2)==-1){
            tgl2 = [];
            tgl2.push(id);
            $('#tip'+id).toggleClass('active');
            $('#tip-content'+id).toggleClass('showw');
        }
        else tgl2 = [];
    }

    $('body').on('click', function (e) {
        $('.analysis-sample-list-content').each(function () {
            // hide any open popovers when the anywhere else in the body is clicked
            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.analysis-sample').has(e.target).length === 0) {
                $(this).removeClass('showw');
            }
        });
    });

    $('body').on('click', function (e) {
        $('[data-toggle=popover]').each(function () {

            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                $(this).popover('hide');
            }
        });
    });

    $("#longday").on('change', function(){
        var select = $(this).find('option:selected');
        longday = select.val();
        var proid = "{{ request()->proid }}";
        var subid = "{{ request()->subid }}";
        var comid = "{{ request()->comid }}";

        if(comid == "")
            var url = "{{ url('/dashboard/p') }}/"+proid+"/"+subid+"/"+longday;
        else
            var url = "{{ url('/dashboard/c/p') }}/"+proid+"/"+subid+"/"+longday+"/"+comid;

        $("#cb-rl2").attr("data-href", url);
        window.location.href = url;
        // $("#longday_link").attr("href", url);
    })

    function checkLongDay() {
        if(longday < 30 ) Swal.fire({ type: 'warning', title: "{{__('action.choose_days')}}", showConfirmButton: false, timer: 3500 });
    }


    $(document).ready(function() {

        var page = "{{ isset($_REQUEST['page']) ? (int)$_REQUEST['page'] : 1 }}";

        var isLoading = false;
        var $window = $(window);
        var $document = $(document);

        $('#ok').bind('DOMMouseScroll mousewheel touchmove', function(e) {
            var last = parseInt($('#ok').data('lastpage')) || 1;
            if (last <= page || isLoading) return;

            if (e.originalEvent.wheelDelta > 0 || e.originalEvent.detail < 0) {
                // Scrolling up
            } else {
                // Scrolling down
                if ($window.scrollTop() + $window.height() + 150 >= $document.height()) {
                    page++;
                    loadMoreData(page);
                }
            }
        });

        function loadMoreData(page) {
            isLoading = true;
            $.ajax({
                url: '?page=' + page,
                type: "get",
                beforeSend: function() {
                    $(document.body).css({
                        'cursor': 'wait'
                    });
                    $(".preloader").show();
                },
                complete: function() {
                    $(document.body).css({
                        'cursor': 'default'
                    });
                    $(".preloader").hide();
                    isLoading = false;
                }
            }).done(function(data) {
                if (data.posts) {
                    $('.posts').append(data.posts);
                    // If there are more pages, update the data attribute
                    if (data.last_page) {
                        $('#ok').data('lastpage', data.last_page);
                    }
                }
            }).fail(function(jqXHR, ajaxOptions, thrownError) { });
        }

    })


    function change_calculation(anaid, poolid, subid, proid, type) {
        if (poolid == "") { poolid = 0; }
        var check = $('#someSwitchOptionDefault' + anaid).is(':checked');
        var dataString = 'subid=' + subid + '&proid=' + proid + '&anaid=' + anaid + '&poolid=' + poolid + '&type='+ type +'&is_ck=' + check;

        jQuery(function($) {
            $('#progress_'+anaid).circleProgress();
        });

        if (check == true) {
            $.ajax({
                type: "POST",
                url: "{{ route('Aajax.saveCalculation') }}",
                data: dataString,
                success: function(data) {
                    $('#progress_'+anaid).circleProgress('value', data.record.ma);
                    $("#progress_"+anaid+" .circle-progress-value").attr("stroke", data.record.color);
                }
            });
        } else {
            $.ajax({
                type: "POST",
                url: "{{ route('Aajax.saveCalculation') }}",
                data: dataString,
                success: function(data) {
                    $('#progress_'+anaid).circleProgress('value', data.record.ma);
                    $("#progress_"+anaid+" .circle-progress-value").attr("stroke", data.record.color);
                }
            });
        }
    }
</script>
<script>$.ajaxPrefilter(function( options, originalOptions, jqXHR ) {    options.async = true; });</script>

@endsection
