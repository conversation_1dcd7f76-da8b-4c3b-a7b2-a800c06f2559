@extends('Frontend.partials.layout')
@section('styles')
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/select2/select2.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.css" rel="stylesheet">
    <style>
        .selectpicker{z-index: 1111!important;}
        .add-menu .bootstrap-select .dropdown-menu .inner{
            max-height: 350px !important;
            overflow-y: auto !important;
        }

        .add-menu .bootstrap-select .dropdown-menu .inner{
            max-height: 350px !important;
            overflow-y: auto !important;
        }
        .box-header{
            padding: .75rem 1.25rem;
            margin-bottom: 0;
            background-color: #f5f5f5;
            border-top-right-radius: 4px;
            border-top-left-radius: 4px;
        }
        #rr-multislider .noUi-connects {
            border-radius: 4px;
            overflow: hidden;
        }
        #rr-multislider .noUi-connect {
            background: #ccc;
        }
        #rr-multislider .noUi-connect:nth-child(1) { background: red !important; }
        #rr-multislider .noUi-connect:nth-child(2) { background: orange !important; }
        #rr-multislider .noUi-connect:nth-child(3) { background: green !important; }
        #color-range-values .card-body {
            min-height: 60px;
        }

        @keyframes blink {
            50% {
                opacity: 0.3;
            }
        }

        @keyframes moveRight {
            from {
                transform: translateX(0px);
            }
            to {
                transform: translateX(10px);
            }
        }

        .glow-button {
            animation: glow 1.5s infinite alternate;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.7), 0 0 10px rgba(76, 175, 80, 0.7), 0 0 15px rgba(76, 175, 80, 0.7);
        }
        
        html {
            scroll-behavior: smooth;
        }
        
        .form-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
        }
        
        .form-highlight {
            position: relative;
            z-index: 1001;
            background: white;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }
        
        .fade-other {
            opacity: 0.3;
            pointer-events: none;
            transition: opacity 0.5s ease;
        }
        
        .default-slider {
            opacity: 0.7;
            position: relative;
        }
        
        .default-slider::after {
            content: "{{__('action.default_values')}}";
            position: absolute;
            top: -25px;
            right: 0;
            background: #ffc107;
            color: #000;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .default-values-warning {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
         @keyframes glow {
            from {
                box-shadow: 0 0 5px rgba(76, 175, 80, 0.5), 0 0 10px rgba(76, 175, 80, 0.5);
            }
            to {
                box-shadow: 0 0 15px rgba(76, 175, 80, 1), 0 0 25px rgba(76, 175, 80, 1);
            }
        }
    </style>
@endsection

@section('content')
<section class="add-menu">
    {{-- logo section --}}
    <div class="row">
        <div class="col-md-6">
            <div class="analysis-content-header d-flex align-items-center">
                <div class="logo">
                    <i class="fas fa-home primary-color"></i>
                </div>
                <div class="heading primary-color">
                    <h2>{{__('action.update_menu')}}</h2>
                    <p>{{ brandName() }} Energiestatus</p>
                </div>
            </div>
        </div>
    </div>
    {{-- logo section End --}}

    {{-- Menu Setting box --}}
    <div class="row mt-2">
        <div class="col-md-12">
            <div class="card mt-4">
                <h6 class="card-header"><i class="fa fa-cogs mr-2"></i>{{ __('action.page_title_menu_setting') }}</h6>
                <div class="card-body">
                    <form action="{{route('menu.update_menu', [$menu->id])}}" method="post">
                    @csrf
                    @method('PUT')
                        <div class="form-group">
                            <label class="form-label mb-2">{{__('action.ownMenuName')}}<span class="text-danger">*</span></label>
                            <input type="text" class="form-control @if ($errors->has('menu_name')) is-invalid @endif"  placeholder="{{__('action.own_menu_name_placeholder')}}" name="menu_name" value="{{$menu->name}}">
                            @if ($errors->has('menu_name'))
                                <span class="text-danger small" role="alert">
                                    <strong>{{ $errors->first('menu_name') }}</strong>
                                </span>
                            @endif
                        </div>
                        <label class="custom-control custom-checkbox mt-3" style="width: auto;">
                            <input type="checkbox" onclick="menusetting()" class="custom-control-input square" @if(request()->get('open_settings') == '1') checked @endif>
                            <span class="custom-control-label">{{__('action.page_title_menu_setting')}} </span>
                        </label>
                        <div class="text-right mt-3">
                            <button type="submit" class="btn btn-success"><i class="fa fa-edit mr-2"></i>{{__('action.update')}}</button>&nbsp;
                            <a href="{{route('menu.menus')}}" type="button" class="btn btn-default">{{__('action.cancel')}}</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {{-- Menu Setting box End--}}

    {{-- Main & Submenu Setting box --}}
    <div class="row">
        <div class="col-lg-6 col-md-6 hide setting-head">
            {{-- Main Menu Setting box --}}
            <div class="card mt-4">
                <h6 class="card-header"><i class="fa fa-cogs mr-2"></i>{{ __('action.main_menu_setting') }}</h6>
                <div class="card-body">
                    <form class="row" action="{{route('menu.update_menusetting')}}" method="post" id="menuSettingsForm">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="gs_menu_id" value="{{ $menu->id }}">

                        <div class="col-12 mb-4">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                {{__('action.color_range_help')}}
                            </div>
                            @if(!$menu->setting || (!$menu->setting->red_max && !$menu->setting->orange_max))
                            <div class="alert alert-warning default-values-warning">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <strong>{{__('action.default_values_notice')}}</strong> {{__('action.please_customize_ranges')}}
                            </div>
                            @endif
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label mb-2">{{__('action.pmin_value')}}</label>
                                <input type="text" class="form-control @if ($errors->has('gs_min_price')) is-invalid @endif" 
                                    placeholder="{{__('action.pmin_value')}}" name="gs_min_price" 
                                    value="{{($menu->setting) ? $menu->setting->gs_min_price : ''}}">
                                @if ($errors->has('gs_min_price'))
                                    <span class="text-danger small" role="alert">
                                        <strong>{{ $errors->first('gs_min_price') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label mb-2">{{__('action.pmaxmax_value')}}</label>
                                <input type="text" class="form-control @if ($errors->has('gs_max_price')) is-invalid @endif" 
                                    placeholder="{{__('action.pmaxmax_value')}}" name="gs_max_price" 
                                    value="{{($menu->setting) ? $menu->setting->gs_max_price : ''}}">
                                @if ($errors->has('gs_max_price'))
                                    <span class="text-danger small" role="alert">
                                        <strong>{{ $errors->first('gs_max_price') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="col-12 mt-4">
                            <h6 class="mb-3">{{__('action.color_ranges')}}</h6>
                        </div>

                        <!-- Multi-handle Slider -->
                        <div class="col-12 mb-4">
                            <div id="rr-multislider"></div>
                            <div class="d-flex justify-content-between mt-2">
                                <span>0</span>
                                <span id="red_max_val">11</span>
                                <span id="orange_max_val">70</span>
                                <span>100</span>
                            </div>
                            <div class="row mt-3 d-flex align-items-stretch" id="color-range-values">
                                <div class="col-12 col-md-4 mb-2 d-flex">
                                    <div class="card border-danger w-100 h-100">
                                        <div class="card-header bg-danger text-white p-2"></div>
                                        <div class="card-body p-2">
                                            <div><strong>{{__('action.red_min')}}:</strong> <span id="show_red_min">0</span></div>
                                            <div><strong>{{__('action.red_max')}}:</strong> <span id="show_red_max">11</span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-4 mb-2 d-flex">
                                    <div class="card border-warning w-100 h-100">
                                        <div class="card-header text-white p-2" style="background-color: #ffa500;"></div>
                                        <div class="card-body p-2">
                                            <div><strong>{{__('action.orange_min')}}:</strong> <span id="show_orange_min">11</span></div>
                                            <div><strong>{{__('action.orange_max')}}:</strong> <span id="show_orange_max">70</span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-4 mb-2 d-flex">
                                    <div class="card border-success w-100 h-100">
                                        <div class="card-header bg-success text-white p-2"></div>
                                        <div class="card-body p-2">
                                            <div><strong>{{__('action.green_min')}}:</strong> <span id="show_green_min">70</span></div>
                                            <div><strong>{{__('action.green_max')}}:</strong> <span id="show_green_max">100</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="red_min" value="0">
                            <input type="hidden" name="red_max" id="red_max" value="11">
                            <input type="hidden" name="orange_min" id="orange_min" value="11">
                            <input type="hidden" name="orange_max" id="orange_max" value="70">
                            <input type="hidden" name="green_min" id="green_min" value="70">
                            <input type="hidden" name="green_max" value="100">
                        </div>

                        <div class="col-12 mt-4">
                            <div class="alert alert-warning d-none" id="validationAlert">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                {{__('action.complete_all_ranges')}}
                            </div>
                        </div>

                        <div class="text-right mt-3 col-12">
                            <button type="submit" class="btn btn-success @if(request()->get('open_settings') == '1') glow-button @endif" id="submitBtn">
                                <i class="fa fa-edit mr-2"></i>{{__('action.update')}}
                            </button>&nbsp;
                            @if(request()->get('open_settings') !== '1')
                            <a href="{{route('menu.menus')}}" type="button" class="btn btn-default">{{__('action.cancel')}}</a>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-lg-12 col-md-12 submenu-head">
            {{-- Submenu Setting box --}}
            <div class="card mt-4">
                <h6 class="card-header"><i class="fa fa-cogs mr-2"></i>{{ __('action.submenu_setting') }}</h6>
                <div class="card-body">
                    <form class="submenu_update" action="{{route('menu.update_submenu')}}" method="post">
                        @csrf
                        @method('PUT')
                        <div class="form-group">
                            <label class="form-label mb-2">{{__('action.select_submenu')}}<span class="text-danger">*</span></label>
                            <select class="custom-select" name="submenu_id" onchange="submenu()">
                                <option value="">{{__('action.Select')}}</option>
                                @if ($menu->usersubmenus)
                                    @foreach ($menu->usersubmenus as $sub)
                                        <option @if($sub->type != 12) value="{{ $sub->id }}-sub" @else value="{{ $sub->id }}-own" @endif data-linkid={{ $sub->id }} data-pro="{{ $sub->product_id }}" id="subid_{{$sub->id}}" {{ (!empty(old('cronsubmenu')) && ((in_array($sub->id."-sub", old('cronsubmenu'))) || in_array($sub->id."-own", old('cronsubmenu'))) ? "selected" : "")}}>{{ $sub->name ?? $sub->menu_name }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                        <div class="form-group remove hide">
                            <label class="form-label mb-2">{{__('action.sub_menu')}}<span class="text-danger">*</span></label>
                            <input type="text" class="form-control submenu_name" placeholder="{{__('action.sub_menu')}}" name="submenu_name" value="">
                            @if ($errors->has('submenu_name'))
                                <span class="text-danger small" role="alert">
                                    <strong>{{ $errors->first('submenu_name') }}</strong>
                                </span>
                            @endif
                        </div>
                        
                        <div class="form-group remove hide">
                            <label class="form-label mb-2">{{__('action.sortingno')}}<span class="text-danger">*</span></label>
                            <input type="number" class="form-control sortingNo" placeholder="{{__('action.sortingno')}}" name="sortingNo" value="">
                            @if ($errors->has('sortingNo'))
                                <span class="text-danger small" role="alert">
                                    <strong>{{ $errors->first('sortingNo') }}</strong>
                                </span>
                            @endif
                        </div>

                        <div class="form-group remove hide">
                            <label class="form-label mb-2"> <b>{{__('action.select_analysis')}}</b> </label>
                            <div class="select2-dark">
                                <select class="selectpicker link_submenu_id" id="link_submenu_id" multiple name="link_submenu_id[]" data-live-search="true" title="{{__('action.nothingSelected')}}" data-count-selected-text= "{0} {{__('action.selectedsubmenus')}}" data-selected-text-format="count > 1">
                                    {{-- <option value="">{{__('action.select_sub_menu')}}</option> --}}
                                    
                                    @foreach ($productSub as $item)
                                    @continue($item->submenus == null)
                                        <option value="{{ $item->id }}" disabled>{{ $item->product_name }}</option>
                                        @foreach ($item->submenus as $sub)
                                            <option @if($sub->type != 12) value="{{ $sub->id }}-sub" @else value="{{ $sub->id }}-own" @endif data-linkid={{ $sub->id }} data-pro="{{ $sub->product_id }}" id="subid_{{$sub->id}}" {{ (!empty(old('cronsubmenu')) && ((in_array($sub->id."-sub", old('cronsubmenu'))) || in_array($sub->id."-own", old('cronsubmenu'))) ? "selected" : "")}}>{{ $sub->name ?? $sub->menu_name }}</option>
                                        @endforeach
                                    @endforeach
                                </select>
                            </div>

                            <span class="invalid-feedback allmsg" role="alert">
                                <strong id="msg1"></strong>
                            </span>
                        </div>
                        <div class="text-right mt-3">
                            <a href="javascript:void(0)" onclick="submenu_delete()" class="btn btn-danger dlt_btn_submenu hide" ><i class="fa fa-trash mr-2"></i>{{__('action.delete')}}</a>&nbsp;
                            <button type="submit" class="btn btn-success btn-update-submenu hide"><i class="fa fa-edit mr-2"></i>{{__('action.update')}}</button>&nbsp;
                            <a href="{{ route('menu.submenu_show') }}" type="button" class="btn btn-primary"><i class="fa fa-plus mr-2"></i>{{trans('action.add_subemnu')}}</a>
                            {{-- <a href="{{route('menu.menus')}}" type="button" class="btn btn-default">{{__('action.cancel')}}</a> --}}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {{-- Main & Submenu Setting box End --}}
</section>

@endsection
@section('scripts')
<script src="{{ asset('/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
<script src="{{ asset('/vendor/libs/select2/select2.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>

<script>

    function submenu_delete(){
        let id = $('select[name="submenu_id"] option:selected').val();
        Swal.fire({
            title: `{{__('action.entry_delete')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText: `{{__('action.yes_delete')}}`,
            cancelButtonText:  `{{__('action.cancel')}}`,
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    type: "GET",
                    dataType: "json",
                    url: "{{url('menu/submenu_delete')}}" +"/"+ id,
                    data: {
                        id: id
                    },
                    success: function(msg) {
                        $('.submenu_name').val('');
                        $('select[name="submenu_id"] option:selected').remove();
                        Swal.fire({
                            type: 'success',
                            title: "{{__('action.delete_success')}}",
                            showConfirmButton: false,
                            timer: 3500
                        });
                    }
                });
            }
        })
    }
    // Select2
    function changeSelect() {
        $(".custom-select").select2({
            tags: true
        })
    }
    function menusetting() {
        $('.setting-head').toggleClass('hide show');
        $('.submenu-head').toggleClass('col-md-12 col-md-6');
        $('.submenu-head').toggleClass('col-lg-12 col-lg-6');
    }

    $(document).ready(function() {
        if (window.location.search.indexOf('open_settings=1') !== -1) {
            
            var url = new URL(window.location);
            url.searchParams.delete('open_settings');
            window.history.replaceState({}, document.title, url.pathname + url.search);
            
            menusetting();
            
            setTimeout(function() {
                $('body').append('<div class="form-backdrop"></div>');
                $('.form-backdrop').fadeIn(500);
                
                $('.add-menu > .row:not(:has(.setting-head))').addClass('fade-other');
                $('.submenu-head').addClass('fade-other');
                
                $('.setting-head .card').addClass('form-highlight');
                
                $('html, body').animate({
                    scrollTop: $('#submitBtn').offset().top - 150
                }, {
                    duration: 800,
                    easing: 'swing'
                });
                
                setTimeout(function() {
                    $('#submitBtn').focus().addClass('btn-lg');
                }, 800);
                
                         }, 500);
        }
    });

    $(document).on('click', '.form-backdrop', function() {
        $('.form-backdrop').fadeOut(300, function() {
            $(this).remove();
        });
        $('.fade-other').removeClass('fade-other');
        $('.form-highlight').removeClass('form-highlight');
        $('#submitBtn').removeClass('btn-lg');
    });

    var shouldAutoSave = window.location.search.indexOf('open_settings=1') !== -1;
    var formSubmitted = false;

    if (shouldAutoSave) {
        $('#menuSettingsForm').on('submit', function() {
            formSubmitted = true;
        });

        $(window).on('beforeunload', function(e) {
            if (!formSubmitted) {
                navigator.sendBeacon('{{ route("menu.update_menusetting") }}', new FormData($('#menuSettingsForm')[0]));
                formSubmitted = true;
            }
        });

        $('a:not([href="#"]):not([href^="javascript:"]), button[type="button"]').on('click', function(e) {
            if (!formSubmitted && !$(this).hasClass('no-auto-save')) {
                e.preventDefault();
                var targetHref = $(this).attr('href');
                var isButton = $(this).is('button');
                
                Swal.fire({
                    title: '{{ __("action.auto_saving") }}',
                    text: '{{ __("action.settings_being_saved") }}',
                    icon: 'info',
                    timer: 2000,
                    showConfirmButton: false,
                    allowOutsideClick: false
                });

                $('#menuSettingsForm').submit();
                
                if (!isButton && targetHref) {
                    setTimeout(function() {
                        window.location.href = targetHref;
                    }, 1000);
                }
            }
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        var slider = document.getElementById('rr-multislider');
        @php
            $red_max = isset($menu->setting) && is_numeric($menu->setting->red_max) ? (int)$menu->setting->red_max : null;
            $orange_max = isset($menu->setting) && is_numeric($menu->setting->orange_max) ? (int)$menu->setting->orange_max : null;
            $sliderDefaults = [0, 11, 70, 100];
            $isDefaultValues = true;
            if ($red_max !== null && $orange_max !== null && $red_max > 0 && $orange_max > $red_max && $orange_max < 100) {
                $sliderDefaults = [0, $red_max, $orange_max, 100];
                $isDefaultValues = false;
            }
        @endphp
        var sliderDefaults = @json($sliderDefaults);
        var isDefaultValues = @json($isDefaultValues);
        noUiSlider.create(slider, {
            start: sliderDefaults,
            connect: [false, true, true, true, false],
            range: {
                'min': 0,
                'max': 100
            },
            step: 1,
            tooltips: [false, true, true, false],
            behaviour: 'drag',
            margin: 1,
            limit: 100
        });

        if (isDefaultValues) {
            slider.classList.add('default-slider');
        }

        slider.noUiSlider.on('update', function(values, handle) {
            document.getElementById('red_max_val').textContent = Math.round(values[1]);
            document.getElementById('orange_max_val').textContent = Math.round(values[2]);
            document.getElementById('red_max').value = Math.round(values[1]);
            document.getElementById('orange_min').value = Math.round(values[1]);
            document.getElementById('orange_max').value = Math.round(values[2]);
            document.getElementById('green_min').value = Math.round(values[2]);
            
            document.getElementById('show_red_min').textContent = 0;
            document.getElementById('show_red_max').textContent = Math.round(values[1]);
            document.getElementById('show_orange_min').textContent = Math.round(values[1]);
            document.getElementById('show_orange_max').textContent = Math.round(values[2]);
            document.getElementById('show_green_min').textContent = Math.round(values[2]);
            document.getElementById('show_green_max').textContent = 100;
        });

        slider.noUiSlider.on('start', function() {
            if (slider.classList.contains('default-slider')) {
                slider.classList.remove('default-slider');
                $('.default-values-warning').fadeOut(300);
            }
        });

        slider.noUiSlider.on('slide', function(values, handle) {
            if (handle === 0) {
                slider.noUiSlider.set([0, null, null, null]);
            }
            if (handle === 3) {
                slider.noUiSlider.set([null, null, null, 100]);
            }
        });
    });
</script>
@endsection
