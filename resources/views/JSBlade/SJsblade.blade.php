<script >
    $(document).ready(function () {
        if($('#reaction-change').is(':checked')) $('.ra-pdf-check').removeClass('hide')

        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        }


        //Data Table
        if ($('#dTable').length > 0) {
            $('#dTable').DataTable({
                language: {
                    sEmptyTable: `{{__('action.table_norecord')}}`,
                    sInfo: "_START_ bis _END_ von _TOTAL_ Einträgen",
                    sInfoEmpty: "0 bis 0 von 0 Einträgen",
                    sInfoFiltered: "(gefiltert von _MAX_ Einträgen)",
                    sInfoPostFix: "",
                    sInfoThousands: ".",
                    sLengthMenu: "_MENU_ "+ `{{__('action.table_showdata')}}`,
                    sLoadingRecords: `{{__('action.table_loading')}}`,
                    sProcessing: `{{__('action.table_pleasewait')}}`,
                    sSearch: `{{__('action.search')}}`,
                    sZeroRecords: `{{__('action.table_nodata')}}`,
                    oPaginate: {
                        sFirst: `{{__('action.table_first')}}`,
                        sPrevious: `{{__('action.table_back')}}`,
                        sNext: `{{__('action.table_next')}}`,
                        sLast: `{{__('action.table_latest')}}`,
                    },
                    oAria: {
                        sSortAscending:
                            ": aktivieren, um Spalte aufsteigend zu sortieren",
                        sSortDescending:
                            ": aktivieren, um Spalte absteigend zu sortieren",
                    },
                },
            });
        };

        // #privant all cart empty hit
        $('#checkcarthavedata').click(function(e) {
            let cart = cartCount;
            if(cart < 1){
                toastr.warning(`{{__('action.empty_cart')}}`);
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();

                return false;
            }
        });

        $('#accesschecklimit').click(function(e) {
            let userCheck = "{{ maxAddUser() }}";
            if(userCheck < 1){
                toastr.warning(`{{trans('action.error_for_max_user_expire')}}`);
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }
        });

        $('.own_delete').click(function(e) {
            e.preventDefault();
            Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes_delete')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
            }).then((result) => {
                if (result.value) {
                    $(this).unbind('submit').submit();
                }
            });
        });

        $('.deleteBTN').click(function(e) {
            e.preventDefault();
            Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes_delete')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
            }).then((result) => {
                if (result.value) {
                    $(this).removeClass("deleteBTN");
                   window.location = this.href;

                }
                else{
                    e.preventDefault();
                }
            });
        });

        //delete user
        $('.btndeleteuser').click(function(e){
            e.preventDefault();
            Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes_delete')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
            }).then((result) => {
                if (result.value) {
                    $(this).removeClass("btndeleteuser");
                    window.location = this.href;
                }
                else{
                    e.preventDefault();
                }
            });
        });

        // Change Year/Month By Clicking
        $("input[type=radio][name=mycb]").on('change',function(){
            let value = $(this).val();
            let link = $(`#cb-my${value}`).attr('data-href');
            $.ajax({
                url: "{{ route('Sajax.changeYearMonth') }}",
                beforeSend: function(){
                    Swal.fire({
                        title: `{{__('action.processing')}}`,
                        imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        confirm: true,
                        showLoaderOnConfirm: true,
                        width: '300px',
                        height: '180px'
                    })
                },
                type: 'POST',
                data: { value: value,'proid':{{request()->proid ?? 0}},'subid':{{request()->subid ?? 0}} },
                dataType:"json",
                success: function(msg) {
                    if(msg.success == true){
                        toastr.success(`{{__('action.setting_saved_succfully')}}`);
                        window.location.href = link;
                        // window.location.reload();
                    }
                }
            });
        })
    })

    function deleteSubuser(e) {
        let url = $(e).data('url');
        Swal.fire({
        title: `{{__('action.areyousure')}}`,
        text: `{{__('action.you_cant_revert')}}`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#fb0404',
        cancelButtonColor: '#d33',
        confirmButtonText:  `{{__('action.yes_delete')}}`,
        cancelButtonText: `{{__('action.cancel')}}`
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: url,
                    type: "GET",
                    success: function (msg) {
                        console.log(msg.message,msg.alert_type,url);
                        Swal.fire(msg.alert_type, msg.message, msg.alert_type);
                        $('.datatables-userList').DataTable().ajax.reload();
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        Swal.fire("Error deleting!", "Please try again", "error");
                    }
                })
            }
            else{
                e.preventDefault();
            }
        });
    }
    window.cartCount = "{{ getCartCount()}}";
    var cartCount  = window.cartCount;

    function switch_user(id) {
        $.ajax({
            method: "POST",
            //async: false,
            url: "{{ route('Sajax.switch') }}",
            beforeSend: function(){
                Swal.fire({
                    title: `{{__('action.processing')}}`,
                    imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    confirm: true,
                    showLoaderOnConfirm: true,
                    width: '300px',
                    height: '180px'
                })
            },
            data: {
                id: id
            },
            dataType:"json",
            success: function (msg) {
                if (msg.success == true) {
                    toastr.success(`{{__('action.account_switch')}}`);
                    window.location = "{{route('dashboard.dashboard')}}";
                } else {
                    Swal.close();
                    toastr.warning(`{{__('action.something_went_wrong')}}`);
                }
            }
        });
    }

    //  CHange Datumcore To users
    function changeShowFilter_boss() {
        var filter_id = $('#changeShowFilter').val();
        if(filter_id==1) {
         $(".single-item").sort(sort_name_asc).appendTo('.posts');
        }else if(filter_id==2) {
            $(".single-item").sort(sort_name_des).appendTo('.posts');
        } else if(filter_id==3) {
            $(".single-item").sort(sort_number_asc).appendTo('.posts');
        }
        else if(filter_id==4) {
            $(".single-item").sort(sort_number_des).appendTo('.posts');
        }
        else {
            $(".single-item").sort(sort_number_asc).appendTo('.posts');
        }

    }
    function sort_name_asc(a, b) {
        return ($(b).data('title')) < ($(a).data('title')) ? 1 : -1;
    }
    function sort_name_des(a, b) {
        return ($(b).data('title')) > ($(a).data('title')) ? 1 : -1;
    }


    function sort_number_asc(a, b) {
        return ($(b).data('order')) < ($(a).data('order')) ? 1 : -1;

    }
    function sort_number_des(a, b) {
        return ($(b).data('order')) > ($(a).data('order')) ? 1 : -1;
    }


    // Add Data from Cart To package
    function addCartToPackage(cart_id){
        let action= "{{ route('Sajax.packageAddToCart') }}";
        $.ajax({
            type:'POST',
            url:action,
            data: {
                id : cart_id
            },
            dataType:"json",
            success:function(msg){
                if (msg.success == true) {
                    toastr.success(msg.message);
                    window.location.reload();
                }else if(msg.success === false){
                    (msg.cartCount == 0)?$("#shoppingCart").hide() : $("#shoppingCart").show();
                    Swal.fire({
                        title: msg._alert,
                        html: msg.message,
                        type: msg._alert_type,
                        showCancelButton: false,
                        confirmButtonColor: '#fb0404',
                        cancelButtonColor: '#d33'

                    })
                }
            },
            error:function(msg){
                console.log(msg);
            }
        });
    }


    function deletePackage(cart_ids){
        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes_delete')}}`,
            cancelButtonText: `{{__('action.cancel')}}`

        }).then((result) => {
            if (result.value) {
            let action= "{{ route('Sajax.deletePackage') }}";
                $.ajax({
                    type:'POST',
                    url:action,
                    data: {
                        ids : cart_ids
                    },beforeSend: function(){
                        Swal.fire({
                            title: `{{__('action.processing')}}`,
                            imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            confirm: true,
                            showLoaderOnConfirm: true,
                            width: '300px',
                            height: '180px'
                        })
                    },
                    dataType:"json",
                    success:function(data){
                        if (data.success == true) {
                            Swal.close();
                            toastr.success(data.message);
                            $('#packages_'+data.cart_id).remove();
                            window.location.reload();
                        }
                    },
                    error:function(data){
                        // console.log(data);
                    }
                });
            }
        })
    }



    function btnAddCart(e){
        if(cartCount >= 200) return toastr.warning(`{{__('action.cart_max_allow_alert')}}`);
        cartCount ++;
        (cartCount == 0)?$("#shoppingCart").hide() : $("#shoppingCart").show();
        $("#cardValue").text(cartCount);
        
        const isFrequencyTab = $(e).closest('#frequencyGeneratorTab').length > 0;

        let content;
        let frequency = '';
        let time = '';
        
        if (isFrequencyTab) {
            content = document.getElementById('frequency_topic')?.value || '';
            frequency = document.getElementById('frequency_hz')?.value || '';
            time = document.getElementById('frequency_time')?.value || '';
        } else {
            content = document.getElementById('own_topic')?.value || '';
            frequency = document.getElementById('frequency-display')?.textContent || '';
            time = (document.getElementById('topic-time-display')?.getAttribute('data-price') || document.getElementById('topic-time-display')?.textContent || '');
        }
        
        if(content == null || content == ''){
            return toastr.warning(`{{__('action.note_couldnt_save')}}`);
        }

        if (isFrequencyTab) {
            if (!frequency || !time) {
                return toastr.warning(`{{__('action.frequency_time_both_required')}}`);
            }
            if (frequency && (frequency < 250 || frequency > 20000)) {
                return toastr.warning(`{{__('action.invalid_frequency')}}`);
            }
            if (time && (time < 5 || time > 3600)) {
                return toastr.warning(`{{__('action.invalid_time')}}`);
            }
        }

        let action= '{{ route("Sajax.add2Cart") }}';
        $.ajax({
            type:'POST',
            url:action,
            beforeSend: function(){
                Swal.fire({
                    title: `{{__('action.processing')}}`,
                    imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    confirm: true,
                    showLoaderOnConfirm: true,
                    width: '300px',
                    height: '180px'
                })
            },
            data: {
                ana_id          : 1,
                name            : content,
                submenu_id      : '',
                proID           : '',
                calculation     : '',
                male            : '',
                heart           : '',
                price           : '',
                causes_id       : '',
                medium_id       : '',
                tipp_id         : '',
                color           : '',
                type            : 'Topic',
                frequency       : frequency,
                time            : time
            },
            dataType:"json",
            success:function(data){
                if (data.success == true) {
                    var dataShow = `<li class="rem_${data.cart_id}"> <div> <p data-cartprice="${data.cart_data.price}"><a href="">${data.result} &nbsp;&nbsp;&nbsp;${data.cart_data.analysisName}</a></p> </div>`;
                    dataShow += `<div class="treatment-list-icon">`;
                    dataShow += `<a href="javascript:void(0)" onclick="removeCid('${data.cart_id}',${data.cart_data.analysisID})"> <i class="fas fa-times-circle danger-color"></i></a>`;

                    dataShow += `</div>`;
                    dataShow += `</li>`;
                    $(".treatment-list" ).append(dataShow);
                    Swal.close();
                    toastr.success(`{{__('action.topic_cart_save')}}`);
                    Livewire.dispatch('refreshComponent');
                }else if(data.success == false){
                    $("#cardValue").text(cartCount - 1);
                    Swal.fire({
                        title: data._alert,
                        html: data.message,
                        type: data._alert_type,
                        showCancelButton: true,
                        confirmButtonColor: '#fb0404',
                        cancelButtonColor: '#d33'

                    })
                }
            },
            error:function(data){
                // console.log(data);
            }
        });
    }
    
    function cartbutton(ana_id,name,submenu_id,proID,calculation,male,heart,price,causes_id,medium_id,tipp_id,color,type,others = []) {
        if(cartCount >= 200) return toastr.warning(`{{__('action.cart_max_allow_alert')}}`);
        cartCount++;
        $("#cardValue").text(cartCount);
        (cartCount == 0) ? $("#shoppingCart").hide() : $("#shoppingCart").show();
        let type_id = null;
        if(type == 'Analysis') type_id = ana_id;
        if(type == 'Causes') type_id = causes_id;
        if(type == 'Medium') type_id = medium_id;
        if(type == 'Tipp') type_id = tipp_id;
        if(type == 'Einfluss') type_id = tipp_id;
        $(".temp_"+type_id).find('a').remove();
        $(".temp_"+type_id).append(`<i class="fas fa-check-circle primary-color f-18"></i>`);


        let action= "{{ route('Sajax.add2Cart') }}";
        $.ajax({
            type:'POST',
            url:action,
            data: {
                ana_id          : (ana_id == '') ? 0 : ana_id,
                name            : name,
                submenu_id      : (submenu_id == '') ? 0 : submenu_id,
                proID           : (proID == '') ? 0 : proID,
                calculation     : (calculation == '') ? 0 : calculation,
                male            : (male == '') ? 0 : male,
                heart           : (heart == '') ? 0 : heart,
                price           : (price == '') ? 0 : price,
                causes_id       : (causes_id == '') ? 0 : causes_id,
                medium_id       : (medium_id == '') ? 0 : medium_id,
                tipp_id         : (tipp_id == '') ? 0 : tipp_id,
                color           : (color == '') ? null : color,
                type            : type,
                others           : others
            },
            dataType:"json",
            success:function(data){
                Livewire.dispatch('refreshComponent');
                
                if (data.success == true) {
                    toastr.success(`{{__('action.cart_save')}}`);

                    var dataShow = `<li class="rem_${data.cart_id}"> <div> <p data-cartprice="${data.cart_data.price}"><a href="">${data.result} &nbsp;&nbsp;&nbsp;${data.cart_data.analysisName}</a></p> </div>`;
                    dataShow += `<div class="treatment-list-icon">`;
                        dataShow += `<a href="javascript:void(0)" onclick="removeCid('${data.cart_id}','${data.cart_data.type}',${type_id})"> <i class="fas fa-times-circle danger-color"></i></a>`;

                    dataShow += `</div>`;
                    dataShow += `</li>`;
                    $(".treatment-list" ).append(dataShow);
                    // alert("Successfully Added to the cart");
                }else if(data.success === false){
                    cartCount--;
                    $("#cardValue").text(cartCount);
                    Swal.fire({
                        title: data._alert,
                        html: data.message,
                        type: data._alert_type,
                        showCancelButton: false,
                        confirmButtonColor: '#fb0404',
                        cancelButtonColor: '#d33'

                    })
                }else {
                    cartCount--;
                    $("#cardValue").text(cartCount);
                }
            },
            error:function(data){
                console.log(data);
            }
        });

    }


    function cartbuttonFocus(boxid,ana_id,name,subID,proID,calculation,male,heart,price,causes_id,color,type) {
        if(cartCount >= 200) return toastr.warning(`{{__('action.cart_max_allow_alert')}}`);
        cartCount ++;
        $("#cardValue").text(cartCount);
        (cartCount == 0) ? $("#shoppingCart").hide() : $("#shoppingCart").show();
        $("#cardValue").text(cartCount);
        $("#boxCart_"+boxid).find('#cartaddbox'+boxid).hide();
        $('#cartaddbox'+boxid).prop('id', 'cartaddboxex'+boxid);
        $('#cartaddboxex'+boxid).addClass("cartaddboxex");
        $("#boxCart_"+boxid).append(`<i id="icon${boxid}" class="fas fa-check-circle primary-color f-18"></i>`);

        $("#boxCartRes_"+boxid).find('#cartaddboxRes'+boxid).remove();
        $("#boxCartRes_"+boxid).append(`<i id="icon${boxid}" class="fas fa-check-circle primary-color f-18"></i>`);

    //Price remove and add null
        let action= "{{ route('Sajax.add2Cart') }}";

        $.ajax({
            type:'POST',
            url:action,
            data: {
                ana_id          : ana_id,
                name            : name,
                submenu_id      : subID,
                proID           : proID,
                calculation     : calculation,
                male            : male,
                heart           : heart,
                price           : null,
                causes_id       : causes_id,
                medium_id       : null,
                tipp_id         : null,
                color           : color,
                type            : type
            },
            dataType:"json",
            success:function(data){
                Livewire.dispatch('refreshComponent');
                if (data.success == true) {
                    toastr.success(`{{__('action.cart_save')}}`);

                    var dataShow = `<li class="rem_${data.cart_id}"> <div> <p data-cartprice="${data.cart_data.price}"><a href="">${data.result} &nbsp;&nbsp;&nbsp;${data.cart_data.analysisName}</a></p> </div>`;
                    dataShow += `<div class="treatment-list-icon">`;
                    dataShow += `<a href="javascript:void(0)" onclick="removeCid('${data.cart_id}',${data.cart_data.analysisID},${boxid})"> <i class="fas fa-times-circle danger-color"></i></a>`;

                    dataShow += `</div>`;
                    dataShow += `</li>`;
                    $(".treatment-list" ).append(dataShow);
                }else if(data.success === false){
                    cartCount--;
                    $("#cardValue").text(cartCount);
                    Swal.fire({
                        title: data._alert,
                        text: data.message,
                        type: data._alert_type,
                        showCancelButton: false,
                        confirmButtonColor: '#fb0404',
                        cancelButtonColor: '#d33'

                    })
                }
            },
            error:function(data){
                // console.log(data);
            }
        });


    }
    //Remove form cart
    function removeCid(cart_id,type,type_id){
        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText: `{{__('action.yes_delete')}}`,
            cancelButtonText: `{{__('action.cancel')}}`

        }).then((result) => {
            if (result.value) {
                cartCount --;
                $("#cardValue").text(cartCount);
                (cartCount == 0) ? $("#shoppingCart").hide() : $("#shoppingCart").show();
                let action= "{{ route('Sajax.removeCart') }}";
                $(".rem_" + cart_id).hide();

                $(".temp_" + type_id).remove();
                $(".cartPluse" + type_id).html(`<div class="temp_${type_id}"></div>`);

                $.ajax({
                    type:'POST',
                    url:action,
                    data: {
                        cart_id : cart_id
                    },
                    dataType:"json",
                    success:function(data){
                        if (data.success == true) {
                            toastr.warning(`{{__('action.removed_successfully')}}`);
                            $('.cartContent').trigger('click');
                            if(data.cart.type == 'Einfluss' || data.cart.type == 'analyse' || data.cart.type == 'Analyse'){
                                if(!$('#boxCartRes_'+type_id).length){
                                    $("#boxCartRes_" + type_id).find('#icon'+type_id).remove();
                                    $("#boxCartRes_" + type_id).find('.boxInfoRes_' + type_id).after(`<a href="javascript:void(0)" id="boxCartRes_${type_id}" onclick="cartbuttonFocus(${type_id},'','${data.cart.analysisName}','${data.cart.submenu_id}', '${data.cart.productID}', '', '', '', '','${data.cart.causes_id}','${data.cart.color}','${data.cart.type}')"><i class="fas fa-plus-circle"></i></a>`);
                                }

                                if(!$('#cartaddbox'+type_id).length){
                                    $("#boxCart_" + type_id).find('#icon'+type_id).remove();
                                    $("#boxCart_" + type_id).find('.boxInfo_' + type_id).after(`<a href="javascript:void(0)" id="cartaddbox${type_id}" onclick="cartbuttonFocus(${type_id},'','${data.cart.analysisName}','${data.cart.submenu_id}', '${data.cart.productID}', '', '', '', '','${data.cart.causes_id}','${data.cart.color}','${data.cart.type}')"><i class="fas fa-plus-circle"></i></a>`);
                                }
                            }
                            else{
                                $(".temp_" + type_id).html(`<a href="javascript:void(0)" onclick="cartbutton(${data.cart.analysisID},null,'${data.cart.submenu_id}','${data.cart.productID}','${data.cart.calculation}','${data.cart.male}','${data.cart.heart}','${data.cart.price}','${data.cart.causes_id}','${data.cart.medium_id}','${data.cart.tipp_id}','${data.cart.color}','${data.cart.type}')"><i class="fas fa-plus-circle primary-color f-18"></i></a>`);
                            }

                            $("#btn_redval").attr("onclick", "addRedValues()").removeClass('fa-check-circle').addClass('fa-plus-circle')

                            $("#btn_orangeval").attr("onclick", "addOrangeValues()").removeClass('fa-check-circle').addClass('fa-plus-circle')

                            $("#btn_greenval").attr("onclick", "addGreenValues()").removeClass('fa-check-circle').addClass('fa-plus-circle')

                            // Notify Livewire components about the cart item removal
                            if (typeof Livewire !== 'undefined') {
                                Livewire.dispatch("cartItemRemoved."+type_id);
                                Livewire.dispatch('refreshComponent');
                            }
                        }
                    },
                    error:function(data){
                        // console.log(data);
                    }
                });
            }
        })

    }

    // Dashboard View
    function dashboardView(user_id,value){
        $('#modals-top').modal('hide');
        let action= "{{ route('Sajax.changeDashView') }}";
        $.ajax({
            type:'POST',
            url:action,
            data: {
                user_id : user_id,
                value   : value
            },
            beforeSend: function(){
                Swal.fire({
                    title: `{{__('action.processing')}}`,
                    imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    confirm: true,
                    showLoaderOnConfirm: true,
                    width: '300px',
                    height: '180px'
                })
            },
            dataType:"json",
            success:function(data){
                if (data.success == true) {
                    Swal.close();
                    toastr.success(`{{__('action.setting_saved_succfully')}}`);
                    window.location.reload();
                }
            },
            error:function(data){
                // console.log(data);
            }
        });
    }

    // Dashboard Topic Save
    function topicSave(e,value){
        var textarea = $(e).parents('.own-topic-text-div').find('#own_topic')
        var content = textarea.val();

        if(content === ''){
            Swal.fire({
                icon: 'success',
                title: "{{__('action.no_product_title')}}",
                type: "warning",
                showConfirmButton: false,
                timer: 1500
            })
        }else{
            let action= "{{ route('Sajax.saveTopic') }}";
            let location = window.location.href.toString();

            if(value == 'Save'){
                $.ajax({
                    type:'POST',
                    url:action,
                    beforeSend: function(){
                        Swal.fire({
                            title: `{{__('action.processing')}}`,
                            imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            confirm: true,
                            showLoaderOnConfirm: true,
                            width: '300px',
                            height: '180px'
                        })
                    },
                    data: {
                        content : content
                    },
                    dataType:"json",
                    success:function(data){
                        Swal.close();
                        if (data.success == true) {
                            $('#topicModal').modal('hide');
                            toastr.success(`{{__('action.topic_saved_successfully')}}`);
                            if(value == 'Delete') textarea.innerHTML = '';
                            else  textarea.innerHTML = content;
                            if(window.location.href.split('/')[4] != 'fv') window.location.reload();
                        }
                    }
                });
            }else{
                $.ajax({
                    type:'POST',
                    url:action,
                    beforeSend: function(){
                        Swal.fire({
                            title: `{{__('action.processing')}}`,
                            imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            confirm: true,
                            showLoaderOnConfirm: true,
                            width: '300px',
                            height: '180px'
                        })
                    },
                    data: {
                        content : ''
                    },
                    dataType:"json",
                    success:function(data){
                        Swal.close();
                        if (data.success == true) {
                            $('#topicModal').modal('hide');
                            textarea.value = "";
                            toastr.success(`{{__('action.delete_success')}}`);
                            textarea.innerHTML = '';
                            if(window.location.href.split('/')[4] != 'fv') window.location.reload();
                        }
                    }
                });
            }
        }

    }
    // Save Package for cart
    function saveCartpkg(){
        let action= "{{ route('Sajax.savePackageCart') }}";
        let package_name = $('#craftTitle').val();
        // console.log(package_name.length)
        if (package_name.length == 0) {
            Swal.fire({
                title: "{{__('action.error')}}",
                text: "Title is required",
                icon: "error"
            });
        }else{
            $.ajax({
                type:'POST',
                url:action,
                data: {
                    package_name : package_name
                },
                beforeSend: function(){
                    Swal.fire({
                        title: `{{__('action.processing')}}`,
                        imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        confirm: true,
                        showLoaderOnConfirm: true,
                        width: '300px',
                        height: '180px'
                    })
                },
                dataType:"json",
                success:function(data){
                    if (data.success == true) {
                        document.getElementById('craftTitle').value = '';
                        $('.modal').modal('hide')
                        Swal.close();
                        toastr.success(`{{__('action.cart_package_added_successfully')}}`);
                        window.location.reload();
                    }
                },
                error:function(data){
                    // console.log(data);
                }
            });
        }

    }

    // Clear Cart Data From Cart
    function clearCart(){
        // var url = $(location).attr('href').split("/").splice(4, 1).join("/");
        let urls = `{{\Request::segment(2)}}`;

        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes_delete')}}`,
            cancelButtonText: `{{__('action.cancel')}}`

        }).then((result) => {
            if (result.value) {
                let action= "{{ route('Sajax.clearCart') }}";
                $(".treatment-list").empty();
                cartCount = 0;
                $("#cardValue").text(cartCount);
                $("#shoppingCart").hide();
                $.ajax({
                    type:'POST',
                    url:action,
                    beforeSend: function(){
                        Swal.fire({
                            title: `{{__('action.processing')}}`,
                            imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            confirm: true,
                            showLoaderOnConfirm: true,
                            width: '300px',
                            height: '180px'
                        })
                    },
                    dataType:"json",
                    success:function(data){
                        if (data.success == true) {
                            for (var i = 0; i < 10; i++) {
                                $('#cartaddboxex'+i).prop('id', 'cartaddbox'+i);
                                $('#cartaddbox'+i).prop('class', '');
                                $('#cartaddbox'+i).show();

                                $('#cartaddboxex'+i).remove();
                            }
                            $(".cartaddboxex").remove();
                            $('#efBoxes .fa-check-circle').remove();
                            toastr.success(data.message);
                            Swal.close();
                            // Hide any shopping modal (works with dynamic IDs)
                            $('[id^="shopping-modal-"]').modal('hide');
                            
                            // Update any Livewire component managing the cart
                            if (typeof Livewire !== 'undefined') {
                                Livewire.dispatch('cartCleared');
                                Livewire.dispatch('refreshComponent');
                            }
                        }
                    },
                    error:function(data){
                        // console.log(data);
                    }
                });
            }
        })
    }

    // Add User own Create Group On Database
    function groupAdd(){
        let group_name = $('#group_name_own').val();
        // let action= "{{ route('Sajax.addUserOwnGroup') }}";
        let action= "{{ route('group.store') }}";
            $.ajax({
                type:'POST',
                url:action,
                data:{
                    group_name : group_name
                },
                dataType:"json",
                success:function(data){
                    if (data.success == true) {
                        window.location.reload();
                        toastr.success(`{{__('action.group_added_successfully')}}`);
                    }
                },
                error:function(data){
                    // console.log(data);
                }
            });
    }

    // #date Change
    function dateChange(){
        var date = $('#datePicker').val();
        let check = "Save";
        $('#changeDate').modal('hide');
        $.ajax({
            url: "{{ route('Sajax.changeDatumcore') }}",
            type: 'POST',
            data: {
                date : date,check : check
            },
            dataType:"json",
            success: function(msg) {
                if(msg.success == true){
                    toastr.success(`{{__('action.setting_saved_succfully')}}`);
                    window.location.reload();
                }
            }
        });
    }

    // Delete PDF Logo
    function deletePDFLogo(id){
        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes_delete')}}`,
            cancelButtonText: `{{__('action.cancel')}}`

        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "{{ route('Sajax.removePDFLogo') }}",
                    type: 'POST',
                    data: {
                        id : id
                    },
                    dataType:"json",
                    success: function(msg) {
                        if(msg.success == true){
                            toastr.warning(`{{__('action.delete_success')}}`);
                            window.location.reload();
                        }
                        // $('#checkym').load(document.URL +   ' #checkym');
                    }
                });
            }
        })
    }

    // delete subuser
    function deleteSubUser(subUserID){
        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.you_cant_revert')}}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes_delete')}}`,
            cancelButtonText: `{{__('action.cancel')}}`

        }).then((result) => {
            if (result.value) {
                $('#show_search_result').find('#subList'+subUserID).remove();
                $.ajax({
                    url: "{{ route('Sajax.deleteSubUser') }}",
                    type: 'POST',
                    beforeSend: function(){
                        Swal.fire({
                            title: `{{__('action.processing')}}`,
                            imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            confirm: true,
                            showLoaderOnConfirm: true,
                            width: '300px',
                            height: '180px'
                        })
                    },
                    data: {
                        id : subUserID
                    },
                    dataType:"json",
                    success: function(msg) {
                        if(msg.success == true){
                            toastr.warning(`{{__('action.delete_success')}}`);
                            Swal.close();
                            window.location.reload();
                        }
                        // $('#checkym').load(document.URL +   ' #checkym');
                    }
                });
            }
        })
    }
    // Delete PDF
    function deletePDF(id, pdf) {
        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.pdf_logo_delete_alert')}}`,
            icon: `{{__('action.warning')}}`,
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: `{{__('action.Yes_delete_it')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
        }).then((result) => {

            if (result.value) {

                $.ajax({

                    type: "POST",

                    url: "{{ route('Aajax.deletePDF') }}",
                    beforeSend: function(){
                        Swal.fire({
                            title: `{{__('action.processing')}}`,
                            imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            confirm: true,
                            showLoaderOnConfirm: true,
                            width: '300px',
                            height: '180px'
                        })
                    },

                    data: {
                        id: id,
                        pdfid: pdf
                    },

                    success: function(data) {
                        Swal.close();
                        toastr.warning(`{{__('action.delete_success')}}`);

                        $("#pdf" + id).remove();
                    }

                });
            }
        })
    }

    // Delete DC PDF
    function deleteDcPDF(id, pdf) {
        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.pdf_logo_delete_alert')}}`,
            icon: `{{__('action.warning')}}`,
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: `{{__('action.Yes_delete_it')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
        }).then((result) => {

            if (result.value) {
                $.ajax({
                    type: "DELETE",
                    url: "{{ url('Aajax/deleteDcPDF') }}"+'/'+id,
                    beforeSend: function(){
                        Swal.fire({
                            title: `{{__('action.processing')}}`,
                            imageUrl: "{!! asset('/') !!}"+"ezgif.com-gif-maker.webp",
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            confirm: true,
                            showLoaderOnConfirm: true,
                            width: '300px',
                            height: '180px'
                        })
                    },
                    success: function(data) {
                        Swal.close();
                        toastr.warning(`{{__('action.delete_success')}}`);
                        $("#pdf" + id).remove();
                    }

                });
            }
        })
    }

    var preloader =`<div class="preloader" id="preload_product">
                        <img src="{!! asset('images/Fill-4.png') !!}" alt="">
                    </div>`;

    /*
    *Cache Clear Function
    */
    $('#cache-clear').click(function(){
        Swal.fire({
            title: `{{__('action.areyousure')}}`,
            text: `{{__('action.wrn_cache_clear')}}`,
            icon: `{{__('action.warning')}}`,
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: `{{__('action.Yes_delete_it')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
        }).then((result) => {
            if (result.value) {
                $('.preloader').show();
                $.ajax({
                    type: "POST",
                    url: "{{ route('Sajax.cache-clear') }}",
                    success: function(data) {
                        $('.preloader').hide();
                        toastr.warning(`{{__('action.success_cache_clear')}}`);
                        window.location.reload();
                    }
                })
            }
        })
    })


    function current_user_update(url,fname,lname,birthdate,cron_email,gebort,biorhythmus){
        Swal.fire({
            title: "{{ trans('action.areyousure') }}",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            cancelButtonText: "{{ trans('action.cancel') }}",
            confirmButtonText: "{{ trans('action.change') }}"
        }).then((result) => {
            if (result.value) {
                $.post(url+'/users/change_current_user_info',{
                    first_name : fname,
                    last_name : lname,
                    gebdatum : birthdate,
                    cron_email : cron_email,
                    gebort: gebort,
                    biorhythmus: biorhythmus
                }, function(data, status){
                    if (data.success == true) {
                        toastr.success(data.message);
                        location.reload();
                    }
                });
            } else {
                // User clicked the "Cancel" button or closed the dialog
                // Do nothing or perform any other action you want
            }
        })
    }

</script>




