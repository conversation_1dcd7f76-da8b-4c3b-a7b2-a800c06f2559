<script>

    $(document).ready(function(){
        // $("input[type=radio][name=mycb]").on('change',function(){
        //     let value = $(this).val();
        //     $(`.mycb`).removeClass('round');
        //     $(`#cb-my${value}`).addClass('round');
        // })
        $("#open-changeDate").on('click',function(){
            $("#changeDate").modal('show');
        })

        $("input[name=rlcb]").on('click',function(){
            let value = $(this).val();
            let link = null;
            // $(`.rlcb`).removeClass('round');
            // $(`#cb-rl${value}`).addClass('round');

            if(value === '1') {
                link = `{{ route('dashboard.changerandomstatus') }}`;
                $.ajax({
                    url: link,
                    type: 'GET',
                    data: { value: null },
                    dataType:"json",
                    success: function(msg) {
                        if(msg.success == true){
                            link = $(`#cb-rl${value}`).attr('data-href');
                            if(link !== "") {
                                window.location.href = link;
                            } else {
                                window.location.reload();
                            }
                        }
                    }
                });
            } else {
                longday = $(`#longday`).find('option:selected').val();
                if(longday < 1 ) {
                    Swal.fire({ type: 'warning', title: "{{ __('action.choose_days') }}", showConfirmButton: false, timer: 3500 });
                    $(this).attr('checked', false);
                } else {
                    link = $(`#cb-rl${value}`).attr('data-href');
                    window.location.href = link;
                }
            }
        })
    })

    function addRedValues(e) {
        let proid = "{{ request()->proid }}";
        let subid = "{{ request()->subid }}";
        let action_url = "{{ route('dashboard.all_red')}}";
        if(proid == "") {
            proid = "{{ request()->ownid }}";
            subid = "{{ request()->ownsubid }}";
            action_url = "{{ route('dashboard.all_own_red')}}";
        }
        Swal.fire({
            title: `{{__('action.Are_you_sure?')}}`,
            text: `{{__('action.all_red_values')}}`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    type: "POST",
                    url: action_url,
                    beforeSend: function(){
                        // Swal.fire({
                        //     title: `{{__('action.processing')}}`,
                        //     imageUrl: "{!! asset('/') !!}"+"loading.gif",
                        //     showConfirmButton: false,
                        //     allowOutsideClick: false,
                        //     confirm: true,
                        //     showLoaderOnConfirm: true,                            
                        //     width: '300px',
                        //     height: '180px'
                        // })
                    },
                    data: {request: 'all_red', subid : subid, proid : proid},
                    success: function(res) {
                        Swal.close();
                        if(res.missing_fields) {
                            Swal.fire({
                                title: '{{__("action.missing_fields_title")}}',
                                text: res.msg,
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonText: '{{__("action.complete_menu_settings")}}',
                                cancelButtonText: '{{__("action.cancel")}}',
                            }).then((modalResult) => {
                                if (modalResult.value) {
                                    window.location.href = res.edit_url;
                                }
                            });
                            return;
                        }
                        if(res.status == true) {
                            // $('.red_div').find('button').removeAttr("onclick", "");
                            // $('.red_div').find('button i').removeClass('fa-plus-circle').addClass('fa-check-circle')
                            $('#btn_redval').removeAttr("onclick", "");
                            $('#btn_redval').removeClass('fa-plus-circle').addClass('fa-check-circle')
                            let dataShow = "";
                            res.data.forEach(function(value) {
                                $(".temp_"+value.analysisID).find('a').remove();
                                $(".temp_"+value.analysisID).append(`<i class="fas fa-check-circle primary-color f-18"></i>`);
                                dataShow += `<li class="rem_${value.cart_id}"> <div> <p data-cartprice="${value.price}"><a href="">${value.minute} &nbsp;&nbsp;&nbsp;${value.analysisName}</a></p> </div>
                                                <div class="treatment-list-icon">
                                                    <a href="javascript:void(0)" onclick="removeCid('${value.cart_id}','${value.type}',${value.analysisID})"> <i class="fas fa-times-circle danger-color"></i></a>

                                                </div>
                                            </li>`;
                            });
                            cartCount = res.data.length + parseInt($("#cardValue").text())
                            $(".treatment-list" ).append(dataShow);
                            $('.cartContent').trigger('click');
                            $("#cardValue").text(cartCount);
                            $("#shoppingCart").show();
                            toastr.success(`{{__('action.added_successfully')}}`);

                        } else if(res.success == false){
                            Swal.fire({                        
                                title: res._alert,
                                text: res.message,
                                type: res._alert_type,
                                showCancelButton: false,
                                confirmButtonColor: '#fb0404',
                                cancelButtonColor: '#d33'

                            })
                        } else {
                            toastr.success(res.msg);
                        }
                    }
                });
            }
        })
    }

    function addOrangeValues(e) {
        let proid = "{{ request()->proid }}";
        let subid = "{{ request()->subid }}";
        let action_url = "{{ route('dashboard.all_orange')}}";
        if(proid == "") {
            proid = "{{ request()->ownid }}";
            subid = "{{ request()->ownsubid }}";
            action_url = "{{ route('dashboard.all_own_orange')}}";
        }
        Swal.fire({
            title: `{{__('action.Are_you_sure?')}}`,
            text: `{{__('action.all_orange_values')}}`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    type: "POST",
                    url: action_url,
                    beforeSend: function(){},
                    data: {request: 'all_orange', subid : subid, proid : proid},
                    success: function(res) {
                        Swal.close();
                        if(res.missing_fields) {
                            Swal.fire({
                                title: '{{__("action.missing_fields_title")}}',
                                text: res.msg,
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonText: '{{__("action.complete_menu_settings")}}',
                                cancelButtonText: '{{__("action.cancel")}}',
                            }).then((modalResult) => {
                                if (modalResult.value) {
                                    window.location.href = res.edit_url;
                                }
                            });
                            return;
                        }
                        if(res.status == true) {
                            $('#btn_orangeval').removeAttr("onclick", "");
                            $('#btn_orangeval').removeClass('fa-plus-circle').addClass('fa-check-circle')
                            let dataShow = "";
                            res.data.forEach(function(value) {
                                $(".temp_"+value.analysisID).find('a').remove();
                                $(".temp_"+value.analysisID).append(`<i class="fas fa-check-circle primary-color f-18"></i>`);
                                dataShow += `<li class="rem_${value.cart_id}"> <div> <p data-cartprice="${value.price}"><a href="">${value.minute} &nbsp;&nbsp;&nbsp;${value.analysisName}</a></p> </div>
                                                <div class="treatment-list-icon">
                                                    <a href="javascript:void(0)" onclick="removeCid('${value.cart_id}','${value.type}',${value.analysisID})"> <i class="fas fa-times-circle warning-color"></i></a>

                                                </div>
                                            </li>`;
                            });
                            cartCount = res.data.length + parseInt($("#cardValue").text())
                            $(".treatment-list" ).append(dataShow);
                            $('.cartContent').trigger('click');
                            $("#cardValue").text(cartCount);
                            $("#shoppingCart").show();
                            toastr.success(`{{__('action.added_successfully')}}`);

                        }else if(res.success == false){
                            Swal.fire({                        
                                title: res._alert,
                                text: res.message,
                                type: res._alert_type,
                                showCancelButton: false,
                                confirmButtonColor: '#fb0404',
                                cancelButtonColor: '#d33'

                            })
                        } else {
                            toastr.success(res.msg);
                        }
                    }
                });
            }
        })
    }

    function addGreenValues(e) {
        let proid = "{{ request()->proid }}";
        let subid = "{{ request()->subid }}";
        let action_url = "{{ route('dashboard.all_green')}}";
        if(proid == "") {
            proid = "{{ request()->ownid }}";
            subid = "{{ request()->ownsubid }}";
            action_url = "{{ route('dashboard.all_own_green')}}";
        }
        Swal.fire({
            title: `{{__('action.Are_you_sure?')}}`,
            text: `{{__('action.all_green_values')}}`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    type: "POST",
                    url: action_url,
                    beforeSend: function(){},
                    data: {request: 'all_green', subid : subid, proid : proid},
                    success: function(res) {
                        Swal.close();
                        if(res.missing_fields) {
                            Swal.fire({
                                title: '{{__("action.missing_fields_title")}}',
                                text: res.msg,
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonText: '{{__("action.complete_menu_settings")}}',
                                cancelButtonText: '{{__("action.cancel")}}',
                            }).then((modalResult) => {
                                if (modalResult.value) {
                                    window.location.href = res.edit_url;
                                }
                            });
                            return;
                        }
                        if(res.status == true) {
                            $('#btn_greenval').removeAttr("onclick", "");
                            $('#btn_greenval').removeClass('fa-plus-circle').addClass('fa-check-circle')
                            let dataShow = "";
                            res.data.forEach(function(value) {
                                $(".temp_"+value.analysisID).find('a').remove();
                                $(".temp_"+value.analysisID).append(`<i class="fas fa-check-circle primary-color f-18"></i>`);
                                dataShow += `<li class="rem_${value.cart_id}"> <div> <p data-cartprice="${value.price}"><a href="">${value.minute} &nbsp;&nbsp;&nbsp;${value.analysisName}</a></p> </div>
                                                <div class="treatment-list-icon">
                                                    <a href="javascript:void(0)" onclick="removeCid('${value.cart_id}','${value.type}',${value.analysisID})"> <i class="fas fa-times-circle success-color"></i></a>

                                                </div>
                                            </li>`;
                            });
                            cartCount = res.data.length + parseInt($("#cardValue").text())
                            $(".treatment-list" ).append(dataShow);
                            $('.cartContent').trigger('click');
                            $("#cardValue").text(cartCount);
                            $("#shoppingCart").show();
                            toastr.success(`{{__('action.added_successfully')}}`);

                        }else if(res.success == false){
                            Swal.fire({                        
                                title: res._alert,
                                text: res.message,
                                type: res._alert_type,
                                showCancelButton: false,
                                confirmButtonColor: '#fb0404',
                                cancelButtonColor: '#d33'

                            })
                        } else {
                            toastr.success(res.msg);
                        }
                    }
                });
            }
        })
    }

    function addFrabklangRedValues() {
        Swal.fire({
            title: `{{__('action.Are_you_sure?')}}`,
            text: `{{__('action.to_treat_all_red')}}`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#fb0404',
            cancelButtonColor: '#d33',
            confirmButtonText:  `{{__('action.yes')}}`,
            cancelButtonText: `{{__('action.cancel')}}`
        }).then((result) => {
            if (result.value) {
                let proid = $("#allred").attr('data-pro')
                let fetch_url = `{{url('reset4me/allred/fetch')}}`

                $.ajax({
                    type:'POST',
                    url: fetch_url,
                    dataType:"json",
                    data: {type: 'allred', proid: proid},
                    success: function(res){
                        let status = true;
                        let data = "";
                        let selected = $("#time_range").find('option:selected').val();
                        $('#time_range').find('.without_red').hide();
                        let treat_url = (selected == 'null') ? `{{url('reset4me/allred/treat')}}/${proid}` : `{{url('reset4me/allred/duepower')}}/${proid}/${selected}`

                        $("#treat_link1").attr("href", treat_url)
                        $("#time_range").attr('data-pro', proid)
                        $("#time_range").attr('data-status', "red")

                        if(res.data.red_analyses != null) {
                            let analyses = res.data.red_analyses
                            $.each(analyses, function(index, analyse) {
                                data += `<p> <i class="fas fa-check-circle text-danger"></i> <b>${analyse.ana_val}% ${analyse.ana_name}</b></p>`
                            })
                        } else {
                            status = false;
                            data += `<h3 class="text-bold text-success">{{__('action.no_analysis')}}</h3>`
                        }
                        if(!status){
                            $(".modal-footer").addClass('hide')
                        } else {
                            $(".modal-footer").removeClass('hide')
                        }

                        $("#anadata").html(data)
                        $("#frabklang_play").modal('show')
                    },
                    error:function(res){}
                });
            }
        })
    }

</script>
