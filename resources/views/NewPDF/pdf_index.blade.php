@extends('NewPDF.pdf_layout')
@section('pdf_content')
    {{-- pdf main contents --}}
    <div class="main-contents">
        {{-- own topics & note --}}
        @if (!empty($cronsetup->client_note) || !empty($cronsetup->topic))
            <div class="topic-note-contents border-top pt-2">
                @if (!empty($cronsetup->topic))
                    <div>
                        <h4>{{__('action.topic_pdf')}}:</h4>
                        <p>{{ $cronsetup->topic }}</p>
                    </div>
                @endif
                @if (!empty($cronsetup->client_note))
                    <div>
                        <h4>{{__('action.customer_note')}}:</h4>
                        <p>{{ $cronsetup->client_note }}</p>
                    </div>
                @endif
            </div>
        @endif
        
        @if(count($allSubmenus))
            {{-- normal pdf content start --}}
            <div class="pdf-contents" style="border-top: 1px solid #333; border-bottom: 1px solid #333;">
                <style>
                    .normal-pdf-item:last-child {
                        border-bottom: none !important;
                    }
                </style>
                @foreach ($allSubmenus as $submenu)
                    @if ($submenu['analyses'] && $submenu['analyses']->count())
                        <div class="normal-pdf-item" style="margin-bottom: 15px; border-bottom: 1px dotted #ccc;">
                            @php
                                $firstAnalysisType = $submenu['analyses']->first()['type'] ?? null;
                            @endphp
                            @if($firstAnalysisType != 6 && $firstAnalysisType != 0 && $submenu['submenu_name'] != "")
                                <h3 class='submenu-name m-0 pt-2'>{{ $submenu['submenu_name'] }}</h3>
                            @endif

                            @foreach ($submenu['analyses'] as $analyse)
                                @if($analyse['type'] == 6)
                                    {{-- Topic Display --}}
                                    <div class="topic-contents">
                                        <div style="display: flex; align-items: baseline;">
                                            <h4 style="margin: 0; margin-right: 10px; font-weight: bold;">{{__('action.topic_pdf')}}:</h4>
                                            <p style="margin: 0;">{{ $analyse['name'] }}</p>
                                        </div>
                                    </div>
                                @elseif($analyse['type'] == 0 || $analyse['type'] == 1 || $analyse['type'] == 2 || $analyse['type'] == 3)
                                    <div class="analysis-contents p-0 py-md">
                                        <div class="analysis-row clearfix">
                                            {{-- analysis name start --}}
                                            @if(isset($analyse['others']['allred']))
                                                <div class="analysis-name">
                                                    <h4>
                                                        <span style="background-color: #E84E1B; color:#fff;padding: 2px 6px;border-radius: 5px;margin-right: 2px;">{{ $analyse['others']['allred'] }}</span>/<span style="background-color: #2FAB66; color:#fff;padding: 2px 6px;border-radius: 5px;margin-left:2px;">{{ $analyse['others']['allgreen'] }}</span>
                                                        - {{ $analyse['name'] }}
                                                    </h4>
                                                </div>
                                            @else
                                                <div class="analysis-name">
                                                    <h4>{{ $analyse['cal'].'%' }} - {{ $analyse['name'] }}</h4>
                                                </div>
                                            @endif
                                            {{-- analysis name end --}}

                                            {{-- progressbar start --}}
                                            <div class="analysis-progressbar">
                                                @if(isset($analyse['others']['allgreen']) || isset($analyse['others']['allred']))
                                                    {{-- <div class="analysis-icons">
                                                        <img src="{{ public_path('/heart-man/'.$analyse['male']) }}" alt="" style="height: 22px;width: auto;">
                                                        <img src="{{ public_path('/heart-man/'.$analyse['heart']) }}" alt="" style="height: 22px;width: auto;">
                                                    </div>
                                
                                                    <div class="progressbar-contianer">
                                                        <div class="progressbar" style="margin-bottom: 3px;">
                                                            <div style="background-color: #E84E1B; width: {{ round((100/$analyse['cal'])*($analyse['others']['allred'])) }}%"></div>
                                                        </div>
                                                        <div class="progressbar" style="margin-bottom: 3px;">
                                                            <div style="background-color: #2FAB66; width: {{ round((100/$analyse['cal'])*($analyse['others']['allgreen'])) }}%"></div>
                                                        </div>
                                                    </div> --}}


                                                    <div class="analysis-icons">
                                                        <img src="{{ public_path('/heart-man/'.$analyse['male']) }}" alt="" style="height: 22px;width: auto;">
                                                        <img src="{{ public_path('/heart-man/'.$analyse['heart']) }}" alt="" style="height: 22px;width: auto;">
                                                    </div>
        
                                                    <div class="progressbar-contianer">
                                                        <div class="progressbar lta-progressbar">
                                                            <div style="background-color: #E84E1B; width: {{ round((100/$analyse['cal'])*($analyse['others']['allred'])) }}%;"></div>
                                                            <div style="background-color: #2FAB66; width: {{ round((100/$analyse['cal'])*($analyse['others']['allgreen'])) }}%;"></div>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="progressbar-contianer">
                                                        <div class="analysis-icons">
                                                            <img src="{{ public_path('/heart-man/'.$analyse['male']) }}" alt="" style="height: 22px;width: auto;">
                                                            <img src="{{ public_path('/heart-man/'.$analyse['heart']) }}" alt="" style="height: 22px;width: auto;">
                                                        </div>
                                                        <div class="progressbar" style="display: inline-block;vertical-align:middle;">
                                                            <div style="background-color: {{ $analyse['color'] }}; width: {{ $analyse['cal'] }}%"></div>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                            {{-- progressbar end --}}
                                        </div>
                                        

                                        {{-- description start --}}
                                        @if(!empty($analyse['desc']))
                                            <div class="desc-contianer">
                                                <h5>{{ __('action.main_desc') }}:</h5>
                                                {{-- <p style="font-weight: bold;font-size: 14px;">Endorphins</p> --}}
                                                <p class="m-0" >{!! $analyse['desc'] !!}</p>
                                            </div>
                                        @endif

                                        @if($analyse['male_val'] <= 30)
                                            @if (!empty($analyse['body']))
                                                <div class="desc-contianer">
                                                    <h5>{{ __('action.body_desc') }}:</h5>
                                                    {{-- <p style="font-weight: bold;font-size: 14px;">Endorphins</p> --}}
                                                    <p class="m-0" >{!! $analyse['body'] !!}</p>
                                                </div>
                                            @endif
                                        @endif

                                        @if($analyse['heart_val'] >= 50)
                                            @if (!empty($analyse['mental']))
                                                <div class="desc-contianer">
                                                    <h5>{{ __('action.mental_desc') }}:</h5>
                                                    {{-- <p style="font-weight: bold;font-size: 14px;">Endorphins</p> --}}
                                                    <p class="m-0" >{!! $analyse['mental'] !!}</p>
                                                </div>
                                            @endif
                                        @endif

                                        @if($analyse['male_val'] > 30 && $analyse['heart_val'] < 50)
                                            @if (!empty($analyse['body']))
                                                <div class="desc-contianer">
                                                    <h5>{{ __('action.body_desc') }}:</h5>
                                                    {{-- <p style="font-weight: bold;font-size: 14px;">Endorphins</p> --}}
                                                    <p class="m-0" >{!! $analyse['body'] !!}</p>
                                                </div>
                                            @endif

                                            @if (!empty($analyse['mental']))
                                                <div class="desc-contianer">
                                                    <h5>{{ __('action.mental_desc') }}:</h5>
                                                    {{-- <p style="font-weight: bold;font-size: 14px;">Endorphins</p> --}}
                                                    <p class="m-0" >{!! $analyse['mental'] !!}</p>
                                                </div>
                                            @endif
                                        @endif
                                        
                                        {{-- causes, mid, tip --}}
                                        @if($analyse['cau'] != null)
                                            <div class="cuz-tip-mid">
                                                <h4>{{__('action.causes')}}:</h4>
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td style="width: 200px; vertical-align: top;">{{ $analyse['cau']->title ?? ''}}</td>
                                                            <td style="width: auto;vertical-align: top; ">{!! $analyse['cau']->description ?? '' !!}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif

                                        @if($analyse['mid'] != null)
                                            <div class="cuz-tip-mid">
                                                <h4>{{ __('action.medium') }}:</h4>
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td style="width: 200px; vertical-align: top;">{{ $analyse['mid']->title ?? ''}}</td>
                                                            <td style="width: auto;vertical-align: top; ">{!! $analyse['mid']->description ?? '' !!}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif

                                        @if($analyse['tip'] != null)
                                            <div class="cuz-tip-mid">
                                                <h4>{{__('action.tipp')}}:</h4>
                    
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td style="width: 200px; vertical-align: top;">{{ $analyse['tip']->title ?? ''}}</td>
                                                            <td style="width: auto;vertical-align: top; ">{!! $analyse['tip']->description ?? '' !!}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif

                                        @if($analyse['ein'] != null)
                                            <div class="cuz-tip-mid">
                                                <h4>{{__('action.einfluss')}}:</h4>
                    
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td style="width: 200px; vertical-align: top;">{{ $analyse['ein']->title ?? ''}}</td>
                                                            <td style="width: auto;vertical-align: top; ">{!! $analyse['ein']->description ?? '' !!}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif

                                        @if($analyse['foc'] != null)
                                            <div class="cuz-tip-mid">
                                                <h4>{{__('action.focus_pdf')}}:</h4>
                    
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td style="width: 200px; vertical-align: top;">{{ $analyse['foc']->title ?? ''}}</td>
                                                            <td style="width: auto;vertical-align: top; ">{!! $analyse['foc']->description ?? '' !!}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif
                                    </div>

                                @elseif($analyse['type'] == 4)
                                    <div class="analysis-contents p-0 py-md">
                                        <div class="analysis-row clearfix">
                                            {{-- analysis name start --}}
                                            <div class="analysis-name">
                                                <h4>{{ $analyse['cal'].'%' }} - {{ $analyse['name'] }}</h4>
                                            </div>
                                            {{-- analysis name end --}}

                                            {{-- progressbar start --}}
                                            <div class="analysis-progressbar">
                                                <div class="progressbar-contianer">
                                                    <div class="analysis-icons">
                                                        <img src="{{ public_path('/heart-man') }}/{{ $analyse['male'] }}" alt="" style="height: 22px;width: auto;">
                                                        <img src="{{ public_path('/heart-man') }}/{{ $analyse['heart'] }}" alt="" style="height: 22px;width: auto;">
                                                    </div>
                                                    <div class="progressbar" style="display: inline-block;vertical-align:middle;">
                                                        <div style="background-color: {{ $analyse['color'] }}; width: {{ $analyse['cal'] }}%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            {{-- progressbar end --}}
                                        </div>

                                        @if($analyse['ein'] != null)
                                            <div class="cuz-tip-mid">
                                                <h4>{{__('action.einfluss')}}:</h4>
                    
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td style="width: 200px; vertical-align: top;">{{ $analyse['ein']->title ?? ''}}</td>
                                                            <td style="width: auto;vertical-align: top; ">{!! $analyse['ein']->description ?? '' !!}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif
                                    </div>

                                @elseif($analyse['type'] == 5)
                                    <div class="analysis-contents p-0 py-md">
                                        <div class="analysis-row clearfix">
                                            {{-- analysis name start --}}
                                            <div class="analysis-name">
                                                <h4>{{ $analyse['cal'].'%' }} - {{ $analyse['name'] }}</h4>
                                            </div>
                                            {{-- analysis name end --}}

                                            {{-- progressbar start --}}
                                            <div class="analysis-progressbar">
                                                <div class="progressbar-contianer">
                                                    <div class="analysis-icons">
                                                        <img src="{{ public_path('/heart-man') }}/{{ $analyse['male'] }}" alt="" style="height: 22px;width: auto;">
                                                        <img src="{{ public_path('/heart-man') }}/{{ $analyse['heart'] }}" alt="" style="height: 22px;width: auto;">
                                                    </div>
                                                    <div class="progressbar" style="display: inline-block;vertical-align:middle;">
                                                        <div style="background-color: {{ $analyse['color'] }}; width: {{ $analyse['cal'] }}%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            {{-- progressbar end --}}
                                        </div>

                                        @if($analyse['foc'] != null)
                                            <div class="cuz-tip-mid">
                                                <h4>{{__('action.focus_pdf')}}:</h4>
                    
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td style="width: 200px; vertical-align: top;">{{ $analyse['foc']->title ?? ''}}</td>
                                                            <td style="width: auto;vertical-align: top; ">{!! $analyse['foc']->description ?? '' !!}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    @endif
                @endforeach
            </div>
            {{-- normal pdf content end --}}
        @endif
    </div>
@endsection