<div>
    @php
        $itemCount = count($fullResults);
        $barThickness = 15;
    @endphp
    <div id="{{$content->id}}_horizontal_chart_container" class="horizontal-chart-container" style="display: none;">
        <div class="horizontal-legend">
            @foreach($fullResults as $item)
                <div class="horizontal-legend-item" style="height: {{ $barThickness }}px;">
                    <div class="legend-label" title="{{ $item['name'] }}">{{ $item['name'] }}</div>
                    <div class="legend-buttons">
                        @if($allowInfo ?? true)
                            <span class="btn-legend btn-info"
                                  data-poolid="{{ $poolId ?? '' }}"
                                  data-analysisid="{{ $item['analysis_id'] }}"
                                  data-container="body"
                                  data-toggle="popover"
                                  data-placement="bottom"
                                  data-html="true"
                                  data-content="">
                                <i class="fas fa-info-circle"></i>
                            </span>
                        @endif
                        <div class="add-to-cart-container">
                            <livewire:dashboard.single-analyses-add-to-cart
                                    :wire:key="'chart-add-to-cart-h-' . $item['analysis_id']"
                                    :poolId="$poolId"
                                    :analysesId="$item['analysis_id']"
                            />
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        <div class="horizontal-chart-wrapper">
            <canvas id="{{$content->id}}_module_head_chart-horizontalBar" class="horizontalBar" style="max-height: 220px;"></canvas>
        </div>
    </div>
    <div id="{{$content->id}}_polar_chart_container" class="chart-container-wrapper">
        <div class="chart-container" id="{{$content->id}}_container">
            <div class="chart-wrapper">
                <canvas id="{{$content->id}}_module_head_chart" class="{{$diagramType}}" style="max-height: 220px; min-height: 150px"></canvas>
            </div>
            <div class="radial-legend">
                @foreach($fullResults as $index => $item)
                    @php
                        $itemCount = count($fullResults);
                        $angle = 0;

                        if ($itemCount > 0) {
                            $maxAngle = 130;
                            $power = 0.7;

                            if ($itemCount == 1) {
                                $angle = 0;
                            } else {
                                $normalizedPosition = ($index / ($itemCount - 1)) * 2 - 1;
                                $sign = ($normalizedPosition > 0) - ($normalizedPosition < 0);
                                $curvedPosition = pow(abs($normalizedPosition), $power);
                                $angle = $sign * $curvedPosition * $maxAngle;
                            }
                        }
                        
                        $radius = '155px';
                    @endphp
                    <div class="legend-item" style="--angle: {{ $angle }}deg; --radius: {{ $radius }};">
                        <div class="legend-label">{{ $item['name'] }}</div>
                        <div class="legend-value" style="color: {{ $item['color'] }};">{{ $item['val'] }}</div>
                        @if($allowInfo ?? true)
                            <span class="btn-legend btn-info"
                                  data-poolid="{{ $poolId ?? '' }}"
                                  data-analysisid="{{ $item['analysis_id'] }}"
                                  data-container="body"
                                  data-toggle="popover"
                                  data-placement="bottom"
                                  data-html="true"
                                  data-content="">
                                <i class="fas fa-info-circle"></i>
                            </span>
                        @endif
                        <div class="add-to-cart-container">
                            <livewire:dashboard.single-analyses-add-to-cart
                                    :wire:key="'chart-add-to-cart-' . $item['analysis_id']"
                                    :poolId="$poolId"
                                    :analysesId="$item['analysis_id']"
                            />
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    {{-- Popover içerikleri için gizli div'ler --}}
    @if($allowInfo ?? true)
        <div class="d-none">
            @foreach($fullResults as $item)
                <div id="popover-content-{{ $poolId ?? '' }}-{{ $item['analysis_id'] }}" class="hide">
                    @if(!empty($item['description']))
                        <h6>{{__('action.main_desc')}}</h6>
                        <p>{!! $item['description'] !!}</p>
                    @endif
                    @if(isset($item['desc_img']))
                        <img loading="lazy" src="{{ asset('/storage/analyse/images/description') }}/{{ $item['desc_img'] }}" class="popover-showimg" alt="" height="250px" width="auto">
                    @endif
                    <hr>
                    @if(!empty($item['bodyDesc']))
                        <h6>{{__('action.body')}}</h6>
                        <p>{!! $item['bodyDesc'] !!}</p>
                    @endif
                    <hr>    
                    @if(!empty($item['mentalDesc']))
                        <h6>{{__('action.mental')}}</h6>
                        <p>{!! $item['mentalDesc'] !!}</p>
                    @endif
                </div>
            @endforeach
        </div>
    @endif
</div>
@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
<style>
    .chart-container-wrapper {
        position: relative;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .chart-container-wrapper .add-to-cart-container {
            line-height: 1;
            margin-left: 2px;
        }

    .chart-container-wrapper .add-to-cart-container .cart-wrapper {
        transform: none !important;
        margin-left: 0 !important;
    }
    .chart-container-wrapper .chart-container {
        position: relative;
        height: 300px;
    }

    .chart-container-wrapper .chart-container .chart-wrapper {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .chart-container-wrapper .radial-legend {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
    }

    .chart-container-wrapper .legend-item {
        position: absolute;
        display: flex;
        align-items: center;
        pointer-events: auto;
        font-size: 12px;
        white-space: nowrap;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(var(--angle)) translateY(var(--radius)) rotate(calc(-1 * var(--angle)));
    }

    .chart-container-wrapper .legend-label {
        font-weight: 600;
        color: #2c3e50;
        margin-right: 6px;
        font-size: 11px;
    }

    .chart-container-wrapper .legend-value {
        font-weight: bold;
        font-size: 12px;
        text-align: center;
    }

    .chart-container-wrapper .btn-legend {
        border: none;
        cursor: pointer;
        font-weight: 500;
        margin-left: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .chart-container-wrapper .btn-legend.btn-info, .horizontal-chart-container .btn-legend.btn-info {
        background: inherit;
        color: inherit;
    }

    .chart-container-wrapper .btn-legend.btn-info:hover, .horizontal-chart-container .btn-legend.btn-info:hover {
        background-color: inherit;
        background: inherit;
        color: inherit;
    }
    
    .chart-container-wrapper .btn-info .fas.fa-info-circle, .horizontal-chart-container .btn-info .fas.fa-info-circle {
        font-size: 14px;
    }

    .horizontal-chart-container {
        display: flex;
        width: 100%;
        align-items: stretch;
    }

    .horizontal-legend {
        flex: 0 0 auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: 30px;
    }

    .horizontal-legend-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        white-space: nowrap;
        font-size: 11px;
    }
    
    .horizontal-legend-item .legend-buttons {
        display: flex;
        align-items: center;
    }

    .horizontal-legend-item .legend-label {
        color: #2c3e50;
        margin-right: 6px;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .horizontal-legend-item .add-to-cart-container {
        line-height: 1;
        margin-left: 4px;
    }
    
    .horizontal-legend-item .add-to-cart-container .cart-wrapper {
        transform: none !important;
        margin-left: 0 !important;
    }

    .horizontal-legend-item .add-to-cart-container .cart-wrapper .fa-plus-circle,
    .horizontal-legend-item .add-to-cart-container .cart-wrapper .fa-check-circle,
    .horizontal-legend-item .add-to-cart-container .cart-wrapper .fa-info-circle {
        font-size: 14px;
    }

    .horizontal-chart-wrapper {
        flex: 1 1 auto;
        min-width: 0;
    }

    @media (min-width: 1440px ) and (max-width: 1500px) {
        .horizontal-chart-container .horizontalBar {
            min-height: 160px;
            height: 160px;
        }
        
        .horizontal-chart-container .horizontal-legend {
            padding-bottom: 40px;
        }
    }

</style>
@endassets

@script
<script>
    let id = @js($content->id);
    let diagram_type = @js($diagramType);
    let filter = @js($filter);
    let results = @json($results);
    let labels = @json($labels);
    let fullResults = @json($fullResults);
    let barThickness = @js($barThickness);
    let currentChartType;

    function initializeLegendTooltips() {
        $('.legend-label[title]').each(function() {
            const element = $(this);
            const textWidth = element[0].scrollWidth;
            const containerWidth = element.width();
            
            if (textWidth > containerWidth) {
                element.tooltip({
                    placement: 'top',
                    trigger: 'hover',
                    delay: { show: 300, hide: 100 }
                });
            }
        });
    }

    function getChartType() {
        const width = window.innerWidth;

        if (width <= 450) {
            return 'horizontalBar';
        }

        if (width < 1440 && width >= 992) {
            return 'horizontalBar';
        }

        if (width <= 1660 && width >= 992) {
            if ($('html').hasClass('layout-collapsed')) {
                return 'polarArea';
            } else {
                return 'horizontalBar';
            }
        }

        return diagram_type;
    }

    function updateChartVisibility(chartType) {
        const polarAreaContainer = document.getElementById(`${id}_polar_chart_container`);
        const horizontalChartContainer = document.getElementById(`${id}_horizontal_chart_container`);

        if (!polarAreaContainer || !horizontalChartContainer) return;

        if (chartType === 'polarArea') {
            polarAreaContainer.style.display = 'block';
            horizontalChartContainer.style.display = 'none';
        } else {
            polarAreaContainer.style.display = 'none';
            horizontalChartContainer.style.display = 'flex';
        }
    }

    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    function createModuleHeadDashboardChart(id, chartType, labels, datasets, filters) {
        const commonOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                zoom: {
                    pan: { enabled: true, mode: 'xy' },
                    zoom: { enabled: true, mode: 'xy' },
                },
            },
        };

        const commonScaleOptions = {
            beginAtZero: true,
            ticks: {
                stepSize: 10,
                reverse: false,
                display: chartType === 'polarArea' ? false : true
            },
            pointLabels: {
                display: chartType === 'polarArea' ? false : true,
                centerPointLabels: true,
                font: { size: 16 }
            },
        };

        const chartOptions = {
            polarArea: {
                type: 'polarArea',
                options: { 
                    scales: {
                        r: commonScaleOptions
                    },
                    ...commonOptions
                },
            },
            horizontalBar: {
                type: 'bar',
                options: {
                    ...commonOptions,
                    indexAxis: 'y',
                    scales: {
                        y: {
                            display: false
                        },
                        x: {
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                },
            },
        };

        const config = {
            type: chartOptions[chartType].type,
            data: {
                labels: chartType === 'polarArea' ? labels.map(label => label.split(':')[0]) : labels,
                datasets: getFormattedDatasets(datasets, chartType, filters),
            },
            options: chartOptions[chartType].options,
        };

        const canvasId = (chartType === 'polarArea') 
            ? `${id}_module_head_chart`
            : `${id}_module_head_chart-horizontalBar`;

        const canvas = document.getElementById(canvasId);
        if (!canvas) return;
        const ctx = canvas.getContext('2d');

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }

        window.chartInstances = { ...(window.chartInstances || {}), [canvasId]: new Chart(ctx, config) };
    }

    function getFormattedDatasets(datasets, chartType, filters) {
        if (!filters) {
            const chartColors = getChartColors(datasets);
            const commonDataset = { data: datasets, backgroundColor: chartColors.backgroundColor };

            if (chartType === 'polarArea') {
                return [{ ...commonDataset, borderWidth: 2 }];
            }
            return [{ ...commonDataset, borderColor: chartColors.borderColor, borderWidth: 1, barThickness: barThickness }];
        }
        if (filters) {
            return datasets.map((data, index) => ({
                data: data,
                backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.2)' : 'rgba(75, 192, 192, 0.2)',
                borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                borderWidth: 1,
            }));
        }
    }

    function getChartColors(dataSet) {
        const getColor = value => {
            if (value <= 10) return ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            if (value <= 69) return ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            if (value <= 100) return ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            return ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
        };

        return dataSet.reduce((colors, value) => {
            const [bgColor, borderColor] = getColor(value);
            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }


    currentChartType = getChartType();
    updateChartVisibility(currentChartType);
    createModuleHeadDashboardChart(id, currentChartType, labels, results, filter);

    const optimizedResize = debounce(() => {
        const newChartType = getChartType();
        if (newChartType !== currentChartType) {
            currentChartType = newChartType;
            updateChartVisibility(currentChartType);
            createModuleHeadDashboardChart(id, currentChartType, labels, results, filter);
            
            setTimeout(() => {
                $('.legend-label[title]').tooltip('dispose');
                initializeLegendTooltips();
            }, 100);
        }
    }, 200);

    window.addEventListener('resize', optimizedResize);

    $(function ($) {
        $('[data-toggle="popover"]').each(function () {
            var popoverTrigger = $(this);
            var poolId = popoverTrigger.data('poolid');
            var analysisId = popoverTrigger.data('analysisid');
            var popoverContent = $('#popover-content-' + poolId + '-' + analysisId);
            popoverTrigger.popover({
                html: true,
                content: function () {
                    return popoverContent.html();
                }
            });
        });

        initializeLegendTooltips();
    });

    $('body').on('click', function (e) {
        $('[data-toggle=popover]').each(function () {
            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                $(this).popover('hide');
            }
        });
    });
</script>
@endscript