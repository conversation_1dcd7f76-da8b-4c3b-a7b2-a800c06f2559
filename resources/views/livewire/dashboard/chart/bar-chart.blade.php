<div class="bar-chart-container">
    @php
        $barThickness = 15;
    @endphp
    
    <div id="{{$content->id}}_horizontal_bar_container" class="horizontal-bar-container" style="display: none; min-height: {{ count($fullResults) > 7 ? '180px' : '160px' }};">
        <div class="horizontal-legend">
            @foreach($fullResults as $item)
                <div class="horizontal-legend-item" style="height: {{ $barThickness }}px;">
                    <div class="legend-label" title="{{ $item['name'] }}">{{ $item['name'] }}</div>
                    <div class="legend-buttons">
                        @if($allowInfo ?? true)
                            <span class="btn-legend btn-info"
                                  data-poolid="{{ $poolId ?? '' }}"
                                  data-analysisid="{{ $item['analysis_id'] }}"
                                  data-container="body"
                                  data-toggle="popover"
                                  data-placement="bottom"
                                  data-html="true"
                                  data-content="">
                                <i class="fas fa-info-circle"></i>
                            </span>
                        @endif
                        <div class="add-to-cart-container">
                            <livewire:dashboard.single-analyses-add-to-cart
                                    :wire:key="'chart-add-to-cart-bar-h-' . $item['analysis_id']"
                                    :poolId="$poolId"
                                    :analysesId="$item['analysis_id']"
                            />
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        <div class="horizontal-chart-wrapper">
            <canvas id="{{$content->id}}_module_head_chart_horizontal" class="horizontalBar" style="max-height: 180px;"></canvas>
        </div>
    </div>

    <div id="{{$content->id}}_regular_bar_container" class="regular-bar-container">
        <div class="bar-chart-wrapper">
            <canvas id="{{$content->id}}_module_head_chart" style="max-height: 400px; min-height: 300px;"></canvas>
            <div class="bar-buttons-overlay">
                @foreach($fullResults as $index => $item)
                    <div class="bar-button-item" data-bar-index="{{ $index }}">
                        @if($allowInfo ?? true) 
                            <span class="btn-legend btn-info"
                                  data-poolid="{{ $poolId ?? '' }}"
                                  data-analysisid="{{ $item['analysis_id'] }}"
                                  data-container="body"
                                  data-toggle="popover"
                                  data-placement="top"
                                  data-html="true"
                                  data-content="">
                                <i class="fas fa-info-circle"></i>
                            </span>
                        @endif
                        <div class="add-to-cart-container">
                            <livewire:dashboard.single-analyses-add-to-cart
                                    :wire:key="'chart-add-to-cart-bar-' . $item['analysis_id']"
                                    :poolId="$poolId"
                                    :analysesId="$item['analysis_id']"
                            />
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    
    {{-- Popover içerikleri için gizli div'ler --}}
    @if($allowInfo ?? true)
        <div class="d-none">
            @foreach($fullResults as $item)
                <div id="popover-content-{{ $poolId ?? '' }}-{{ $item['analysis_id'] }}" class="hide">
                    @if(!empty($item['description']))
                        <h6>{{__('action.main_desc')}}</h6>
                        <p>{!! $item['description'] !!}</p>
                    @endif
                    @if(isset($item['desc_img']))
                        <img loading="lazy" src="{{ asset('/storage/analyse/images/description') }}/{{ $item['desc_img'] }}" class="popover-showimg" alt="" height="250px" width="auto">
                    @endif
                    <hr>
                    @if(!empty($item['bodyDesc']))
                        <h6>{{__('action.body')}}</h6>
                        <p>{!! $item['bodyDesc'] !!}</p>
                    @endif
                    <hr>    
                    @if(!empty($item['mentalDesc']))
                        <h6>{{__('action.mental')}}</h6>
                        <p>{!! $item['mentalDesc'] !!}</p>
                    @endif
                </div>
            @endforeach
        </div>
    @endif
</div>

@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
<style>
    .bar-chart-container {
        width: 100%;
    }

    .regular-bar-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .bar-chart-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        justify-content: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .horizontal-bar-container {
        display: flex;
        width: 100%;
        align-items: stretch;
    }

    .horizontal-legend {
        flex: 0 0 auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: 30px;
    }

    .horizontal-legend-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        white-space: nowrap;
        font-size: 11px;
    }
    
    .horizontal-legend-item .legend-buttons {
        display: flex;
        align-items: center;
    }

    .horizontal-legend-item .legend-label {
        font-weight: 600;
        color: #2c3e50;
        margin-right: 6px;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .horizontal-legend-item .add-to-cart-container {
        line-height: 1;
        margin-left: 4px;
    }
    
    .horizontal-legend-item .add-to-cart-container .cart-wrapper {
        transform: none !important;
        margin-left: 0 !important;
    }

    .horizontal-chart-wrapper {
        flex: 1 1 auto;
        min-width: 0;
    }

    .bar-legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        font-size: 12px;
    }

    .legend-color-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .legend-content {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .legend-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 11px;
    }

    .legend-value {
        font-weight: bold;
        font-size: 12px;
        color: #666;
    }

    .legend-buttons {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .bar-chart-container .btn-legend {
        border: none;
        cursor: pointer;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        background: inherit;
        color: inherit;
    }

    .bar-chart-container .btn-legend:hover {
        background-color: inherit;
        background: inherit;
        color: inherit;
    }
    
    .bar-chart-container .btn-info .fas.fa-info-circle {
        font-size: 14px;
    }

    .bar-chart-container .add-to-cart-container {
        line-height: 1;
    }
    
    .bar-chart-container .add-to-cart-container .cart-wrapper {
        transform: none !important;
        margin-left: 0 !important;
    }

    .bar-chart-wrapper {
        width: 100%;
        position: relative;
    }

    .bar-buttons-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 10;
    }

    .bar-button-item {
        position: absolute;
        display: flex;
        align-items: center;
        gap: 2px;
        pointer-events: auto;
        transform: translateX(-50%);
    }

    .bar-button-item .fa-check-circle, .bar-button-item .fa-plus-circle {
        font-size: 14px;
    }

    @media (max-width: 768px) {
        .bar-chart-legend {
            flex-direction: column;
            align-items: stretch;
        }
        
        .bar-legend-item {
            justify-content: space-between;
        }
    }
    @media (max-width: 450px) {
        .bar-chart-container .horizontal-bar-container .horizontal-chart-wrapper .horizontalBar {
            min-height: {{ count($fullResults) > 7 ? '180px' : '160px' }};
            height: {{ count($fullResults) > 7 ? '180px' : '160px' }};
        }
    }

    @media (min-width: 992px) and (max-width: 1440px) {
        .bar-chart-container .horizontal-bar-container .horizontal-chart-wrapper .horizontalBar {
            min-height: {{ count($fullResults) > 7 ? '180px' : '160px' }};
            height: {{ count($fullResults) > 7 ? '180px' : '160px' }};
        }
    }

    @media (min-width: 1440px ) and (max-width: 1500px) {
        .46_horizontal_chart_container .horizontal-legend {
            padding-bottom: 35px;
        }
    }
</style>
@endassets

@script
<script>
    let id = @js($content->id);
    let filter = @js($filter);
    let results = @json($results);
    let labels = @json($labels);
    let fullResults = @json($fullResults);
    let barThickness = @js($barThickness);
    let currentChartType;

    function initializeLegendTooltips() {
        $('.legend-label[title]').each(function() {
            const element = $(this);
            const textWidth = element[0].scrollWidth;
            const containerWidth = element.width();
            
            if (textWidth > containerWidth) {
                element.tooltip({
                    placement: 'top',
                    trigger: 'hover',
                    delay: { show: 300, hide: 100 }
                });
            }
        });
    }

    function positionBarButtons(canvasId) {
        try {
            const chart = window.chartInstances && window.chartInstances[canvasId];
            if (!chart) return;

            const canvas = document.getElementById(canvasId);
            if (!canvas || !canvas.parentElement) return;

            const overlay = canvas.parentElement.querySelector('.bar-buttons-overlay');
            if (!overlay) return;

            const meta = chart.getDatasetMeta(0);
            if (!meta || !meta.data) return;

            const buttonItems = overlay.querySelectorAll('.bar-button-item');
            if (!buttonItems.length) return;
            
            const chartArea = chart.chartArea;
            if (!chartArea) return;
            
            const chartBottom = chartArea.bottom;
            const xScale = chart.scales && chart.scales.x;
            if (!xScale) return;
            
            const labelRotation = xScale.labelRotation || 35;
            
            buttonItems.forEach((item, index) => {
                if (meta.data[index] && item) {
                    const bar = meta.data[index];
                    const x = bar.x;
                    
                    if (typeof x === 'number' && typeof chartBottom === 'number') {
                        item.style.left = `${x + 3}px`;
                        item.style.top = `${chartBottom}px`;
                        item.style.transform = `rotate(-${labelRotation}deg)`;
                    }
                }
            });
        } catch (error) {
            console.warn('Error positioning bar buttons:', error);
        }
    }



    function getChartType() {
        const width = window.innerWidth;

        if (width <= 450) {
            return 'horizontalBar';
        }

        if (width < 1440 && width >= 992) {
            return 'horizontalBar';
        }

        if (width <= 1660 && width >= 992) {
            if ($('html').hasClass('layout-collapsed')) {
                return 'bar';
            } else {
                return 'horizontalBar';
            }
        }

        return 'bar';
    }

    function updateChartVisibility(chartType) {
        const regularBarContainer = document.getElementById(`${id}_regular_bar_container`);
        const horizontalBarContainer = document.getElementById(`${id}_horizontal_bar_container`);

        if (!regularBarContainer || !horizontalBarContainer) return;

        if (chartType === 'bar') {
            regularBarContainer.style.display = 'block';
            horizontalBarContainer.style.display = 'none';
        } else {
            regularBarContainer.style.display = 'none';
            horizontalBarContainer.style.display = 'flex';
        }
    }

    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    function createBarChart(id, chartType, labels, datasets, filters) {
        const commonOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
            },
        };

        const chartOptions = {
            bar: {
                type: 'bar',
                options: {
                    ...commonOptions,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 10,
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 0
                            }
                        }
                    }
                },
            },
            horizontalBar: {
                type: 'bar',
                options: {
                    ...commonOptions,
                    indexAxis: 'y',
                    scales: {
                        y: {
                            display: false
                        },
                        x: {
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                },
            },
        };

        const config = {
            type: chartOptions[chartType].type,
            data: {
                labels: labels,
                datasets: getFormattedDatasets(datasets, chartType, filters),
            },
            options: chartOptions[chartType].options,
        };

        const canvasId = (chartType === 'bar') 
            ? `${id}_module_head_chart`
            : `${id}_module_head_chart_horizontal`;

        const canvas = document.getElementById(canvasId);
        if (!canvas) return;
        const ctx = canvas.getContext('2d');

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }
        window.chartInstances = { ...(window.chartInstances || {}), [canvasId]: new Chart(ctx, config) };
        
        if (chartType === 'bar') {
            setTimeout(() => {
                positionBarButtons(canvasId);
                setupChartUpdateHandler(canvasId);
            }, 100);
        }
    }

    function getFormattedDatasets(datasets, chartType, filters) {
        if (filters) {
            return datasets.map((data, index) => ({
                data: data,
                backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.2)' : 'rgba(75, 192, 192, 0.2)',
                borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                borderWidth: 1,
            }));
        }

        const chartColors = getChartColors(datasets);
        const commonDataset = { 
            data: datasets, 
            backgroundColor: chartColors.backgroundColor,
            borderColor: chartColors.borderColor,
            borderWidth: 1
        };

        if (chartType === 'horizontalBar') {
            return [{ ...commonDataset, barThickness: barThickness }];
        }
        
        return [commonDataset];
    }

    function getChartColors(dataSet) {
        const getColor = value => {
            if (value <= 10) return ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            if (value <= 69) return ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            if (value <= 100) return ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            return ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
        };

        return dataSet.reduce((colors, value) => {
            const [bgColor, borderColor] = getColor(value);
            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }

    currentChartType = getChartType();
    updateChartVisibility(currentChartType);
    createBarChart(id, currentChartType, labels, results, filter);

    const optimizedResize = debounce(() => {
        const newChartType = getChartType();
        if (newChartType !== currentChartType) {
            currentChartType = newChartType;
            updateChartVisibility(currentChartType);
            createBarChart(id, currentChartType, labels, results, filter);
            
            setTimeout(() => {
                $('.legend-label[title]').tooltip('dispose');
                initializeLegendTooltips();
                
                if (currentChartType === 'bar') {
                    positionBarButtons(`${id}_module_head_chart`);
                    setupChartUpdateHandler(`${id}_module_head_chart`);
                }
            }, 100);
        }
    }, 200);

    window.addEventListener('resize', optimizedResize);
    
    const repositionButtons = debounce(() => {
        if (currentChartType === 'bar') {
            const canvas = document.getElementById(`${id}_module_head_chart`);
            if (canvas && canvas.parentElement) {
                setTimeout(() => {
                    positionBarButtons(`${id}_module_head_chart`);
                }, 150);
            }
        }
    }, 100);
    
    window.addEventListener('resize', repositionButtons);
    
    function setupChartUpdateHandler(canvasId) {
        const chart = window.chartInstances && window.chartInstances[canvasId];
        if (!chart) return;
        
        chart.options.onResize = function() {
            setTimeout(() => {
                if (currentChartType === 'bar') {
                    const canvas = document.getElementById(canvasId);
                    if (canvas && canvas.parentElement) {
                        positionBarButtons(canvasId);
                    }
                }
            }, 50);
        };
        
        chart.options.animation = {
            ...chart.options.animation,
            onComplete: function() {
                if (currentChartType === 'bar') {
                    const canvas = document.getElementById(canvasId);
                    if (canvas && canvas.parentElement) {
                        positionBarButtons(canvasId);
                    }
                }
            }
        };
    }

    $(function ($) {
        $('[data-toggle="popover"]').each(function () {
            var popoverTrigger = $(this);
            var poolId = popoverTrigger.data('poolid');
            var analysisId = popoverTrigger.data('analysisid');
            var popoverContent = $('#popover-content-' + poolId + '-' + analysisId);
            popoverTrigger.popover({
                html: true,
                content: function () {
                    return popoverContent.html();
                }
            });
        });

        initializeLegendTooltips();
    });

    $('body').on('click', function (e) {
        $('[data-toggle=popover]').each(function () {
            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                $(this).popover('hide');
            }
        });
    });
</script>
@endscript