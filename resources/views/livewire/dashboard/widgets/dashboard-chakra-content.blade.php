<div>
    @if($chakraContent->status == 1)
        <div class="dashboard-product-aria position-relative">
            <div class="card mb-0">
                <livewire:dashboard.color-analyses-add-to-cart
                    :poolId="$chakraContent->pool_id"
                    :title="$chakraContent->title"
                    :wire:key="$selectedLongDay.'dashboard-chakra-content-'.$chakraContent->id"
                >
                <div class="cus-card-body card-body chakra-body mw-100 row justify-content-between align-items-center col-md-8 mx-auto flex-row-reverse position-relative p-10">
                    <div class="text-center col-md-5 chakra-img-container">
                        <img class="cus-chakra-img img-fluid" src="{{ asset('storage/images/'.$chakraContent->image) }}" alt="Chakra Image">
                    </div>
                    <div wire:loading.class="loading-animation" class="cus-chakra-content chakra-content col-md-7 pt-0">
                        @if($moduleHeadChakraContent['diagram_type'] == 'progress')
                            @if(!$moduleHeadChakraContent['filter'])
                                <livewire:dashboard.widgets.analyses-progressbar-container
                                    :poolId="$chakraContent->pool_id"
                                    :results="$moduleHeadChakraContent['results']"
                                    :allowInfo="true"
                                    :wire:key="$selectedLongDay.'analyses-progressbar-container-'.$chakraContent->id"
                                    :right-content="false"
                                >
                            @else
                                <livewire:dashboard.widgets.long-day-analyses-progressbar-container
                                    :poolId="$chakraContent->pool_id"
                                    :results="$moduleHeadChakraContent['results']"
                                    :wire:key="$selectedLongDay.'long-day-analyses-progressbar-container-'.$chakraContent->id"
                                >
                            @endif
                        @else
                            <livewire:dashboard.widgets.analyses-chart-js-container
                                :results="$moduleHeadChakraContent"
                                :content="$chakraContent"
                                :poolId="$chakraContent->pool_id"
                                :wire:key="$selectedLongDay.'analyses-chart-js-container-'.$chakraContent->id"
                            >
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif
    <style>
            .dashboard-product-aria .dashboard-progress-container {
                flex-direction: column;
            }

            .dashboard-product-aria .dashboard-progress-container:last-child {
                margin-bottom: 0 !important;
            }

            .dashboard-product-aria .dashboard-progress-container .col-lg-3.col-md-3,
            .dashboard-product-aria .dashboard-progress-container .col-lg-9.col-md-9 {
                max-width: 100%;
            }

            .dashboard-product-aria .dashboard-progress-container .col-lg-3.col-md-3 {
                margin-bottom: 10px;
            }

            .dashboard-product-aria .dashboard-progress-container .col-lg-9.col-md-9 svg,
            .dashboard-product-aria .dashboard-progress-container .col-lg-9.col-md-9 svg rect {
                height: 25px;
            }

            .dashboard-product-aria .info-and-add-button {
                margin-right: 15px !important
            }
            
            @media only screen and (max-width: 992px) {
                .dashboard-product-aria {
                    margin-bottom: 0.5rem;
                }
            }
            
            @media only screen and (max-width: 767px) {
                .cus-chakra-img {
                    display: none;
                }
            }
        </style>
</div>


