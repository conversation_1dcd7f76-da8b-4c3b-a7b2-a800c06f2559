<div>
    @if($results['diagram_type'] === 'polarArea')
        <livewire:dashboard.chart.polar-area-chart
                :content="$content"
                :results="$results['results']"
                :labels="$results['labels']"
                :filter="$results['filter']"
                :diagramType="$results['diagram_type']"
                :fullResults="$results['full_results']"
                :poolId="$poolId"
        />
    @elseif($results['diagram_type'] === 'bar')
        <livewire:dashboard.chart.bar-chart
                :content="$content"
                :results="$results['results']"
                :labels="$results['labels']"
                :filter="$results['filter']"
                :fullResults="$results['full_results']"
                :poolId="$poolId"
        />
    @elseif($results['diagram_type'] === 'radar')
        <livewire:dashboard.chart.radar-chart
                :content="$content"
                :results="$results['results']"
                :labels="$results['labels']"
                :filter="$results['filter']"
                :diagramType="$results['diagram_type']"
                :fullResults="$results['full_results']"
                :poolId="$poolId"
        />
    @else
        <canvas id="{{$content->id}}_module_head_chart" style="max-height: 220px"></canvas>
    @endif
</div>

@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
@endassets

@script
<script>
    let id = '{{$content->id}}';
    let diagram_type = '{{$results['diagram_type']}}';
    let filter = '{{$results['filter']}}';
    let results = @json($results['results']);
    let labels = @json($results['labels']);
    let currentChartType = getChartType();

    function getChartType() {
        const width = window.innerWidth;

        if (width <= 450) {
            return 'horizontalBar';
        }

        if (width < 1440 && width >= 992) {
            return 'horizontalBar';
        }

        if (width <= 1660 && width >= 992) {
            if ($('html').hasClass('layout-collapsed')) {
                return '{{$results['diagram_type']}}';
            } else {
                return 'horizontalBar';
            }
        }

        return '{{$results['diagram_type']}}';
    }

    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    function createModuleHeadDashboardChart(id, chartType, labels, datasets, filters) {
        const commonOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false, position: 'top' },
                zoom: {
                    pan: { enabled: true, mode: 'xy' },
                    zoom: { enabled: true, mode: 'xy' },
                },
            },
        };

        const commonScaleOptions = {
            beginAtZero: true,
            ticks: { stepSize: 10, reverse: false },
            pointLabels: { display: true, centerPointLabels: true, font: { size: 16 } },
        };

        const chartOptions = {
            polarArea: {
                type: 'polarArea',
                options: { scales: { r: commonScaleOptions }, ...commonOptions },
            },
            radar: {
                type: 'radar',
                options: { scales: { r: commonScaleOptions }, ...commonOptions },
            },
            bar: {
                type: 'bar',
                options: { scales: { y: commonScaleOptions }, ...commonOptions },
            },
            horizontalBar: {
                type: 'bar',
                options: {
                    ...commonOptions,
                    indexAxis: 'y',

                },
            },
        };

        const config = {
            type: chartOptions[chartType].type,
            data: {
                labels: labels,
                datasets: getFormattedDatasets(datasets, chartType, filters),
            },
            options: chartOptions[chartType].options,
        };

        const canvasId = `${id}_module_head_chart`;
        const canvas = document.getElementById(canvasId);
        if (!canvas) return;
        const ctx = canvas.getContext('2d');

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }
        window.chartInstances = { ...(window.chartInstances || {}), [canvasId]: new Chart(ctx, config) };
    }

    function getFormattedDatasets(datasets, chartType, filters) {
        if (!filters) {
            const chartColors = getChartColors(datasets);
            return [{
                data: datasets,
                ...(chartType === 'radar' && {
                    fill: true,
                    backgroundColor: 'transparent',
                    pointBackgroundColor: chartColors.backgroundColor,
                    pointBorderColor: chartColors.borderColor,
                    pointHoverBackgroundColor: chartColors.borderColor,
                    pointHoverBorderColor: chartColors.backgroundColor,
                    pointRadius: 5,
                }),
                ...(chartType === 'polarArea' && {
                    backgroundColor: chartColors.backgroundColor,
                    borderWidth: 2
                }),
                ...(chartType === 'bar' && {
                    backgroundColor: chartColors.backgroundColor,
                    borderColor: chartColors.borderColor,
                    borderWidth: 1,
                }),
                ...(chartType === 'horizontalBar' && {
                    backgroundColor: chartColors.backgroundColor,
                    borderColor: chartColors.borderColor,
                    borderWidth: 1,
                }),
            }];
        }
        if (filters) {
            return datasets.map((data, index) => ({
                data: data,
                backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.2)' : 'rgba(75, 192, 192, 0.2)',
                borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                borderWidth: 1,
            }));
        }
    }

    function getChartColors(dataSet) {
        const getColor = value => {
            if (value <= 10) return ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            if (value <= 69) return ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            if (value <= 100) return ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            return ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
        };

        return dataSet.reduce((colors, value) => {
            const [bgColor, borderColor] = getColor(value);
            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }

    createModuleHeadDashboardChart(id, currentChartType, labels, results, filter);

    const optimizedResize = debounce(() => {
        const newChartType = getChartType();
        if (newChartType !== currentChartType) {
            currentChartType = newChartType;
            createModuleHeadDashboardChart(id, currentChartType, labels, results, filter);
        }
    }, 200);

    window.addEventListener('resize', optimizedResize);
</script>
@endscript