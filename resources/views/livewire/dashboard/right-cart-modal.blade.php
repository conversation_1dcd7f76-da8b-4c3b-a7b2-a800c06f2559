<div>
    <div class="right-panel-icon cartContent"  id="">
        <p class="d-inline-block" data-toggle="modal" data-target="#right-panel-slide">
            <i class="fas fa-cart-plus"></i>
            <span class="badge badge-danger indicator" id="cardValue">{{ count($cartData) }}</span>
        </p>
    </div>

    <div class="modal modal-slide fade" id="right-panel-slide" tabindex="-1" role="dialog" aria-labelledby="rightShoppingModalLabel" aria-hidden="true"    wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                {{-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button> --}}
                <div class="modal-body">
                    <div class="right-sidepanel padding-10">
                        <div class="right-sidepanel-header">
                            <div>
                                <p class="font-weight-bold mb-3">{{__('action.topictobecoverd')}}</p>
                            </div>
                            <div class="d-block text-right mb-4">
                                <span class="d-flex align-items-center float-left badge badge-pill badge-warning cart-total-time mt-1">
                                    <i class="fas fa-clock f-18 text-light pr-1"></i>
                                    <span class="editable-time-display">
                                        @php
                                            $timeParts = explode(':', $this->totalTime);
                                            $hours = $timeParts[0] ?? '00';
                                            $minutes = $timeParts[1] ?? '00';
                                            $seconds = $timeParts[2] ?? '00';
                                        @endphp
                                        <span class="time-part editable-hour" data-toggle="tooltip" data-placement="bottom" title="{{__('action.edit_hour')}}" onclick="makeEditableRight(this, 'hour', '{{ $hours }}')">{{ $hours }}</span>:
                                        <span class="time-part editable-minute" data-toggle="tooltip" data-placement="bottom" title="{{__('action.edit_minute')}}" onclick="makeEditableRight(this, 'minute', '{{ $minutes }}')">{{ $minutes }}</span>:
                                        <span class="time-part editable-second" data-toggle="tooltip" data-placement="bottom" title="{{__('action.edit_second')}}" onclick="makeEditableRight(this, 'second', '{{ $seconds }}')">{{ $seconds }}</span>
                                    </span>
                                </span>

                                <label class="switcher switcher-success switcher-sm mr-1" style="margin-right: 0px; min-height: 1.125rem !important">
                                    <input type="checkbox" class="switcher-input reaction-change" id="reaction-change" onclick="changeRAstatus('{{$useroption->ra_status}}', event)" {{$useroption->ra_status == false ? "checked" : ""}}>
                                    <span class="switcher-indicator">
                                    <span class="switcher-yes" data-toggle="tooltip" data-placement="bottom" title="{{__('action.show_reaction')}}">
                                        <span class="ion ion-md-checkmark"></span>
                                    </span>
                                    <span class="switcher-no" data-toggle="tooltip" data-placement="bottom" title="{{__('action.hide_reaction')}}">
                                        <span class="ion ion-md-close"></span>
                                    </span>
                                </span>
                                    {{--<span class="switcher-label">{{__('action.show_ra')}}</span>--}}
                                </label>

                                <label class="switcher switcher-success switcher-sm ra-pdf-check hide" style="margin-right: 0; min-height: 1.125rem !important">
                                    <input type="checkbox" class="switcher-input reaction-pdf"  onclick="changeRApdf('{{$useroption->ra_pdf_status}}', event)" {{$useroption->ra_pdf_status == false ? "checked" : ""}}>
                                    <span class="switcher-indicator">
                                    <span class="switcher-yes" data-toggle="tooltip" data-placement="bottom" title="{{__('action.show_pdf')}}">
                                        <span class="ion ion-md-checkmark"></span>
                                    </span>
                                    <span class="switcher-no" data-toggle="tooltip" data-placement="bottom" title="{{__('action.hide_pdf')}}">
                                        <span class="ion ion-md-close"></span>
                                    </span>
                                </span>
                                    {{--<span class="switcher-label">{{__('action.show_ra')}}</span>--}}
                                </label>

                                @if(Auth::user()->user_type == 2)
                                    <a href="javascript:void(0)" class="d-inline-block ml-2 cursor-pointer" data-toggle="modal" data-target="#pdfGenerateModal">
                                        <span class="d-inline-block" data-toggle="tooltip" data-placement="bottom" title="{{__('action.pdf_title')}}" ><i class="fas fa-file-pdf f-18"></i></span>
                                    </a>
                                @endif
                                <a href="javascript:void(0)" class="d-inline-block ml-2 cursor-pointer" data-toggle="modal" data-target="#topicModal">
                                <span class="d-inline-block">
                                    <i class="fas fa-edit f-18" data-toggle="tooltip" data-placement="bottom" title="{{__('action.manage_topic')}}"></i>
                                </span>
                                </a>
                                <a href="javascript:void(0)" class="d-inline-block  ml-2 cursor-pointer" onclick="openCartModal()">
                                    {{-- <a href="javascript:void(0)" class="d-inline-block  ml-2 cursor-pointer" data-toggle="modal" data-target="#openCartModal"> --}}
                                    <span class="d-inline-block  cursor-pointer" data-toggle="tooltip" data-placement="bottom" title="{{__('action.open_basket')}}">
                                    <i class="ion ion-md-open d-block f-18"></i>
                                </span>
                                </a>
                                <a href="javascript:void(0)" class="d-inline-block ml-2 cursor-pointer" data-toggle="modal" data-target="#saveCartModal">
                                <span class="d-inline-block" data-toggle="tooltip" data-placement="bottom" title="{{__('action.list_cart')}}" >
                                    <i class="ion ion-md-download f-18" ></i>
                                </span>
                                </a>
                                <a href="javascript:void(0)" class="d-inline-block ml-2 cursor-pointer" onclick="clearCart()">
                                    <span class="d-inline-block mr-2" ><i class="far fa-times-circle f-18" data-toggle="tooltip" data-placement="bottom" title="{{__('action.clear')}}"></i></span>
                                </a>

                                {{-- Topic Modal --}}
                                <div class="modal modal-top fade" id="topicModal" tabindex="-1" role="dialog"
                                     aria-labelledby="myModalLabel">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content own-topic-text-div">
                                            <div class="modal-header">
                                                <h4 class="modal-title" id="myModalLabel">{{ __('action.topic_name') }}</h4>
                                                <button type="button" class="close" data-dismiss="modal"
                                                        aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                            </div>
                                            <div class="modal-body">
                                            <textarea name="" class="form-control" id="own_topic" cols="" rows="5"
                                                      placeholder="{{ __('action.cron_note_placeholder') }}">{{ getUserDetails()->thema_speichern }}</textarea>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-primary icon" onclick="btnAddCart(this)" id="add_to_cart" >{{ __('action.cart') }}</button>
                                                <button type="button" class="btn btn-success icon" id="save_topic" onclick="topicSave(this,'Save')">{{ __('action.save') }}</button>
                                                <button type="button" class="btn btn-default icon" id="delete_topic" onclick="topicSave('Delete')">{{ __('action.delete') }}</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="right-sidepanel-body " id="">
                            <ul class="treatment-list">
                                @foreach ($cartData as $key => $cartValue)
                                    <li class="rem_{{$cartValue['rowId']}}" wire:key="item-{{ $cartValue['rowId'] }}-{{ now() }}" id="__cartList" data-anaid="{{ $cartValue['options']['analysisName'] }}">
                                        <p data-toggle="tooltip" title="{{ $cartValue['options']['analysisName']  }}">
                                            @php
                                                $seconds = $cartValue['options']['price'];
                                                $hours = floor($seconds / 3600);
                                                $minutes = floor(($seconds % 3600) / 60);
                                                $remainingSeconds = $seconds % 60;
                                                
                                                if ($hours > 0) {
                                                    $timeDisplay = sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
                                                } else {
                                                    $timeDisplay = sprintf('%02d:%02d', $minutes, $remainingSeconds);
                                                }
                                            @endphp
                                            {{ $timeDisplay }} &nbsp;&nbsp;&nbsp;{{ $cartValue['options']['analysisName'] }}
                                        </p>

                                        <div class="treatment-list-icon">
                                            @php
                                                $type_id = '';
                                                if($cartValue['options']['type'] == 'Analysis'){
                                                    $type_id = $cartValue['options']['analysisID'];
                                                }elseif($cartValue['options']['type'] == 'Causes'){
                                                    $type_id = $cartValue['options']['causes_id'];
                                                }elseif($cartValue['options']['type'] == 'Medium'){
                                                    $type_id = $cartValue['options']['medium_id'];
                                                }elseif($cartValue['options']['type'] == 'Tipp'){
                                                    $type_id = $cartValue['options']['tipp_id'];
                                                }

                                            @endphp
                                            <a href="javascript:void(0)" onclick="removeCid('{{ $cartValue['rowId'] }}','{{ $cartValue['options']['type'] }}',{{ $type_id }})">
                                                <i class="fas fa-times-circle danger-color"></i>
                                            </a>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        <div class="session-info">
                            <livewire:treatment.session-id
                                    wire:key="session-id-{{ $auth->id }}-{{ now() }}"
                            >
                        </div>
                        @if (Gate::allows('checkAccess','checkCron'))
                            <div class="model-footer-button-one mb-1">
                                <a class="btn btn-info w-100 checkcarthavedata_rerender_href" id="checkcarthavedata" href="{{ route('cron.remote-analysis',["remote"]) }}">{{ __('action.remote_analysis') }}</a>
                            </div>
                        @endif
                        <div class="right-sidepanel-footer mb-1">
                            <a href="{{ route('treat.treat') }}" id="checkcarthavedata" class="btn btn-outline-success">{{ __('action.behandein') }}</a>
                            <a href="{{ route('treat.dur_power') }}" id="checkcarthavedata" class="btn btn-success">{{ __('action.duration_power') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .editable-time-display {
            position: relative;
        }
        
        .time-part {
            font-weight: bold;
        }
        
        .editable-hour, .editable-minute, .editable-second {
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.2s ease;
            position: relative;
        }

        .editable-hour:hover, .editable-minute:hover, .editable-second:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .editable-hour:hover::after, .editable-minute:hover::after, .editable-second:hover::after {
            content: "\f044";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 0.7em;
            color: #fff;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .time-inline-input {
            background: #fff;
            border: 2px solid #007bff;
            border-radius: 4px;
            color: #333;
            font-size: inherit;
            font-weight: bold;
            text-align: center;
            width: 50px;
            height: 20px;
            padding: 2px;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }

        .time-inline-input:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 8px rgba(0, 123, 255, 0.5);
        }
    </style>

    <script>
        var isFirstRenderRightCart = true;
        var currentHourValueRight = 0;

        // Basit kopya: aynı fonksiyonlar ama "Right" suffix'i ile
        function makeEditableRight(element, type, currentValue) {
            if (element.querySelector('input')) {
                return;
            }
            
            const originalText = element.textContent;
            
            let defaultValue, minValue;
            if (type === 'minute') {
                const hourElement = document.querySelector('.editable-hour');
                const currentHour = hourElement ? parseInt(hourElement.textContent) : 0;
                minValue = currentHour > 0 ? 0 : 1;
                defaultValue = parseInt(currentValue) || minValue;
            } else {
                minValue = 0;
                defaultValue = parseInt(currentValue) || 0;
            }
            
            const value = defaultValue;
            
            let maxValue;
            if (type === 'hour') {
                const cartItems = document.querySelectorAll('.treatment-list li').length;
                maxValue = Math.max(1, cartItems);
            } else {
                maxValue = type === 'minute' ? 59 : 59;
            }

            const input = document.createElement('input');
            input.type = 'number';
            input.value = value;
            input.min = minValue;
            input.max = maxValue;
            input.className = 'time-inline-input';
            
            element.innerHTML = '';
            element.appendChild(input);

            setTimeout(() => {
                input.focus();
                input.select();
            }, 10);
            
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveInlineEditRight(element, input, type, originalText);
                }
                if (e.key === 'Escape') {
                    cancelInlineEditRight(element, originalText);
                }
            });
            
            input.addEventListener('blur', function() {
                setTimeout(() => {
                    if (document.activeElement !== input) {
                        saveInlineEditRight(element, input, type, originalText);
                    }
                }, 100);
            });
        }

        function saveInlineEditRight(element, input, type, originalText) {
            let value = parseInt(input.value) || 0;
            
            if (type === 'hour') {
                const cartItems = document.querySelectorAll('.treatment-list li').length;
                const maxHour = Math.max(1, cartItems);
                
                if (value < 0) {
                    value = 0;
                } else if (value > maxHour) {
                    value = maxHour;
                }
            } else if (type === 'minute') {
                const hourElement = document.querySelector('.editable-hour');
                const currentHour = hourElement ? parseInt(hourElement.textContent) : 0;
                const cartItems = document.querySelectorAll('.treatment-list li').length;
                const maxHour = Math.max(1, cartItems);
                
                if (currentHour >= maxHour) {
                    value = 0;
                } else {
                    const minMinute = currentHour > 0 ? 0 : 1;
                    if (value < minMinute) {
                        value = minMinute;
                    }
                }
            } else if (type === 'second') {
                const hourElement = document.querySelector('.editable-hour');
                const currentHour = hourElement ? parseInt(hourElement.textContent) : 0;
                const cartItems = document.querySelectorAll('.treatment-list li').length;
                const maxHour = Math.max(1, cartItems);
                
                if (currentHour >= maxHour) {
                    value = 0;
                } else if (value < 0) {
                    value = 0;
                }
            }
            
            const formattedValue = String(value).padStart(2, '0');
            element.textContent = formattedValue;
            
            const hourElement = document.querySelector('.editable-hour');
            const minuteElement = document.querySelector('.editable-minute');
            const secondElement = document.querySelector('.editable-second');
            
            if (hourElement.textContent === '00' && minuteElement.textContent === '00' && secondElement.textContent === '00') {
                minuteElement.textContent = '10';
                updateTimeValue('hour', 0);
                updateTimeValue('minute', 10);
                updateTimeValue('second', 0);
            } else {
                updateTimeValueRight(type, value);
            }
        }

        function cancelInlineEditRight(element, originalText) {
            element.textContent = originalText;
        }

        async function updateTimeValueRight(type, value) {
            if (type === 'hour') {
                currentHourValueRight = value;
                
                const cartItems = document.querySelectorAll('.treatment-list li').length;
                const maxHour = Math.max(1, cartItems);
                
                if (value >= maxHour) {
                    const minuteElement = document.querySelector('.editable-minute');
                    const secondElement = document.querySelector('.editable-second');
                    
                    if (minuteElement) minuteElement.textContent = '00';
                    if (secondElement) secondElement.textContent = '00';
                }
            }
            
            if (type === 'hour') {
                Livewire.dispatch('updateMaxHourRight', { value: value });
            } else if (type === 'minute') {
                Livewire.dispatch('updateMaxMinuteRight', { value: value });
            } else if (type === 'second') {
                Livewire.dispatch('updateMaxSecondRight', { value: value });
            }

            await new Promise((resolve) => {
                Livewire.dispatch('applyMaxTimeRightCart');
                
                setTimeout(() => {
                    resolve();
                }, 400);
            });

            if (isFirstRenderRightCart) {
                isFirstRenderRightCart = false;
                Livewire.dispatch('toggleRightCartModal');

                $('[id^="right-panel-slide"]').modal('hide');

                $(document).one('hidden.bs.modal', function () {
                    $('#right-panel-slide').modal('show');
                });
            }
        }

        $(document).ajaxSuccess(function(event, xhr, settings) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response?.cart_updated === true) {
                    Livewire.dispatch('refreshComponent');
                }
            } catch(e) {
                
            }
        });
    </script>
    
</div>


    