<div class="nav-item mx-1 cartContent" id="shoppingCart">
    <a class="nav-link" href="#" data-toggle="modal" data-target="#shopping-modal-{{ $auth->id }}" style="@if(count($cartData) == 0) display: none; @endif">
        <span class="d-inline-block" data-toggle="tooltip" data-placement="bottom" data-state="secondary" title="{{trans('action.showbasket')}}">
            <i class="ion ion-md-cart"></i>
        </span>
    </a>
    <div class="modal modal-top fade" id="shopping-modal-{{ $auth->id }}" tabindex="-1" role="dialog" aria-labelledby="shoppingModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        {{trans('action.open_basket')}}
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button>
                </div>
                <div class="modal-body">
                    <div class="d-block text-right mb-4">
                        <span class="d-flex align-items-center float-left badge badge-pill badge-warning cart-total-time ttcountbadge">
                            <i class="fas fa-clock f-18 text-light pr-1"></i>
                            <span class="editable-time-display">
                                @php
                                    $timeParts = explode(':', $this->totalTime);
                                    $hours = $timeParts[0] ?? '00';
                                    $minutes = $timeParts[1] ?? '00';
                                    $seconds = $timeParts[2] ?? '00';
                                @endphp
                                <span class="time-part editable-hour" data-toggle="tooltip" data-placement="bottom" title="{{__('action.edit_hour')}}" onclick="makeEditable(this, 'hour', '{{ $hours }}')">{{ $hours }}</span>:
                                <span class="time-part editable-minute" data-toggle="tooltip" data-placement="bottom" title="{{__('action.edit_minute')}}" onclick="makeEditable(this, 'minute', '{{ $minutes }}')">{{ $minutes }}</span>:
                                <span class="time-part editable-second" data-toggle="tooltip" data-placement="bottom" title="{{__('action.edit_second')}}" onclick="makeEditable(this, 'second', '{{ $seconds }}')">{{ $seconds }}</span>
                            </span>
                        </span>

                        {{-- /* Ra PDF Show/Hide */ --}}
                        <label class="switcher switcher-success switcher-sm ra-pdf-check {{$useroption->ra_status == false ? "" : "hide"}}">
                            <input type="checkbox" class="switcher-input reaction-pdf"
                                   onclick="changeRApdf({{$useroption->ra_pdf_status}},event)"
                                   title="{{trans('action.show_ra')}}" {{$useroption->ra_pdf_status == false ? "checked" : ""}}>

                            <span class="switcher-indicator">
                                <span class="switcher-yes" data-toggle="tooltip"
                                      data-placement="bottom" title="{{trans('action.show_pdf')}}">
                                    <span class="ion ion-md-checkmark"></span>
                                </span>
                                <span class="switcher-no" data-toggle="tooltip"
                                      data-placement="bottom" title="{{trans('action.hide_pdf')}}">
                                    <span class="ion ion-md-close"></span>
                                </span>
                            </span>
                        </label>
                        <label class="switcher switcher-success switcher-sm ">
                            <input type="checkbox" class="switcher-input reaction-change"
                                   onclick="changeRAstatus({{$useroption->ra_status}},event)"
                                   title="{{trans('action.show_ra')}}" {{$useroption->ra_status == false ? "checked" : ""}}>

                            <span class="switcher-indicator">
                                                    <span class="switcher-yes" data-toggle="tooltip"
                                                          data-placement="bottom"
                                                          title="{{trans('action.show_reaction')}}">
                                                        <span class="ion ion-md-checkmark"></span>
                                                    </span>
                                                    <span class="switcher-no" data-toggle="tooltip"
                                                          data-placement="bottom"
                                                          title="{{trans('action.hide_reaction')}}">
                                                        <span class="ion ion-md-close"></span>
                                                    </span>
                                                </span>
                        </label>
                        <a href="javascript:void(0)" class="d-inline-block mr-2 cursor-pointer" data-toggle="modal"
                           data-target="#topicModal">
                                                <span class="d-inline-block">
                                                    <i class="fas fa-edit f-18 text-success" data-toggle="tooltip"
                                                       data-placement="bottom"
                                                       title="{{trans('action.manage_topic')}}"></i>
                                                </span>
                        </a>
                        @if(Auth::user()->user_type == 2)
                            <a href="javascript:void(0)" class="d-inline-block mr-2 cursor-pointer" data-toggle="modal"
                               data-target="#pdfGenerateModal">
                                                    <span class="d-inline-block">
                                                        <i class="fas fa-file-pdf f-18 text-success"
                                                           data-toggle="tooltip" data-placement="bottom"
                                                           title="{{trans('action.pdf_title')}}"></i>
                                                    </span>
                            </a>
                        @endif

                        <a href="javascript:void(0)" class="d-inline-block mr-2 cursor-pointer"
                           onclick="openCartModal()">
                            <span class="d-inline-block">
                                                    <i class="text-success ion ion-md-open d-block f-18"
                                                       data-toggle="tooltip" data-placement="bottom"
                                                       title="{{trans('action.open_basket')}}"></i>
                                                </span>
                        </a>
                        <a href="javascript:void(0)" class="d-inline-block mr-2 cursor-pointer" data-toggle="modal"
                           data-target="#saveCartModal">
                                                <span class="d-inline-block">
                                                    <i class="text-success ion ion-md-download f-18"
                                                       data-toggle="tooltip" data-placement="bottom"
                                                       title="{{trans('action.list_cart')}}"></i>
                                                </span>
                        </a>
                        <a href="javascript:void(0)" class="d-inline-block mr-2 cursor-pointer" data-toggle="tooltip"
                           data-placement="bottom" title="{{trans('action.clear')}}" onclick="clearCart()">
                            <span class="d-inline-block"><i class="text-success far fa-times-circle f-18"></i></span>
                        </a>
                    </div>
                    <ul class="treatment-list --cart-datas">
                        @foreach ($cartData as $key => $cartValue)
                            <li class="rem_{{ $cartValue['rowId'] }}" wire:key="item-{{ $cartValue['rowId'] }}-{{ now() }}">
                                <p data-toggle="tooltip"
                                   title="{{ $cartValue['options']['analysisName'] }}"
                                   data-cartprice="{{ $cartValue['options']['price'] }}">
                                    @php
                                        $seconds = $cartValue['options']['price'];
                                        $hours = floor($seconds / 3600);
                                        $minutes = floor(($seconds % 3600) / 60);
                                        $remainingSeconds = $seconds % 60;
                                        
                                        if ($hours > 0) {
                                            $timeDisplay = sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
                                        } else {
                                            $timeDisplay = sprintf('%02d:%02d', $minutes, $remainingSeconds);
                                        }
                                    @endphp
                                    {{ $timeDisplay }}
                                    &nbsp;&nbsp;&nbsp;{{ $cartValue['options']['analysisName'] }}
                                </p>

                                <div class="treatment-list-icon">
                                    @php
                                        $type_id = '';
                                        if($cartValue['options']['type'] == 'Analysis'){
                                            $type_id = $cartValue['options']['analysisID'];
                                        }elseif($cartValue['options']['type'] == 'Causes'){
                                            $type_id = $cartValue['options']['causes_id'];
                                        }elseif($cartValue['options']['type'] == 'Medium'){
                                            $type_id = $cartValue['options']['medium_id'];
                                        }elseif($cartValue['options']['type'] == 'Tipp'){
                                            $type_id = $cartValue['options']['tipp_id'];
                                        }elseif($cartValue['options']['type'] == 'Einfluss'){
                                            $type_id = $cartValue['options']['causes_id'];
                                        }
                                    @endphp
                                    <a href="javascript:void(0)"
                                       onclick="removeCid('{{ $cartValue['rowId'] }}', '{{ $cartValue['options']['type'] }}',{{ $type_id }})">
                                        <i class="fas fa-times-circle danger-color"></i>
                                    </a>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="modal-footer">
                    <div class="navCartSessionId-box">
                        <livewire:treatment.session-id
                                wire:key="session-id-{{ $auth->id }}-{{ now() }}"
                        >
                    </div>
                    @if (Gate::allows('checkAccess','checkCron') && Request::segment(3) != 'remote')
                        <div class="model-footer-button-one mb-1">
                            <a class="btn btn-info w-100 checkcarthavedata_rerender_href" id="checkcarthavedata"
                               href="{{ route('cron.remote-analysis',["remote"]) }}">{{ trans('action.remote_analysis') }}</a>
                        </div>
                    @endif
                    <div class="right-sidepanel-footer mb-1">
                        <a @if($userCanTreat) href="{{ route('treat.treat') }}"
                           @else href="javascript:void(0)" onclick="paymentWarning()" @endif  id="checkcarthavedata"
                           class="btn btn-outline-success">{{ trans('action.behandein') }}</a>
                        <a @if($userCanTreat) href="{{ route('treat.dur_power') }}"
                           @else href="javascript:void(0)" onclick="paymentWarning()" @endif  id="checkcarthavedata"
                           class="btn btn-success">{{ trans('action.duration_power') }}</a>
                    </div>
                    <a @if($userCanTreat) href="{{ route('treat.enfitgpt') }}"
                       @else href="javascript:void(0)" onclick="paymentWarning()" @endif  id="checkcarthavedata"
                       class="btn btn-outline-success w-100">
                        <i class="fas fa-robot mr-2"></i>
                        <span>EnfitGPT</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
    div[aria-labelledby="shoppingModalLabel"] {
        .modal-footer {
            display: block;
        }
    }
    
    .editable-time-display {
        position: relative;
    }
    
    .time-part {
        font-weight: bold;
    }
    
        .editable-hour, .editable-minute, .editable-second {
        cursor: pointer;
        padding: 2px 4px;
        border-radius: 3px;
        transition: all 0.2s ease;
        position: relative;
    }

    .editable-hour:hover, .editable-minute:hover, .editable-second:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }

    .editable-hour:hover::after, .editable-minute:hover::after, .editable-second:hover::after {
        content: "\f044";
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        position: absolute;
        top: -8px;
        right: -8px;
        font-size: 0.7em;
        color: #fff;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .time-inline-input {
        background: #fff;
        border: 2px solid #007bff;
        border-radius: 4px;
        color: #333;
        font-size: inherit;
        font-weight: bold;
        text-align: center;
        width: 50px;
        height: 20px;
        padding: 2px;
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
    }

    .time-inline-input:focus {
        outline: none;
        border-color: #0056b3;
        box-shadow: 0 0 8px rgba(0, 123, 255, 0.5);
    }
    
    </style>
    
    <script>
        
        function makeEditable(element, type, currentValue) {
            if (element.querySelector('input')) {
                return;
            }
            
            const originalText = element.textContent;
            
            let defaultValue, minValue;
            if (type === 'minute') {
                const hourElement = document.querySelector('.editable-hour');
                const currentHour = hourElement ? parseInt(hourElement.textContent) : 0;
                minValue = currentHour > 0 ? 0 : 1;
                defaultValue = parseInt(currentValue) || minValue;
            } else {
                minValue = 0;
                defaultValue = parseInt(currentValue) || 0;
            }
            
            const value = defaultValue;
            
            let maxValue;
            if (type === 'hour') {
                const cartItems = document.querySelectorAll('.treatment-list.--cart-datas li').length;
                maxValue = Math.max(1, cartItems);
            } else {
                maxValue = type === 'minute' ? 59 : 59;
            }

            const input = document.createElement('input');
            input.type = 'number';
            input.value = value;
            input.min = minValue;
            input.max = maxValue;
            input.className = 'time-inline-input';
            
            element.innerHTML = '';
            element.appendChild(input);

            setTimeout(() => {
                input.focus();
                input.select();
            }, 10);
            
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveInlineEdit(element, input, type, originalText);
                }
                if (e.key === 'Escape') {
                    cancelInlineEdit(element, originalText);
                }
            });
            
            input.addEventListener('blur', function() {
                setTimeout(() => {
                    if (document.activeElement !== input) {
                        saveInlineEdit(element, input, type, originalText);
                    }
                }, 100);
            });
        }

        function saveInlineEdit(element, input, type, originalText) {
            let value = parseInt(input.value) || 0;
            
            if (type === 'hour') {
                const cartItems = document.querySelectorAll('.treatment-list.--cart-datas li').length;
                const maxHour = Math.max(1, cartItems);
                
                if (value < 0) {
                    value = 0;
                } else if (value > maxHour) {
                    value = maxHour;
                }
            } else if (type === 'minute') {
                const hourElement = document.querySelector('.editable-hour');
                const currentHour = hourElement ? parseInt(hourElement.textContent) : 0;
                const cartItems = document.querySelectorAll('.treatment-list.--cart-datas li').length;
                const maxHour = Math.max(1, cartItems);
                
                if (currentHour >= maxHour) {
                    value = 0;
                } else {
                    const minMinute = currentHour > 0 ? 0 : 1;
                    if (value < minMinute) {
                        value = minMinute;
                    }
                }
            } else if (type === 'second') {
                const hourElement = document.querySelector('.editable-hour');
                const currentHour = hourElement ? parseInt(hourElement.textContent) : 0;
                const cartItems = document.querySelectorAll('.treatment-list.--cart-datas li').length;
                const maxHour = Math.max(1, cartItems);
                
                if (currentHour >= maxHour) {
                    value = 0;
                } else if (value < 0) {
                    value = 0;
                }
            }
            
            const formattedValue = String(value).padStart(2, '0');
            element.textContent = formattedValue;
            
            const hourElement = document.querySelector('.editable-hour');
            const minuteElement = document.querySelector('.editable-minute');
            const secondElement = document.querySelector('.editable-second');
            
            if (hourElement.textContent === '00' && minuteElement.textContent === '00' && secondElement.textContent === '00') {
                minuteElement.textContent = '10';
                updateTimeValue('hour', 0);
                updateTimeValue('minute', 10);
                updateTimeValue('second', 0);
            } else {
                updateTimeValue(type, value);
            }
        }

        function cancelInlineEdit(element, originalText) {
            element.textContent = originalText;
        }
        
        var isFirstRenderCartModal = true;
        var currentHourValue = 0;

        async function updateTimeValue(type, value) {
            if (type === 'hour') {
                currentHourValue = value;
                
                const cartItems = document.querySelectorAll('.treatment-list.--cart-datas li').length;
                const maxHour = Math.max(1, cartItems);
                
                if (value >= maxHour) {
                    const minuteElement = document.querySelector('.editable-minute');
                    const secondElement = document.querySelector('.editable-second');
                    
                    if (minuteElement) minuteElement.textContent = '00';
                    if (secondElement) secondElement.textContent = '00';
                }
            }
            
            if (type === 'hour') {
                Livewire.dispatch('updateMaxHour', { value: value });
            } else if (type === 'minute') {
                Livewire.dispatch('updateMaxMinute', { value: value });
            } else if (type === 'second') {
                Livewire.dispatch('updateMaxSecond', { value: value });
            }

            await new Promise((resolve) => {
                Livewire.dispatch('applyMaxTimeCartModal');
                
                setTimeout(() => {
                    resolve();
                }, 400);
            });

            if (isFirstRenderCartModal) {
                isFirstRenderCartModal = false;
                Livewire.dispatch('toggleCartModal');

                $('[id^="shopping-modal-"]').modal('hide');

                $(document).one('hidden.bs.modal', function () {
                    $('#shopping-modal-{{ $auth->id }}').modal('show');
                });
            }
        }

        $(document).ajaxSuccess(function(event, xhr, settings) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response?.cart_updated === true) {
                    Livewire.dispatch('refreshComponent');
                }
            } catch(e) {
                
            }
        });
    </script>    

</div>