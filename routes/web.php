<?php

use App\Http\Controllers\Frontend\CalculationController;
use App\Http\Controllers\Frontend\CronController;
use App\Http\Controllers\Frontend\DashboardController;
use App\Http\Controllers\Frontend\TreatController;
use App\Http\Controllers\Frontend\UsersController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/view_pdf/{type}/{id}', 'PdfViewController@viewPdf');
Route::get('/checkProducts/{proid}/{subid}', 'Frontend\DashboardController@productAnalyses')->name('checkpro');

Route::get('/new_register', 'Frontend\UsersController@register_user')->name('register')->middleware('setLanguage');
Route::get('/getTranslation', 'HomeController@translation')->name('language/translation')->middleware('setLanguage');

// Route::get('/', ['middleware' => ['setLanguage'], function () {
//     if (Auth::check()) return redirect()->route('dashboard.dashboard');
//     else return view('auth.login');
// }]);

Route::post('/cache/activity', function () {
    // Set the desired locale
    $expiresAt = Carbon\Carbon::now()->addMinutes(2);
    cache()->put('user-is-online-' . request()->userid, true, $expiresAt);

    $userOnlineCount = cache()->get('user-online-count', []);
    if (!in_array(request()->userid, $userOnlineCount)) {
        $userOnlineCount[] = request()->userid;
        cache()->put('user-online-count', $userOnlineCount, now()->addHours(2));
    }

})->name('cache-activity');

// routes/web.php or your custom route file
Route::get('/langfile/{langcode}/{text}', function () {
    // Set the desired locale
    setServerLocal(request()->langcode);
    $langData = trans('action.'.request()->text); // Access the language file data
    return response()->json($langData);
});

Route::permanentRedirect('/', '/login')->middleware('setLanguage');

Route::get('/chclr', 'HomeController@cacheClear')->name('cache_clear');
Route::get('/run/{code}', 'HomeController@script')->name('script');
Route::get('/testx/{code}', 'HomeController@testScript')->name('testx')->middleware('setLanguage');

Route::post('/langchange', "Ajax\SajaxController@changeLanguage")->name('langchange');
Route::post('/gdpr_acceptence', 'Frontend\UsersController@gdpr_acceptence')->name('gdpr_acceptence')->middleware('setLanguage');
Route::get('/user_gdpr', 'Frontend\UsersController@get_gdpr')->name('user_gdpr')->middleware('setLanguage');
Route::post('/requiredFilds', 'Frontend\UsersController@updateRequiredFilds')->name('requiredFilds')->middleware('setLanguage');
#delete old session
Route::get('delete_session/{id}', 'Ajax\MajaxController@delete_session')->name('delete.session');
Route::post('store-client-display-size',function(){
    $width = request()->width;
    $height = request()->height;
    session()->put('client_display_size',['screenWidth' => $width, 'screenHeight' => $height]);
})->name('store-client-display-size');

Route::get('cron/open_treat/{cronid}/{crontimeid}/{unique}', 'Frontend\CronController@openTreatmentByEmail')->name('cron.openTreat');
Route::post('cron/open_treat/submit-feedback', 'Frontend\CronController@saveBadfeedback')->name('cron.saveBadfeedback');

Route::get('cron/open_treat_anytime/{cronid}/{crontimeid}/{unique}',[CronController::class, 'openTreatmentAnyTime'])->name('cron.anyTimeOpenTreat');
#cron optimized route
Route::post('cron/optimized/{userid}', 'Frontend\CronController@optimizedCorn')->name('cron.optimized');
Route::get('cron_optimize/{userid}/{cronid}/{crontimeid}/{uniqueid}', 'Frontend\CronController@optimizedTreat')->name('cron.cron_optimize');

Route::post('optimizedResultSave/{userid}', 'Frontend\CronController@optimizedResultSave')->name('cron.optimizedResultSave');
Route::post('createPDF/{userid}/{admin}', 'Frontend\CronController@createPDF')->name('cron.createPDF');
Route::post('saveCartToPackage/{userid}', 'Frontend\CronController@saveCartToPackage')->name('cron.saveCartToPackage');
Route::post('save/feedback', [TreatController::class, 'saveFeedback'])->name('save-user-feedback');

Route::get('cron/overview/{cronid}/{uniqueid}', [CronController::class, 'show_treat_overview'])->name('cron-get-overview');


Route::get('treatment/view_pdf/{userid}/{cronid}/{crontimeid}/{unique}', 'Frontend\CronController@viewCronPDF')->name('cron.viewpdf');
Route::get('/treat_finish', 'Frontend\CronController@treatFinish')->name('cron.treatFinish');
Route::post('/Aajax/getAudio', 'Ajax\AajaxController@getAudio')->name('Aajax.getAudio');
Route::post('/Sajax/changebgImage', 'Ajax\SajaxController@changebgImage')->name('changebgImage');
#clean cart after treatment

Route::post('/clean_cart', "Frontend\TreatController@clean_cart")->name('treat.clean-cart-treatment');
Route::post('/clean-email-cart', [CronController::class, 'clearCronEamilCart'])->name('cron.clean-cart-treatment');

Route::get('/get/tutorial/video', 'CronVideoController@getTutorialVideoLink');
Route::get('/test', function () {
    return view('test');
});

Auth::routes(['register' => false]);
Route::get('/login','App\Http\Controllers\Auth\LoginController@loginRoute')->name('login')->middleware('setLanguage');

Route::get('/autologin', 'Auth\AutoLoginController@login')->name('autologin');

Route::middleware(['auth','setLanguage'])->group(function () {

    Route::get('/home', 'HomeController@index')->name('home');
    Route::get('/fillStatus', 'HomeController@fillStatus')->name('fillStatus');

    Route::group(['as' => 'dashboard.', 'prefix' => 'dashboard', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {
        Route::get('/getDashboardContent', [DashboardController::class, 'getDashboardContent'])->name('get-dashboard-content');
        Route::get('/getDashboardWidget', [DashboardController::class, 'getDashboardWidget'])->name('get-dashboard-widget');
        Route::post('/add-red-values-from-widget', [DashboardController::class, 'add2CartDashboardWidgetRedValues'])->name('add-red-values-from-widget');
        Route::get('/', 'DashboardController@index')->name('dashboard');
        Route::get('/module/{moduleId}/', 'DashboardController@moduleDashboard')->name('module');
        Route::get('/p/{proid}/{subid}', 'DashboardController@product')->name('product')->middleware(['ordercheck']);
        Route::get('/p/{proid}/{subid}/{days}', 'DashboardController@longProduct')->name('longterm')->middleware(['ordercheck']);
        Route::get('/ps/{proid}/', 'DashboardController@products')->name('products')->middleware(['ordercheck']);
        Route::get('/fv/{proid}/{subid}', 'DashboardController@focusView')->name('focusView')->middleware(['ordercheck']);
        Route::get('/ppb', 'DashboardController@productsProgressbar')->name('productsProgressbar');
        Route::get('/op/{ownid}/{ownsubid}', 'DashboardController@ownProduct')->name('ownProduct')->middleware(['ordercheck']);
        Route::get('/op/{ownid}/{ownsubid}/{days}', 'DashboardController@longOwnProduct')->name('longownproduct')->middleware(['ordercheck']);
        Route::get('/ops/{ownid}', 'DashboardController@ownProducts')->name('ownProducts')->middleware(['ordercheck']);
        Route::get('/pp/{proid}', 'DashboardController@premiumPro')->name('preproduct')->middleware(['ordercheck']);

        Route::get('/pkv', 'DashboardController@packageView')->name('packageView');
        Route::get('/get-pkv-data', 'DashboardController@getPackageViewData')->name('get-pkv-data');
        Route::get('/opk', 'DashboardController@oldPackageView')->name('oldPackage');
        Route::get('/al', 'DashboardController@activityLog')->name('activityLog');
        Route::get('/cr', 'DashboardController@changerandomstatus')->name('changerandomstatus');
        Route::post('/all_red', 'DashboardController@allRedValues')->name('all_red');
        Route::post('/all_own_red', 'DashboardController@allOwnRedValues')->name('all_own_red');

        Route::post('/all_orange', 'DashboardController@allOrangeValues')->name('all_orange');
        Route::post('/all_own_orange', 'DashboardController@allOwnOrangeValues')->name('all_own_orange');
        Route::post('/all_green', 'DashboardController@allGreenValues')->name('all_green');
        Route::post('/all_own_green', 'DashboardController@allOwngreenValues')->name('all_own_green');

        // combo menu routes
        Route::get('c/p/{proid}/{subid}/{comid}', 'DashboardController@product')->name('combo.product')->middleware(['ordercheck']);
        Route::get('c/ps/{proid}/{comid}', 'DashboardController@products')->name('combo.products')->middleware(['ordercheck']);
        Route::get('c/fv/{proid}/{subid}/{comid}', 'DashboardController@focusView')->name('combo.focusView')->middleware(['ordercheck']);
        Route::get('c/ppb', 'DashboardController@productsProgressbar')->name('combo.productsProgressbar');
        Route::get('c/op/{ownid}/{ownsubid}/{comid}', 'DashboardController@ownProduct')->name('combo.ownProduct')->middleware(['ordercheck']);
        Route::get('c/ops/{ownid}/{comid}', 'DashboardController@ownProducts')->name('combo.ownProducts')->middleware(['ordercheck']);
        Route::get('c/pp/{proid}/{comid}', 'DashboardController@premiumPro')->name('combo.preproduct')->middleware(['ordercheck']);
        Route::get('c/p/{proid}/{subid}/{days}/{comid}', 'DashboardController@longProduct')->name('combo.longterm')->middleware(['ordercheck']);
        Route::get('c/op/{ownid}/{ownsubid}/{days}/{comid}', 'DashboardController@longOwnProduct')->name('combo.longownproduct')->middleware(['ordercheck']);

        Route::get('designer', 'DashboardDesigner@index')->name('designer');
        Route::get('designer/edit/{dashboardId}/{widgetId}', 'DashboardDesigner@editWidget')->name('designer.edit');
        Route::post('designer/edit/{dashboardId}/{widgetId}', 'DashboardDesigner@saveWidget')->name('designer.save');
        Route::get('designer/users/{dashboardId}', 'DashboardDesigner@users')->name('designer.assigned_users');
    });

    Route::group(['as' => 'products.', 'prefix' => 'products', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {
        // modal rendering routes
        Route::post('modal_render', 'ProductController@modalRender')->name('modal-render');
    });

    Route::group(['as' => 'payment.', 'prefix' => 'payment', 'namespace' => 'Payment', 'middleware' => ['setLanguage']], function () {
        Route::get('/payment', 'paymentController@getPaymentView')->name('payment');
    });

    Route::group(['as' => 'activity.', 'prefix' => 'activity', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {
        Route::get('/log', 'ActivityController@index')->name('log');
        Route::get('/get_log_lists', 'ActivityController@getLogLists')->name('log-lists');
        Route::get('/get_mail_log_lists', 'ActivityController@getMailLogLists')->name('mail-log-lists');
    });

    Route::group(['as' => 'Treatment.', 'prefix' => 'Treatment', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {
        Route::get('/optimize_ra/{proid}/{subid}/{type}', 'TreatmentController@optimizedCartForRA')->name('optimize-RA');
        Route::get('/allred/optimize_ra/{proid}/{time}/{type}', 'TreatmentController@optimizedCartForRA')->name('allred-optimize-RA');
        Route::post('/optimize_result_save/{type}', 'TreatmentController@optimizedResultSave')->name('OPSave');
        Route::post('/optimize_cart/{type}', 'TreatmentController@optimizedCartRecord')->name('cart_optimize');
        Route::post('/cart_to_package/{type}/{package_name}', 'TreatmentController@saveCartToPackage')->name('cartToPackage');
        Route::post('/clear_cart/{type}', 'TreatmentController@clean_cart')->name('cartEmpty');
        #create pdf
        Route::post('/create_pdf/{userId}/{creator_id}/{type}', 'TreatmentController@create_pdf')->name('create-pdf');
        #email Treatment Route
        Route::get('optimize_ra/{userid}/{cronid}/{crontimeid}/{uniqueid}', 'TreatmentController@optimizedCartForRA')->name('TreatmentOptimize');
        // <!-- Route::get('optimize_ra/{userid}/{cronid}/{crontimeid}/{uniqueid}', 'Frontend\TreatmentController@optimizedCartForRA')->name('TreatmentOptimize'); -->
        #save submenu watch records
        Route::post('/watch/{user_id}/{product_id}/{submenu_id}', 'TreatmentController@saveWatchRecordFrabklang')->name('save-watch-records');
    });

    Route::group(['as' => 'frabklang.', 'prefix' => 'reset4me', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {
        Route::get('/dashboard/{proid}', 'FrabklangController@index')->name('frabklang')->middleware(['ordercheck']);
        Route::get('/c/dashboard/{proid}/{comid}', 'FrabklangController@index')->name('combo');
        Route::get('/treat/{proid}/{subid}', 'FrabklangController@treat')->name('treat');
        Route::get('/duepower/{proid}/{subid}/{time}', 'FrabklangController@treat')->name('timetreat');
        Route::post('/fetch/{proid}/treat/{subid}', 'FrabklangController@fetchAnalysesData')->name('fetch');
        Route::post('/allred/fetch/', 'FrabklangController@fetchAnalysesData')->name('fetch_allred');
        Route::get('/allred/treat/{proid}', 'FrabklangController@treat')->name('treat_allred');
        Route::get('/allred/duepower/{proid}/{time}', 'FrabklangController@treat')->name('timetreat_allred');
    });


    Route::group(['as' => 'users.', 'prefix' => 'users', 'namespace' => 'Frontend', 'middleware' =>  ['auth', 'setLanguage']], function () {

        Route::get('/index', 'UsersController@index')->name('users');
        Route::get('/feedback', 'UsersController@feedback')->name('feedback');
        Route::get('/add_user', 'UsersController@show')->name('show');
        Route::get('/create', 'UsersController@create')->name('create');
        Route::post('/store', 'UsersController@store')->name('store');
        Route::get('/edit_user/{id}', 'UsersController@edit')->name('edit');
        Route::put('/update/{id}', 'UsersController@update')->name('update');
        Route::get('/delete/{id}', 'UsersController@delete')->name('delete');
        Route::get('/admin/{id}', 'UsersController@adminShow')->name('adminShow')->middleware(['setLanguage']);
        Route::put('/adminUpdate/{id}', 'UsersController@adminUpdate')->name('adminUpdate')->middleware(['setLanguage']);
        Route::get('/generateTerms/{id}', 'UsersController@generateTerms')->name('generateTerms');

        Route::post('/shareUserLimit', 'UsersController@shareUserLimit')->name('shareUserLimit');
        Route::post('/deleteimg', 'UsersController@deleteProfileImage')->name('deleteimg');
        Route::get('/export', 'UsersController@exportSubusers')->name('export');
        Route::get('/changelang/{id}', 'UsersController@language_change')->name('changelang');
        Route::post('/delete_all_save_cart', 'UsersController@removeAllSaveCart')->name('RemoveAllCart');
        Route::post('/change_current_user_info', 'UsersController@change_current_user')->name('ChangeCurrentUser');

        Route::get('_user_subusers/{status}', [UsersController::class, 'getSubuserList'])->name('get-subusers-list');

        // user
        Route::get('user-type', [UsersController::class,'checkUserType'])->name('check-user-type');
        Route::get('change_language', function () {
            cache()->forget('system_menu'. Auth::id());
            session()->put('lang_id'.Auth::id(), DB::table('languages')->find(DB::table('users')->find(getUserId(),['language_id'])->language_id)->short_code);
            $notification = array(
                'message' => trans('action.successfullychange'),
                'alert-type' => 'success'
            );
            return redirect()->back()->with($notification);
        })->name('change-language');

        // system settings
        Route::get('/user_settings/{id}', 'UsersController@userSettingsIndex')->name('user-settings');
        Route::post('/update_user_settings','UsersController@updateUserSettings')->name('update-user-settings');
        Route::get('/get_pdf_template', 'UsersController@getPdfTemplate')->name('get-pdf-template');
        Route::get('/check-user-info-exists', [UsersController::class,'checUserInfoExists'])->name('check-user-info-exists');
        Route::get('/get-user-limit/{id}', [UsersController::class,'getShareUserLimit'])->name('get-user-limit');

    });

    Route::group(['as' => 'calculation.', 'prefix' => 'calc', 'namespace' => 'Frontend'], function () {
        Route::get('/biorhythmus', [CalculationController::class, 'getBiorhythmus'])->name('biorhythmus');
        Route::post('/calculateFrequency', [CalculationController::class, 'calculateFrequency'])->name('calculateFrequency');
    });

    Route::group(['as' => 'group.', 'prefix' => 'group', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {

        Route::get('/', 'GroupController@index')->name('group');
        Route::post('/store', 'GroupController@store')->name('store');
        Route::post('/update/{id}', 'GroupController@update')->name('update');
        Route::get('/get_group_users_by_id/{id}', 'GroupController@get_group_users_by_id')->name('get_group_users');
        Route::get('/get_add_user_modal/{id}', 'GroupController@get_add_user_modal')->name('add_user_modal');
        Route::get('/get_staff_add_user_modal/{id}', 'GroupController@get_staff_add_user_modal')->name('add_staff_user_modal');
    });


    Route::group(['as' => 'menu.', 'prefix' => 'menu', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {

        Route::get('/index', 'MenuController@index')->name('menus');
        Route::get('/add_menu', 'MenuController@show')->name('show');
        Route::get('/add_menu_combo', 'MenuController@show')->name('add.combo');
        Route::get('/add_submenu', 'MenuController@submenu_show')->name('submenu_show');
        // Route::post('/store', 'MenuController@store')->name('store');
        Route::post('/store_combo', 'MenuController@storeCombo')->name('store_combo');
        Route::post('/store_menu', 'MenuController@storeMenu')->name('store_menu');
        Route::post('/store_submenu', 'MenuController@storeSubmenu')->name('store_submenu');
        Route::get('/edit_menu/{id}', 'MenuController@edit')->name('edit');
        Route::get('/edit_combo_menu/{id}', 'MenuController@combo_edit')->name('combo_edit');
        // Route::put('/update/{menu_id}', 'MenuController@update')->name('update');
        Route::put('/update_menu/{menu_id}', 'MenuController@updateMenu')->name('update_menu');
        Route::put('/update_menusetting', 'MenuController@updateMenuSetting')->name('update_menusetting');
        Route::put('/update_submenu', 'MenuController@updateSubmenu')->name('update_submenu');
        // Route::put('/update/{submenu_id}/{menu_id}', 'MenuController@update')->name('update');
        Route::get('/delete/{id}', 'MenuController@delete')->name('delete');
        Route::get('/submenu_delete/{id}', 'MenuController@submenu_delete')->name('submenu_delete');
        Route::get('/combo_delete/{id}', 'MenuController@combo_delete')->name('combo_delete');
        Route::put('/combo_update/{menu_id}', 'MenuController@combo_update')->name('combo_update');
        Route::get('/combo_list', 'MenuController@combo_list')->name('combo_list');
    });


    Route::group(['as' => 'ownAnalysis.', 'prefix' => 'ownAnalysis', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {

        Route::get('/', 'OwnAnalysisController@index')->name('ownAnalysis');
        Route::get('/create', 'OwnAnalysisController@create')->name('create');
        Route::post('/store', 'OwnAnalysisController@store')->name('store');
        Route::get('/edit/{id}/', 'OwnAnalysisController@edit')->name('edit');
        Route::delete('/body/img', 'OwnAnalysisController@deleteBodyImg')->name('deleteBodyImg');
        Route::delete('/mental/img', 'OwnAnalysisController@deleteMentalImg')->name('deleteMentalImg');
        Route::delete('/body/audio', 'OwnAnalysisController@deleteBodyAudio')->name('deleteBodyAudio');
        Route::delete('/mental/audio', 'OwnAnalysisController@deleteMentalAudio')->name('deleteMentalAudio');
        Route::delete('/analyse/audio', 'OwnAnalysisController@deleteAnalyseAudio')->name('deleteAnalyseAudio');
        Route::put('/update/{id}', 'OwnAnalysisController@update')->name('update');
        Route::delete('/delete/{id}', 'OwnAnalysisController@delete')->name('delete');
        Route::get('/export_import', 'OwnAnalysisController@analysisExportImport')->name('export_import');
        Route::post('/import', 'OwnAnalysisController@importAnalysis')->name('import');
        Route::post('/export', 'OwnAnalysisController@exportAnalysis')->name('export');

        Route::post('/desc_img', 'OwnAnalysisController@deleteDescImg')->name('desc_img');
    });

    Route::group(['as' => 'cron.', 'prefix' => 'cron', 'namespace' => 'Frontend', 'middleware' => ['auth', 'setLanguage']], function () {

        Route::get('/', 'CronController@index')->name('cron');
        Route::get('/list/{archive}', 'CronController@index')->name('cron_archive');
        Route::get('/cron_show', 'CronController@cron_show')->name('cron_show');

        Route::get('/cron_setting/{id}', 'CronController@showCronSetting')->name('cronSetting');
        Route::post('/store_cronsetting', 'CronController@storeCronSetting')->name('storeCronSetting');
        Route::get('/edit_cronsetting/{id}', 'CronController@editCronSetting')->name('editCronSetting');
        Route::put('/update_cronsetting/{id}', 'CronController@updateCronSetting')->name('updateCronSetting');

        Route::get('/remote-analysis/{type}', 'CronController@remoteAnalysis')->name('remote-analysis');

        Route::get('/cron_setup', 'CronController@showCronSetup')->name('cronSetup');
        Route::post('/store_cronsetup', 'CronController@storeCronSetup')->name('storeCronSetup');
        Route::post('/store_cronsetup/{type}', 'CronController@storeCronSetup')->name('storeCronSetupRemote');
        Route::get('/edit_cronsetup/{id}', 'CronController@editCronSetup')->name('editCronSetup');
        Route::put('/update_cronsetup/{id}', 'CronController@updateCronSetup')->name('updateCronSetup');
        Route::delete('/delete_cronsetup/{id}', 'CronController@deleteCronSetup')->name('deleteCronSetup');
        Route::post('/cron_trash/{id}', 'CronController@cronTrashRestore')->name('cronTrashRestore'); // cron Restore route
        Route::post('/empty_trash', 'CronController@empty_trash')->name('emptyTrash'); // empty user trash

        Route::get('/cronPreview', 'CronController@cronPreview')->name('cronPreview');
        Route::get('/updateCronOption/{id}', 'CronController@updateCronOption')->name('updateCronOption');

        Route::get('/cron_pdf_view/{id}', 'CronController@cronPDFview')->name('cronPDFview');

        Route::get('/cron_trash', 'CronController@cronTrash')->name('cronTrash');
        Route::post('/cronShowAnalysis', 'CronController@cronShowAnalysis')->name('cronShowAnalysis');
        Route::post('/cronUpdateEmail', 'CronController@cronUpdateEmail')->name('cronUpdateEmail');
        Route::post('/stopEmail', 'CronController@stopEmail')->name('stopEmail');
        Route::post('/stop/{id}', 'CronController@stop')->name('stop');
        Route::get('/oldcron', 'CronController@oldcron')->name('oldcron');
        Route::get('/cron_duplicate/{cronid}', 'CronController@cronDuplicate')->name('duplicate');
        Route::delete('/delete_time/{timeid}', 'CronController@deleteCronTime')->name('delete_time');

        Route::get('/open_treatment/{cronid}/{crontimeid}/{unique}', 'CronController@openTreatment')->name('openTreatment');
        Route::get('/getCronCart/{cronid}/{crontimeid}/{unique}', 'CronController@getCronInCart')->name('getCronCart');
        Route::get('/progress_status', 'CronController@getCronProgress')->name('progress_status');
        Route::post('/store_mail_template','CronController@storeMailTemplates')->name('store-mail-template');
        Route::post('/store_smtp','CronController@storeSMTP')->name('store-smtp');
        Route::get('get_template','CronController@getTemplate')->name('get-template');
        Route::get('get_date_time', [CronController::class, 'getAvilableDateTime'])->name('get-date-time');
        Route::get('/clear_smpt_setting', 'CronController@clearSmptSetting')->name('clear_smpt_setting');

        Route::post('/submenu/save', [CronController::class, 'saveSubmenuSelections'])->name('submenu.save');
        Route::get('/submenu/get', [CronController::class, 'getSubmenuSelections'])->name('submenu.get');
        Route::post('/submenu/clear', [CronController::class, 'clearSubmenuSelections'])->name('submenu.clear');
    });

    Route::group(['as' => 'treat.', 'prefix' => 'treat', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {

        Route::get('/', 'TreatController@treat')->name('treat');
        Route::get('/treat_optimize/{music}/{frequency}', 'TreatController@optimizedTreat')->name('treat_optimize');
        Route::get('/dur_power', 'TreatController@durPower')->name('dur_power');
        Route::post('/optimized', "TreatController@optimizedAnalysis")->name('optimized');
        Route::post('/optimizedResultSave', "TreatController@optimizedResultSave")->name('optimizedResultSave');
        Route::post('/createPDF',  [TreatController::class,'createPDF'])->name('createPDF');
        Route::post('/createandsand', "TreatController@createandsand")->name('createandsand');
        Route::get('/enfitgpt', "TreatController@enfitgpt")->name('enfitgpt');
        Route::post('/enfitgptCreate', "TreatController@enfitgptCreate")->name('enfitgpt.create');
        Route::post('/enfitgptChat', "TreatController@chat")->name('enfitgpt.chat');
        Route::post('/enfitgptChatClear', "TreatController@clearEnfitgptChatHistory")->name('enfitgpt.chat.clear');

        Route::post('/clean_cart', "TreatController@clean_cart")->name('clean-cart');
    });

    Route::group(['as' => 'Aajax.', 'prefix' => 'Aajax', 'namespace' => 'Ajax', 'middleware' => ['setLanguage']], function () {
        Route::post('/focusimg', 'AajaxController@GetFocusImage')->name('focusimg');
        Route::post('/groupCauses', 'AajaxController@GetGroupCauses')->name('groupCauses');
        Route::post('/groupTopCauses', 'AajaxController@GetTopGroupCauses')->name('groupTopCauses');
        Route::post('/saveCalculation', 'AajaxController@saveCalculation')->name('saveCalculation');

        Route::post('/deletePDF', 'AajaxController@deletePDF')->name('deletePDF');
        Route::delete('/deleteDcPDF/{id}', 'AajaxController@deleteDCPDF')->name('deleteDcPDF');
        Route::post('/resetmenu', "AajaxController@resetMenu")->name('resetmenu');
        Route::post('/menu_auto_hide', "AajaxController@menuAutoHide")->name('menuAutoHide');
        Route::post('/autoGenerate', "AajaxController@autoGenerate")->name('autoGenerate');
        Route::post('/check_cron', "AajaxController@CheckCron")->name('check_cron');
        Route::post('/fetchdata', "AajaxController@fetchdata")->name('fetchdata');
        Route::post('/direction', "AajaxController@updateDirection")->name('direction');
    });

    Route::group(['as' => 'focus.', 'prefix' => 'focus', 'namespace' => 'Frontend'], function () {

        Route::post('/focus', 'FocusController@saveSession')->name('saveSession');
        Route::post('/opensession', 'FocusController@openSession')->name('openSession');
        Route::post('/createpdf', 'FocusController@createSessionPDF')->name('createpdf');
        Route::post('/sendpdf', 'FocusController@sendSessionPDF')->name('sendpdf');
        Route::post('/deletesession', 'FocusController@deleteSession')->name('deletesession');

        Route::post('/addScreenShot', 'FocusController@addScreenShot')->name('screenshot');
        Route::post('/saveownimage', 'FocusController@saveOwnImage')->name('saveownimage');
        Route::get('/showownimage', 'FocusController@showOwnImage')->name('showownimage');
        Route::post('/openOwnImage', 'FocusController@openOwnImage')->name('openOwnImage');
        Route::post('/deleteownimage', 'FocusController@deleteOwnImage')->name('deleteownimage');
        Route::post('/focusimage', 'FocusController@getFocusCartImage')->name('focusimage');
        Route::post('/delete_cartimg', 'FocusController@deleteFocusCartImage')->name('delete_cartimg');
        Route::get('/dcicon_link', 'FocusController@getDcIconLink')->name('dcicon_link');
        Route::get('/dc_default_icon_links', 'FocusController@getDefaultIconLinks')->name('dcdefaulticons');
        Route::get('/dcicon_sizes', 'FocusController@getDciconSizes')->name('dcicon_sizes');
        Route::get('/dcicon_size', 'FocusController@getDciconSize')->name('dcicon_size');
        Route::get('/dcicon_directions', 'FocusController@getDcDirections')->name('dcicon_directions');
    });



    Route::group(['as' => 'Sajax.', 'prefix' => 'Sajax', 'namespace' => 'Ajax', 'middleware' => ['setLanguage']], function () {
        Route::post('/switch_user', 'SajaxController@switch_user')->name('switch');
        Route::post('/search_user', 'SajaxController@search_user')->name('search_user');
        Route::post('/changeYearMonth', 'SajaxController@changeYearMonth')->name('changeYearMonth');
        Route::post('/changeDatumcore', 'SajaxController@changeDatumcore')->name('changeDatumcore');
        Route::post('/changeShowFilter', 'SajaxController@changeShowFilter')->name('changeShowFilter');
        Route::post('/saveToday', 'SajaxController@saveToday')->name('saveToday');
        Route::post('/packageAddToCart', 'SajaxController@packageAddToCart')->name('packageAddToCart');
        Route::post('/deletePackage', 'SajaxController@deletePackage')->name('deletePackage');
        Route::post('/addNotePKG', 'SajaxController@addNotePKG')->name('addNotePKG');
        Route::post('/add2Cart', 'SajaxController@add2Cart')->name('add2Cart');

        Route::post('/add2CartAnalysis', 'SajaxController@add2CartAnalysis')->name('add2CartCMT');
        Route::post('/add2CartCMT', 'SajaxController@add2CartCMT')->name('add2CartAnalysis');

        Route::post('/removeCart', 'SajaxController@removeCart')->name('removeCart');
        Route::post('/changeDashView', 'SajaxController@changeDashView')->name('changeDashView');
        Route::post('/saveTopic', 'SajaxController@saveTopic')->name('saveTopic');
        Route::post('/showMenusClick', 'SajaxController@showMenusClick')->name('showMenusClick');
        Route::post('/savePackageCart', 'SajaxController@savePackageCart')->name('savePackageCart');
        Route::post('/saveCartToPackage', 'SajaxController@saveCartToPackage')->name('saveCartToPackage');
        Route::post('/clearCart', 'SajaxController@clearCart')->name('clearCart');
        Route::post('/addUserOwnGroup', 'SajaxController@addUserOwnGroup')->name('addUserOwnGroup');
        Route::post('/userThemaSpich', 'SajaxController@userThemaSpich')->name('userThemaSpich');
        Route::post('/updateFillStatus', 'SajaxController@updateFillStatus')->name('updateFillStatus');
        Route::post('/delete_staff', 'SajaxController@staffDelete')->name('delete_staff');
        Route::post('/delete_from_group', 'SajaxController@userDeleteFromGroup')->name('delete_from_group');
        Route::post('/deleteGroup', 'SajaxController@deleteGroup')->name('deleteGroup');
        Route::post('/deleteSubUser', 'SajaxController@deleteSubUser')->name('deleteSubUser');
        Route::post('/removePDFLogo', 'SajaxController@removePDFLogo')->name('removePDFLogo');
        // Route::post('/changebgImage', 'SajaxController@changebgImage')->name('changebgImage');

        Route::post('/add_shareUserlimit', 'SajaxController@add_shareUserlimit')->name('add_shareUserlimit');
        Route::post('/change_reaction_status', 'SajaxController@change_reaction_status')->name('change-reaction-status');
        Route::post('/change_reaction_pdf_show', 'SajaxController@change_reaction_pdf_show')->name('change-reaction-pdf-show');
        Route::post('/group_member_add', 'SajaxController@addGroupMember')->name('group_member_add');
        Route::post('/staff_add', 'SajaxController@addNewStaff')->name('staff_add');
        Route::post('/save_assign_staff', 'SajaxController@saveAssignStaffForGroup')->name('save_assign_staff');
        Route::get('/getGroupUsers/{id}', 'SajaxController@get_group_users')->name('get_group_users');
        Route::delete('/removeStaffFromAssign/{id}/{gid}', 'SajaxController@removeStaffFromAssign')->name('removeStaffFromAssign');

        Route::GET('/getCartContent/{id}', "SajaxController@getCartContent")->name('get-cart-content');

        Route::post('/cache_clear', "SajaxController@cache_clear")->name('cache-clear');
        Route::post('/add_group', "SajaxController@addGroup")->name('add-group');


        Route::get('get_data/{type}', 'SajaxController@getDataFroDashRight')->name('dash-right-content');
        Route::get('get_save_modal', 'SajaxController@getSaveCartModal')->name('save-cart-content');

        Route::get('get_pkg_content', 'SajaxController@getPackageContent')->name('get-package-content');
    });


    // Route::group(['as' => 'majax.', 'prefix' => 'Majax', 'namespace' => 'Ajax'], function () {

    //     Route::get('get-group/{id}', 'MajaxController@getgroup')->name('get-group');
    //     Route::post('add-group', 'MajaxController@addgroup')->name('add-group');
    //     Route::get('analyse_delete/{id}', 'MajaxController@analysedelete')->name('analyse-delete');
    //     Route::get('profile-status/{id}', 'MajaxController@changestatus');
    //     Route::get('login-reset/{id}', 'MajaxController@resetlogin');
    //     Route::post('/add-user', 'MajaxController@addUser');
    // });


    // Route::group(['as' => 'Tajax.', 'prefix' => 'Tajax', 'namespace' => 'Ajax'], function () {
    //     Route::post('/analyses', 'AjaxTranslate@analyses')->name('analyses');
    //     Route::post('/causes', 'AjaxTranslate@causes')->name('causes');
    //     Route::post('/products', 'AjaxTranslate@products')->name('products');
    //     Route::post('/submenus', 'AjaxTranslate@submenus')->name('submenus');
    //     Route::post('/group', 'AjaxTranslate@group')->name('group');
    //     Route::post('/grouptype', 'AjaxTranslate@groupType')->name('grouptype');
    //     Route::post('/translate_analyses', 'AjaxTranslate@translateAnalyses')->name('translation_analyses');
    //     Route::post('/translate_causes', 'AjaxTranslate@translateCauses')->name('translate_causes');
    //     Route::post('/translate_enfluess', 'AjaxTranslate@translateEnfluess')->name('translate_enfluess');
    //     Route::post('/translate_product', 'AjaxTranslate@translateProduct')->name('translate_product');
    //     Route::post('/translate_submenu', 'AjaxTranslate@translateSubmenu')->name('translate_submenu');
    //     Route::post('/translate_grouptype', 'AjaxTranslate@translateGroupType')->name('translate_grouptype');
    //     Route::post('/translate_group', 'AjaxTranslate@translateGroup')->name('translate_group');
    // });

    #Route::get('get-group/{id}', 'MajaxController@getgroup')->name('get-group');

    Route::group(['as' => 'cart.', 'prefix' => 'cart', 'namespace' => 'Frontend', 'middleware' => ['setLanguage']], function () {

        Route::post('/pdf', 'CartController@generatePDF')->name('pdf');
        Route::get('/getpdf', 'CartController@generatePDF')->name('getpdf');
    });

    Route::group(['as' => 'backgroundImage.', 'prefix' => 'backgroundImage'], function () {

        Route::get('/', 'TreatmentCustomIamgeController@index')->name('backgroundImages');
        Route::get('/upcoming_cron', 'TreatmentCustomIamgeController@upcomingCron')->name('upcoming_cron');
        Route::get('/all_cron', 'TreatmentCustomIamgeController@allCron')->name('all_cron');
    });


    Route::get('member-signupform', "FormgenerateController@formView");
    Route::get('/singup/{?website}/{param?}', "FormgenerateController@getform");
    Route::post('user-save', "FormgenerateController@save");
    Route::post('/cron/smpt', 'Frontend\SmtpController@checksmtp');
    Route::get('/cronmailsend', 'Frontend\CronMailSendController@index')->name('cronmailsend');
    Route::post('/resendcronmail', 'Frontend\CronController@resendcronmail')->name('resendcronmail');
    Route::post('/resendtreatmail', 'Frontend\CronMailSendController@resendTreatMail')->name('resendtreatmail');
    Route::post('/sendpdf', 'Frontend\SendPDFController@index')->name('sendpdf');
    Route::get('/getImageUploadView', 'HomeController@getUploadView')->name('upload-image');
    Route::post('/add_image', 'HomeController@add_image')->name('add_image');

    Route::get('/cronmail_check/{userid}/{from}/{to}', 'Frontend\CronMailSendController@resendMailByUser');


    Route::get('/demopage', 'HomeController@demoPage')->name('demopage');
    #sorting
    Route::post('short', "AdminCmsUsersController@short");

    Route::post('refresh-csrf', function () {
        return csrf_token();
    });


    // pdf view
    Route::get('/pdf_view', 'PdfViewController@pdfView')->name('pdfView');

    //pdf view email temp settings
    Route::get('/pdf_view_mail_temp', 'PdfViewController@emailTemplateSettings')->name('pdf_view_mail_temp');
    Route::get('get_mail_template','PdfViewController@getMailTemplate')->name('get-mail-template');
    Route::post('/store_resend_mail_temp','PdfViewController@storeResendMailTemplate')->name('store-resend-mail-temp');

    // resend mail
    Route::post('/pdfresendmail', 'PdfViewController@pdfResendMail')->name('pdf-resend-mail');

    // get pdf data
    Route::get('get-pdf-data', 'PdfViewController@getPdfData')->name('get-pdf-data');

    // delete pdf
    Route::post('delete-pdf', 'PdfViewController@deletePdf')->name('delete-pdf');
    // delete all pdf
    Route::post('delete-all-pdf', 'PdfViewController@deleteAllPdf')->name('delete-all-pdf');

    // get sub user for pdf
    Route::get('get_subusers', 'PdfViewController@authSubUsers')->name('get-subusers');

    // test new menu
    Route::view('/test-new-menu', 'Frontend.partials.includes.test-new-menu')->name('welcome');

    Route::get('/test-new-pdf', 'PdfViewController@createNewPdfFile')->name('create-new-pdf-file');
});

Route::get('/unsubscribe', 'Frontend\CronMailSendController@unsubscribe')->name('unsubscribe')->middleware('setLanguage');
