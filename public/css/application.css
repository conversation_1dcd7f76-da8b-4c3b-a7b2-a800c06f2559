body::-webkit-scrollbar {
    width: 10px;
}

body::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

body::-webkit-scrollbar-thumb {
    background-color: #93b7af;
    /* outline: 1px solid slategrey; */
}
.modal{
    z-index: 9999 !important;
    overflow-y: auto;
}
.web-kit-scroll::-webkit-scrollbar,
.popover-body::-webkit-scrollbar {
    width: 5px;
}

.web-kit-scroll::-webkit-scrollbar-track,
.popover-body::-webkit-scrollbar-track {
    border-radius: 10px !important;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.17);
}

.web-kit-scroll::-webkit-scrollbar-thumb,
.popover-body::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}

body {
    scrollbar-color: #93b7af white;
    scrollbar-width: thin;
}

label {
    margin-bottom: 0 !important;
}

.btn {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
}

.ui-ch-30{
    height: 30px !important;
}

.ui-ch-60{
    height: 60px !important;
}

a.canvasjs-chart-credit {
    display: none !important;
}

.badge.indicator:not(.badge-dot) {
    font-size: 12px;
}

body .modal-open {
    overflow: auto;
}

.modal-open[style] {
    /* padding-right: 17px !important; */
}
.bg-theme{
    background-color: #2fab66;
    color: #ffffff;
}
.bg-red{
   background: #E84E1B !important;
}
.bg-green{
    background: #2FAB66 !important;
}
.form-control {
    border: 1px solid rgb(228, 228, 228) !important;
}
.from-input-error {
    border-color: #d9534f !important;
    color: #d9534f;
  }
.custom-control-input:checked~.custom-control-label::before {
    border-color: #02bc77 !important;
    background-color: #02bc77 !important;
}

.page-item.active .page-link {
    background-color: #02a065 !important;
    border-color: #02a065 !important;
}

.mce-statusbar .mce-container-body {
    position: relative;
    font-size: 11px;
    display: none !important;
}

.hide {
    display: none !important;
}
.closed{
    display: none !important;
}
.opened{
    display: inline-block !important;
}
.popover {
    /* height: 267px;
    overflow-y: scroll;
    scrollbar-color: #93b7af white;
    scrollbar-width: thin; */
    border-color: #02a065 !important;
    max-width: 450px;
}
.popover-body ul{
    padding-left: 15px;
}
.bs-popover-bottom>.arrow::before,
.bs-popover-auto[x-placement^="bottom"]>.arrow::before {
    border-bottom-color: #02a065 !important;
}
/*==================
    Common Css
 ===================*/


.modal {
    z-index: 1111111 !important;
}

.preloader {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background-color: #969696;
    animation: movebg 30s infinite;
    opacity: 0.8;
    background-size: auto;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    align-items: center;
}
.card-body {
    position: relative;
}
.preloader img {
    height: 200px;
    width: 200px;
    animation: rotating 5s linear infinite;
    position: absolute;
    content: '';
    /* top: 150px; */
}
.corndraft .bootstrap-select > .dropdown-toggle {
    background: #fff;
}
.no-hover>* {
    display: none !important;
}

.blur{
    filter: blur(3px);
}

.primary-color {
    color: #2fab66;
}

.color-border-one {
    border-color: #ccc !important;
}

.danger-color {
    color: red;
}

.border-none {
    border: 0 !important;
}

.no-padding {
    padding: 0 !important;
}

.padding-10 {
    padding: 10px !important;
}

.width-auto {
    width: auto !important;
}

.f-16 {
    font-size: 16px !important;
}

.f-18 {
    font-size: 18px;
}

.f-22 {
    font-size: 22px;
    cursor: pointer;
}

.f-14 {
    font-size: 14px !important;
}

.min-w-1 {
    min-width: 504px;
}

.height-100 {
    height: 100%;
}

.bg-whight {
    background-color: #fff;
}

.showw {
    display: block !important;
}
.toggel_show{
    display: block !important;
}
.cursor-pointer {
    cursor: pointer;
}

.hover-primary:hover {
    color: #2fab66;
}

.w-32 {
    width: 32% !important;
}
.custom-breadcrumb ol {
    background: #fff !important;
    border: 1px solid rgb(24 28 33 / 13%);
    padding: 0.5rem;
    text-align: center !important;
    display: block;
}
.custom-breadcrumb ol li {
    /* color: #4E5155!important; */
    font-weight: 500;
    font-size: 18px;
}
.mar-bot-20{
    margin-bottom: 20px !important;
}
.mar-bot-0{
    margin-bottom: 0px !important;
}
pre {
    display: block;
    font-size: 100%;
    color: rgba(24, 28, 33, 0.9);
    font-family: "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}
/*=================
    Navbar Css
===================*/

/* .dshbrd-cart-option#saveCartModal .modal-dialog{
    margin: 140px 502px 30px auto !important;
    width: 380px !important;
} */
#saveCartModal .modal-dialog{
    width: 380px !important;
}
.dshbrd-cart-option#openCartModal .modal-dialog {
    width: 380px !important;
}
#openCartModal .modal-body {
    position: relative;
    padding: 20px;
    max-height: max-content;
    margin: 0;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: #93b7af white;
    scrollbar-width: thin;
}
#openCartModal .modal-dialog {
    top: 10px !important;
    height: 95vh;
}
#openCartModal .modal-content {
    overflow: unset;
    padding-top: 0px;
    padding-bottom: 0px;
}
#pdfGenerateModal .modal-content {
    top: 10px;
}
#pdfGenerateModal {
    padding-right: 1px !important;
    z-index: 99999;
}
#saveCartModal .modal-content {
    top: 10px;
}
#shopping-modal .treatment-list {
    max-height: 300px;
    overflow-y: auto;
    scrollbar-color: #93b7af white;
    scrollbar-width: thin;
    overflow: auto;
    overflow-x: hidden;

}

#shopping-modal .treatment-list::-webkit-scrollbar {
    width: 5px;
}

#shopping-modal .treatment-list::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

#shopping-modal .treatment-list::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}

.dropdown-menu-user-list-left {
    display: flex;
    align-items: center;
}

.dropdown-menu-user-list-left p {
    margin: 0;
}

.dropdown-menu.dropdown-menu-right.menu-right-custom.show {
    max-height: 90vh;
    width: auto;
    min-width: 18rem;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    margin-right: -20px;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}

/* user tab content css Start  */
.user-single-box {
    position: relative;
    margin-bottom: 15px;
}

.usb-photo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    -webkit-box-shadow: 0px 0px 15px -4px rgba(112,112,112,1);
    -moz-box-shadow: 0px 0px 15px -4px rgba(112,112,112,1);
    box-shadow: 0px 0px 15px -4px rgba(112,112,112,1);
}
.usb-photo {
    position: relative;
    text-align: center;
    cursor: pointer;
    display: flex !important;
    justify-content: center;
    align-content: center;
}
.usb-content {
    position: relative;
    margin-top: 10px;
}

.usb-content p {
    font-size: 14px;
    margin-bottom: 2px;
    line-height: 18px;
}

.usb-content span {
    font-size: 14px;
    letter-spacing: 0;
}
.usb-icon i {
    position: relative;
    height: 80px;
    color: #d2d2d2;
    width: 80px;
    cursor: pointer;
    font-size: 44px;
    line-height: 80px;
    border-radius: 50%;
    -webkit-box-shadow: 0px 0px 15px -4px rgba(112,112,112,1);
    -moz-box-shadow: 0px 0px 15px -4px rgba(112,112,112,1);
    box-shadow: 0px 0px 15px -4px rgba(112,112,112,1);
}
.minus-btn {
    background: none;
    border: none;
    position: absolute;
    color: #D2D2D2;
    font-size: 20px;
    content: '';
    top: 0;
    right: 12px;
    cursor: pointer;
}

.minus-btn:focus {
    outline: 0;
}
.user-tab-content {
    position: relative;
    padding: 35px 20px;
}
/* user tab content css End  */

.dropdown-menu.dropdown-menu-right.menu-right-custom.show::-webkit-scrollbar {
    width: 5px;
}

.dropdown-menu.dropdown-menu-right.menu-right-custom.show::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.dropdown-menu.dropdown-menu-right.menu-right-custom.show::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}

.layout-navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

nav#layout-navbar {
    position: sticky;
    top: 0px;
    z-index: 11111;
}

.layout-sidenav-toggle .nav-item {
    border: 1px solid #eaeaea;
    padding: 0;
}

.layout-sidenav-toggle .nav-item i {
    padding: 8px 12px;
}

.layout-navbar .navbar-brand img {
    width: 150px;
}

.navbar-nav .nav-item .nav-link i {
    font-size: 20px;
    color: #2fab66;
}

.navbar-nav .nav-item .dashboard-option {
    padding-right: 20px;
}

.navbar-nav .nav-item .dashboard-option i {
    border: 1px solid #d4f1d4;
    padding: 6px 10px;
}

#modals-top .modal-dialog {
    margin: 70px auto !important;
}

#shopping-modal .modal-footer {
    display: block;
}

/* #shopping-modal .modal-dialog {
    width: 500px !important;
    margin: 70px 0 39px auto !important;
} */
#shopping-modal .modal-dialog {
    margin-top: 10px;
}

.model-footer-button-two button {
    width: 32%;
}

/* ==========================
    Right Slide Panel
=============================*/

.right-panel-icon {
    position: fixed;
    top: 145px;
    right: 0;
    z-index: 1;
}

.right-panel-icon p {
    background-color: #ffc107;
    padding: 8px 15px;
    border-radius: 5px 0 0 5px;
    cursor: pointer;
}

.right-panel-icon p i {
    color: #fff;
    font-size: 18px;
}

#right-panel-slide .modal-dialog {
    max-height: 500px;
    top: 70px !important;
}

#right-panel-slide .modal-body {
    margin: 0 !important;
    padding: 0 !important;
}

#right-panel-slide .modal-content {
    padding-top: 3px !important;
    padding-bottom: 5px !important;
    overflow: hidden;
    height: auto;
}

.right-sidepanel {
    position: relative;
    padding-bottom: 0 !important;
}

.right-sidepanel-body {
    max-height: 314px;
    min-height: 314px;
    overflow-y: auto;
    overflow: auto;
    overflow-x: hidden;
    scrollbar-color: #93b7af white;
    scrollbar-width: thin;
    margin: 10px 0;
    padding-right: 5px;
}

.right-sidepanel-body .treatment-list {}

.right-sidepanel-body::-webkit-scrollbar {
    width: 5px;
}

.right-sidepanel-body::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.right-sidepanel-body::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}

.right-sidepanel-footer {
    padding: 0 !important;
    width: 100%;
}

.right-sidepanel-footer a {
    width: 49%;
}

.right-sidepanel-header p {
    font-size: 18px;
}

.right-sidepanel-header {
    border-bottom: 1px solid #ccc;
}

.right-sidepanel-header a span i {
    color: #02a065 !important;
}

#topicModal .modal-dialog{
    margin: 140px 321px 30px auto !important;
    width: 550px !important;
}

#pdfGenerateModal .modal-dialog{
    margin: 140px 321px 30px auto !important;
    width: 550px !important;
}

#saveCartModal .modal-dialog{
    margin: 140px 321px 30px auto !important;
    width: 550px !important;
}

#topicModal .modal-footer {
    padding: 10px !important;
}

#openCartModal .modal-body .cart-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

#openCartModal .modal-body .cart-list li {
    margin-bottom: 10px;
}

/* =================
    Dashboard Css
====================*/
#navs-top-four,
#navs-top-three,
#navs-top-five{
    max-height: 270px;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: #a9a9a9 white;
    scrollbar-width: thin;
}
#navs-top-four::-webkit-scrollbar,
#navs-top-three::-webkit-scrollbar,
#navs-top-five::-webkit-scrollbar{
    width: 5px;
}

#navs-top-four::-webkit-scrollbar-track,
#navs-top-three::-webkit-scrollbar-track,
#navs-top-five::-webkit-scrollbar-track{
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

#navs-top-four::-webkit-scrollbar-thumb,
#navs-top-three::-webkit-scrollbar-thumb,
#navs-top-five::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}
.home .nav-tabs .nav-item {
    margin-right: 3px;
    margin-bottom: 5px;
    border: 1px solid #d8d1d1;
}

.home .nav-tabs .nav-link.active,
.home .nav-tabs .nav-link {
    border: 0 !important;
}

.home .nav-tabs .nav-link.active,
.home .nav-tabs .nav-link {
    margin-right: 0 !important;
}

.home .nav-tabs .dropdown-menu {
    top: 0 !important;
    left: 100% !important;
}

.dashboard-card .card-header {
    font-size: 20px;
    font-weight: 500;
}

.circle-progress-value {
    stroke-width: 6px;
    /* stroke: hsl(0, 100%, 42%); */
    stroke-linecap: round;
}

.circle-progress-circle {
    stroke-width: 2px;
}

.progress-circle-large {
    padding-top: 50px;
}

.progress-circle-large text.circle-progress-text {
    font-size: 18px;
}

.progress-circle-large p {
    margin-top: 30px;
}

.progress-circle-large svg.circle-progress {
    height: 150px;
    width: 150px;
}

.progress-circle-mini {
    padding: 30px 0;
}

.topic textarea {
    border: none;
    box-shadow: 0 0 9px -2px #ccc;
    resize: none;
}

.topic textarea:focus {
    outline: 1px solid #f5f5f5;
}

.treatment-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.treatment-list p {
    margin-bottom: 0;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    text-overflow: ellipsis !important;
    padding-right: 48px;
}

.treatment-list p a {
    color: #2f3337;
}

.treatment-list li {
    position: relative;
    display: block;
    padding-bottom: 6px;
}
.treatment-list-icon {
    position: absolute;
    content: '';
    top: 0;
    right: 0;
    width: auto;
    height: 100%;
}
.treatment-list-icon a i {
    margin: 0 2px;
    font-size: 16px;
    top: 1px;
    position: relative;
}
.treatment-list p i {
    /* color: #2fab66; */
    margin-right: 5px;
    vertical-align: middle;
    display: inline-block !important;
}
.sys-setting {
    margin: 0;
    padding: 0;
    list-style: none;
}

.sys-setting li {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.sys-setting li p {
    width: 40%;
    margin-bottom: 0;
}

.dashboard-navtabs .nav-button {
    padding: 10px 15px !important;
    width: 152px;
    text-align: left;
}

.dashboard-navtabs-content {
    display: none;
}

/* ==================
    SideNav Css
 ====================*/
.sidenav-vertical .sidenav-inner {
    width: 100% !important;
    background: #fff !important;
}
#layout-sidenav {
    width: 230px !important;
    max-height: 100vh !important;
    scrollbar-color: #a9a9a9 white;
    scrollbar-width: thin;
    background-color: unset !important;
    /* -webkit-overflow-scrolling: touch;  */
}
.ps::-webkit-scrollbar {
    width: 5px !important;
}

.ps::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
    border-radius: 50px;
}

.ps::-webkit-scrollbar-thumb {
    background-color: #2fab66 !important;
    /* outline: 1px solid slategrey !important; */
    border-radius: 50px;
}
.sidenav-inner li a {
    color: #2f3337 !important;
    border-radius: 50%;
}

.sidenav-inner li a span {
    display: inline-block;
    padding-right: 10px;
}
.sidenav-menu .sidenav-item .sidenav-link {
    color: #091a2aed !important;
    padding: 8px 20px !important;
}
.sidenav-item .sidenav-link {
    padding: 8px 20px !important;
}

.sidenav-menu {
    padding: 0 !important;
    border-bottom: 1px solid #ddd;
    border-top: 1px solid #ddd;
}

/* ==============
    Products Css
=================*/

.products .nav-tabs .nav-item {
    margin-right: 3px;
    margin-bottom: 5px;
    border: 1px solid #eae5e5;
}

.products .nav-tabs .nav-link.active,
.products .nav-tabs .nav-link {
    border: 0 !important;
}

.products .nav-tabs .nav-link.active,
.products .nav-tabs .nav-link {
    margin-right: 0 !important;
}

.products .nav-tabs .dropdown-menu {
    top: 0 !important;
    left: 100% !important;
}

/* ==============
    Analysis Css
=================*/

.analysis-content-header .logo {
    margin-right: 10px;
}

.analysis-content-header .logo i {
    font-size: 50px;
    padding-right: 5px;
    border-right: 1px solid #02a065;
}

.analysis-content-header .heading h2,
.analysis-content-header .heading p {
    margin-bottom: 0;
}

.analysis-content-right ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.analysis-content-right ul li {
    display: inline-block;
    padding: 0 5px;
    height: auto;
    vertical-align: middle;
    position: relative;
}
.analysis-content-right ul li:nth-child(1) {
    border: none !IMPORTANT;
    padding: 0;
}

.analysis-content-right ul li:last-child {
    border-right: 0;
    padding-right: 0;
}

.analysis-content-right ul li .chngDate {
    display: inline-block;
    /* vertical-align: sub; */
    position: relative;
    top: 0;
    cursor: pointer;
}
.yearMonth-toggle{
    position: relative;
    top: 0;
}
.analysis-content-right ul li .chngDate i {
    padding-left: 5px;
    vertical-align: baseline;
}

.analysis-sample-header {
    display: flex;
    justify-content: space-between;
}

.analysis-sample-header-left {
    position: relative;
}

.analysis-sample-header-left p {
    font-size: 16px;
}

.analysis-sample-header-right {
    display: flex;
    align-items: center;
}

.analysis-title {
    height: 25px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.analysis-sample-body {
    display: flex;
    justify-content: space-evenly;
}

.dsh-view .analysis-sample-header-left {
    width: 82%;
}

.analysis-sample-header {
    padding-left: 15px;
    padding-right: 15px;
}

.analysis-sample-body .left-icon span i {
    font-size: 32px;
}

.analysis-sample-body .right-icon span i {
    font-size: 32px;
    color: #d3d3d3;
}

.mid svg.circle-progress {
    height: 150px;
    width: 150px;
}

.analysis-sample-footer {
    padding: 0 !important;
}

.analysis-sample-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.analysis-sample-list li {
    text-align: center;
    display: inline-block;
    cursor: pointer;
    width: 32%;
    border-right: 1px solid #f1f1f1;
}

.analysis-sample-list li:last-child {
    border-right: 0;
}

.analysis-sample .card-body {
    padding: 10px;
}

.analysis-sample-list li span {
    display: inline-block;
    padding: 10px 0;
    width: 100%;
    color: #2fab66;
}

.analysis-sample-list-content,
.boxInfo-content{
    display: none;
    width: 100%;
    background-color: #fff;
}

.analysis-sample-list li span.active {
    background-color: #02bc77;
    color: #fff;
    position: relative;
    width: 100%;
}

.analysis-sample-list li span.active:after {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    background-color: #02bc77;
    bottom: -8px;
    left: 42%;
    transform: rotate(45deg);
    z-index: 1;
}

.analysis-sample-list-content-heading {
    max-width: 235px;
    font-weight: 600;
}

.analysis-sample-list-content .card-body p {
    max-height: 100px !important;
    overflow: inherit;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: #93b7af white;
    scrollbar-width: thin;
    text-align: justify;
    padding: 0 5px;
}

.analysis-sample-list-content .card-body p::-webkit-scrollbar {
    width: 5px;
}

.analysis-sample-list-content .card-body p::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.analysis-sample-list-content .card-body p::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}

.info-des::-webkit-scrollbar {
    width: 5px;
}

.info-des::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.info-des::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    outline: 1px solid slategrey;
}

.info-des p {
    font-size: 14px;
}

.info-des {
    position: absolute;
    width: 225px;
    background-color: #fff;
    z-index: 9;
    border-radius: 5px;
    border: 1px solid #02bc77;
    padding: 14px;
    display: none;
    height: 267px;
    overflow-y: scroll;
    scrollbar-color: #93b7af white;
    scrollbar-width: thin;
}

.info.actv {
    position: relative;
}

.info.actv::after {
    position: absolute;
    top: 23px;
    left: -2px;
    content: "";
    height: 20px;
    width: 20px;
    background-color: #02bc77;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
}

.analysis-progressbar .left-icon span i {
    font-size: 32px;
    color: #808080;
}

.analysis-progressbar .right-icon span i {
    font-size: 32px;
    color: #d3d3d3;
}

.analysis-progressbar .analysis-title {
    height: 25px;
    overflow: hidden;
    max-width: 270px;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.analysis-progressbar-sample-list {
    margin-top: 12px;
    padding: 0;
    list-style: none;
}

.analysis-progressbar-sample-list li {
    display: inline-block;
    margin: 0 6px;
    position: relative;
}

.analysis-progressbar-sample-list li>span {
    padding: 5px 10px;
}

.analysis-progressbar-sample-list li>span:hover {
    background-color: #02a065;
    color: #fff;
    cursor: pointer;
    border-radius: 3px;
}

.analysis-progressbar .analysis-sample-header {
    margin-top: 15px;
}

.analysis-progressbar .progress {
    margin-top: 15px;
}

.analysis-progressbar .left-icon {
    margin-top: 15px;
}

.analysis-progressbar .progress,
.systematic-view .progress,
.chakra .progress {
    border-radius: 5px !important;
}

.switcher-sm,
.form-group-sm .switcher {
    min-height: 1.375rem !important;
}

.analysis-progressbar {
    /* overflow-x: scroll; */
}

.analysis-progressbar table {
    min-width: 736px;
}

.analysis-progressbar .analysis-sample-list-content {
    position: absolute;
    width: 225px;
    top: 40px;
    right: 0;
    padding: 10px;
    text-align: left;
    background-color: #fff;
    display: none;
    z-index: 1;
    border: 1px solid #2fab66;
    border-radius: 5px;
    max-height: 250px;
    overflow-y: scroll;
    scrollbar-color: #93b7af white;
    scrollbar-width: thin;
}

.analysis-progressbar .analysis-sample-list-content::-webkit-scrollbar {
    width: 5px;
}

.analysis-progressbar .analysis-sample-list-content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.analysis-progressbar .analysis-sample-list-content::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}

.analysis-progressbar-sample-list li span {
    position: relative;
    display: inline-block;
    color: #02BC77;
}

.analysis-progressbar-sample-list li span.actv::after {
    position: absolute;
    top: 35px;
    right: 26%;
    content: "";
    height: 20px;
    width: 20px;
    background-color: #02bc77;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
}

.analysis-progressbar span.m-minus {
    margin-bottom: -18px;
}

.analysis-progressbar .right-icon {
    margin-top: 10px;
}

@-webkit-keyframes progress {
    from {
        width: 0;
    }

    to {
        width: 100%;
    }
}

@keyframes progress {
    from {
        width: 0;
    }

    to {
        width: 100%;
    }
}

@-webkit-keyframes progress1 {
    from {
        width: 100%;
    }

    to {
        width: 0;
    }
}

@keyframes progress1 {
    from {
        width: 100%;
    }

    to {
        width: 0;
    }
}

@-webkit-keyframes show {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes show {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.analysis-content .progress-bar,
.chakra .progress-bar {
    width: 0;
    -webkit-animation: progress 3s ease-in-out forwards;
    animation: progress 3s ease-in-out forwards;
}

.analysis-content .progress-bar .title,
.chakra .progress-bar .title {
    opacity: 0;
    -webkit-animation: show 3s forwards ease-in-out 3s;
    animation: show 0.5s forwards ease-in-out 0.5s;
}

.info-pr .progress-bar {
    width: 0;
    -webkit-animation: progress 5s ease-in-out forwards;
    animation: progress 5s ease-in-out forwards;
}

.info-pr .progress-bar .title {
    opacity: 0;
    -webkit-animation: show 5s forwards;
    animation: show 5s forwards;
}

.analysis-content .progress,
.systematic-view .progress,
.chakra .progress {
    background-color: rgba(0, 0, 0, 0.10);
    height: 25px;
}

.left-icon .male-icon i {
    font-size: 40px !important;
}

/*=====================
        User Css
=======================*/

.user input {
    width: 300px !important;
}
.custom_user input{
    width: 100% !important;
}
.user input[type=radio] {
    width: auto !important;
}

.user .select2-container {
    width: 300px !important;
}

.user .select2-search--dropdown .select2-search__field {
    width: 100% !important;
}

.user select {
    width: 300px !important;
}
.custom_user select {
    width: 100% !important;
}

label.lb-1 {
    width: 23% !important;
}

label.lb-100 {
    width: 100px !important;
}
.l-100{
    width: 100% !important;
}
.l-150{
    width: 150px !important;
}
.l-100-px{
    width: 122px !important;
}
label.lb-150 {
    width: 150px !important;
}

label.lb-101 {
    width: 160px;
}

label.lb-250 {
    width: 250px;
}

.user label {
    display: inline-block;
}

/*==========================
        User List Css
============================*/

#user-list_length {
    margin-bottom: 20px;
}

#user-list_info,
#user-list_paginate {
    margin-top: 20px;
}

#user-list .act-btn {
    display: inline-block;
    margin: 0 5px;
}

#user-list td {
    padding: 10px 12px;
    vertical-align: middle !important;
}

.modal#shareModal .modal-header p {
    background-color: #fcf8e3;
    padding: 10px;
}

.modal#shareModal .lg {
    width: 100% !important;
}

.modal#shareModal .form-group.form-inline label {
    margin-right: 10px;
}

.modal#shareModal .modal-header .close,
.modal#shareModal .modal-slide .close {
    top: 10% !important;
}

/*======================
        Corn Css
========================*/

.create-corn .card-footer {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.create-corn .card-footer button {
    width: 25%;
}

.corndraft .sh-date:hover {
    color: #2fab66;
}

.corndraft .sh-date {
    cursor: pointer;
}

.corndraft .dropdown-list-item {
    height: 200px;
    overflow-y: scroll;
    scrollbar-color: darkgrey white;
    scrollbar-width: thin;
}

.corndraft .dropdown-list-item::-webkit-scrollbar {
    width: 5px;
}

.corndraft .dropdown-list-item::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.corndraft .dropdown-list-item::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}

.corndraft button#dropdownMenuButton {
    padding: 8px 13px !important;
    width: 100%;
    text-align: left;
}

.corndraft .dropdown .dropdown-menu {
    width: 100%;
}

.corndraft .dropdown-toggle::after {
    margin-left: 70% !important;
}

#crnDraftModal>.modal-dialog {
    margin: auto;
    max-width: 100%;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    padding: 0 1%;
}
.crn-modal .modal-dialog {
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.corn-setting .card-footer button {
    width: 30% !important;
}

#preview-modal .modal-content {
    width: 700px !important;
}

.hover-green:hover {
    background-color: #02a065;
    color: #fff;
}

.hover-red:hover {
    background-color: red;
    color: #fff;
}

div.mce-edit-area {
    height: 200px !important;
}
.mce-edit-area iframe{
    height: 100% !important;
}

/*===================================
        Systematic View Css
=====================================*/

.systematic-view .widget-three {
    background-color: #f3f3f3;
    padding: 20px 15px;
    margin-top: 22px !important;
    height: 100%;
    position: relative;
    padding-bottom: 65px;
    min-height: 385px;
    z-index: 1;
}
.widget-three-button {
    position: absolute;
    content: '';
    width: 92%;
    bottom: -55px;
}

.widget-three-button-default {
    bottom: -84px !important;
}

.widget-three-button-x {
    top: 305px !important;
}
.systematic-view .analysis-sample-list-content,
.chakra .analysis-sample-list-content {
    position: absolute;
    width: 225px;
    top: 25px;
    right: 0;
    padding: 10px;
    text-align: left;
    background-color: #fff;
    display: none;
    z-index: 1;
    border: 1px solid #2fab66;
    border-radius: 5px;
}

.systematic-view .info-des,
.chakra .info-des {
    top: 30px;
    left: 0;
}

.main-sys-view {
    position: relative;
    background-color: #f3f3f3;
    min-height: 93%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    /* padding-bottom: 70px; */
    padding-top: 20px;
}

.direction_btn1 {
    position: absolute;
    content: '';
    top: 15px;
    right: 15px;
    z-index: 1;
}

.direction_btn {
    position: absolute;
    content: '';
    top: 15px;
    right: 70px;
    z-index: 2;
}

.direction_btn_default {
    position: absolute;
    content: '';
    top: 15px;
    right: 15px;
    z-index: 2;
}

.direction_btns {
    position: absolute;
    content: '';
    top: 53px;
    right: 15px;
    z-index: 1;
}

.direction_btn4 {
    position: absolute;
    content: '';
    top: 91px;
    right: 15px;
    z-index: 1;
}

.main-sys-view-image {
    height: 100%;
    width: 100%;
    position: relative;
    margin-bottom: 70px;
}

.main-sys-view-image .box {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
}

.main-sys-view-image .box img {
    height: auto !important;
    width: auto !important;
}

/* .main-sys-view-image img {
    height: 100%;
    width: 100%;
} */

/* .widget-two {
    min-height: 55%;
} */

.kindMH-default {
    padding-top: 32px;
}

.widget-three-image img.baby-default {
    padding-top: 56px;
}

.widget-two-box-content {
    width: 100%;
    height: 120px;
    margin-bottom: 8px;
    overflow: hidden;
    border: 1px solid #ccc;
    display: none;
}

.widget-two-box {
    border: 1px solid #efefef;
    min-height: 380px;
}

.widget-two-box-content-inner {
    padding: 10px;
    height: 100%;
    width: 100%;
    background-color: #02a065;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    text-align: center;
}

.widget-two-box-content-inner.pdf-ready {
    padding: 5px !important;
}

.widget-two-box-content-plusIcon {
    position: absolute;
    top: 5px;
    right: 10px;
    cursor: pointer;
}

.widget-two-box-content-plusIcon.pdf-ready {
    top: 3px !important;
    right: 3px !important;
}

.widget-two-box-content-plusIcon i {
    color: #fff;
    font-size: 16px;
    margin-left: 2px;
}

.kindM svg, .Medium svg, .kindM_foot svg{
    padding-top: 16px;
    padding-bottom: 16px;
}
.kindw svg, .kindw_foot svg{
    padding-top: 16px;
    padding-bottom: 16px;
}
.baby svg, .Small svg, .baby_foot svg{
    padding-top: 28px;
    padding-bottom: 28px;
}

@-webkit-keyframes grow {
    0% {
        -webkit-transform: scale(0);
        -moz-transform: scale(0);
        -o-transform: scale(0);
        -ms-transform: scale(0);
        transform: scale(0);
    }

    50% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }

    100% {
        -webkit-transform: scale(0);
        -moz-transform: scale(0);
        -o-transform: scale(0);
        -ms-transform: scale(0);
        transform: scale(0);
    }
}

/* @keyframes grow {
    0% {
        -webkit-transform: scale(0);
        -moz-transform: scale(0);
        -o-transform: scale(0);
        -ms-transform: scale(0);
        transform: scale(0);
    }

    100% {
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }
} */

.styled-font {
    font-size: 14px;
    color: #000;
}

.info-test-content-box-two {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ccc;
    display: none;
}

.info-test-content-box-two-inner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.info-test-content-box-two .progress {
    height: 10px !important;
    width: 380px !important;
}

.info-test-content-box {
    height: 135px;
}

.info-button button {
    width: 160px;
}

.widget-two-box-content p {
    margin-bottom: 0;
    color: #fff;
    font-size: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

#txt {
    /* animation: textareaAnimation 4s ease-in-out; */
    /* animation: textareaAnimation2 4s ease-in-out; */
    /* animation: textareaAnimation4 4s ease-in-out; */
    /* animation: textareaAnimation1 4s ease-in-out; */
    /* animation: textareaAnimation3 4s ease-in-out; */
    resize: none;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    text-align: center;
}

#txt2 {
    /* animation: textareaAnimation 4s ease-in-out; */
    /* animation: textareaAnimation2 4s ease-in-out; */
    /* animation: textareaAnimation4 4s ease-in-out; */
    /* animation: textareaAnimation1 4s ease-in-out; */
    /* animation: textareaAnimation3 4s ease-in-out; */
    resize: none;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    text-align: center;
}

@keyframes textareaAnimation {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes textareaAnimation1 {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes textareaAnimation2 {
    0% {
        transform: scaleX(1);
    }

    50% {
        transform: scaleX(0);
        border-radius: 100%;
    }

    100% {
        transform: scaleX(1);
    }
}

@keyframes textareaAnimation3 {
    0% {
        transform: scale(1);
    }

    10% {
        transform: scale(0.7);
    }

    20% {
        transform: translate(100px, 0) scale(0.7);
    }

    30% {
        transform: scale(0.5);
    }

    40% {
        transform: translate(100px, 10px) scale(0.5);
    }

    50% {
        transform: scale(0.3);
    }

    60% {
        transform: translate(80px, 10px) scale(0.3);
    }

    70% {
        transform: scale(0.1);
    }

    80% {
        transform: translate(60px, -20px) scale(0.1);
    }

    100% {
        transform: scale(0);
    }
}

@keyframes textareaAnimation4 {
    0% {
        max-height: 100%;
    }

    50% {
        max-height: 0;
    }

    100% {
        max-height: 100%;
    }
}

.imgdiv {
    color: #333;
    position: absolute;
    text-align: center;
    cursor: pointer;
    /* background-color: red; */
    animation: movement 7s linear;
    z-index: 2;
}

.imgdiv .default-img {
    height: auto;
    width: auto;
}

.imgdiv .other-img {
    height: 133px;
    width: auto;

}

.flipEast {
    transform: rotate(0deg) skewy(30deg) !important;
    -ms-transform: rotate(0deg) skewy(30deg) !important;
    -webkit-transform: rotate(0deg) skewy(30deg) !important;
}
.flipNorth{
    transform: scaleX(-1) skewy(30deg) !important;
}
.main-sys-view-shape {
    position: absolute;
    opacity: 0.9;
}

:root{
    --pos1: 0px;
    --pos2: 0px;
    --height: 0px;
    --width: 0px;
    --colour: #C1C1C1;
}

@keyframes positionChange {
    0% {}

    25% {
        top: 60px;
        left: 200px;
        background-color: #FF0000;
        opacity: 0.5;
    }

    50% {
        top: 120px;
        left: 250px;
        width: 100px;
        height: 70px;
        background-color: #4a90e2;
        opacity: 0.5;
    }

    75% {
        top: 80px;
        left: 120px;
        width: 100px;
        height: 70px;
    }

    100% {
        height: var(--height);
        width: var(--width);
        top: var(--pos1);
        left: var(--pos2);
        background-color: #4a90e2;
    }
}

@keyframes positionChange1 {
    0% {}

    25% {
        top: 50px;
        right: 220px;
    }

    50% {
        width: 80px;
        height: 100px;
        top: 180px;
        right: 220px;
        background-color: #4a90e2;
        opacity: 0.5;
    }

    75% {
        width: 70px;
        height: 50px;
        top: 180px;
        right: 120px;
        background-color: #4a90e2;
    }

    100% {

        height: var(--height);
        width: var(--width);
        top: var(--pos1);
        right: var(--pos2);
        background-color: #FF0000;
    }
}

@keyframes positionChange2 {
    0% {}

    25% {
        bottom: 104px;
        left: 250px;
        background-color: #4a90e2;
    }

    50% {
        height: 200px;
        width: 150px;
    }

    75% {
        bottom: 120px;
        left: 240px;
        background-color: #FF0000;
        opacity: 0.5;
    }

    100% {
        height: var(--height);
        width: var(--width);
        bottom: var(--pos1);
        left: var(--pos2);
        background-color: #2fab66;
    }
}

@keyframes positionChange3 {
    0% {}

    25% {
        bottom: 250px;
        right: 274px;
        width: 150px;
        height: 150px;
    }

    50% {
        bottom: 250px;
        right: 350px;
        background-color: #FF0000;
        opacity: 0.5;
    }

    75% {
        bottom: 100px;
        right: 300px;
        width: 150px;
        height: 100px;
        background-color: #4a90e2;
    }

    100% {
        height: var(--height);
        width: var(--width);
        bottom: var(--pos1);
        right: var(--pos2);
        background-color: #4a90e2;
        opacity: 0.5;
    }
}

@keyframes positionChange4 {
    0% {}

    25% {
        top: 50px;
        right: 173px;
        background-color: #FF0000;
        opacity: 0.5;
    }

    50% {
        top: 50px;
        right: 173px;
        background-color: #FF0000;
    }

    75% {
        top: 50px;
        right: 290px;
        background-color: #4a90e2;
        width: 100px;
        height: 130px;
        opacity: 0.5;
    }

    100% {

        height: var(--height);
        width: var(--width);
        top: var(--pos1);
        right: var(--pos2);
        background-color: #2fab66;
        opacity: 0.5;
    }
}

@keyframes positionChange5 {
    0% {}

    25% {
        bottom: 300px;
        left: 43px;
        background-color: #4a90e2;
        opacity: 0.5;
    }

    50% {
        bottom: 300px;
        left: 100px;
        width: 50px;
        height: 70px;
        background-color: #4a90e2;
    }

    75% {
        bottom: 200px;
        left: 300px;
        width: 60px;
        height: 70px;
        background-color: #FF0000;
    }

    100% {

        height: var(--height);
        width: var(--width);
        bottom: var(--pos1);
        left: var(--pos2);
        background-color: #FF0000;
        opacity: 0.5;
    }
}

@keyframes positionChange6 {
    0% {}

    25% {
        background-color: #FF0000;
    }

    50% {
        top: 160px;
        left: 350px;
        height: 100px;
        width: 40px;
    }

    75% {
        width: 100px;
        height: 100px;
        background-color: #4a90e2;
    }

    100% {
        height: var(--height);
        width: var(--width);
        top: var(--pos1);
        left: var(--pos2);
        background-color: #FF0000;
        opacity: 0.5;
    }
}

@keyframes positionChange7 {
    0% {}

    25% {
        bottom: 300px;
        right: 175px;
        background-color: #4a90e2;
        opacity: 0.5;
    }

    50% {
        width: 90px;
        height: 90px;
    }

    75% {
        width: 140px;
        height: 60px;
        bottom: 220px;
        right: 325px;
        background-color: #FF0000;
        opacity: 0.5;
    }

    100% {
        height: var(--height);
        width: var(--width);
        bottom: var(--pos1);
        right: var(--pos2);
        background-color: #4a90e2;
    }
}

.widget-three-image span {
    height: 150px;
}

#infovalue {
    height: 50%;
}

#infovalue2 {
    height: 50%;
}

.sys-view-male i,
.sys-view-female i {
    font-size: 96px;
    padding-top: 44px;
    color: #000;
}

.sys-view-male-big i,
.sys-view-female-big i {
    font-size: 128px;
    color: #000;
    padding-top: 12px;
}

.widget-content {
    position: relative;
}

/* .dc-preview-right-view{
    min-height: 200px;
} */

#preview-title {
    min-height: 25px;
}

.btn-append {
    margin-top: 15px;
    /* display: flex; */
    justify-content: right;
    min-height: 35px;
}

.infotest-content {
    position: absolute;
    background-color: #f3f3f3;
    padding: 10px;
    display: none;
    z-index: 50;
    top: -456px;
    left: 0;
    width: 100%;
    height: 455px;
    right: 0;
    bottom: 0;
}

.infotest-content-default {
    position: absolute;
    background-color: #f3f3f3;
    padding: 10px;
    display: none;
    z-index: 50;
    top: -22px;
    left: 0;
    width: 100%;
    height: 290px;
    right: 0;
    bottom: 0;
}

.widget-three-button .crcl {
    display: inline-block;
    height: 45px;
    width: 45px;
    border-radius: 50%;
    background-color: #808080;
    border: 1px solid #2f3337;
    z-index: 999;
}

.append-crcl {
    position: absolute !important;
    height: max-content !important;
    width: max-content !important;
    cursor: pointer;
}

.append-crcl .crcl {
    display: inline-block;
    height: 30px;
    width: 30px;
    border-radius: 50%;
    background-color: #333;
    border: 1px solid #2f3337;
    z-index: 999;
    text-align: center;
    color: white;
    padding-top: 15%;
}

.widget-three-image img {
    padding-bottom: 5px;
}

.widget-three-image img.sm {
    padding-top: 32px;
}

.imgdiv .iconspan {
    position: relative;
    display: inline-block;
}

.imgdiv .popname {
    position: absolute;
    top: -37px;
    left: 47%;
    background-color: #fff;
    color: #000;
    padding: 5px 12px;
    font-size: 14px;
    border-radius: 4px;
    z-index: 1;
    transform: translateX(-50%);
    text-align: center;
    width: max-content;
}

.imgdiv .popname::after {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    background-color: #fff;
    top: 22px;
    left: 50%;
    transform: rotate(45deg) translateX(-50%);
    z-index: -9;
}

.widget-input-box-one {
    height: 50px;
    width: 115px;
    display: inline;
    margin-top: 10%;
    box-shadow: 0 0 1px 1px #b1b1b1;
    text-align: center;
}

.widget-input-box-two {
    height: 85px;
    width: 150px;
    box-shadow: 0 0 1px 1px #ccc;
    /* background-color: #efefef47; */
    background-color: #ccc;
    position: relative;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ccc;
}

.widget-input-box-two-input {
    height: 50px;
    width: 115px;
    box-shadow: 0 0 1px 1px #f1f1f1;
    text-align: center;
}

.inputbg2 {
    height: 50px;
    width: 96px;
    box-shadow: 0 0 1px 1px #ccc;
    /* background-color: #efefef47; */
    background-color: #ccc;
    position: relative;
    border-radius: 50%;
    display: inline-block;
    border: 1px solid #ccc;
}

.inputbg2>span {
    height: 30px;
    box-shadow: 0 0 1px 1px #ccc;
    position: absolute;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
}

#preview-icon svg {
    height: 100%;
    width: auto;
}

.dcicons svg {
    height: 133px;
    width: auto;
}

/*===================================
            Group List Css
=====================================*/
.group-list td {
    padding: 15px 10px !important;
}

#editGroupModal .modal-dialog,
#addGroupModal .modal-dialog {
    margin: 70px auto !important;
}

/*======================
        Menu Css
========================*/
.menu-list td {
    padding: 15px 10px !important;
}

.default-style .account-settings-links .list-group-item.active {
    color: #fff !important;
    background: #26b4ff !important;
}

/*===========================
        Own Analysis Css
=============================*/

.own-analysis td {
    padding: 15px 10px !important;
}

/*=========================
        Chakra Css
==========================*/

.chakra-content-list .left-icon span:first-child {
    font-size: 30px;
}

.chakra-content-list .left-icon span:last-child {
    font-size: 22px;
}

.chakra-content-list .dropdown-submenu {
    position: relative;
}

.chakra .card-header {
    min-width: 690px;
}

.chakra-content-list .dropdown-menu>li>a:hover,
.chakra-content-list .dropdown-menu>li>a:focus,
.chakra-content-list .dropdown-submenu:hover>a,
.chakra-content-list .dropdown-submenu:focus>a {
    background-color: #02a065 !important;
    background-image: none;
    color: #fff;
}

.chakra-content-list li.list-one {
    margin-top: -7px;
}

.chakra-content-list .dropdown-menu {
    min-width: 7rem !important;
}

.chakra-content-list .dropdown-submenu:hover .dropdown-menu,
.chakra-content-list .dropdown-submenu:focus .dropdown-menu {
    display: flex;
    flex-direction: column;
    position: absolute !important;
    left: 104%;
}

.chakra-content-list .dropdown-submenu a {
    text-align: left;
    background: #fff;
}

.chakra-content-list .dropdown-submenu .dropdown-menu {
    position: relative;
    width: 225px;
    background: none;
    box-shadow: none;
    outline: 0;
    border: none;
}

.chakra-content-list .dropdown-submenu .dropdown-menu::after {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    background-color: #fff;
    top: 10px;
    right: 98%;
    transform: rotate(45deg);
    z-index: -1;
}
.list-two  .dropdown-submenu .dropdown-menu::after {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    background-color: #fff !important;
    top: 10px;
    left: 98% !important;
    transform: rotate(45deg);
    z-index: -1;
}

.chakra-content-list .dropleft .dropdown-submenu a::before {
    display: none;
}

.chakra-content-list .dropdown-submenu:hover .dropdown-menu.drp-left,
.chakra-content-list .dropdown-submenu:focus .dropdown-menu.drp-left {
    display: flex;
    flex-direction: column;
    position: absolute !important;
    right: 100%;
}

.ch-cntn {
    width: 225px;
    padding: 10px;
    text-align: left;
    max-height: 205px;
    background-color: #fff;
    z-index: -1;
    border: 1px solid #2fab66;
    border-radius: 5px;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    margin-left: -8px;
    margin-top: -1px;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}
.list-two .ch-cntn {
    direction: rtl !important;
    position: relative;
    left: 7px;
    top: -1px;
    margin-left: 0;
    margin-top: 0;
}
.list-two .temp_dis {
    left: 10px !important;
    right: unset;
}
.ch-cntn p.d-flex {
    position: relative;
    width: 180px;
    text-align: right!important;
}
.temp_dis{
    position: absolute;
    content: '';
    right: 20px;
    top: 18px;
}
.ch-cntn::-webkit-scrollbar{
    width: 5px;
}

.ch-cntn::-webkit-scrollbar-track{
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
}

.ch-cntn::-webkit-scrollbar-thumb{
    background-color: #2fab66;
}
.chakra-body {
    position: relative;
    z-index: 1;
    min-width: 690px;
}

.chakra-img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: -1;
}

.chakra-content-list ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.chakra-content-list ul li {
    width: 47%;
}

.chakra-content-list .analysis-progressbar-sample-list li {
    width: auto;
}

.chakra-content-list li p.chakra-list-heading {
    max-width: 200px;
    height: 20px;
    overflow: hidden;
    white-space: nowrap;
    width: 152px;
    text-overflow: ellipsis;
}

/* .ch-cntn p {
    max-width: 200px !important;
    height: auto !important;
    overflow: visible !important;
    white-space: normal !important;
} */

.chakra-content {
    padding-top: 46px;
    /* min-width: 680px;
    overflow-x: scroll; */
}

.chakra-view-page.chakra-content {
    padding-top: 30px;
    background-image: url("/images/cakra-new.png");
    background-size: contain;
    background-repeat: no-repeat;
    text-align: center;
    background-position: center center;
}

/* .col-xl-5.dis-w-100 {
    max-width: 100%;
} */
.chakra .card {
    /* overflow: hidden;
    overflow-x: scroll; */
    margin-bottom: 170px;
    padding-bottom: 33px;
}

.chakra-content-list ul li.list-two,
.chakra-content-list ul li.list-four,
.chakra-content-list ul li.list-six {
    margin-top: -14px;
}

.chakra-content-list ul li.list-three,
.chakra-content-list ul li.list-five,
.chakra-content-list ul li.list-seven {
    margin-top: -20px;
}

.chakra .analysis-progressbar-sample-list li {
    margin: 0 0px;
}

.chakra span.m-minus {
    margin-bottom: -18px;
}

.right-icon span:first-child i {
    color: #888;
}

.right-icon span:last-child i {
    color: #d3d3d3;
}

.chakra-content-list .dropleft .dropdown-submenu:hover .dropdown-menu,
.chakra-content-list .dropleft .dropdown-submenu:focus .dropdown-menu {
    right: 149%;
    left: -232px;
}

.chakra-content-list .dropleft .dropdown-submenu .dropdown-menu::after {
    top: 10px;
    left: 97%;
}

/*=========================
    Package List Css
============================*/

.package-list-box {
    margin-bottom: 20px;
}
.package-list-box .card-body{
    padding-right: 0px !important;
}
.package-list-box .card-body ul {
    height: 146px;
    overflow-y: auto ;
    scrollbar-color: #a9a9a9 white;
    scrollbar-width: thin;
}

.package-list-box .card-body ul::-webkit-scrollbar {
    width: 5px;
}

.package-list-box .card-body ul::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.package-list-box .card-body ul::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    outline: 0px !important;
}

#note-modal .modal-content {
    width: 300px;
}

#note-modal .modal-dialog {
    margin-top: 70px;
    margin: 32px;
}

#note-modal .modal-header,
#note-modal .modal-footer {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}

/*====================
    Treat Css
====================== */

section.treat {
    margin-top: -21px;
    margin-left: -29px;
    padding: 0;
    margin-right: -29px;
    position: relative;
}

.treat-main-content {
    height: 92vh;
}

/* .treat-main-content-image {} */

.ap-disk-img {
    height: 475px;
    width: 475px;
    border-radius: 50%;
    opacity: 0.5;
    /* animation: rotating 1s linear infinite; */
}

.right-panel .card {
    height: 80vh;
}

@-webkit-keyframes rotating {
    from {
        -webkit-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes rotating {
    from {
        -ms-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -ms-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.audio-player {
    width: 152px;
    height: 152px;
    border-radius: 50%;
    margin: 0 auto;
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #fff;
    transform: translate(-50%, -50%);
    overflow: hidden;
    z-index: 1;
}

.audio-player-name {
    position: absolute;
    left: 50%;
    width: max-content !important;
    display: flex;
    justify-content: center;
    border-radius: 5px;
    padding: 5px 15px;
    color: #fff;
    font-size: 18px;
    top: 335px;
    transform: translate(-50%, 10px);
    z-index: 1;
}

#analysis_name {
    padding: 10px 15px;
    border-radius: 5px;
    max-width: 550px;
    overflow: hidden;
    white-space: normal;
}
.farbklang-name{
    opacity: 0.9;
}

.analysis_price {
    width: 100%;
    text-align: center;
    font-size: 30px;
    color: #fff;
    position: absolute;
    bottom: 10%;
}

.analysis_box {
    background-color: rgba(0, 0, 0, 0.38);
    position: absolute;
    width: 100%;
    top: 53%;
    height: 51%;
    border-radius: 0px 0px 160px 160px;
}

.audio-player img {
    width: 100%;
}

.treat-main-content-image-inner {
    position: relative;
    z-index: 99;
}

.treat-main-content-animation {
    height: 100vh;
    text-align: center;
    background: linear-gradient(270deg, #f33f09, #09f345, #0918f3, #f309b7);
    background-size: auto;
    background-size: 800% 800%;
    -webkit-animation: movebg 30s infinite;
    -moz-animation: movebg 30s infinite;
    -o-animation: movebg 30s infinite;
    animation: movebg 30s infinite;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

@-webkit-keyframes movebg {
    0% {
        background-position: 0% 50%;
    }

    25% {
        background-position: 50% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@-moz-keyframes movebg {
    0% {
        background-position: 0% 50%;
    }

    25% {
        background-position: 50% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@-o-keyframes movebg {
    0% {
        background-position: 0% 50%;
    }

    25% {
        background-position: 50% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes movebg {
    0% {
        background-position: 0% 50%;
    }

    25% {
        background-position: 50% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.right-panel .card-body {
    height: 100%;
    padding-right: 5px;
    padding-top: 5px;
    padding-bottom: 5px;
}

.right-panel-button {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 5px;
}

.right-panel-button>div button {
    width: 49%;
    background: #fff;
}

.right-panel-list {
    max-height: 88%;
    overflow-y: auto;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}

.right-panel-list::-webkit-scrollbar {
    width: 5px;
}

.right-panel-list::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.right-panel-list::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}
#playlist li p {
    white-space: nowrap;
    text-overflow: ellipsis !important;
    width: 100% !important;
    overflow: hidden;
    padding-right: 10px;
    margin-bottom: 10px;
}
/* Amin Edited CSS */

a {
    outline: none !important;
}

.card-background {
    border-radius: 4px !important;
    background: rgba(255, 255, 255, 0.5) !important;
}

.menu-right-custom {
    width: 16rem;
    font-size: 1rem;
}

.custom-select-box {
    width: 205px !important;
}

.dashboardSubmenu {
    text-decoration: none;
    color: #333;
}
.dashboardSubmenu:hover {
    color: #2fab66;
    transition: .5s;
}

audio {
    /* display: none; */
    opacity: 0;
}
.own-audio input{
    margin-bottom: 2%;
}
.own-audio audio{
    display: inline-block !important;
}
.own-audio button{
    margin-top: -35px;
}

.list-item.active p,
.list-item.active:focus p,
.list-item.active:hover p {
    color: #02bc77 !important;
    font-weight: bold !important;
}

.products-progressbar .nav-tabs .nav-item {
    margin-right: 3px;
    margin-bottom: 5px;
    border: 1px solid #eae5e5;
}

#optimizedModal .modal-dialog {
    box-shadow: none !important;
}

.products-progressbar .nav-tabs .nav-link.active,
.products-progressbar .nav-tabs .nav-link {
    border: 0 !important;
}

.products-progressbar .nav-tabs .nav-link.active,
.products-progressbar .nav-tabs .nav-link {
    margin-right: 0 !important;
}

.products-progressbar .nav-tabs .dropdown-menu {
    top: 0 !important;
    left: 100% !important;
}

.products-progressbar .progress {
    height: 30px;
    border-radius: 2px;
    border: 2px solid #dddddd;
    box-shadow: 0 5px 17px rgba(196, 196, 196, 0.5),
        0 0 3px 1px rgba(255, 255, 255, 0) inset;
    padding: 1px 0;
    background-color: #fff;
}

.products-progressbar .progress .progress-bar {
    border-radius: 0px;
}

.pro-progress-title{
    font-size: 1rem;
    font-weight: bold;
}

.menu-active {
    color: #2fab66;
    font-weight: bold;
}

.inputbg {
    max-width: 80px !important;
    height: 29px;
    border-radius: 4px;
    min-width: 80px;
    padding: 5px 5px;
    background-color: #fff;
    box-shadow: 0 0 1px 1px #ccc;
    overflow: hidden;
}

#trash {
    height: 100px;
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    bottom: -10px;
    position: absolute;
    right: 10px;
}

.duePowerdivs {
    display: none;
}
.dropdown.bootstrap-select.show-tick {
    height: 38px !important;
}

.freqtime {
    display: none;
}

#my-div {
    height: 590px;
    position: relative;
    margin-top: 50px;
    overflow-y: hidden;
}

#my-div iframe {
    position: absolute;
    top: -148px;
    left: 0;
    height: 140%;
    overflow-x: scroll;
    min-width: 966px;
    width: 100%;
}

.mypac-div iframe{
    top: -95px !important;
}
.analysis-sample-body .circle-progress-text {
    font-weight: 600 !important;
}
.minus-button {
    display: flex;
    position: relative;
    top: -20px;
    right: 0px;
}
.dmul-content p {
    margin: 0;
    /* width: 84%; */
    display: block;
    white-space: break-spaces;
    overflow: hidden;
    color: #4E5155;
    font-size: 16px;
    font-weight: 400;
    text-transform: capitalize;
}
.dropdown-menu-user-list {
    position: relative !important;
    padding: 0px 9px;
    height: auto;
}
.dmul-content {
    position: relative;
    content: '';
    top: -25px !important;
    padding-left: 34px !important;
    width: auto;
    height: 100%;
}

.btn-circle {
    width: auto;
    height: 20px;
    padding: 5px 0px;
    border-radius: 8px;
    text-align: center;
    font-size: 10px;
    line-height: 1.42857;
}

/* cron setup checkbox custom style  */
.custom-checkbox {
    width: max-content;
    cursor: pointer !important;
    transition: .5s;
}

.custom-checkbox:hover .custom-control-label {
    color: #2fab66;
    transition: .5s;
}

.card-footer button {
    margin-right: 4px;
    margin-bottom: 8px;
    font-size: 12px;
    padding: 5px 15px;
}

.show_search_result .fa.fa-minus-circle.text-danger {
    position: relative;
    right: -6px;
    z-index: 999;
}

.modal-footer button {
    font-size: 13px;
    line-height: 18px;
}

.boxHr{
    margin: 8px 0 0;
}
.popover-body {
    padding: 0.625rem 0.75rem;
    color: #4E5155;
    max-height: 300px;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}

.popover-body h6 {
    font-size: 16px;
}

.popover-body p {
    font-size: 14px;
}

#pre_title{
    font-size: 20px;
}
#pre_analyses{
    max-height: 180px;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}

#pre_analyses::-webkit-scrollbar,
.crn-modal .modal-content .modal-body::-webkit-scrollbar,
.ShowData_Modal .modal-content .modal-body::-webkit-scrollbar{
    width: 5px;
}
#pre_analyses::-webkit-scrollbar-track,
.crn-modal .modal-content .modal-body::-webkit-scrollbar-track,
.ShowData_Modal .modal-content .modal-body::-webkit-scrollbar-track{
    border-radius: 10px !important;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.17);
}
#pre_analyses::-webkit-scrollbar-thumb,
.crn-modal .modal-content .modal-body::-webkit-scrollbar-thumb,
.ShowData_Modal .modal-content .modal-body::-webkit-scrollbar-thumb{
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
    border-radius: 10px !important;
}

.crn-modal,
.ShowData_Modal{
    height: 100%;
}

.crn-modal .modal-content,
.ShowData_Modal .modal-content{
    height: 100% !important;
    overflow: hidden;
}

.crn-modal .modal-content .modal-body,
.ShowData_Modal .modal-content .modal-body {
    max-height: 230px;
    min-height: 100px;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 15px 25px;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}

.card-content .modal-content .modal-body{
    max-height: 450px;
    min-height: 400px;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 15px 25px;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}

/* Custom loader css */
.loading {
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: center;
            justify-content: center;
  }
  .loading div {
    width: 1rem;
    height: 1rem;
    margin: 2rem 0.3rem;
    background: #2fab66;
    border-radius: 50%;
    -webkit-animation: 0.9s bounce infinite alternate;
            animation: 0.9s bounce infinite alternate;
  }
  .loading div:nth-child(2) {
    -webkit-animation-delay: 0.3s;
            animation-delay: 0.3s;
  }
  .loading div:nth-child(3) {
    -webkit-animation-delay: 0.6s;
            animation-delay: 0.6s;
  }

  @-webkit-keyframes bounce {
    to {
      opacity: 0.3;
      -webkit-transform: translate3d(0, -1rem, 0);
              transform: translate3d(0, -1rem, 0);
    }
  }

  @keyframes bounce {
    to {
      opacity: 0.3;
      -webkit-transform: translate3d(0, -1rem, 0);
              transform: translate3d(0, -1rem, 0);
    }
}
#crnDraftModal {
    z-index: 9999999999;
    padding-right: 0 !IMPORTANT;
}

.dropdown-item:not(.disabled):active i {
    color: #fff !important;
}
select#changeShowFilter {
    border: 1px solid #02a065 !important;
    color: #02a065 !important;
    height: 34px;
    line-height: 16px;
}
.analysis-content-right {
    select#longday {
        border: 1px solid #02a065 !important;
        color: #02a065 !important;
        height: 34px;
        line-height: 16px;
    }
}
.bg-navbar-theme .navbar-nav .nav-link:hover, .bg-navbar-theme .navbar-nav .nav-link:focus {
    color: #02a065 !important;
}
.dropdown-menu-right .dropdown-item:hover{
    color: #02a065 !important;
    text-decoration: none;
    background-color: rgba(24, 28, 33, 0.03);
}
.bg-footer-theme .footer-link {
    color:#02a065 !important;
}
.bg-footer-theme {
    border-top: 1px solid #02a065 !important;
}
.dropdown-item:hover i{
    color: #02a065 !important;
}
.dropdown-item:not(.disabled).active, .dropdown-item:not(.disabled):active {
    background-color: #26B4FF;
    color: #fff !important;
}
.dropdown-item:not(.disabled):active .btn-circle{
    color: #fff !important;
}
/* reeent modal z-index */
.default-style .swal2-container {
    z-index: 99998888888 !important;
}

.progress-view-table .table-bordered th, .table-bordered td {
    border: 1px solid #ccccccdb;
}
/* lock btn color danger */
/* .armdisarm {
    background: #dc3545;
    color: #fff;
    border: 0;
    padding: 5px 11px;
    border-radius: 4px;
} */

.disarmed{
    background: #dc3545 !important;
    color: #fff !important;
    border: 0 !important;
    padding: 5px 11px;
    border-radius: 4px;
}
.armdisarm{
    background: #007bff;
    color: #fff;
    border: 0;
    padding: 5px 11px;
    border-radius: 4px;
}
.armdisarm:focus {
    outline: 0;
}
.dropdown-menu-right .dropdown-item i {
    width: 18px;
}

.dropdown-menu-right .dropdown-item .btn-circle {
    position: relative;
    justify-content: flex-end;
    display: flex;
    margin-top: -8px;
    margin-right: -13px;
    font-weight: 700;
}
.dropdown-menu-right .dropdown-item .btn-circle:hover {
    background: none !IMPORTANT;
    color: #02a065 !important;
    transition: .5s;
}

.dropdown-menu-right .dropdown-item {
    position: relative;
}
.custom-control-label::before {
    border-color: #2FAB66 !important;
}
.custom_user .box-header {
    padding: .75rem 1.25rem;
    margin-bottom: 0;
    background-color: #f5f5f5;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
}

.custom_user .box-header h2 {
    font-size: 16px;
    margin-bottom: 0;
    font-weight: 400;
    letter-spacing: 0;
}
.custom_user .box-body {
    padding: .75rem 1.25rem;
    border: 1px solid #f5f5f5;
}
.note {
    margin-bottom: 0;
    font-weight: 300;
}
.pAna {
    max-height: 190px;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}
#corn_view_modla{
    max-height: 450px;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}
.custom_user .box-body input:focus {
    border-color: #2fab66 !important;
}
.activebox .box-header {
    background: #2fab66 !important;
    color: #fff;
}
.activebox .box-body {
    border-color: #2fab6682 !important;
}
.activebox .activeSubmit_btn {
    background: #2FAB67 !important;
    color: #fff;
    border-color: #2fab66 !important;
}
/* secrch preloder */
.save_prelodaer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background-color: #9c9c9c36;
    animation: movebg 30s infinite;
    opacity: 0.8;
    background-size: auto;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    padding-top: 150px;
}
.save_prelodaer2 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background-color: #9c9c9c36;
    animation: movebg 30s infinite;
    opacity: 0.8;
    background-size: auto;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    padding-top: 300px;
}
/* .focus-dropdown .dropdown-menu {
    margin-left: -120px;
} */
.focus-dropdown button#dropdownMenuButton {
    background: none;
    color: #a3a4a6;
    border: 1px solid #ddd;
    width: 40px;
    text-align: center;
    padding: 0;
    height: 36px;
}

.focus-dropdown button#dropdownMenuButton:focus {
    outline: 0;
}

.focus-dropdown .dropdown-item:focus {
    outline: 0;
}

.focus-dropdown .dropdown-item:active {
    color: #4E5155 !important;
    background-color: rgba(24, 28, 33, 0.03);
}
.modalHeader_padding{
    padding: 14px 41px 14px 25px;
}
.modalFooter_padding{
    padding: 14px 25px 14px 25px;
}
table.table-borders {border-left: 1px solid #e8e8e9 !important;border-right: 1px solid #e8e8e9 !important;}
.opensession-body .col-md-6{
    padding: 0px !important;
}
.focus-dropdown .dropdown-item:not(.disabled):active i {
    color: #4E5155 !important;
}
.blue-btn:hover,
.blue-btn:focus,
.blue-btn {
  outline: none !important;
  padding: 10px 20px;
}

.fileUpload {
  position: relative;
  overflow: hidden;
  margin-top: 0;
  width: 231px;
  white-space: normal;
  border-top: 1px solid #dddddd82;
}
.fileUpload span{
    width: 200px;
    display: block;
    white-space: normal;
}
.fileUpload input.uploadlogo {
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0;
    filter: alpha(opacity=0);
    width: 100%;
    height: 100%;
    padding: 0 20px;
    cursor: pointer;
}

.fileUpload span i {
    color: #4e515585;
    margin-right: 5px;
}
.file-alert{
    display: inline-flex;
}
.file-alert i{
    margin-left: 5px;
}
.file-icon img {
    height: 35px;
    margin-bottom: 6px;
    margin-top: 6px;
}
.focusAction_btn button {
    padding: 5px 12px;
    line-height: 18px;
}
.tabs-itemBox-Style{
    color: #4E5155;
    background-color: #fff;
    border: 1px solid #d8d1d1;
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 3px;
}
 span.select2-selection.select2-selection--single {
    height: 42px ;
    color: #4E5155;
    background-color: #fff;
    border: 1px solid #d8d1d1;
    border-radius: 3px;

}

.tab-navigation .dropdown-item i {
    width: 20px;
}
#topic_button:focus{
    border-color: #2FAB66;
    color: #2FAB66;
}
.tab-navigation .dashActive_btn{
    border-color: #2FAB66;
    color: #2FAB66;
}
.tab-navigation .dashSelectActive_btn{
    border-color: #2FAB66;
    /* color: #2FAB66; */
}
.tabs-itemBox-Style:focus {
    outline: 0;
    border-color: #2FAB66;
}
#topic_button {
    float: left;
    margin-right: 4px;
}
/* span.select2-dropdown.select2-dropdown--below {
    position: relative;
    left: -45px;
} */
.arrow2{
    display: inline-block !important;
    vertical-align: middle !important;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0);
    position: relative;
    border-color: #2FAB66;
    color: #2FAB66;
}
.arrow2:before {
    pointer-events: none;
    position: absolute;
    z-index: -1;
    content: '';
    border-style: solid;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-property: transform;
    transition-property: transfor4m;
    left: calc(50% - 8px);
    bottom: 0;
    border-width: 10px 10px 0 10px;
    border-color: #2FAB66 transparent transparent transparent;
    -webkit-transform: translateY(10px);
    transform: translateY(10px)
}
/* .arrow:hover:before{
    -webkit-transform: translateY(10px);
    transform: translateY(10px);
} */


.tab-content {
    position: relative;
}

/* .tab-content:after {
    position: absolute;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #2FAB66;
    content: '';
    top: -17px;
    left: 195px;
} */
.tab-content .card {
    position: relative;
    background-clip: padding-box;
    border-color: #02a065 !important;
    top: -1px;
}
.tab-content .arrow {
    left: calc(230px - 10px) !important;
    z-index: 1;
}

#shopping-modal {
    overflow-y: hidden !important;
    padding-right: 0 !important;
    z-index: 1111111 !important;
}

.default-style .flatpickr-calendar.open {
    z-index: 111111111 !important;
}

.tab-contents .arrow1 {
    position: absolute;
    content: '';
    top: -9px;
    left: calc(98px - 10px);
}

.tab-content .arrow3 {
    left: calc(230px - 88px) !important;
}

.tab-contents .card-body {
    max-height: 270px;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-color: #2fab66 #ddd;
    scrollbar-width: thin;
    padding: 15px 10px 15px 15px;
}

/* .tab-contents .card-body::-webkit-scrollbar{
    width: 5px;
}

.tab-contents .card-body::-webkit-scrollbar-track{
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.tab-contents .card-body::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    outline: 1px solid slategrey;
} */

.tab-contents .arrow1::before,
.tab-contents .arrow1::after {
    position: absolute;
    display: block;
    content: "";
    border-color: transparent;
    border-style: solid;
}
.tab-contents .arrow1::after{
    top: 1px;
    border-width: 0 0.5rem 0.5rem 0.5rem;
    border-bottom-color: #fff;
}
.tab-contents .arrow1::before{
    top: 0;
    border-width: 0 0.5rem 0.5rem 0.5rem;
    border-bottom-color: #02a065 !important;
}


/* animation on saving */

@keyframes blink {
    0% {
      opacity: .2;
    }
    20% {
      opacity: 1;
    }
    100% {
      opacity: .2;
    }
}

.saving span {
    animation-name: blink;
    animation-duration: 1.4s;
    animation-iteration-count: infinite;
    animation-fill-mode: both;
}

.saving span:nth-child(2) {
    animation-delay: .2s;
}

.saving span:nth-child(3) {
    animation-delay: .4s;
}

#preview-icon {
    display: flex;
    justify-content: center;
    height: 133px;
}

.star-six {
    width: 0;
    height: 0;
    border-left: 60px solid transparent;
    border-right: 60px solid transparent;
    border-bottom: 90px solid #ccc;
    position: relative;
    top: -10px;
}
.star-six:after {
    width: 0;
    height: 0;
    border-left: 60px solid transparent;
    border-right: 60px solid transparent;
    border-top: 90px solid #ccc;
    position: absolute;
    content: "";
    top: 27px;
    left: -59px;
}
.widget-input-box-three{
    position: relative;
    z-index: 1;
    top: 36px;
    width: 76px;
    height: 44px;
    left: -37px;
    padding: 5px;
    text-align: center;
}

.six_start_point {
    width: 0;
    height: 0;
    border-left: 45px solid transparent;
    border-right: 45px solid transparent;
    border-bottom: 65px solid #ccc;
    position: absolute;
    top: 10px;
}

    .six_start_point .inputbg {
        position: absolute;
        /* max-height: 55px; */
        min-width: 56px;
        max-width: 53px !important;
        top: 23px;
        height: 38px;
        z-index: 1;
        left: -28px;
        line-height: 31px;
        text-overflow: ellipsis !important;
        display: block !important
    }

    .six_start_point:after {
        width: 0;
        height: 0;
        border-left: 45px solid transparent;
        border-right: 45px solid transparent;
        border-top: 65px solid #ccc;
        position: absolute;
        content: "";
        top: 19px;
        left: -45px;
    }
    .page-item {
        margin-bottom: 4px;
    }

    .tab-contents .arrow::before,
    .tab-contents .arrow::after {
        position: absolute;
        display: block;
        content: "";
        border-color: transparent;
        border-style: solid;
    }
    .tab-contents .arrow::after{
        top: 1px;
        border-width: 0 0.5rem 0.5rem 0.5rem;
        border-bottom-color: #fff;
    }
    .tab-contents .arrow::before{
        top: 0;
        border-width: 0 0.5rem 0.5rem 0.5rem;
        border-bottom-color: rgba(24, 28, 33, 0.11);
    }
    .tab-contents .arrow::before{
        border-bottom-color: #02a065 !important;
    }
    .tab-contents .arrow {
        position: absolute;
        content: '';
        top: -9px;
        left: calc(53px - 10px);
    }
    .tab-contents {
        position: relative;
        margin-top: 11px;
        width: 100%;
        display: inline-block;
    }

    .focusbuttons{
        padding-right: 0px !important;
        width: auto !important;
        vertical-align: middle !important;
    }
    .dashboard-card .card-title {
        margin-bottom: 0.875rem;
        display: inline-block;
        margin: 0;
        line-height: 1;
    }
    .dashboard-card .card-tools a:focus {box-shadow: 0 0 0 2px rgb(242 182 73 / 25%) !important;}


    /* .elfuless_select_box span.select2-selection.select2-selection--multiple {
        max-height: 68px;
        overflow: hidden;
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-color: #93b7af white;
        scrollbar-width: thin;
    }
    select#select-box {
        display: none !IMPORTANT;
    }

    .tab-navigation .custom-select-box button.btn.dropdown-toggle.btn-light {
        height: 42px;
        line-height: 30px;
        background: #fff !IMPORTANT;
        border: 1px solid #d8d1d1;
        font-size: 14px;
        font-family: inherit;
        font-weight: 400;
    }
    .tab-navigation .dropdown.bootstrap-select.tabs-itemBox-Style.custom-select.custom-select-box.icon.bs-select.show button {
        border-color: #02a065 !important;
    }
    .tab-navigation .custom-select-box .filter-option-inner-inner {
        text-overflow: ellipsis !important;
    }
    .tab-navigation.font-awesome {
        width: 100% !important;
        display: block !important;
    } */
    .tab-navigation .bootstrap-select{
        display: inline;
    }
    .tab-navigation .bootstrap-select button.btn.dropdown-toggle.btn-default {
        width: 200px;
        height: 42px;
        line-height: 31px;
        text-align: left;
        display: inline-block;
        padding-left: 12px;
        background: #fff;
        border: 1px solid #d8d1d1;
        font-size: 14px;
    }

    .tab-navigation .bootstrap-select button.btn.dropdown-toggle.btn-default .filter-option-inner-inner {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
    .tab-navigation .bootstrap-select .dropdown-menu.inner li span:nth-child(2) {width: 15px;}

    .tab-navigation .bootstrap-select .dropdown-menu.inner li a {
        padding-left: 12px;
    }

    .filter-option-inner-inner i {
        width: 15px;
    }
    .form-group.form-inline.allBgImgSelectBox .dropdown.bootstrap-select {
        position: absolute;
        content: '';
        width: 145px;
        top: 10px;
        right: 10px;
    }
    .bootstrap-select .dropdown-toggle .filter-option-inner-inner {
        overflow: hidden;
        text-overflow: ellipsis !IMPORTANT;
        white-space: nowrap;
    }
    .analysis-ExportImport-section .box-header {
        padding: .75rem 1.25rem;
        margin-bottom: 0;
        background-color: #f5f5f5;
        border-top-right-radius: 4px;
        border-top-left-radius: 4px;
    }
    .analysis-ExportImport-section .box-header h2 {
        font-size: 16px;
        margin-bottom: 0;
        font-weight: 400;
        letter-spacing: 0;
    }
    .analysis-ExportImport-section .box-body {
        padding: .75rem 1.25rem;
        border: 1px solid #f5f5f5;
    }


    /* .products-progressbar .progress .progress-bar {
        border-top-right-radius: 10rem !important;
        border-bottom-right-radius: 10rem !important;
        border-top-left-radius: unset !important;
        border-bottom-left-radius: unset !important;
    }

     .custom_progressBar {
         display: -ms-flexbox;
         display: flex;
         height: 15px !important;
         overflow: hidden;
         font-size: .625rem;
         background-color: rgb(24 28 33 / 13%) !important;
         border-radius: none !important;
         border: none !important;
         box-shadow: none !important;
     }
     .customProgressView .percentage {
        font-size: 14px;
        font-weight: 500;
        font-style: italic;
        padding-right: 6px;
    } */

    span.input-group-addon {
        border-left: 1px solid #ddd;
    }
    .sameFile_downloadBtn{
        position: absolute;
        content: '';
        right: 5px;
    }

/*
*
* ==========================================
* CUSTOM UTIL CLASSES
* ==========================================
*
*/

.progressBar1 {
    width: 220px;
    height: 220px;
    background: none;
    position: relative;
    transition: .5s;
}

  .progressBar1::after {
    content: "";
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 15px solid #eee;
    position: absolute;
    top: 0;
    left: 0;
  }

  .progressBar1>span {
    width: 50%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    z-index: 1;
  }

  .progressBar1 .progress-left {
    left: 0;
  }

  .progressBar1 .progress-bar {
    width: 100%;
    height: 100%;
    background: none;
    border-width: 15px;
    border-style: solid;
    position: absolute;
    top: 0;
  }

  .progressBar1 .progress-left .progress-bar {
    left: 100%;
    border-top-right-radius: 385px;
    border-bottom-right-radius: 385px;
    border-left: 0;
    -webkit-transform-origin: center left;
    transform-origin: center left;
  }


  .progressBar1 .progress-right {
    right: 0;
  }
  .progressBar1 .progress-value .h2.font-weight-bold {
    font-size: 35px;
    color: #595959d4;
    font-weight: 500 !important;
   }
  .progressBar1 .progress-right .progress-bar {
    left: -100%;
    border-top-left-radius: 385px;
    border-bottom-left-radius: 385px;
    border-right: 0;
    -webkit-transform-origin: center right;
    transform-origin: center right;
  }

  .progressBar1 .progress-value {
    position: absolute;
    top: 8px;
    left: 5px;
  }


/* circle progrss bar 2 */
.progressBar2 {
    width: 150px;
    height: 150px;
    background: none;
    position: relative;
    transition: .5s;
  }
  .progressBar2::after {
    content: "";
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 10px solid #eee;
    position: absolute;
    top: 0;
    left: 0;
  }
  .progressBar2>span {
    width: 50%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    z-index: 1;
  }
  .progressBar2 .progress-left {
    left: 0;
  }

  .progressBar2 .progress-value .h2.font-weight-bold {
    font-size: 25px;
    color: #595959d4;
    font-weight: 500 !important;
   }
  .progressBar2 .progress-right .progress-bar {
    left: -100%;
    border-top-left-radius: 80px;
    border-bottom-left-radius: 80px;
    border-right: 0;
    -webkit-transform-origin: center right;
    transform-origin: center right;
  }
  .progressBar2 .progress-bar {
    width: 100%;
    height: 100%;
    background: none;
    border-width: 10px;
    border-style: solid;
    position: absolute;
    top: 0;
  }
  .progressBar2 .progress-right {
    right: 0;
  }

  .progressBar2 .progress-left .progress-bar {
    left: 100%;
    border-top-right-radius: 80px;
    border-bottom-right-radius: 80px;
    border-left: 0;
    -webkit-transform-origin: center left;
    transform-origin: center left;
  }
  .progressBar2 .progress-value {
    position: absolute;
    top: 8px;
    left: 5px;
  }
  html.default-style.layout-offcanvas.layout-sidenav-100vh.js-focus-visible.layout-collapsed .analysis-progressbar-sample-list li span {
    margin-bottom: 0px !important;
  }

.analysis-content,
.chakra,
.custom_user,
.user-list,
.corn-setting{
    overflow: hidden;
}
.analysis-content>.row:first-child,
.chakra>.row,
.custom_user>.row,
.user-list>.row,
.corn-setting>.row,
.create-corn>.row:first-child,
.corndraft>.row:first-child,
.add-menu>.row:first-child,
#custom-top-padding>section>.row:first-child{
    border-bottom: 1px solid #cdcdcda8;
    padding-bottom: 15px;
    margin-bottom: 15px;
}
#custom-top-padding>.home>.row,
#custom-top-padding>.systematic-view>.row {
    border: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
#custom-top-padding>section{
    overflow: hidden !important;
}
.create-corn>.row:first-child{
    margin-bottom: 10px;
}
.package-list{
    overflow: hidden;
}
.package-list>.row {
    border-bottom: 1px solid #cdcdcda8;
    padding: 15px 0px;
}
.sorting_1 img {
    margin-right: 8px !important;
    position: absolute;
    content: '';
    top: 21px;
    left: 17px;
}

.combosidenav-menu {
    border-top: 0px !important;
}
.combo-menu {
    border-bottom: 1px solid #ddd !important;
    border-radius: 0px !important;
}
.combo-childmenu-item{
    padding-left: 40px !important;
    color: #908a8a !important;
}
.combosidenav-menu a.sidenav-link.sidenav-toggle {
    padding: 23px 20px!important;
}
.sidenav-vertical .sidenav-inner > .sidenav-item {
    margin: 0px;
    width: 100% !important;
}
.toggle-icon.sidenav-link.sidenav-toggle{
   padding: 19px 0 !important;
    /* background: red; */
    height: 20px;
    width: 20px;
    margin-top: 0 !important;
    border-radius: 0 !important;
    z-index: 1111;
    cursor: pointer;
}
a.sidenav-link.sidenav-toggle {
    position: absolute;
    content: '';
    top: 0;
    right: 0;
    z-index: 1;
    width: auto;
    padding: 0 !important;
}
.sidenav-link.sidenav-toggle.sideNavs {
  padding: 8px 20px !important;
}
.circle-number-label .cnl-label{
    display: inline-block;
    height: 35px;
    width: 35px;
    border-radius: 50%;
    background-color: #333;
    border: 1px solid #2f3337;
    z-index: 999;
    color: #fff !important;
    line-height: 35px;
    font-weight: 700;
    display: none;
}

.hidedot{
    display: none;
}

#btnScrShot {
    padding: 5px 13px;
    border: 1px solid #ddd !important;
}
.custom_modal .modal-body {
    min-height: 360px !important;
    display: block;
    max-height: 360px;
    overflow: hidden;
    overflow-y: auto;
}
.custom_modal .modal-content {
    position: relative;
    left: -185px;
    top: 13px;
}
.lta-leftIcon {
    position: absolute;
    content: '';
    left: 63px;
    top: 85px;
    left: 6%;
}

.lta-rightIcon {
    position: absolute;
    content: '';
    top: 85px;
    right: 67px;
    right: 6%
}

.lta-progress-body {
    height: 100px;
    display: block;
}

.lta-progress {
    width: 65%;
    margin-top: 32px;
    margin-left: 20%;
}

.lta-progress-body .progress {
    border-radius: 0;
    height: 1rem;
}

.lta-leftIcon-long {
    position: absolute;
    content: '';
    top: 6px;
    left: 6%;
}

.lta-rightIcon-long {
    position: absolute;
    content: '';
    top: 18px;
    right: 6%;
}
.relative-mt{
    top: 6px;
    position: relative;
}
.audio-animation-text p {
    color: #fff;
    white-space: break-spaces;

}


.audio-animation-text {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    /* border-radius: 50%; */
     /* -webkit-animation: myOrbit 8s linear infinite;
    -moz-animation: myOrbit 8s linear infinite;
    -o-animation: myOrbit 8s linear infinite; */
    animation: myOrbit 8s linear infinite;
    animation-direction: alternate;
}
.audio-animation-text p {
    width: 180px;
    height: auto;
    position: absolute;
    left: 0;
    font-size: 16px;
    top: 0;
    font-weight: 400;
    line-height: 18px;
  }
  @keyframes myOrbit {
    0%   {left: 15px; top: 15px;}
    25%  {left: 280px; top: 15px;}
    50%  {left: 280px; top: 420px;}
    75%  {left: 15px; top: 420px;}
    100% {left: 15px; top: 15px;}
  }

  /* @-webkit-keyframes myOrbit {
    from { -webkit-transform: rotate(0deg) translateX(150px) rotate(0deg); }
    to   { -webkit-transform: rotate(360deg) translateX(150px) rotate(-360deg); }
}

@-moz-keyframes myOrbit {
    from { -moz-transform: rotate(0deg) translateX(150px) rotate(0deg); }
    to   { -moz-transform: rotate(360deg) translateX(150px) rotate(-360deg); }
}

@-o-keyframes myOrbit {
    from { -o-transform: rotate(0deg) translateX(150px) rotate(0deg); }
    to   { -o-transform: rotate(360deg) translateX(150px) rotate(-360deg); }
}

@keyframes myOrbit {
    from { transform: rotate(0deg) translateX(150px) rotate(0deg); }
    to   { transform: rotate(360deg) translateX(150px) rotate(-360deg); }
} */

/* blink random analysis text  */
.random_blinking {
    animation: blinker 2s linear infinite !important;
    font-weight: bolder;
    color:red !important;
}

@keyframes blinker {
    50% {
        opacity: 0;
    }
}

/* END blink random analysis text  */


/* Order list */
.track_tbl td.track_dot {
    width: 50px;
    position: relative;
    padding: 0;
    text-align: center;
}
.track_tbl td.track_dot:after {
    margin-left: -6px;
    top: 17px;
    position: absolute;
    content: '';
    height: 14px;
    width: 14px;
    background: #2fab66;
    border-radius: 50%;
    border: 2px solid #fff;
}
.track_tbl td.track_dot span.track_line {
    background: #c1baba;
    width: 3px;
    min-height: 50px;
    position: absolute;
    height: 101%;
}
.track_tbl tbody tr:first-child td.track_dot span.track_line {
    top: 30px;
    min-height: 25px;
}
.track_tbl tbody tr:last-child td.track_dot span.track_line {
    top: 0;
    min-height: 25px;
    height: 10%;
}

/* payment methoed card radio btn  */
.paymentWrap {
	position: relative;
}

.paymentWrap .paymentBtnGroup {
    /* width: 100%; */
    position: relative;
	margin: auto;
}

.paymentWrap .paymentBtnGroup .paymentMethod {
    padding: 48px;
    box-shadow: none;
    position: relative;
    display: block;
    height: 75px;
}

.paymentWrap .paymentBtnGroup .paymentMethod.active {
	outline: none !important;
}

.paymentWrap .paymentBtnGroup .paymentMethod.active .method {
	border-color: #2fab66;
	outline: none !important;
    /* box-shadow: 0px 3px 22px 0px #7b7b7b; */
    /* box-shadow: #2fab6675 0px 6px 12px -2px, #2fab66d1 0px 3px 7px -3px; */
    box-shadow: #2fab6675 0px 3px 8px;
}

.paymentWrap .paymentBtnGroup .paymentMethod .method {
	position: absolute;
	right: 3px;
	top: 3px;
	bottom: 3px;
	left: 3px;
	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;
	border: 1px solid #00000026;
	transition: all 0.5s;
}
.paymentMethod input {
    opacity: 0;
}
.paymentWrap .paymentBtnGroup .paymentMethod .method.visa {
	background-image: url("../images/payment/visa.png");
}

.paymentWrap .paymentBtnGroup .paymentMethod .method.master-card {
	background-image: url("../images/payment/master-card.png");
}

.paymentWrap .paymentBtnGroup .paymentMethod .method.amex {
	background-image: url("../images/payment/des.png");
}

.paymentWrap .paymentBtnGroup .paymentMethod .method.vishwa {
	background-image: url("../images/payment/pay-pal.png");
}


.paymentWrap .paymentBtnGroup .paymentMethod .method:hover {
	border-color: #2fab66;
	outline: none !important;
}
.paymentInfo {
    position: relative;
}
.treat-mid-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    content: '';
    z-index: op;
    opacity: .8;
}

.treat-mid-animation img {
    height: 600px;
    width: 600px;
}

/* ==================================
========= Guest page css START==== */
.author-content-section,
.guest-header-section,
.guest-section-wrapper{
    position: relative;
}
.guest-footer{
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    bottom: 0;
    height: auto;
    padding: 0;
}
.guest-header-section .layout-navbar {
    box-shadow: 0 1px 0 rgb(24 28 33 / 17%);
}
.guest-header-section .site-logo img {
    max-width: 100%;
    height: auto;
}
.author-info-section {
    position: relative;
    padding: 40px 20px;
}

.single-author {
    position: relative;
}

.single-author img {
    height: 150px;
    width: 150px;
    /* border-radius: 50%; */
    margin-bottom: 16px;
}
.blog-right-section ol {
    padding-left: 15px;
}
.ui-w-30{
    width: 30px;
    height: 30px;
}

.ui-w-45{
    width: 45px;
    height: 45px;
}

.guest-section-wrapper {
    position: relative;
    padding-bottom: 80px;
}
.text-thin-blue{
    color: #93b7af;
}
/* ===== Guest page css END */

.tooltip {
    position: fixed;
    z-index: 9999999 !important;
}

.erro_page .icon-section img{
    max-width: 600px;
    margin-bottom: 40px;
    max-height: 250px !important;
}
.cache-refresh-btn button#cache-clear {
    border: 1px solid #d4f1d4;
    padding: 6px 10px !important;
    background: #fff;
}
.switcher-lg .switcher-indicator, .form-group-lg .switcher .switcher-indicator {
    width: 3.25rem;
    height: 22px;
    font-size: 0.625rem;
    line-height: 1.75rem;
    top: 4px;
}
.switcher-lg .switcher-indicator::after, .form-group-lg .switcher .switcher-indicator::after {
    top: -1px;
    margin: 0.25rem 0 0 0.25rem;
    width: 16px;
    height: 16px;
}
.switcher-lg .switcher-input:checked ~ .switcher-indicator::after, .form-group-lg .switcher .switcher-input:checked ~ .switcher-indicator::after {
    left: 1.8rem;
}
.ion-md-checkmark:before {
    content: "\f2bc";
    position: absolute;
    top: 5px;
    left: 6px;
}
.ion-md-close:before {
    content: "\f2c0";
    position: absolute;
    top: 5px;
    right: 6px;
}
#payment_absolation {
    position: fixed;
    bottom: 40px;
    left: 0;
    text-decoration: none;
    color: #fff !important;
    font-size: 20px;
    display: none;
    font-weight: bold;
    text-align: center;
    z-index: 100;
    cursor: pointer;
    transition: all 0.3s ease 0s;
    -webkit-transition: all 0.3s ease 0s;
    -moz-transition: all 0.3s ease 0s;
    -ms-transition: all 0.3s ease 0s;
    -o-transition: all 0.3s ease 0s;
}

#payment_absolation a {
    position: relative;
    background: #ca1e19;
    font-weight: 500;
    font-size: 14px;
    color: #fff;
    text-transform: capitalize;
    padding: 10px 17px;
    display: inline-block;
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px;
}
#payment_absolation button {
    background: #ad1612;
    color: #fff;
    border: none;
    height: 40px;
    width: 20px;
    line-height: 40px;
    float: left;
    font-size: 14px;
    padding: 0
}
#payment_absolation button:focus {
    outline: 0;
}

.blinking {
    -webkit-animation: 1s blink ease infinite;
    -moz-animation: 1s blink ease infinite;
    -ms-animation: 1s blink ease infinite;
    -o-animation: 1s blink ease infinite;
    animation: 1s blink ease infinite;

  }

  @keyframes blink {
    from, to {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }

  @-moz-keyframes blink {
    from, to {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }

  @-webkit-keyframes blink {
    from, to {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }

  @-ms-keyframes blink {
    from, to {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }

  @-o-keyframes blink {
    from, to {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }

.analysis_price {
    width: 100%;
    text-align: center;
    font-size: 30px;
    color: #fff;
    position: absolute;
    bottom: 10%;
    z-index: 999999;
    content: '';
}
.treat-body-mental-img{
    position: absolute;
    content: '';
    height: 152px;
    width: 152px;
    opacity: .7;
}
.farblang-inner {
    width: 365px;
    padding: 0 !important;
}

.farblang-section {
    position: relative;
    background: #fff;
    /* padding: 35px 20px; */
    justify-content: center;
    display: flex;
    min-height: auto;
    height: 100vh;
}
.farblang-inner .frablang-card {
    position: relative;
    padding: 30px 20px;
    background-clip: padding-box;
    box-shadow: 0 1px 4px rgb(24 28 33 / 1%);
    border: 1px solid rgba(24, 28, 33, 0.06);
    border-radius: 0.25rem;
}
.frablang-main-animation-section {
    height: 84vh;
    text-align: center;
    background: linear-gradient(270deg, #f33f09, #09f345, #0918f3, #f309b7);
    background-size: auto;
    background-size: 800% 800%;
    -webkit-animation: movebg 30s infinite;
    -moz-animation: movebg 30s infinite;
    -o-animation: movebg 30s infinite;
    animation: movebg 30s infinite;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 0;
}

.frablang-mid-section {
    position: absolute;
    content: '';
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* background-color: red; */
    height: 500px;
    width: 500px;
}
.frablang-full-bg img {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
.frablang-user-img {
    width: 152px;
    height: 152px;
    border-radius: 50%;
    margin: 0 auto;
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #fff;
    transform: translate(-50%, -50%);
    overflow: hidden;
    z-index: 11;
    opacity: .5;
}

.frablang-user-img img {
    width: 100%;
}
.frablang-animation-gif {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    z-index: 11;
    height: 100%;
    width: 100%;
}

.frablang-animation-gif img {
    height: 500px;
    width: 500px;
}
.frablang-animation-gif-bg{
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.frablang-animation-gif-bg img{
    width: 100%;
    height: 100%;
}
/* .dashboardSubmenu {
    border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
    width: 100%;
    text-align: left;
    padding: 12px 15px;
    border-color: rgba(24, 28, 33, 0.075);
    background: transparent;
    box-shadow: none;
} */
.dashboardSubmenuBtn{
    outline: 0;
    border: none;
    padding: 0;
    background: no-repeat;
    margin: 0;
}
button:focus{
    outline: 0 !important;
}
.p-10{
    padding: 10px !important;
}
.frablang-subcategory-detils button {
    padding: 0 10px;
    display: inline-block !important;
}
/* .frablang-subcategory-detils {
    padding: 25px 0 !important;
} */
.frablang-subcategory-detils button img {
    height: 24px;
    width: 24px;
}
.frablang-text{
    position: absolute;
    content: '';
    width: 100%;
    height: auto;
    bottom: 14px;
}

.frablang-text p {
    font-size: 20px;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    display: -webkit-box;
}
@keyframes fadeIn {
    from { opacity: 0; }
  }

.frablang-text, .animate-frabklang {
    animation: fadeIn 2s infinite alternate;
    -webkit-animation: fadeIn 2s infinite alternate;
   -moz-animation: fadeIn 2s infinite alternate;
   -o-animation: fadeIn 2s infinite alternate;
}

div.dataTables_wrapper div.dataTables_processing {
    top: 5% !important;
    background-color:#3f884b;
    color:white;
}


.skitter .box_clone img {
    width: 100% !important;
    height: 100% !important;
}
div#content {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
    opacity: .3;
    overflow: hidden;
}
.skitter.skitter-large {
    width: 100% !important;
    max-width: 100% !important;
    min-height: 100vh !IMPORTANT;
}
.skitter .box_clone img {
    width: 100vw !important;
    max-width: 100vw !important;
    height: 100vh !IMPORTANT;
    min-height: 100vh !important;
}
.frablang-treat .preloader img {
    top: 283px;
    left: 45.2%;
    transform: translate(-50%, -50%);
}
.treat .preloader{
    height: 105% !important;
}
.treat-action-btn button{
    position: absolute;
    content: '';
    right: 20px;
    top: 20px;
}

.treat-home-btn button{
    position: absolute;
    content: '';
    right: 20px;
    top: 20px;
}

.treat-start-stop-btn button {
    position: absolute;
    content: '';
    right: 85px;
    top: 20px;
}

.treat-start-stop-btn .btn-soundtrun{
    margin-right: 118px;
}

.frablang-subcategory img {
    height: 135px;
    object-fit: cover;
    width: 100%;
}

.frablang-subcategory:after {
    position: absolute;
    content: '';
    background: black;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: .1;
}
.frablang-subcategory {
    position: relative;
    overflow: hidden;
}
.frablang-treat-video-content {
    position: absolute;
    content: '';
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}
.frablang-treat-video-content button{
    /* background: #2fab66; */
    height: 45px;
    width: 80px;
    border-radius: 10px;
    line-height: 50px;
    border: none !important;
    cursor: pointer;
    opacity: 0.9;
}
.frablang-treat-video-content i {
    font-size: 18px;
    color: #fff;
}
span.info_slide_dots {
    display: none !important;
}
.custom-fullHight-SelectBox .inner{
    max-height: 400px !important;
}
.modal-auto-scroll .modal-body{
    min-height: 360px !important;
    display: block;
    max-height: 360px;
    overflow: hidden;
    overflow-y: hidden;
    overflow-y: auto;
}
.shows{
    display: block;
    max-height: 90vh;
    width: auto;
    overflow: hidden;
    overflow-y: auto;
    overflow-x: hidden;
    margin-right: -5px;
    scrollbar-color: #2fab66 #ddd9d9 !important;
    scrollbar-width: thin !important;
}
select#search_group_id {
    width: 97%;
    margin-left: 5px;
}

.dropdown-menu.dropdown-menu-right.menu-right-custom.shows::-webkit-scrollbar {
    width: 5px;
}

.dropdown-menu.dropdown-menu-right.menu-right-custom.shows::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.dropdown-menu.dropdown-menu-right.menu-right-custom.shows::-webkit-scrollbar-thumb {
    background-color: #2fab66;
    /* outline: 1px solid slategrey; */
}
/* .card-border-theme{
    border: 1px solid #2fab66 !important;
}
.card-spcialDesign{
    color: #2fab66;
    border-bottom: 3px solid #2fab66;
    font-size: 14px;
    padding: 10px 12px;
} */
.bottom-Fixed {
    position: fixed;
    width: 95%;
    bottom: 0;
    background: #fff;
    left: 33px;
    padding: 3px 0;
    transition: .2s;
    z-index: 1;
}
.some_class {
    bottom: 46px !important;
    transition: .2s;
}

.user-profile-header{
    display: inline-flex !important;
}

.user-profile-img {
    display: flex;
    justify-content: center;
}


.font-weight-600{
    font-weight: 600 !important;
}

/* popover modal image show */
#myImg {
    border-radius: 5px;
    cursor: pointer;
    transition: 0.3s;
}
.popover-body img {
    border-radius: 5px;
    cursor: pointer;
    transition: 0.3s;
    width: 100%;
    height: auto;
}
.popover-body img :hover {opacity: 0.7;}

/* The Modal (background) */
.analysisPopup_Modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    padding-top: 100px; /* Location of the box */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgb(0 0 0 / 70%);
}

/* Modal Content (image) */
.analysisPopup_Modal .modal-content {
    margin: auto;
    display: block;
    width: auto;
    height: 100%;
    top: -48px;
    position: relative;
}

/* Add Animation */
.analysisPopup_Modal .modal-content, #caption {
    -webkit-animation-name: zoom;
    -webkit-animation-duration: 0.6s;
    animation-name: zoom;
    animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
    from {-webkit-transform:scale(0)}
    to {-webkit-transform:scale(1)}
}

@keyframes zoom {
    from {transform:scale(0)}
    to {transform:scale(1)}
}

/* The Close Button */
.analysisPopup_Modal .close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #fff !important;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
    font-size: 53px !important;
    opacity: unset !important;
}

.analysisPopup_Modal .close:hover,
.analysisPopup_Modal .close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

/* 100% Image Width on Smaller Screens */
@media only screen and (max-width: 700px){
    .analysisPopup_Modal .modal-content {
        width: auto;
    }
}
#custom-top-padding {
    padding-top: 0px !important;
}

#custom-top-padding section.home,
#custom-top-padding section.treatment-overview-container,
#custom-top-padding section.products {
    padding-top: 10px;
}
.analysis_header_flex{
    padding-top: 0 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.active_img{
    background: #2fab66;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 5px auto;
}
.top_arrow {
    transform: rotate(90deg);
}
.bottom_arrow {
    transform: rotate(-270deg);
}
.arrow_margin_top{
    margin-left: 45px;
    margin-bottom: 4px;
}
.arrow_margin_bottom{
    margin-left: 45px;
    margin-top: 4px;
}
.mr-43{
    margin-right: 44px;
}
.direction_btn button {
    margin-bottom: 42px;
    display: block;
}
.direction_btn_default button {
    margin-bottom: 4px;
    display: block;
}
.row-top-border{
    border-bottom: 1px solid #cdcdcda8;
    padding-bottom: 15px;
}
.flex-btn{
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
}

.ss_divs{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.set-huebee-bg-color{
    background-color: #c1c1c1
}

.icon-transform1 {
    transform: rotate(45deg)
}

.icon-transform2 {
    transform: rotate(-45deg)
}

.dc-select-size .dropdown-menu .inner {
    max-height: 265px !important;
}
.main_menu_section .sidenav-link.sideNavs{
  width: 100% !important;
}
.backtwoback>.backTwoback-btn {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    cursor: pointer;
    padding-left: 10px;
    padding-right: 3px;
    height: 39px;
    padding-top: 9px;
}
.backtwoback>.backTwoback-btn:hover{
    color: #2fab66 !important;
    transition: .5s;
}
.backtwoback>.sidenav-link.sideNavs{
  padding-left: 30px !important;
}

/* support classes*/
.table-head-width {
    width: 100px;
}

/* LTA 4x toggle */
.fourX-toggle-section .toggle-container {
    display: flex;
    align-items: center;
    height: 26px;
    width: 145px;
    overflow: hidden;
    background: #fff;
    border: 3px solid #f7f7f7;
    border-radius: 25px;
    position: relative;
}

.fourX-toggle-section input {
  display: none;
}

.fourX-toggle-section label {
    padding: 3px;
    width: 100%;
    cursor: pointer;
    position: relative;
    margin: 0 !important;
}

.round {
    position: absolute !important;
    top: 3px !important;
    /* left: 10px !important; */
    right: 0 !important;
    bottom: 0 !important;
    height: 14px !important;
    width: 14px !important;
    background: #2FAB66 !important;
    border-radius: 50% !important;
    transition: all .3s ease !important;
}

.fourX-toggle-section .checkbox1:checked ~ .round {
  transform: translateX(0px);
}

.fourX-toggle-section .checkbox2:checked ~ .round {
  transform: translateX(35.66px);
}

.fourX-toggle-section .checkbox3:checked ~ .round {
  transform: translateX(70.32px);
}

.fourX-toggle-section .checkbox4:checked ~ .round {
  transform: translateX(104px);
}
span.checklistBox {
    position: absolute;
    content: '';
    top: 50%;
    left: 50%;
    height: 10px;
    width: 10px;
    background: #4e5155;
    transform: translate(-50%,-50%);
    border-radius: 50%;
}

.checkbox-names {
    display: flex;
    align-items: center;
    height: 12px;
    width: 144px;
    overflow: hidden;
    position: relative;
    text-align: center;
}

.checkbox-names-three {
    width: 135px !important;
}

.checkbox-names span {
    padding: 12px;
    width: 100%;
    cursor: pointer;
    position: relative;
    margin: 0 !important;
    font-weight: bold;
}

.male-counts {
    position: absolute;
    margin-left: 27px;
    margin-top: -42px;
}
.male-counts span {
    font-size: 18px;
    font-weight: bold;
}

.heart-counts {
    position: absolute;
    margin-left: 35px;
    margin-top: -35px;
}

.heart-counts span {
    font-size: 18px;
    font-weight: bold;
}

.analysis-count {
    text-align: center;
}

.analysis-count span {
    font-weight: bold;
    font-size: 18px;
}

.analysis-pointer {
    position: relative;
    top: 0;
    margin-top: -14px;
    bottom: 0 !important;
    height: 12px !important;
    width: 12px !important;
    background: #ffffff !important;
    border-radius: 50% !important;
    transition: all .3s ease !important;
}


.swiper-container .swiper-slide {
    padding: 1rem 0;
    text-align: center;
    font-size: 1rem;
    background: #ffffff;
    cursor: pointer;
    justify-content: center;
}

.dc-swiper-select {
    border: 2px solid #2FAB66;
}

.dc-title-preview {
    display: flex;
    position: relative;
}

.color-input {
    position: absolute;
    left: 0;
    top: 0;
    margin-left: 25%;
    margin-top: 1%;
    border: 2px solid #ffffff !important;
}

.dc-img-focus {
    height: 45px
}

.active-icon {
    border: 2px solid #2FAB66;
}

.widget-input-box-one-x {
    height: 40px;
    width: 115px;
    /* display: inline; */
    margin-top: 5%;
    /* box-shadow: 0 0 1px 1px #b1b1b1; */
    text-align: center;
}

.dc-name {
    margin: auto;
    display: block;
}

.dc-bottom-content {
    align-self: flex-end;
}

#icon-title, #dc-anchor-box {
    text-align: center;
}

#show_search_result > div {
    cursor: pointer;
}

#timezone-select-wrapper .select2-container {
    width: auto !important;
}

.gender-radio-contaienr{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* password field toggler design */
.eye-icon-btn {
    cursor: pointer;
}

.eye-icon-btn i {
    transition: all 0.3s ease;
}

.eye-icon-btn.opened i {
    transform: rotate(-45deg);
}

.password-container .pass-input-container {
    width: calc(100% - 48px);
}

.password-container .password-field {
    border-radius: 0.25rem 0 0 0.25rem;
}

.password-container .eye-icon-btn {
    border-radius: 0 0.25rem 0.25rem 0;
    border-left: none !important;
    display: unset;
}




.session-info {
    position: relative;
    border: 1px solid #bfbfbf;
    border-radius: 3px;
    padding: 5px 10px;
    margin-bottom: 5px;
}

.session-id-content {
    position: relative;
}

.session-id-content p {
    position: relative;
    margin: 0px auto;
    display: block;
    text-align: center;
    font-size: 14px;
}

.session-id-content span.regen-session-id {
    margin-left: 5px;
    position: relative;
    display: inline-block;
    font-size: 8px;
    background-color: #02bc77;
    color: #fff;
    padding: 3px;
    width: 16px;
    border-radius: 2px;
    height: 16px;
    cursor: pointer;
    vertical-align: middle;
    top: 0px;
}

.session-id-content span.sessionIdValue {
    position: relative;
    display: inline-block;
    vertical-align: middle;
}

.navCartSessionId-box {
    position: relative;
    border: 1px solid #b5b5b5;
    padding: 5px;
    border-radius: 3px;
    display: block;
    margin: 5px;
}
.ttcountbadge {
    position: relative;
    margin-top: 2px;
}

.current-player-session {
    position: relative;
    margin: 10px auto;
}


ul.users-rankwise {
    position: relative;
    padding: 0px;
    list-style-type: none;
    text-align: center;
}
ul.users-rankwise li {
    display: inline-block;
    position: relative;
    margin-bottom: 14px;
    padding: 0px 7px;
}
ul.users-rankwise li:first-child,ul.users-rankwise li:last-child {
    width: 100%;
}
/* width */
.tab-contents .card-body::-webkit-scrollbar {
    width: 3px;
}
/* Track */
.tab-contents .card-body::-webkit-scrollbar-track {
    background: #f1f1f1;
}
/* Handle */
.tab-contents .card-body::-webkit-scrollbar-thumb {
    background: #2FAB66;
}
/* Handle on hover */
.tab-contents .card-body::-webkit-scrollbar-thumb:hover {
    background: #555;
}
ul.users-rankwise li .user-single-box {
    width: 145px;
    margin: 0px auto;
    border: 1px solid #2fab66;
    border-radius: 3px;
    position: relative;
    padding: 10px;
}
.usb-photo img {
    height: 60px;
    width: 60px;
}

.loading-animation {
    animation: loading-move 1s infinite;
    opacity: 50%;
}

@keyframes loading-move {
    50% {opacity: 0;}
}

.dashboard-progress-bar-container{
    max-height: 540px;
    overflow-y: auto;
    scrollbar-width: thin;
}

.rightside-bottom-container .dashboard-progress-bar-container{
    max-height: 325px !important;
    overflow-y: auto;
    scrollbar-width: thin;
    overflow-x: hidden;
    padding-inline: 12px;
}

.rightside-bottom-container .dashboard-progress-bar-container.small-collapsed {
    overflow-y: scroll;
    scrollbar-width: thin;
}

.rightside-bottom-container .dashboard-right-chart-max-height-0{
    margin-top: -17px;
}
.rightside-bottom-container .dashboard-right-chart-max-height-1{
    margin-top: -17px;
}
