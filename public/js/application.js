    (function (e, a) {
        for (var i in a) e[i] = a[i];
    }(window, /******/(function (modules) { // webpackBootstrap
    /******/ // The module cache
    /******/
    var installedModules = {};
    /******/
    /******/ // The require function
    /******/
    function __webpack_require__(moduleId) {
        /******/
        /******/ // Check if module is in cache
        /******/
        if (installedModules[moduleId]) {
            /******/
            return installedModules[moduleId].exports;
            /******/
        }
        /******/ // Create a new module (and put it into the cache)
        /******/
        var module = installedModules[moduleId] = {
            /******/
            i: moduleId,
            /******/
            l: false,
            /******/
            exports: {}
            /******/
        };
        /******/
        /******/ // Execute the module function
        /******/
        modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
        /******/
        /******/ // Flag the module as loaded
        /******/
        module.l = true;
        /******/
        /******/ // Return the exports of the module
        /******/
        return module.exports;
        /******/
    }
    /******/
    /******/
    /******/ // expose the modules object (__webpack_modules__)
    /******/
    __webpack_require__.m = modules;
    /******/
    /******/ // expose the module cache
    /******/
    __webpack_require__.c = installedModules;
    /******/
    /******/ // define getter function for harmony exports
    /******/
    __webpack_require__.d = function (exports, name, getter) {
        /******/
        if (!__webpack_require__.o(exports, name)) {
            /******/
            Object.defineProperty(exports, name, {
                enumerable: true,
                get: getter
            });
            /******/
        }
        /******/
    };
    /******/
    /******/ // define __esModule on exports
    /******/
    __webpack_require__.r = function (exports) {
        /******/
        if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
            /******/
            Object.defineProperty(exports, Symbol.toStringTag, {
                value: 'Module'
            });
            /******/
        }
        /******/
        Object.defineProperty(exports, '__esModule', {
            value: true
        });
        /******/
    };
    /******/
    /******/ // create a fake namespace object
    /******/ // mode & 1: value is a module id, require it
    /******/ // mode & 2: merge all properties of value into the ns
    /******/ // mode & 4: return value when already ns object
    /******/ // mode & 8|1: behave like require
    /******/
    __webpack_require__.t = function (value, mode) {
        /******/
        if (mode & 1) value = __webpack_require__(value);
        /******/
        if (mode & 8) return value;
        /******/
        if ((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
        /******/
        var ns = Object.create(null);
        /******/
        __webpack_require__.r(ns);
        /******/
        Object.defineProperty(ns, 'default', {
            enumerable: true,
            value: value
        });
        /******/
        if (mode & 2 && typeof value != 'string')
            for (var key in value) __webpack_require__.d(ns, key, function (key) {
                return value[key];
            }.bind(null, key));
        /******/
        return ns;
        /******/
    };
    /******/
    /******/ // getDefaultExport function for compatibility with non-harmony modules
    /******/
    __webpack_require__.n = function (module) {
        /******/
        var getter = module && module.__esModule ?
            /******/
            function getDefault() {
                return module['default'];
            } :
            /******/
            function getModuleExports() {
                return module;
            };
        /******/
        __webpack_require__.d(getter, 'a', getter);
        /******/
        return getter;
        /******/
    };
    /******/
    /******/ // Object.prototype.hasOwnProperty.call
    /******/
    __webpack_require__.o = function (object, property) {
        return Object.prototype.hasOwnProperty.call(object, property);
    };
    /******/
    /******/ // __webpack_public_path__
    /******/
    __webpack_require__.p = "/";
    /******/
    /******/
    /******/ // Load entry module and return exports
    /******/
    return __webpack_require__(__webpack_require__.s = 120);
    /******/
    })
    /************************************************************************/
    /******/
    ({

        /***/
        "./resources/assets/js/application.js":
            /*!********************************************!*\
                !*** ./resources/assets/js/application.js ***!
                \********************************************/
            /*! no static exports found */
            /***/
            (function (module, exports, __webpack_require__) {

                __webpack_require__( /*! ./bootstrap */ "./resources/assets/js/bootstrap.js");

                /***/
            }),

        /***/
        "./resources/assets/js/bootstrap.js":
            /*!******************************************!*\
                !*** ./resources/assets/js/bootstrap.js ***!
                \******************************************/
            /*! no static exports found */
            /***/
            (function (module, exports) {

                // Auto update layout
                if (window.layoutHelpers) {
                    window.layoutHelpers.setAutoUpdate(true);
                }

                $(function () {
                    // Initialize sidenav
                    $('#layout-sidenav').each(function () {
                        new SideNav(this, {
                            orientation: $(this).hasClass('sidenav-horizontal') ? 'horizontal' : 'vertical'
                        });
                    }); // Initialize sidenav togglers

                    $('body').on('click', '.layout-sidenav-toggle', function (e) {
                        e.preventDefault();
                        window.layoutHelpers.toggleCollapsed();
                    }); // Swap dropdown menus in RTL mode

                    if ($('html').attr('dir') === 'rtl') {
                        $('#layout-navbar .dropdown-menu').toggleClass('dropdown-menu-right');
                    }
                });
                /**
                 * We'll load the axios HTTP library which allows us to easily issue requests
                 * to our Laravel back-end. This library automatically handles sending the
                 * CSRF token as a header based on the value of the "XSRF" token cookie.
                 */
                // window.axios = require('axios');
                //
                // window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

                /**
                 * Next we will register the CSRF Token as a common header with Axios so that
                 * all outgoing HTTP requests automatically have it attached. This is just
                 * a simple convenience so we don't have to attach every token manually.
                 */
                // let token = document.head.querySelector('meta[name="csrf-token"]');
                //
                // if (token) {
                //     window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
                // } else {
                //     console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
                // }

                /**
                 * Echo exposes an expressive API for subscribing to channels and listening
                 * for events that are broadcast by Laravel. Echo and event broadcasting
                 * allows your team to easily build robust real-time web applications.
                 */
                // import Echo from 'laravel-echo'
                // window.Pusher = require('pusher-js');
                // window.Echo = new Echo({
                //     broadcaster: 'pusher',
                //     key: process.env.MIX_PUSHER_APP_KEY,
                //     cluster: process.env.MIX_PUSHER_APP_CLUSTER,
                //     encrypted: true
                // });

                /***/
            }),

        /***/
        120:
            /*!**************************************************!*\
                !*** multi ./resources/assets/js/application.js ***!
                \**************************************************/
            /*! no static exports found */
            /***/
            (function (module, exports, __webpack_require__) {

                module.exports = __webpack_require__( /*! C:\xampp\htdocs\efitdesign\resources\assets\js\application.js */ "./resources/assets/js/application.js");


                /***/
            })

        /******/
    })));


    /**
     * Add Global Variables
     */
    let __datePicker;
    var locale = document.querySelector('html').getAttribute('lang');
    var dateFormat = (locale === 'de' || locale === '') ? 'd.m.Y' : 'Y/m/d';

    $(window).on('load', function () {
        $('.preloader').fadeOut('slow');
    });
    rightDropdownScriptLoad();
    function rightDropdownScriptLoad() {

        $(document).ready(function () {
            // Append to body before showing modal.
            $('.modal').appendTo($('body'));

            // Chanage dashboard right side view
            $('.btn-dash').on('click', function () {
                var changename = $(this).data('name');
                document.getElementById("select-box_change_view").innerHTML='<i class="ion ion-ios-list">&nbsp;</i>'+changename;
                let url = $(this).data('url');
                var dropdown = $(this).attr('id');
                if(dropdown == 1 || dropdown == 4){
                    $('#topicTab').hide();
                    $('.tab-content').hide();
                    $('#' + 'tab-' + dropdown).removeClass('hide');
                    $('#' + 'tab-' + dropdown).show();
                }else{
                    $('#topicTab').hide();
                    $('.tab-content').hide();
                    $('#' + 'tab-' + dropdown).removeClass('hide').show();
                    if($('#' + 'tab-' + dropdown+' .card .card-body ul li').length == 0){
                        $('#' + 'tab-' + dropdown+' .card .card-body').html(`<div class="preloader" > <img style="top:20px !important" src="/images/Fill-4.png" alt="">  </div>`).attr('style','min-height:270px')
                        $.ajax({
                            type:'GET',
                            url: url+'/Sajax/get_data/'+dropdown,
                            dataType:"json",
                            success:function(res){
                                $('#' + 'tab-' + dropdown+' .card .card-body').removeAttr('style').empty()
                                if(dropdown == 2){
                                    $('#show_normal_pdfs').empty();
                                    $('#show_normal_pdfs').append(res.pdf_list)
                                }
                                else if(dropdown == 3){
                                    $('#save_treatment_cart_list').empty();
                                    $('#save_treatment_cart_list').append(res.treatment_list)
                                }else if(dropdown == 5){
                                    $('#dc_pdf_view_show').empty();
                                    $('#dc_pdf_view_show').append(res.dcpdf_list)
                                }

                            },
                            error:function(data){
                                console.log(data);
                            }
                        })
                    }

                }

            });
            // JavaScript
            var datePickers = $('#datePicker,.datePicker,.__datePicker');
                datePickers.each(function(index, datePicker) {
                __datePicker = flatpickr(datePicker, {
                    dateFormat: dateFormat,
                    allowInput: true,
                    monthDropdown: false,
                    defaultDate: datePicker.value,
                    parseDate: function(dateStr, format) {
                        var parts = dateStr.split('.');
                        if (format === 'd.m.Y' && parts.length === 3) {
                            var day = parseInt(parts[0], 10);
                            var month = parseInt(parts[1], 10) - 1; // Month is zero-based
                            var year = parseInt(parts[2], 10);
                            return new Date(year, month, day);
                        }
                        return new Date(dateStr); // Return null for invalid dates
                    },
                    onOpen: function(selectedDates, dateStr, instance) {
                        if(!$('.flatpickr-monthDropdown-month').hasClass(''))$('.flatpickr-monthDropdown-month').addClass('flatpickr-month')
                        $(instance.calendarContainer).css('z-index', 9999999);
                        // display date in input field
                        $(this.element).val(dateStr);
                        // display date in altInput element
                        $(this.element).siblings('input.time-input').eq(0).val(dateStr);
                    },
                    onChange: function(selectedDates, dateStr, instance) {
                        if (selectedDates.length > 0) {
                        var month = selectedDates[0].getMonth();
                        instance.currentYearElement.blur(); // Blur the year input to close the calendar
                        instance.changeMonth(month, false); // Show the selected month in the month list
                        }
                        // display date in input field
                        $(this.element).val(dateStr);
                        // display date in altInput element
                        $(this.element).siblings('input.time-input').eq(0).val(dateStr);
                    },
                    onMonthChange: function(selectedDates, dateStr, instance) {
                        if(instance.currentMonthElement != undefined)instance.currentMonthElement.focus(); // Keep focus on the month input after selecting a month
                    },
                    // onChange: function(selectedDates, dateStr, instance) {
                    //     if (instance.isMobile) {
                    //         instance.close(); // Close the calendar after selecting a date on mobile devices
                    //     }
                    // },

                    // Add other options as needed
                });
            });
            // $("[id=datePicker]").flatpickr(datepickerOptions);

            $('img').each(function() {
                if($(this).data('src')){
                    $(this).attr('src',$(this).data('src')).removeAttr('data-src')
                }
                // Perform actions on each image here
                if ($(this).data('profileimage')) {
                    $(this).attr('src',$('.info-current-user img').attr('src'))
                }
            });

            // protfolio Popup
            $('.image-link').magnificPopup({type:'image'});



            // Get the value of the 'menu_head' query parameter from the current URL
            const menuHeadParamValue = new URLSearchParams(window.location.search).get('menu_head');
            const menuHeadParamOwnComboValue = new URLSearchParams(window.location.search).get('ownC') ?? 1;

        if (menuHeadParamValue) {
            // Loop through each <a> tag within elements with class .dashboard-card
            $('.dashboard-card, .pagination-div li').find('a').each(function() {
                // Get the current href attribute value
                const href = $(this).attr('href');

                // Check if the href already has query parameters
                if (href.includes('?')) {
                    // Append the 'menu_head' parameter to the existing query parameters
                    $(this).attr('href', href + '&menu_head=' + menuHeadParamValue + '&ownC=' + menuHeadParamOwnComboValue);
                } else {
                    // Add the 'menu_head' parameter as the only query parameter
                    $(this).attr('href', href + '?menu_head=' + menuHeadParamValue + '&ownC=' + menuHeadParamOwnComboValue);
                }
            });
        }
        $('#topic_button').click(function () {
            $("#select-box_change_view").removeClass("dashSelectActive_btn");
            $( "#select-box_change_view option[value='0']" ).show();
            $('.tab-content').hide();
            $("#select-box_change_view").find('option:selected').prop("selected", false);
            $("#topic_button").addClass("dashActive_btn");
            $('#topicTab').show();
        });

        });

    }
    /**
     * Get translation
     * @param {text} code
     * @returns translated code
     */
    function getTrans(code){
        let langText;
        let log = $.ajax({
            url: window.location.origin+'/langfile/' + locale+'/'+code,
            type:'GET', cache: false,async: false,
            success: function(text)
            {
                langText = text;
            }
        })
        return langText;
    }


    $(function ($) {
        $('#infotestBtn').on('click', function () {
            $('.infotest-content').toggleClass('showw');
        });

        $('#infotestBtnDefault').on('click', function () {
            $('.infotest-content-default').toggleClass('showw');
        });
    });

    // Image Preview
    $('img').click(function () {
        let view = $(this).data('view')
        if(view === true){
            let imgSource = $(this).attr('src');
            let name = $(this).data('name');
            let email = $(this).data('email');
            let subuser = $(this).data('subuser');

            const swalOptions = {
                title: name,
                text: email,
                imageUrl: imgSource,
                showConfirmButton: false,
                imageWidth: 300,
                imageHeight: 300,
                imageAlt: name,
            };

            if (subuser) {
                swalOptions.showCancelButton = true;
                swalOptions.cancelButtonText = "Switch";
                swalOptions.customClass = {
                    cancelButton: "switch-user-class"
                };
                // swalOptions.showCloseButton = true;
                swalOptions.allowOutsideClick = true;
                swalOptions.allowEscapeKey = false;
            }

            Swal.fire(swalOptions);

            if (subuser) {
                $('.switch-user-class').on('click', function () {
                    switch_user(subuser);
                });
            }
        }else return;

    });

    // Create Pdf with reaction result and save
    function createPDF(name) {
        $.ajax({
                type:'POST',
                url:'createPDF',
                data: { name : name },

                dataType:"json",
                success:function(data){
                    if (data.success == true) {
                        Swal.fire({ type: 'success', title: data.message, showConfirmButton: false, timer: 1500 });
                        cleanCart();
                    }
                },
                error:function(data){
                    console.log(data);
                }
        })
    };

    // Treatment Backgroung Image change
    $('#changeImage').on('change',function() {
        var id = $(this).find('option:selected').val();
        if(id != 'Random' && id != ''){
            let image = $(this).find('option:selected').data('src');
            $("#bgimgContainer").prop("src", image);
        }else if(id == '') $("#bgimgContainer").prop("src", window.location.origin+'/public/images/circle_new.gif');
        getBGImage(id,$(this).data('userid'))
    });

    function getBGImage(id,userid){
        $.ajax({
            type: "POST",
            data: {id: id,user_id: userid},
            url: window.location.origin+'/Sajax/changebgImage',
            success: function(data) {
                if(id == 'Random'){
                    if(data.image) {
                        $("#bgimgContainer").prop("src", data.image);
                        toastr.success(data.message);
                    }
                }
            }
        });

    }

    function noteModalView(id){
        $('#noteAddSaveCart').attr('onclick','addNote('+id+')');
        let content  = $('#pkg'+id).data('content');

        if(content !== ""){
            $('#noteContent').val(content);
        } else{
            $('#noteContent').val('');
        }

        $('#note-modal').modal('show')
    }

    $( document ).ready(function() {
		$("#custom-top-padding>section>div.row").has('.analysis-content-header').addClass('analysis_header_flex');
	});

    function addNote(cartid){
        let note = $("#note-modal").find("#noteContent").val();
        let url  = $('#pkg'+cartid).data('url')
        $.post(url,{
            id : cartid,
            note : note
        }, function(data, status){
            if (data.success == true) {
                Swal.close();
                toastr.warning(data.message);
                $('#note-modal').modal('toggle');
                $('#package-list-table').DataTable().clear().draw();
            }
        });
    }

    function viewPackageContent(id){
        $("#view-modal").addClass("hide");
        $(".pkg-preloader").fadeIn();

        $.get($('#_content'+id).data('url'),{
            id:id
        },function(data,status){
            let lists = [];
            $('#view-modal ul').empty();
            data.contents.forEach(function(value){
                lists.push(`<li class="px-1 py-1">${value}</li>`)
            });

            $('#view-modal ul').append(lists);
            $(".pkg-preloader").fadeOut();
            $("#view-modal").removeClass("hide");
        })
    }

    /**
     * keypress restriction on 13
     */
    $('input[name=group_name]').keypress(function (e) {
        var code = e.keyCode || e.which;
        if (code === 13){
            $('#groupUpdateBTN').trigger('click');
        }

    });

    /**
     * Submenu change
     */
    function submenu(){
        let name = $('select[name="submenu_id"] option:selected').text();
        let sortNo = $('select[name="submenu_id"] option:selected').attr("data-sNo");
        let position = $('select[name="submenu_id"] option:selected').attr("data-position");

        if($('select[name="submenu_id"] option:selected').val().trim().length == 0) $('.remove').addClass("hide");
        else $('.remove').removeClass("hide");

        $('.submenu_name').val(name);
        let submenu_id =[];
        if(position != null){
            $.each(jQuery.parseJSON(position), function(key, value) {
                submenu_id.push(value);
            });
        }

        $(".submenu_update").find('.link_submenu_id').val(submenu_id).trigger('change');

        $('.sortingNo').val(sortNo);

        let isSelectSubmenu = $('select[name="submenu_id"] option:selected').val()

        if(isSelectSubmenu.length && $('.submenu_update').find('.btn-update-submenu').hasClass('hide')) $('.submenu_update').find('.dlt_btn_submenu, .btn-update-submenu').removeClass('hide');
        else if(!isSelectSubmenu) $('.submenu_update').find('.dlt_btn_submenu, .btn-update-submenu').addClass('hide')

    }

    $(".saidToggle").click(function(){
        $('.layout-offcanvas').toggleClass('customClss');
    });

    $("#inputCustom_dorp").click(function(){
        $(".menu-right-custom").addClass( "shows" );
    });


    $('body').on('click', function(e) {
        $('.menu-right-custom').each(function() {
            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.menu-right-custom').has(e.target).length === 0) {
                $(".menu-right-custom").removeClass( "shows" );
                $('#search_group_id option:selected').prop('selected', false);
            }
        });
    });

    function openCartModal(){
        if ($('.openCartModal .cart-list li').length == 0) {
            $('#openCartModal .modal-body').html(`<div class="preloader"> <img src="${window.location.origin}/images/Fill-4.png" alt="">  </div>`).attr('style','height:100%')
            $.get(window.location.origin+'/Sajax/get_save_modal', function(data, status){
                $('#openCartModal .modal-body').empty().html('<ul class="cart-list">'+data.save_cart+'</ul');
            });
        }
        $(document).find('.openCartModal').modal('show');
    }
    // Change Year/Month By Clicking
    function changeYearMonth() {
        let v = ($('#changeYearMonth').is(':checked'));
        $.post(window.location.origin+'/Sajax/changeYearMonth', { value: v }, function(data, status) {
            var msg = data; // Assuming the response contains a JSON object with "success" and "message" properties

            if (msg.success == true) {
                toastr.success(msg.message);
                window.location.reload();
            }
        });
    }


    //  Change Filter Option To users
    function changeShowFilter() {
        var filter_id = $('#changeShowFilter').val();
        $.post(window.location.origin+'/Sajax/changeShowFilter', { filter_id : filter_id }, function(data, status) {
            var msg = data; // Assuming the response contains a JSON object with "success" and "message" properties

            if (msg.success == true) {
                toastr.success(msg.message);
                window.location.reload();
            }
        });
    }

    // Save the tody value in database
    function saveToday(sub_id, ana_id, cau_id, type) {
        $.post(window.location.origin+'/Sajax/saveToday', { sub_id: sub_id,ana_id: ana_id,cau_id: cau_id,type: type }, function(data, status) {
            var msg = data; // Assuming the response contains a JSON object with "success" and "message" properties
            // toastr.success(msg.message);
        });
    }
    /**
     * SubUsers search
     * @param keyward
     * @return html_view
     */
    var __cancel;
    function serach_sub_user() {
        // If there's an ongoing search request, abort it
        if (__cancel) {
            __cancel.abort();
        }
        let group_id = $('#search_group_id').val();
        if(group_id === null) return;
        var search_value = $("#serach_sub_user").val();
        if(group_id == 'staff') $("#serach_sub_user").hide();
        else $("#serach_sub_user").show();

        __cancel = $.ajax({
            url: window.location.origin+'/Sajax/search_user',
            type: 'POST',
            data: {
                search_key: search_value,group_id: group_id
            },
            dataType: "json",
            success: function (msg) {
                if(msg.length){
                    if($('#show_search_result').length) $('#show_search_result').empty()
                    $.each(msg, function (key, value) {
                        // if (value.photo != "" && value.photo != null) value.photo = `${window.location.origin}/profile/users/${value.photo}`;
                        // else if(value.gender == 1 || value.gender == 0) value.photo=`${window.location.origin}/images/avatar.png`;
                        // else if(value.gender == 2) value.photo=`${window.location.origin}/images/female.jpg`;
                        value.photo = jsGetProfilImage(value);
                        value.iconhed = (value.first_name.charAt(0)) + value.last_name.charAt(0);
                        formatUser(value)
                    });
                }else{
                    formatUser()
                }

            }
        });
    }

    function formatUser (user = null) {
        if(user == null){
            if($('#show_search_result').length) $('#show_search_result').empty()
            return $('#show_search_result').append(`<div>
                    <div class="media m-2">
                        <img class="d-block ui-w-45 ui-ch-45 mr-1" style="padding: 0px 0px;margin-left:40%;background: #e00;" src="${window.location.origin}/image_not_found.png">

                    </div>
                </div>`)

        }else
            $('#show_search_result').append(`<div id="subList${user.id}">
            <a href="javascript:void(0)" onclick="switch_user('${user.id}')" class="media subuser_search_result m-2" data-id="${user.id}">
                <img class="d-block ui-w-30 ui-ch-30 rounded-circle mr-1" style="padding: 4px 5px;color: #333304;background: #2d458e5e;" src="${ user.photo }" alt="${ user.iconhed }">
                <div class="media-body pt-1">
                    <h5 class="mt-0" style="color: #4E5155;">${user.name} - ${user.gebdatum}</h5>
                </div>
            </a>
        </div>`)
    };
        /**
    * Change Reaction Status
    * @param current status
    * @return success status
    */
    function changeRAstatus(status,e){
        $('.ra-pdf-check').toggleClass('hide');

        $.post(window.location.origin+'/Sajax/change_reaction_status', {status: status }, function(data, status) {
            var msg = data; // Assuming the response contains a JSON object with "success" and "message" properties

            if (msg.success == true) {
                toastr.success(msg.message);

                if(!msg.status)$(".reaction-change").prop( "checked", true );
                else $(".reaction-change").prop("checked", false );
            }
        });
    }
    /**
    * Change Reaction PDF Status
    * @param current status
    * @return success status
    */
    function changeRApdf(status,e){
        e.preventDefault();
        $.post(window.location.origin+'/Sajax/change_reaction_pdf_show', {status: status }, function(data, status) {
            var msg = data; // Assuming the response contains a JSON object with "success" and "message" properties

            if (msg.success == true) {
                toastr.success(msg.message);

                if(!msg.status)$(".reaction-pdf").prop( "checked", true );
                else $(".reaction-pdf").prop( "checked", false );
            }
        });
    }


    // Change Datumcore To users
    function changeDatumcore(check) {
        var date = $('#datePicker').val();
        $.post(window.location.origin+'/Sajax/changeDatumcore', { date : date,check : check }, function(data, status) {
            var msg = data; // Assuming the response contains a JSON object with "success" and "message" properties

            if (msg.success == true) {
                __datePicker.setDate(msg.date);
                toastr.success(msg.message);
                window.location.reload();
            }
        });
    }

    var xhr; // Variable to hold the XMLHttpRequest object
    let globUserName = null;
    function usernameFunc(e) {
        // If there's an ongoing AJAX request, abort it
        if (xhr) xhr.abort();

        var firstname = $('#first_name').val().toLowerCase();
        var lastname = $('#last_name').val().toLowerCase();

        var suggestedUsername = ($(e).attr('name') == 'user_name' || globUserName) ? $('#user_name').val() : (firstname + lastname + (Math.floor(Math.random() * 100))).replace(/\s/g, '');
        if($(e).attr('name') == 'user_name') globUserName = suggestedUsername;

        // Send an AJAX request to check if the username exists in the database
        xhr = $.ajax({
            url: window.location.origin+'/users/check-user-info-exists/?user_name=' + suggestedUsername,
            method: 'GET',
            dataType: 'json',
            success: function(data) {
                if (data.exists) {
                    // Username already exists, show an error message or take other action
                    $('#user_name').addClass('error');
                    // Display an error message, e.g., $('#error_message').text("Username already exists.");
                } else {
                    // Username doesn't exist, set the value of the input field
                    if($('#user_name').hasClass('error')) $('#user_name').removeClass('error')
                    $('#user_name').val(suggestedUsername);
                }
            }
        });
    }

    var emailReq; // Variable to hold the XMLHttpRequest object

    async function userEmailFunc(e) {
        // If there's an ongoing AJAX request, abort it
        if (emailReq)  emailReq.abort();

        var email = $(e).val()
        // Send an AJAX request to check if the username exists in the database
        const response = await fetch(`/users/check-user-info-exists/?email=${email}`);
        const data = await response.json();

        if (data.exists) {
            // Username already exists, show an error message or take other action
            $(e).addClass('error');
            // Display an error message, e.g., $('#error_message').text("Username already exists.");
        } else {
            // Username doesn't exist, set the value of the input field
            if($(e).hasClass('error')) $(e).removeClass('error')
            $(e).val(email);
        }
    }

    // You can always Copy URL By Using This Class .copy-link
    $(document).on('click','.copy-link',function(){
        event.preventDefault();
        var url = $(this).data('url');
        var tempInput = $('<input>');
        $('body').append(tempInput);
        tempInput.val(url).select();
        try {
            document.execCommand('copy');
            toastr.success('URL copied to clipboard: ' + url);
        } catch (err) {
            console.error('Unable to copy URL to clipboard:', err);
        }
        tempInput.remove();
    });


    $(document).on('click', '.cartContent, .treatment-list-icon', function () {
        let price = 0;
        $('.--cart-datas').find('li:not([style="display: none;"])').each(function () {
            // Use $(this) to reference the current 'li' element
            let p = $(this).find('p').data('cartprice')
            if(p)price += $(this).find('p').data('cartprice');
        });
        // Calculate hours, minutes, and seconds
        const hours = Math.floor(price / 3600);
        price %= 3600;
        const minutes = Math.floor(price / 60);
        const seconds = price % 60;

        // Format the time as "hh:mm:ss"
        const formattedTime =
            String(hours).padStart(2, '0') + ':' +
            String(minutes).padStart(2, '0') + ':' +
            String(seconds).padStart(2, '0');
        // Update the cart total time element
        $(document).find('.right-cart-total-time').html('<i class="fas fa-clock f-12 text-light pr-1 "></i>'+formattedTime);
    });

    function jsGetProfilImage(value) {
        const assetPaths = [
            'default-profile/user-female.svg',   // Person (Male base)
            'default-profile/user-male.svg',     // Person (Female base)
            'default-profile/office.svg',        // Company
            'default-profile/animal.svg',        // Animal
            'default-profile/house.svg',         // House
            'default-profile/product.svg',       // Thing
        ];

        if (value.photo) {
            if (value.user_type === 0) {
              return value.photo = `${window.location.origin}/profile/users/${value.photo}`;
            } else {
                return value.photo = `${window.location.origin}/profile/admin/${value.photo}`;
            }
        } else {
            if (value.select_user_view === 1) {
              return value.gender === 2 ? `${window.location.origin}/${assetPaths[0]}` : `${window.location.origin}/${assetPaths[1]}`;
            } else {
              return `${window.location.origin}/${assetPaths[value.select_user_view]}`;
            }
        }
    }

    document.addEventListener('swal:modal', function (e) {
        Swal.fire(e.detail);
    });

    document.addEventListener('showShoppingCart', function (e) {
        $("#shoppingCart").show();
    });

    document.addEventListener('toastrAdded', function(event) {
        const [data] = event.detail;

        switch(data.type) {
            case 'success':
                toastr.success(data.text);
                break;
            case 'error':
                toastr.error(data.text);
                break;
            case 'warning':
                toastr.warning(data.text);
                break;
            default:
                toastr.info(data.text);
        }
    });
